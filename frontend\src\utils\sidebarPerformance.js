/**
 * 侧边栏性能监控和优化工具
 * 用于检测和解决侧边栏闪烁问题
 */

/**
 * 性能监控器
 */
export class SidebarPerformanceMonitor {
  constructor() {
    this.renderCount = 0
    this.lastRenderTime = 0
    this.renderTimes = []
    this.isMonitoring = false
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    this.isMonitoring = true
    this.renderCount = 0
    this.renderTimes = []
    console.log('🔍 侧边栏性能监控已启动')
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false
    this.logPerformanceReport()
  }

  /**
   * 记录渲染
   */
  recordRender() {
    if (!this.isMonitoring) return

    const now = performance.now()
    this.renderCount++
    
    if (this.lastRenderTime > 0) {
      const timeDiff = now - this.lastRenderTime
      this.renderTimes.push(timeDiff)
      
      // 检测异常频繁的渲染
      if (timeDiff < 16) { // 小于一帧的时间
        console.warn('⚠️ 检测到频繁渲染:', timeDiff + 'ms')
      }
    }
    
    this.lastRenderTime = now
  }

  /**
   * 输出性能报告
   */
  logPerformanceReport() {
    if (this.renderTimes.length === 0) {
      console.log('📊 性能报告: 无渲染数据')
      return
    }

    const avgRenderTime = this.renderTimes.reduce((a, b) => a + b, 0) / this.renderTimes.length
    const minRenderTime = Math.min(...this.renderTimes)
    const maxRenderTime = Math.max(...this.renderTimes)
    const frequentRenders = this.renderTimes.filter(time => time < 16).length

    console.log('📊 侧边栏性能报告:')
    console.log(`   总渲染次数: ${this.renderCount}`)
    console.log(`   平均渲染间隔: ${avgRenderTime.toFixed(2)}ms`)
    console.log(`   最小渲染间隔: ${minRenderTime.toFixed(2)}ms`)
    console.log(`   最大渲染间隔: ${maxRenderTime.toFixed(2)}ms`)
    console.log(`   频繁渲染次数: ${frequentRenders} (${((frequentRenders / this.renderTimes.length) * 100).toFixed(1)}%)`)
    
    if (frequentRenders > this.renderTimes.length * 0.3) {
      console.warn('⚠️ 检测到过多频繁渲染，可能存在性能问题')
    }
  }
}

/**
 * 防抖工具函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func(...args)
  }
}

/**
 * 节流工具函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 检测浏览器性能
 */
export function detectBrowserPerformance() {
  const performance = {
    supportsWillChange: CSS.supports('will-change', 'transform'),
    supportsTransform3d: CSS.supports('transform', 'translate3d(0,0,0)'),
    supportsBackfaceVisibility: CSS.supports('backface-visibility', 'hidden'),
    supportsContain: CSS.supports('contain', 'layout'),
    devicePixelRatio: window.devicePixelRatio || 1,
    hardwareConcurrency: navigator.hardwareConcurrency || 4
  }

  console.log('🔧 浏览器性能检测:', performance)
  return performance
}

/**
 * 优化建议
 */
export function getOptimizationSuggestions() {
  const perf = detectBrowserPerformance()
  const suggestions = []

  if (!perf.supportsWillChange) {
    suggestions.push('浏览器不支持 will-change，考虑使用 transform3d 替代')
  }

  if (!perf.supportsTransform3d) {
    suggestions.push('浏览器不支持 3D 变换，硬件加速可能无效')
  }

  if (!perf.supportsBackfaceVisibility) {
    suggestions.push('浏览器不支持 backface-visibility，可能存在渲染问题')
  }

  if (perf.devicePixelRatio > 2) {
    suggestions.push('高分辨率屏幕，建议优化图像和动画性能')
  }

  if (perf.hardwareConcurrency < 4) {
    suggestions.push('CPU核心数较少，建议减少复杂动画')
  }

  return suggestions
}

/**
 * 侧边栏优化配置
 */
export const sidebarOptimizationConfig = {
  // 滚动事件防抖延迟
  scrollDebounceDelay: 16,
  
  // 拖拽事件节流延迟
  dragThrottleDelay: 8,
  
  // 位置更新防抖延迟
  positionUpdateDelay: 10,
  
  // 样式计算缓存时间
  styleCacheTime: 100,
  
  // 是否启用硬件加速
  enableHardwareAcceleration: true,
  
  // 是否禁用动画
  disableAnimations: true,
  
  // 是否启用性能监控
  enablePerformanceMonitoring: process.env.NODE_ENV === 'development'
}

/**
 * 应用优化配置到元素
 */
export function applySidebarOptimizations(element) {
  if (!element) return

  const config = sidebarOptimizationConfig

  if (config.enableHardwareAcceleration) {
    element.style.transform = 'translateZ(0)'
    element.style.backfaceVisibility = 'hidden'
    element.style.perspective = '1000px'
  }

  if (config.disableAnimations) {
    element.style.transition = 'none !important'
    element.style.animation = 'none !important'
  }

  // 设置 will-change 属性
  element.style.willChange = 'transform'

  // 设置 contain 属性（如果支持）
  if (CSS.supports('contain', 'layout')) {
    element.style.contain = 'layout style paint'
  }
}

// 创建全局性能监控实例
export const globalPerformanceMonitor = new SidebarPerformanceMonitor()

// 在开发环境下自动启动监控
if (process.env.NODE_ENV === 'development') {
  globalPerformanceMonitor.startMonitoring()
  
  // 5秒后输出报告
  setTimeout(() => {
    globalPerformanceMonitor.logPerformanceReport()
  }, 5000)
}
