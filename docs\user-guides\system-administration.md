# 🔧 系统管理功能指南

## 📋 概述

本指南介绍了梆梆安全-运维信息登记平台的系统管理功能，包括系统配置、文件管理、定时任务、权限管理等核心管理功能。

## 🎛️ 系统管理界面

### 访问方式
1. 使用管理员账户登录系统
2. 点击导航栏的"系统管理"菜单
3. 选择相应的管理功能模块

### 功能模块
- **用户权限管理**: 用户、角色、权限配置
- **模板管理**: Excel模板文件管理
- **文件管理**: 系统文件清理和管理
- **定时任务管理**: 自动化任务配置
- **系统配置**: 系统参数和设置
- **监控面板**: 系统状态监控

## 👥 用户权限管理

### 用户管理
#### 用户列表
- 查看所有系统用户
- 支持分页和搜索功能
- 显示用户状态和最后登录时间
- 批量操作（启用/禁用/删除）

#### 用户操作
```javascript
// 创建用户
POST /api/users
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "roles": ["user"],
  "groups": ["default"]
}

// 更新用户状态
PUT /api/users/{userId}/status
{
  "status": "active" | "inactive"
}

// 重置用户密码
POST /api/users/{userId}/reset-password
{
  "newPassword": "newpassword123"
}
```

### 角色管理
#### 角色配置
- **系统角色**: 预定义的系统角色
- **自定义角色**: 根据业务需求创建的角色
- **权限分配**: 为角色分配具体权限
- **角色继承**: 支持角色间的权限继承

#### 权限矩阵
| 功能模块 | 超级管理员 | 系统管理员 | 业务管理员 | 普通用户 |
|----------|------------|------------|------------|----------|
| 用户管理 | ✅ | ✅ | ❌ | ❌ |
| 角色管理 | ✅ | ✅ | ❌ | ❌ |
| 表单管理 | ✅ | ✅ | ✅ | ✅ |
| 组件管理 | ✅ | ✅ | ❌ | ❌ |
| 系统配置 | ✅ | ❌ | ❌ | ❌ |
| 文件管理 | ✅ | ✅ | ❌ | ❌ |

### 用户组管理
#### 组织架构
- **层级结构**: 支持多级组织架构
- **权限继承**: 子组继承父组权限
- **批量管理**: 批量分配用户和权限
- **数据隔离**: 基于用户组的数据访问控制

## 📄 模板管理

### 模板文件管理
#### 模板上传
1. 点击"模板管理"选项卡
2. 选择表单类型（安全测评/安全监测/应用加固）
3. 点击"上传模板"按钮
4. 选择Excel模板文件
5. 填写模板描述信息
6. 确认上传

#### 模板版本控制
- **版本管理**: 支持多个版本的模板
- **版本切换**: 可以动态切换使用的模板版本
- **版本备份**: 自动备份历史版本
- **版本对比**: 对比不同版本的差异

#### 模板配置
```javascript
// 模板配置示例
{
  "templateId": "security_testing_v2",
  "name": "安全测评模板 v2.0",
  "formType": "安全测评",
  "version": "2.0",
  "description": "更新了组件分类和占位符",
  "filePath": "/templates/security_testing_v2.xlsx",
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-15T00:00:00Z"
}
```

### 模板导入导出
#### 导出模板
1. 在模板列表中选择要导出的模板
2. 点击"导出"按钮
3. 选择导出格式（Excel文件/配置JSON）
4. 下载导出文件

#### 导入模板
1. 点击"导入模板"按钮
2. 选择模板文件或配置文件
3. 系统自动验证模板格式
4. 确认导入并设置模板信息

## 📁 文件管理

### Excel文件清理
#### 清理规则配置
- **保留时间**: 设置文件保留天数
- **文件大小**: 设置单个文件大小限制
- **总容量**: 设置目录总容量限制
- **文件类型**: 指定清理的文件类型

#### 清理策略
```javascript
// 清理规则配置
{
  "retentionDays": 30,        // 保留30天
  "maxFileSize": "100MB",     // 单文件最大100MB
  "maxTotalSize": "10GB",     // 总容量最大10GB
  "fileTypes": [".xlsx", ".xls"], // 清理的文件类型
  "excludePatterns": ["backup_*"], // 排除的文件模式
  "cleanupSchedule": "0 2 * * *"   // 每天凌晨2点执行
}
```

#### 手动清理
1. 访问"文件管理"页面
2. 查看当前文件统计信息
3. 设置清理条件（时间范围、文件大小等）
4. 预览将要清理的文件列表
5. 确认执行清理操作

#### 自动清理
- **定时清理**: 根据配置的时间自动执行
- **容量触发**: 当存储容量达到阈值时自动清理
- **清理日志**: 记录每次清理的详细信息
- **清理报告**: 生成清理统计报告

### 备份文件管理
#### 备份策略
- **自动备份**: 系统自动创建重要文件备份
- **手动备份**: 用户手动创建备份
- **增量备份**: 只备份变更的文件
- **压缩备份**: 压缩备份文件节省空间

#### 备份恢复
1. 在备份列表中选择要恢复的备份
2. 选择恢复目标位置
3. 确认恢复操作
4. 系统自动恢复文件并验证完整性

## ⏰ 定时任务管理

### 任务类型
- **文件清理任务**: 定期清理过期文件
- **数据备份任务**: 定期备份重要数据
- **系统监控任务**: 定期检查系统状态
- **报告生成任务**: 定期生成统计报告

### 任务配置
#### 创建定时任务
1. 点击"新增任务"按钮
2. 选择任务类型
3. 配置任务参数：
   - 任务名称和描述
   - 执行时间（Cron表达式）
   - 任务参数和配置
   - 失败重试策略
4. 保存任务配置

#### Cron表达式示例
```bash
# 每天凌晨2点执行
0 2 * * *

# 每周一凌晨3点执行
0 3 * * 1

# 每月1号凌晨4点执行
0 4 1 * *

# 每小时执行一次
0 * * * *

# 每5分钟执行一次
*/5 * * * *
```

### 任务监控
#### 任务状态
- **等待中**: 任务已创建，等待执行
- **运行中**: 任务正在执行
- **已完成**: 任务执行成功
- **失败**: 任务执行失败
- **已停止**: 任务被手动停止

#### 任务日志
```javascript
// 任务执行日志
{
  "taskId": "cleanup_task_001",
  "taskName": "Excel文件清理",
  "startTime": "2024-01-01T02:00:00Z",
  "endTime": "2024-01-01T02:05:30Z",
  "status": "completed",
  "result": {
    "filesProcessed": 150,
    "filesDeleted": 45,
    "spaceFreed": "2.3GB"
  },
  "logs": [
    "开始扫描文件...",
    "找到150个文件",
    "删除45个过期文件",
    "清理完成，释放空间2.3GB"
  ]
}
```

## ⚙️ 系统配置

### 基础配置
#### 系统参数
- **系统名称**: 平台显示名称
- **系统版本**: 当前系统版本信息
- **时区设置**: 系统默认时区
- **语言设置**: 系统默认语言

#### 安全配置
- **密码策略**: 密码复杂度要求
- **会话超时**: 用户会话超时时间
- **登录限制**: 登录失败次数限制
- **IP白名单**: 允许访问的IP地址

### 业务配置
#### 表单配置
- **默认表单类型**: 系统默认的表单类型
- **必填字段**: 全局必填字段配置
- **字段验证**: 字段格式验证规则
- **自动保存**: 表单自动保存间隔

#### Excel配置
- **模板路径**: Excel模板文件存储路径
- **生成路径**: Excel生成文件存储路径
- **文件命名**: 生成文件的命名规则
- **压缩设置**: 文件压缩和优化设置

### 缓存配置
#### Redis配置
- **缓存服务器**: Redis服务器地址和端口
- **缓存数据库**: 使用的Redis数据库编号
- **缓存过期**: 各类缓存的过期时间
- **缓存策略**: 缓存更新和淘汰策略

## 📊 监控面板

### 系统状态
#### 服务状态
- **Web服务**: Flask应用运行状态
- **数据库**: MySQL连接状态
- **缓存服务**: Redis连接状态
- **文件系统**: 磁盘空间使用情况

#### 性能指标
```javascript
// 系统性能指标
{
  "cpu": {
    "usage": "25.6%",
    "cores": 4
  },
  "memory": {
    "used": "2.1GB",
    "total": "8GB",
    "usage": "26.3%"
  },
  "disk": {
    "used": "45.2GB",
    "total": "100GB",
    "usage": "45.2%"
  },
  "network": {
    "inbound": "1.2MB/s",
    "outbound": "0.8MB/s"
  }
}
```

### 业务统计
#### 用户统计
- **在线用户**: 当前在线用户数量
- **活跃用户**: 最近活跃用户统计
- **新增用户**: 新注册用户统计
- **用户分布**: 按角色和用户组分布

#### 表单统计
- **表单提交**: 表单提交数量统计
- **Excel生成**: Excel文件生成统计
- **文件下载**: 文件下载次数统计
- **错误统计**: 系统错误和异常统计

### 告警配置
#### 告警规则
- **CPU使用率**: 超过80%时告警
- **内存使用率**: 超过85%时告警
- **磁盘空间**: 剩余空间少于10%时告警
- **服务异常**: 服务停止或连接失败时告警

#### 告警通知
- **邮件通知**: 发送告警邮件
- **短信通知**: 发送告警短信
- **系统通知**: 在系统内显示告警信息
- **日志记录**: 记录告警详细信息

## 🔧 系统维护

### 数据库维护
#### 数据备份
- **自动备份**: 定期自动备份数据库
- **手动备份**: 管理员手动创建备份
- **备份验证**: 验证备份文件完整性
- **备份清理**: 清理过期的备份文件

#### 数据恢复
1. 选择要恢复的备份文件
2. 确认恢复目标数据库
3. 停止相关服务
4. 执行数据恢复操作
5. 验证数据完整性
6. 重启相关服务

### 系统更新
#### 版本升级
- **版本检查**: 检查是否有新版本
- **升级包下载**: 下载升级包
- **备份当前版本**: 升级前备份
- **执行升级**: 自动或手动升级
- **升级验证**: 验证升级结果

#### 配置迁移
- **配置导出**: 导出当前系统配置
- **配置导入**: 导入新的系统配置
- **配置对比**: 对比配置差异
- **配置回滚**: 回滚到之前的配置

## 📚 相关文档

- [用户权限管理](user-permissions.md)
- [API接口文档](../backend/api-documentation.md)
- [监控指南](../operations/monitoring.md)
- [备份恢复指南](../operations/backup-recovery.md)
