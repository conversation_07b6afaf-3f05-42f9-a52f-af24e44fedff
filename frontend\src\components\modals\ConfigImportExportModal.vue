<template>
  <!-- 配置导入导出模态框 -->
  <div class="modal fade" id="configImportExportModal" tabindex="-1" aria-labelledby="configImportExportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="configImportExportModalLabel">
            <i class="bi bi-arrow-down-up me-2"></i>
            配置导入导出
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- 选项卡 -->
          <ul class="nav nav-tabs" id="configTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button 
                class="nav-link active" 
                id="export-tab" 
                data-bs-toggle="tab" 
                data-bs-target="#export-pane" 
                type="button" 
                role="tab"
              >
                <i class="bi bi-download me-1"></i>导出配置
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button 
                class="nav-link" 
                id="import-tab" 
                data-bs-toggle="tab" 
                data-bs-target="#import-pane" 
                type="button" 
                role="tab"
              >
                <i class="bi bi-upload me-1"></i>导入配置
              </button>
            </li>
          </ul>

          <div class="tab-content mt-3" id="configTabContent">
            <!-- 导出配置 -->
            <div class="tab-pane fade show active" id="export-pane" role="tabpanel">
              <div class="export-section">
                <h6>导出当前配置</h6>
                <p class="text-muted">将当前表单类型的字段配置导出为JSON文件</p>
                
                <div class="mb-3">
                  <label class="form-label">选择导出内容</label>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="exportGroups" v-model="exportOptions.groups">
                    <label class="form-check-label" for="exportGroups">
                      字段分组 ({{ fieldGroups.length }} 个)
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="exportFields" v-model="exportOptions.fields">
                    <label class="form-check-label" for="exportFields">
                      字段配置 ({{ fieldConfigs.length }} 个)
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="exportMetadata" v-model="exportOptions.metadata">
                    <label class="form-check-label" for="exportMetadata">
                      元数据信息
                    </label>
                  </div>
                </div>

                <div class="mb-3">
                  <label for="exportFileName" class="form-label">文件名</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    id="exportFileName"
                    v-model="exportFileName"
                    placeholder="配置文件名"
                  >
                  <div class="form-text">不需要包含.json扩展名</div>
                </div>

                <button class="btn btn-primary" @click="exportConfig" :disabled="!canExport">
                  <i class="bi bi-download me-1"></i>导出配置
                </button>
              </div>
            </div>

            <!-- 导入配置 -->
            <div class="tab-pane fade" id="import-pane" role="tabpanel">
              <div class="import-section">
                <h6>导入配置文件</h6>
                <p class="text-muted">从JSON文件导入字段配置</p>

                <div class="mb-3">
                  <label for="importFile" class="form-label">选择配置文件</label>
                  <input 
                    type="file" 
                    class="form-control" 
                    id="importFile"
                    accept=".json"
                    @change="handleFileSelect"
                  >
                  <div class="form-text">仅支持JSON格式的配置文件</div>
                </div>

                <div v-if="importPreview" class="mb-3">
                  <h6>导入预览</h6>
                  <div class="card">
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-6">
                          <strong>表单类型:</strong> {{ importPreview.formType }}
                        </div>
                        <div class="col-md-6">
                          <strong>导出时间:</strong> {{ formatDate(importPreview.exportTime) }}
                        </div>
                      </div>
                      <div class="row mt-2">
                        <div class="col-md-4">
                          <strong>字段分组:</strong> {{ importPreview.groups ? importPreview.groups.length : 0 }} 个
                        </div>
                        <div class="col-md-4">
                          <strong>字段配置:</strong> {{ importPreview.fields ? importPreview.fields.length : 0 }} 个
                        </div>
                        <div class="col-md-4">
                          <strong>版本:</strong> {{ importPreview.version || 'N/A' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="importPreview" class="mb-3">
                  <label class="form-label">导入选项</label>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="importGroups" v-model="importOptions.groups">
                    <label class="form-check-label" for="importGroups">
                      导入字段分组
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="importFields" v-model="importOptions.fields">
                    <label class="form-check-label" for="importFields">
                      导入字段配置
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="importMode" id="importModeAdd" v-model="importMode" value="add">
                    <label class="form-check-label" for="importModeAdd">
                      添加模式（保留现有配置）
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="importMode" id="importModeReplace" v-model="importMode" value="replace">
                    <label class="form-check-label" for="importModeReplace">
                      替换模式（覆盖现有配置）
                    </label>
                  </div>
                </div>

                <button 
                  class="btn btn-success" 
                  @click="importConfig" 
                  :disabled="!canImport"
                  v-if="importPreview"
                >
                  <i class="bi bi-upload me-1"></i>导入配置
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfigImportExportModal',
  props: {
    formType: {
      type: String,
      default: ''
    },
    fieldGroups: {
      type: Array,
      default: () => []
    },
    fieldConfigs: {
      type: Array,
      default: () => []
    }
  },
  emits: ['show-toast', 'config-imported'],
  data() {
    return {
      exportOptions: {
        groups: true,
        fields: true,
        metadata: true
      },
      exportFileName: '',
      importOptions: {
        groups: true,
        fields: true
      },
      importMode: 'add',
      importPreview: null,
      selectedFile: null
    }
  },
  computed: {
    canExport() {
      return this.exportOptions.groups || this.exportOptions.fields
    },
    canImport() {
      return this.importPreview && (this.importOptions.groups || this.importOptions.fields)
    }
  },
  methods: {
    show() {
      // 重置状态
      this.exportFileName = `${this.formType}-字段配置-${new Date().toISOString().split('T')[0]}`
      this.importPreview = null
      this.selectedFile = null

      // 清理可能存在的模态框实例和遮罩
      this.cleanupModal()

      // 使用强力遮罩清理器
      if (window.backdropKiller) {
        window.backdropKiller.kill()
        setTimeout(() => {
          window.backdropKiller.safeShow('configImportExportModal', {
            backdrop: false,
            keyboard: true
          })
        }, 100)
      } else {
        // 备用方案
        const modalElement = document.getElementById('configImportExportModal')
        if (modalElement) {
          setTimeout(() => {
            modalElement.style.display = 'block'
            modalElement.classList.add('show')
            modalElement.setAttribute('aria-modal', 'true')
            modalElement.removeAttribute('aria-hidden')
            document.body.classList.add('modal-open')
          }, 100)
        }
      }
    },

    cleanupModal() {
      try {
        // 清理可能存在的模态框实例
        const modalElement = document.getElementById('configImportExportModal')
        if (modalElement) {
          const existingModal = window.bootstrap.Modal.getInstance(modalElement)
          if (existingModal) {
            existingModal.dispose()
          }
        }

        // 清理遮罩层
        const backdrops = document.querySelectorAll('.modal-backdrop')
        backdrops.forEach(backdrop => {
          if (backdrop.parentNode) {
            backdrop.parentNode.removeChild(backdrop)
          }
        })

        // 重置body样式
        document.body.classList.remove('modal-open')
        document.body.style.overflow = ''
        document.body.style.paddingRight = ''
      } catch (error) {
        console.warn('清理配置导入导出模态框时出错:', error)
      }
    },

    exportConfig() {
      try {
        const config = {
          formType: this.formType,
          exportTime: new Date().toISOString(),
          version: '1.0'
        }

        if (this.exportOptions.groups) {
          config.groups = this.fieldGroups
        }

        if (this.exportOptions.fields) {
          config.fields = this.fieldConfigs
        }

        if (this.exportOptions.metadata) {
          config.metadata = {
            totalGroups: this.fieldGroups.length,
            totalFields: this.fieldConfigs.length,
            exportedBy: 'FormFieldConfig',
            exportedAt: new Date().toISOString()
          }
        }

        // 创建下载
        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${this.exportFileName}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        this.$emit('show-toast', '配置导出成功', '成功', 'success')
      } catch (error) {
        console.error('导出配置失败:', error)
        this.$emit('show-toast', '导出配置失败', '错误', 'error')
      }
    },

    handleFileSelect(event) {
      const file = event.target.files[0]
      if (!file) return

      this.selectedFile = file
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const config = JSON.parse(e.target.result)
          this.importPreview = config
        } catch (error) {
          console.error('解析配置文件失败:', error)
          this.$emit('show-toast', '配置文件格式错误', '错误', 'error')
          this.importPreview = null
        }
      }
      
      reader.readAsText(file)
    },

    async importConfig() {
      if (!this.importPreview) return

      try {
        // 这里应该调用后端API来导入配置
        // 目前只是模拟导入过程
        
        this.$emit('show-toast', '配置导入功能开发中', '提示', 'info')
        
        // 关闭模态框
        const modal = window.bootstrap.Modal.getInstance(document.getElementById('configImportExportModal'))
        modal.hide()
        
        // 触发导入完成事件
        this.$emit('config-imported')
      } catch (error) {
        console.error('导入配置失败:', error)
        this.$emit('show-toast', '导入配置失败', '错误', 'error')
      }
    },

    formatDate(dateString) {
      if (!dateString) return 'N/A'
      return new Date(dateString).toLocaleString()
    }
  },

  beforeUnmount() {
    // 组件销毁前清理模态框
    this.cleanupModal()
  }
}
</script>

<style scoped>
/* 确保模态框层级正确 */
:deep(.modal) {
  z-index: 1060 !important;
}

:deep(.modal-backdrop) {
  z-index: 1055 !important;
}

/* 修复模态框显示问题 */
:deep(.modal.show) {
  display: block !important;
}

:deep(.modal-dialog) {
  margin: 1.75rem auto;
  pointer-events: auto;
}

:deep(.modal-content) {
  pointer-events: auto;
}

.export-section,
.import-section {
  padding: 1rem 0;
}

.form-check {
  margin-bottom: 0.5rem;
}

.card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.nav-tabs .nav-link {
  color: #495057;
}

.nav-tabs .nav-link.active {
  color: #0d6efd;
  border-color: #0d6efd #0d6efd #fff;
}

.form-text {
  font-size: 0.875rem;
  color: #6c757d;
}
</style>
