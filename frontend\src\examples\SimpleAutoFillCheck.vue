<template>
  <div class="simple-auto-fill-check">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-check-circle me-2"></i>
                简单自动填充检查
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 步骤说明 -->
              <div class="alert alert-info mb-4">
                <h6 class="alert-heading">测试步骤</h6>
                <ol class="mb-0">
                  <li>点击"添加测试服务器"按钮</li>
                  <li>观察控制台日志</li>
                  <li>检查访问信息字段是否自动填充</li>
                </ol>
              </div>

              <!-- 操作按钮 -->
              <div class="row mb-4">
                <div class="col-12">
                  <button class="btn btn-primary me-2" @click="addTestServer">
                    添加测试服务器
                  </button>
                  <button class="btn btn-success me-2" @click="checkResults">
                    检查结果
                  </button>
                  <button class="btn btn-warning" @click="clearData">
                    清空数据
                  </button>
                </div>
              </div>

              <!-- 服务器信息显示 -->
              <div class="row mb-4">
                <div class="col-12">
                  <h6>服务器信息 ({{ testData.服务器信息.length }})</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <pre>{{ JSON.stringify(testData.服务器信息, null, 2) }}</pre>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 直接测试DynamicAccessInfoSection -->
              <div class="row mb-4">
                <div class="col-12">
                  <h6>访问信息组件测试</h6>
                  <div class="card">
                    <div class="card-body">
                      <dynamic-access-info-section
                        v-model="accessData"
                        :field-config="accessFieldConfig"
                        form-type="安全测评"
                        :server-list="testData.服务器信息"
                        :key="`access-${refreshKey}`"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 访问数据显示 -->
              <div class="row">
                <div class="col-12">
                  <h6>访问数据结果</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-6">
                          <strong>访问数据:</strong>
                          <pre>{{ JSON.stringify(accessData, null, 2) }}</pre>
                        </div>
                        <div class="col-md-6">
                          <strong>字段配置:</strong>
                          <pre>{{ JSON.stringify(accessFieldConfig, null, 2) }}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicAccessInfoSection from '@/components/forms/common/DynamicAccessInfoSection.vue'
import { getFormFieldConfig } from '@/config/formFieldConfig'
import { getSecurityTestingInitialData, createNewServerItem } from '@/config/formDataConfig'

export default {
  name: 'SimpleAutoFillCheck',
  components: {
    DynamicAccessInfoSection
  },
  data() {
    return {
      testData: {},
      accessData: {},
      refreshKey: 0
    }
  },
  computed: {
    accessFieldConfig() {
      const config = getFormFieldConfig('安全测评')
      return config.access || {}
    }
  },
  methods: {
    /**
     * 添加测试服务器
     */
    addTestServer() {
      console.log('🚀 开始添加测试服务器...')
      
      const server = createNewServerItem()
      server.IP地址 = `192.168.1.${100 + this.testData.服务器信息.length}`
      server.部署应用 = ['front-ssp-admin', 'front-ssp-user', 'luna', 'backend-ssp-user']
      server.组件端口 = {
        'front-ssp-admin': '8080',
        'front-ssp-user': '8081',
        'luna': '9001',
        'backend-ssp-user': '8082'
      }
      
      console.log('📝 创建的服务器:', server)
      
      // 添加到服务器列表
      this.testData.服务器信息.push(server)
      
      console.log('📋 当前服务器列表:', this.testData.服务器信息)
      console.log('🔧 访问字段配置:', this.accessFieldConfig)
      
      // 强制刷新组件
      this.refreshKey += 1
      
      // 等待一下再检查结果
      setTimeout(() => {
        console.log('⏰ 延迟检查访问数据:', this.accessData)
      }, 1000)
    },

    /**
     * 检查结果
     */
    checkResults() {
      console.log('🔍 检查自动填充结果...')
      
      const expectedFields = {
        '管理员页面IP': '*************:8080',
        '用户页面IP': '*************:8081', 
        '升级页面IP': '*************:9001',
        '对外服务端口': '*************:8082'
      }
      
      console.log('📊 期望的字段值:', expectedFields)
      console.log('📊 实际的访问数据:', this.accessData)
      
      let successCount = 0
      let totalCount = Object.keys(expectedFields).length
      
      Object.keys(expectedFields).forEach(field => {
        const expected = expectedFields[field]
        const actual = this.accessData[this.getFieldKey(field)]
        
        if (actual === expected) {
          console.log(`✅ ${field}: ${actual}`)
          successCount++
        } else {
          console.log(`❌ ${field}: 期望 "${expected}", 实际 "${actual || '(空)'}"`)
        }
      })
      
      console.log(`📈 自动填充结果: ${successCount}/${totalCount} 成功`)
      
      if (successCount === totalCount) {
        alert('🎉 自动填充完全成功！')
      } else {
        alert(`⚠️ 自动填充部分成功: ${successCount}/${totalCount}`)
      }
    },

    /**
     * 获取字段对应的key
     */
    getFieldKey(fieldName) {
      const config = this.accessFieldConfig
      return Object.keys(config).find(key => config[key].field === fieldName)
    },

    /**
     * 清空数据
     */
    clearData() {
      console.log('🧹 清空数据...')
      this.testData = getSecurityTestingInitialData()
      this.accessData = {}
      this.refreshKey += 1
    }
  },
  watch: {
    // 监听服务器信息变化
    'testData.服务器信息': {
      handler(newList) {
        console.log('👀 服务器信息变化监听:', newList)
      },
      deep: true
    },

    // 监听访问数据变化
    accessData: {
      handler(newData) {
        console.log('👀 访问数据变化监听:', newData)
      },
      deep: true
    }
  },
  mounted() {
    console.log('🚀 SimpleAutoFillCheck 组件已挂载')
    this.testData = getSecurityTestingInitialData()
    console.log('📋 初始测试数据:', this.testData)
    console.log('🔧 访问字段配置:', this.accessFieldConfig)
  }
}
</script>

<style scoped>
.simple-auto-fill-check {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
}

pre {
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.75rem;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
}
</style>
