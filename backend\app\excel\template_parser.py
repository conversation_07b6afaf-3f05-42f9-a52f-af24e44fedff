"""
Excel模板解析器
支持解析Excel模板中的所有占位符，并从导入的Excel文件中提取对应的值
"""

import os
import re
import json
from datetime import datetime, date
from openpyxl import load_workbook
from flask import current_app
from typing import Dict, List, Any, Tuple, Optional


class TemplateParser:
    """Excel模板解析器"""
    
    def __init__(self, form_type: str):
        """
        初始化模板解析器
        
        Args:
            form_type: 表单类型（安全测评、安全监测、应用加固）
        """
        self.form_type = form_type
        self.template_path = self._get_template_path()
        self.placeholders = {}  # 存储所有占位符及其位置
        self.range_placeholders = {}  # 存储range类型的占位符
        self.component_placeholders = {}  # 存储组件类型的占位符
        
        if self.template_path:
            self._parse_template()
    
    def _get_template_path(self) -> Optional[str]:
        """获取模板文件路径"""
        try:
            templates_dir = current_app.config.get('EXCEL_TEMPLATES_FOLDER', 'backend/excel_files/templates')
            template_filename = f"{self.form_type}-运维信息登记模板.xlsx"
            template_path = os.path.join(templates_dir, template_filename)
            
            if os.path.exists(template_path):
                return template_path
            else:
                current_app.logger.warning(f"模板文件不存在: {template_path}")
                return None
        except Exception as e:
            current_app.logger.error(f"获取模板路径失败: {str(e)}")
            return None
    
    def _parse_template(self):
        """解析模板文件，提取所有占位符"""
        try:
            wb = load_workbook(self.template_path)
            
            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                self._parse_sheet(sheet, sheet_name)
                
            current_app.logger.info(f"模板解析完成，共找到 {len(self.placeholders)} 个占位符")
            
        except Exception as e:
            current_app.logger.error(f"解析模板失败: {str(e)}")
    
    def _parse_sheet(self, sheet, sheet_name: str):
        """解析单个sheet页"""
        for row_idx, row in enumerate(sheet.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value and isinstance(cell.value, str):
                    self._extract_placeholders(cell.value, sheet_name, row_idx, col_idx)
    
    def _extract_placeholders(self, cell_value: str, sheet_name: str, row_idx: int, col_idx: int):
        """从单元格值中提取占位符"""

        # 1. 提取简单占位符 {{字段名}}
        simple_pattern = r'\{\{\s*([^|}]+?)\s*\}\}'
        simple_matches = re.finditer(simple_pattern, cell_value)

        for match in simple_matches:
            field_name = match.group(1).strip()
            if '.' not in field_name and '|' not in field_name:
                # 计算占位符在单元格中的位置
                start_pos = match.start()
                end_pos = match.end()

                self.placeholders[field_name] = {
                    'sheet': sheet_name,
                    'row': row_idx,
                    'col': col_idx,
                    'type': 'simple',
                    'original_value': cell_value,
                    'placeholder_start': start_pos,
                    'placeholder_end': end_pos,
                    'placeholder_text': match.group(0)
                }
        
        # 2. 提取range占位符 {{item.字段名 | range:数据类型}}
        range_pattern = r'\{\{\s*item\.([^|}]+?)\s*\|\s*range:([^}]+?)\s*\}\}'
        range_matches = re.findall(range_pattern, cell_value)
        
        for field_name, data_type in range_matches:
            key = f"{data_type}.{field_name}"
            if data_type not in self.range_placeholders:
                self.range_placeholders[data_type] = {}
            
            self.range_placeholders[data_type][field_name] = {
                'sheet': sheet_name,
                'row': row_idx,
                'col': col_idx,
                'type': 'range',
                'data_type': data_type,
                'original_value': cell_value
            }
        
        # 3. 提取组件占位符 {{组件名.字段}}
        component_pattern = r'\{\{\s*([\w\-]+)\.([\w\-]+)\s*\}\}'
        component_matches = re.findall(component_pattern, cell_value)
        
        for comp_name, field_name in component_matches:
            if comp_name not in self.component_placeholders:
                self.component_placeholders[comp_name] = {}
            
            self.component_placeholders[comp_name][field_name] = {
                'sheet': sheet_name,
                'row': row_idx,
                'col': col_idx,
                'type': 'component',
                'component': comp_name,
                'original_value': cell_value
            }
        
        # 4. 提取split占位符 {{字段名 | split:col,分隔符}}
        split_pattern = r'\{\{\s*([^|}]+?)\s*\|\s*split:(col|row)(?:,([^}]+?))?\s*\}\}'
        split_matches = re.findall(split_pattern, cell_value)
        
        for field_name, split_type, separator in split_matches:
            field_name = field_name.strip()
            separator = separator.strip() if separator else ','
            
            self.placeholders[f"{field_name}_split"] = {
                'sheet': sheet_name,
                'row': row_idx,
                'col': col_idx,
                'type': 'split',
                'split_type': split_type,
                'separator': separator,
                'field_name': field_name,
                'original_value': cell_value
            }
    
    def parse_excel_file(self, excel_path: str) -> Dict[str, Any]:
        """
        解析Excel文件，根据模板占位符的位置提取数据

        Args:
            excel_path: Excel文件路径

        Returns:
            解析后的数据字典
        """
        try:
            wb = load_workbook(excel_path)
            extracted_data = {
                'basic_info': {},
                'servers': [],
                'components': {},
                'range_data': {},
                'form_type': self.form_type,
                'errors': [],
                'warnings': []
            }

            # 按照模板占位符的位置提取数据
            self._extract_by_template_positions(wb, extracted_data)

            # 后处理数据
            self._post_process_data(extracted_data)

            return extracted_data

        except Exception as e:
            current_app.logger.error(f"解析Excel文件失败: {str(e)}")
            return {
                'basic_info': {},
                'servers': [],
                'components': {},
                'range_data': {},
                'form_type': self.form_type,
                'errors': [f"解析Excel文件失败: {str(e)}"],
                'warnings': []
            }

    def _extract_by_template_positions(self, workbook, extracted_data: Dict[str, Any]):
        """根据模板占位符的位置提取数据"""

        position_success_count = 0
        total_fields = len(self.placeholders)

        # 1. 提取简单占位符的值
        for field_name, placeholder_info in self.placeholders.items():
            if placeholder_info['type'] == 'simple':
                value = self._extract_value_by_position(
                    workbook,
                    placeholder_info['sheet'],
                    placeholder_info['row'],
                    placeholder_info['col'],
                    field_name  # 传入字段名以支持多占位符解析
                )
                if value:
                    extracted_data['basic_info'][field_name] = value
                    position_success_count += 1
                    current_app.logger.debug(f"位置提取成功 {field_name}: {value} (位置: {placeholder_info['sheet']}[{placeholder_info['row']},{placeholder_info['col']}])")

        # 检查位置提取的成功率，如果太低则使用回退方法
        success_rate = position_success_count / total_fields if total_fields > 0 else 0
        current_app.logger.info(f"位置提取成功率: {success_rate:.2%} ({position_success_count}/{total_fields})")

        if success_rate < 0.3:  # 如果成功率低于30%，使用回退方法
            current_app.logger.warning("位置提取成功率过低，使用字段名搜索回退方法")
            self._extract_by_field_names(workbook, extracted_data)

        # 2. 提取range占位符的数据
        for data_type, fields in self.range_placeholders.items():
            range_data = self._extract_range_by_positions(workbook, data_type, fields)
            if range_data:
                if data_type == '服务器信息':
                    extracted_data['servers'].extend(range_data)
                else:
                    extracted_data['range_data'][data_type] = range_data
                current_app.logger.debug(f"从位置提取range数据 {data_type}: {len(range_data)}条记录")

        # 3. 提取组件占位符的数据
        for comp_name, fields in self.component_placeholders.items():
            component_data = self._extract_component_by_positions(workbook, comp_name, fields)
            if component_data:
                extracted_data['components'][comp_name] = component_data
                current_app.logger.debug(f"提取组件数据 {comp_name}: {component_data}")

    def _extract_by_field_names(self, workbook, extracted_data: Dict[str, Any]):
        """回退方法：根据字段名搜索提取数据"""
        current_app.logger.info("使用字段名搜索方法提取数据")

        # 遍历所有sheet页
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]

            # 提取简单字段值
            for field_name in self.placeholders.keys():
                if field_name not in extracted_data['basic_info']:  # 避免覆盖已有数据
                    value = self._find_field_value_in_sheet(sheet, field_name)
                    if value:
                        extracted_data['basic_info'][field_name] = value
                        current_app.logger.debug(f"字段名搜索成功 {field_name}: {value}")

            # 提取额外的常见字段（包括用户信息和系统架构相关字段）
            extra_fields = [
                # 版本信息
                '部署包版本', '前端版本', '后端版本', '标准或定制', '日活',
                # 用户账号信息
                '超级管理员账号', '超级管理员密码', '管理员账号', '管理员密码',
                '用户账号', '用户密码', '客户管理员账号', '客户管理员密码',
                # 访问信息
                '管理员页面IP', '用户页面IP', '升级页面IP', '对外服务端口',
                'SDK外网流量入口', 'SDK流量转发到Nginx入口', '业务功能页面地址',
                'init地址', 'init用户名', 'init密码', 'kibana地址', 'kibana认证信息',
                '平台访问地址', '管理员信息', '升级平台地址',
                # 功能信息
                '产品功能', '授权功能', '授权开始日期', '授权结束日期'
            ]
            for field_name in extra_fields:
                if field_name not in extracted_data['basic_info']:
                    value = self._find_field_value_in_sheet(sheet, field_name)
                    if value:
                        extracted_data['basic_info'][field_name] = value
                        current_app.logger.debug(f"额外字段搜索成功 {field_name}: {value}")

            # 提取服务器信息（如果还没有）
            if not extracted_data['servers']:
                servers = self._find_server_table(sheet)
                if servers:
                    extracted_data['servers'].extend(servers)
                    current_app.logger.debug(f"字段名搜索找到服务器信息: {len(servers)}台")

            # 提取组件信息（如果还没有）
            if not extracted_data['components']:
                components = self._find_component_table_in_sheet(sheet)
                if components:
                    extracted_data['components'].update(components)
                    current_app.logger.debug(f"字段名搜索找到组件信息: {len(components)}个")

            # 提取部署架构图信息
            architecture_data = self._find_architecture_table(sheet)
            if architecture_data:
                extracted_data['range_data']['部署架构'] = architecture_data
                current_app.logger.debug(f"找到部署架构图: {len(architecture_data)}条记录")

    def _extract_value_by_position(self, workbook, sheet_name: str, row: int, col: int, field_name: str = None) -> Optional[str]:
        """根据位置提取单个值"""
        try:
            # 尝试找到对应的sheet
            target_sheet = None

            # 首先尝试精确匹配sheet名称
            if sheet_name in workbook.sheetnames:
                target_sheet = workbook[sheet_name]
            else:
                # 如果精确匹配失败，尝试模糊匹配或使用第一个sheet
                current_app.logger.warning(f"Sheet '{sheet_name}' 不存在，尝试使用主sheet")
                target_sheet = workbook.active

            if target_sheet and row <= target_sheet.max_row and col <= target_sheet.max_column:
                cell = target_sheet.cell(row=row, column=col)
                if cell.value:
                    value = str(cell.value).strip()

                    # 如果提供了字段名，尝试从多占位符单元格中提取特定值
                    if field_name and field_name in self.placeholders:
                        placeholder_info = self.placeholders[field_name]
                        extracted_value = self._extract_field_from_multi_placeholder_cell(
                            value, field_name, placeholder_info
                        )
                        if extracted_value:
                            return extracted_value

                    # 清理占位符格式，提取实际值
                    cleaned_value = self._clean_template_value(value)
                    return cleaned_value if cleaned_value else None

            return None

        except Exception as e:
            current_app.logger.error(f"提取位置 {sheet_name}[{row},{col}] 的值失败: {str(e)}")
            return None

    def _extract_field_from_multi_placeholder_cell(self, cell_value: str, field_name: str, placeholder_info: Dict) -> Optional[str]:
        """从包含多个占位符的单元格中提取特定字段的值"""
        try:
            original_template = placeholder_info['original_value']
            placeholder_text = placeholder_info['placeholder_text']

            # 如果单元格值包含占位符，说明这是模板文件，不是填充后的文件
            if '{{' in cell_value and '}}' in cell_value:
                return None

            # 使用模板和实际值进行智能匹配
            extracted_value = self._smart_extract_from_template(original_template, cell_value, field_name)
            return extracted_value

        except Exception as e:
            current_app.logger.debug(f"从多占位符单元格提取字段 {field_name} 失败: {str(e)}")
            return None

    def _smart_extract_from_template(self, template: str, actual_value: str, target_field: str) -> Optional[str]:
        """智能从模板和实际值中提取目标字段的值"""
        try:
            # 特殊处理标题格式的模板
            if '运维文档' in template and '更新时间' in template:
                return self._extract_from_title_template(template, actual_value, target_field)

            # 找到所有占位符
            placeholder_pattern = r'\{\{\s*([^|}]+?)\s*\}\}'
            placeholders = list(re.finditer(placeholder_pattern, template))

            if len(placeholders) <= 1:
                # 单个占位符，直接返回值
                return actual_value.strip()

            # 多个占位符，构建匹配模式
            pattern = template
            field_groups = {}
            group_index = 1

            # 按照占位符在模板中的顺序处理
            for match in placeholders:
                field_name = match.group(1).strip()
                placeholder_full = match.group(0)

                # 为每个字段创建一个捕获组
                if field_name == target_field:
                    # 目标字段使用更精确的匹配
                    pattern = pattern.replace(placeholder_full, r'([^-（）\s]+(?:\s+[^-（）\s]+)*)', 1)
                else:
                    # 其他字段使用更宽松的匹配
                    pattern = pattern.replace(placeholder_full, r'([^-（）\s]+(?:\s+[^-（）\s]+)*)', 1)

                field_groups[group_index] = field_name
                group_index += 1

            # 转义正则表达式特殊字符（除了我们的捕获组）
            pattern = re.escape(pattern)
            # 恢复捕获组
            pattern = pattern.replace(r'\([^-（）\\s]\+\(\?\:\\s\+\[^-（）\\s]\+\)\*\)', r'([^-（）\s]+(?:\s+[^-（）\s]+)*)')

            # 尝试匹配
            match = re.search(pattern, actual_value)
            if match:
                # 找到目标字段对应的组
                for group_num, field_name in field_groups.items():
                    if field_name == target_field:
                        value = match.group(group_num)
                        if value:
                            return value.strip()

            # 如果正则匹配失败，尝试简单的关键词匹配
            return self._fallback_extract_by_keywords(actual_value, target_field)

        except Exception as e:
            current_app.logger.debug(f"智能提取失败: {str(e)}")
            return self._fallback_extract_by_keywords(actual_value, target_field)

    def _extract_from_title_template(self, template: str, actual_value: str, target_field: str) -> Optional[str]:
        """从标题格式的模板中提取字段值"""
        try:
            # 模板格式：{{公司名称}}-运维文档-安全监测（更新时间：{{记录日期}}）
            # 实际值：安全监测科技有限公司-运维文档-安全监测（更新时间：2024-06-03）

            if target_field == '公司名称':
                # 提取公司名称：从开头到第一个"-运维文档"
                match = re.search(r'^([^-]+)(?=-运维文档)', actual_value)
                if match:
                    return match.group(1).strip()

            elif target_field == '记录日期':
                # 提取记录日期：从"更新时间："后到"）"前
                match = re.search(r'更新时间：([^）]+)', actual_value)
                if match:
                    return match.group(1).strip()

            return None

        except Exception as e:
            current_app.logger.debug(f"标题模板提取失败: {str(e)}")
            return None

    def _fallback_extract_by_keywords(self, text: str, target_field: str) -> Optional[str]:
        """回退方法：根据关键词提取值"""

        # 尝试提取IP地址
        if 'IP' in target_field:
            ip_pattern = r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
            ip_matches = re.findall(ip_pattern, text)
            if ip_matches:
                return ip_matches[0]

        # 尝试提取端口
        if '端口' in target_field:
            port_pattern = r'(\d{1,5}(?:,\d{1,5})*)'
            port_matches = re.findall(port_pattern, text)
            if port_matches:
                return port_matches[0]

        # 特殊处理账号和密码字段
        if target_field == '超级管理员账号':
            # 匹配 "超级管理员账号：值" 或 "超管账号：值"
            patterns = [
                r'超级管理员账号[：:\s]*([^\s密码]+)',
                r'超管账号[：:\s]*([^\s密码]+)',
                r'root账号[：:\s]*([^\s密码]+)'
            ]
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(1).strip()

        elif target_field == '超级管理员密码':
            # 匹配 "密码：值" 在超级管理员相关内容中
            patterns = [
                r'超级管理员账号[^密码]*密码[：:\s]*([^\s]+)',
                r'超管[^密码]*密码[：:\s]*([^\s]+)',
                r'root[^密码]*密码[：:\s]*([^\s]+)',
                r'密码[：:\s]*([^\s]+)'  # 通用密码匹配
            ]
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(1).strip()

        elif target_field == '管理员账号':
            # 匹配 "管理员账号：值"，但排除超级管理员
            patterns = [
                r'(?<!超级)管理员账号[：:\s]*([^\s密码]+)',
                r'admin账号[：:\s]*([^\s密码]+)'
            ]
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(1).strip()

        elif target_field == '管理员密码':
            # 匹配管理员相关的密码，但排除超级管理员
            patterns = [
                r'(?<!超级)管理员账号[^密码]*密码[：:\s]*([^\s]+)',
                r'admin[^密码]*密码[：:\s]*([^\s]+)'
            ]
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(1).strip()

            # 如果没有找到特定的管理员密码，且文本中没有超级管理员，则匹配通用密码
            if '超级管理员' not in text and '超管' not in text and 'root' not in text:
                pattern = r'密码[：:\s]*([^\s]+)'
                match = re.search(pattern, text)
                if match:
                    return match.group(1).strip()

        elif target_field == '用户账号':
            patterns = [
                r'用户账号[：:\s]*([^\s密码]+)',
                r'用户名[：:\s]*([^\s密码]+)'
            ]
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(1).strip()

        elif target_field == '用户密码':
            patterns = [
                r'用户账号[^密码]*密码[：:\s]*([^\s]+)',
                r'用户[^密码]*密码[：:\s]*([^\s]+)'
            ]
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(1).strip()

        # 通用关键词匹配（作为最后的回退）
        field_keywords = {
            '管理员页面IP': ['地址'],
            '用户页面IP': ['地址'],
            '升级页面IP': ['地址'],
            '对外服务端口': ['端口', '服务端口'],
        }

        keywords = field_keywords.get(target_field, [])
        for keyword in keywords:
            if keyword in text:
                pattern = rf'{re.escape(keyword)}[：:\s]*([^\s，,]+(?:\s+[^\s，,]+)*)'
                match = re.search(pattern, text)
                if match:
                    return match.group(1).strip()

        return None

    def _clean_template_value(self, value: str) -> Optional[str]:
        """清理模板值，如果是占位符则返回None，如果是实际值则返回清理后的值"""
        if not value:
            return None

        # 如果包含占位符格式，说明这是模板文件，不是填充后的文件
        if '{{' in value and '}}' in value:
            return None

        # 清理常见的模板格式
        value = value.strip()

        # 移除一些常见的模板标记
        if value.startswith('地址：') and not any(char.isdigit() for char in value):
            return None

        return value if value else None

    def _extract_range_by_positions(self, workbook, data_type: str, fields: Dict) -> List[Dict]:
        """根据模板位置提取range数据"""
        range_data = []

        if not fields:
            return range_data

        # 获取第一个字段的位置信息作为参考
        first_field = next(iter(fields.values()))
        sheet_name = first_field['sheet']
        start_row = first_field['row']

        # 尝试找到对应的sheet
        target_sheet = None
        if sheet_name in workbook.sheetnames:
            target_sheet = workbook[sheet_name]
        else:
            # 如果sheet不存在，尝试查找相关的sheet
            if data_type == '服务器信息':
                # 查找服务器相关的sheet
                for sheet_name_candidate in workbook.sheetnames:
                    if '服务器' in sheet_name_candidate:
                        target_sheet = workbook[sheet_name_candidate]
                        break

            if not target_sheet:
                # 如果还是找不到，使用主sheet并调用回退方法
                target_sheet = workbook.active
                return self._extract_range_data_fallback(target_sheet, data_type, fields)

        if not target_sheet:
            return range_data

        # 如果找到了服务器相关的sheet，使用回退方法解析
        if data_type == '服务器信息' and '服务器' in target_sheet.title:
            return self._extract_range_data_fallback(target_sheet, data_type, fields)

        # 从模板位置开始，向下查找数据行
        current_row = start_row + 1  # 跳过模板行，从下一行开始

        while current_row <= target_sheet.max_row:
            row_data = {}
            has_data = False

            # 根据字段位置提取每列的数据
            for field_name, field_info in fields.items():
                col = field_info['col']
                if col <= target_sheet.max_column:
                    cell = target_sheet.cell(row=current_row, column=col)
                    if cell.value:
                        value = str(cell.value).strip()
                        # 确保不是占位符
                        if not ('{{' in value and '}}' in value):
                            row_data[field_name] = value
                            has_data = True

            if has_data:
                range_data.append(row_data)
                current_row += 1
            else:
                # 连续空行则停止
                break

        current_app.logger.debug(f"从位置提取range数据 {data_type}: {len(range_data)}条记录")
        return range_data

    def _extract_component_by_positions(self, workbook, comp_name: str, fields: Dict) -> Dict[str, Any]:
        """根据组件占位符的位置提取组件数据"""
        comp_data = {'name': comp_name}

        for field_name, placeholder_info in fields.items():
            value = self._extract_value_by_position(
                workbook,
                placeholder_info['sheet'],
                placeholder_info['row'],
                placeholder_info['col']
            )
            if value:
                comp_data[field_name] = value

        return comp_data if len(comp_data) > 1 else {}

    def _extract_range_data_fallback(self, sheet, data_type: str, fields: Dict) -> List[Dict]:
        """回退方法：在sheet中查找表格数据"""
        if data_type == '服务器信息':
            # 查找服务器信息表格
            return self._find_server_table(sheet)
        return []

    def _find_server_table(self, sheet) -> List[Dict]:
        """查找服务器信息表格"""
        servers = []

        # 查找表头行
        header_row = None
        header_mapping = {}

        # 常见的服务器表头（根据实际Excel文件调整）
        server_headers = ['IP地址', 'IP', '用途', '系统发行版', '操作系统', '内存', 'CPU', '磁盘', '硬盘',
                         '网络区域', '业务IP', '带外IP', '外网ip', '存储', 'root密码', 'Root密码',
                         'SSH端口', '运维用户', '运维密码', '部署应用', '部署组件']

        for row_idx, row in enumerate(sheet.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value and str(cell.value).strip() in server_headers:
                    header_row = row_idx
                    break
            if header_row:
                break

        if not header_row:
            return servers

        # 建立列映射
        header_row_cells = list(sheet.iter_rows(min_row=header_row, max_row=header_row))[0]
        for col_idx, cell in enumerate(header_row_cells):
            if cell.value:
                header_text = str(cell.value).strip()
                # 标准化字段名（根据实际Excel文件调整）
                if header_text in ['IP地址', 'IP', '业务IP(程序之间)', '业务IP']:
                    header_mapping[col_idx] = 'IP地址'
                elif header_text in ['用途', '服务器用途', '网络区域']:
                    header_mapping[col_idx] = '用途类型'
                elif header_text in ['系统发行版', '操作系统', 'OS']:
                    header_mapping[col_idx] = '系统发行版'
                elif header_text in ['内存', 'RAM']:
                    header_mapping[col_idx] = '内存'
                elif header_text in ['CPU', 'cpu']:
                    header_mapping[col_idx] = 'CPU'
                elif header_text in ['磁盘', '硬盘', '存储']:
                    header_mapping[col_idx] = '磁盘'
                elif header_text in ['带外IP（登录机器）', '带外IP']:
                    header_mapping[col_idx] = '外网IP地址'
                elif header_text in ['外网ip（有网关）', '外网ip']:
                    header_mapping[col_idx] = '外网IP'
                elif header_text in ['root密码']:
                    header_mapping[col_idx] = 'Root密码'

        # 提取数据行
        for row in sheet.iter_rows(min_row=header_row + 1):
            row_data = {}
            has_data = False

            for col_idx, cell in enumerate(row):
                if col_idx in header_mapping and cell.value:
                    field_name = header_mapping[col_idx]
                    row_data[field_name] = str(cell.value).strip()
                    has_data = True

            if has_data and row_data.get('IP地址'):  # 必须有IP地址
                # 处理运维信息，转换为新的数据结构
                self._process_server_ops_info(row_data)
                servers.append(row_data)
            elif len(servers) > 0:
                # 如果已经有数据行，遇到空行就停止
                break

        return servers

    def _process_server_ops_info(self, server):
        """处理服务器运维信息，转换为新的数据结构"""
        ops_users = []

        # 处理Root用户
        root_password = server.get('Root密码', server.get('root密码', ''))
        if root_password:
            ops_users.append({
                '用户名': 'root',
                '密码': root_password,
                'isDefault': True
            })

        # 处理其他运维用户（如果存在旧格式数据）
        for i in range(1, 10):  # 支持最多9个运维用户的迁移
            user_key = f'运维用户{i}'
            password_key = f'运维用户{i}密码'

            ops_user = server.get(user_key, '')
            ops_password = server.get(password_key, '')

            if ops_user or ops_password:
                ops_users.append({
                    '用户名': ops_user,
                    '密码': ops_password,
                    'isDefault': False
                })

        # 设置运维用户数组
        if ops_users:
            server['运维用户'] = ops_users

        # 确保SSH端口字段存在
        if 'SSH端口' not in server:
            server['SSH端口'] = '22'  # 默认SSH端口

        # 处理部署应用字段
        if '部署应用' in server or '部署组件' in server:
            apps = server.get('部署应用', server.get('部署组件', ''))
            if isinstance(apps, str) and apps:
                # 将字符串转换为列表
                server['部署应用'] = [app.strip() for app in apps.replace('\n', ',').split(',') if app.strip()]
            elif not isinstance(apps, list):
                server['部署应用'] = []


    
    def _parse_excel_sheet(self, sheet, sheet_name: str) -> Dict[str, Any]:
        """解析单个Excel sheet页"""
        sheet_data = {
            'basic_info': {},
            'servers': [],
            'components': {},
            'range_data': {},
            'errors': [],
            'warnings': []
        }

        # 1. 提取简单字段值（从所有占位符中提取，不限制sheet名称）
        for field_name, placeholder_info in self.placeholders.items():
            if placeholder_info['type'] == 'simple':
                value = self._find_field_value_in_sheet(sheet, field_name)
                if value:
                    sheet_data['basic_info'][field_name] = value

        # 1.1. 提取额外的常见字段（即使不在模板占位符中）
        extra_fields = [
            # 版本信息
            '部署包版本', '前端版本', '后端版本', '标准或定制', '日活',
            # 用户账号信息
            '超级管理员账号', '超级管理员密码', '管理员账号', '管理员密码',
            '用户账号', '用户密码', '客户管理员账号', '客户管理员密码',
            '升级用户账号', '升级用户密码',
            '升级用户账号', '升级用户密码',
            # 访问信息
            '管理员页面IP', '用户页面IP', '升级页面IP', '对外服务端口',
            'SDK外网流量入口', 'SDK流量转发到Nginx入口', '业务功能页面地址',
            'init地址', 'init用户名', 'init密码', 'kibana地址', 'kibana认证信息',
            '平台访问地址', '管理员信息', '升级平台地址',
            # 功能信息
            '产品功能', '授权功能', '授权开始日期', '授权结束日期'
        ]
        for field_name in extra_fields:
            if field_name not in sheet_data['basic_info']:  # 避免重复
                value = self._find_field_value_in_sheet(sheet, field_name)
                if value:
                    sheet_data['basic_info'][field_name] = value

        # 2. 提取range数据（如服务器信息）
        for data_type, fields in self.range_placeholders.items():
            range_data = self._extract_range_data(sheet, data_type, fields)
            if range_data:
                if data_type == '服务器信息':
                    sheet_data['servers'].extend(range_data)
                else:
                    sheet_data['range_data'][data_type] = range_data

        # 3. 提取组件数据
        for comp_name, fields in self.component_placeholders.items():
            comp_data = self._extract_component_data(sheet, comp_name, fields)
            if comp_data:
                sheet_data['components'][comp_name] = comp_data

        return sheet_data
    
    def _find_field_value_in_sheet(self, sheet, field_name: str) -> Optional[str]:
        """在sheet中查找字段值"""
        # 定义可能的字段名变体
        possible_names = [
            field_name,
            field_name.replace('_', ''),
            field_name.replace('-', ''),
        ]

        # 添加常见的字段名映射
        field_mappings = {
            '公司名称': ['公司名称', '客户名称', '企业名称', '单位名称', '客户'],
            '客户标识': ['客户标识', '客户ID', '客户编号'],
            '记录日期': ['记录日期', '日期', '创建日期', '填写日期'],
            '部署包版本': ['部署包版本', '版本信息', '版本', '系统版本'],
            '前端版本': ['前端版本', 'Web版本', '界面版本'],
            '后端版本': ['后端版本', '服务版本', '后台版本'],
            '管理员页面IP': ['管理员页面IP', '管理IP', '后台IP', '管理员IP'],
            '用户页面IP': ['用户页面IP', '前台IP', '用户IP'],
            '升级页面IP': ['升级页面IP', '升级IP'],
            '对外服务端口': ['对外服务端口', '服务端口', '端口'],
            '超级管理员账号': ['超级管理员账号', 'root账号', '超管账号'],
            '超级管理员密码': ['超级管理员密码', 'root密码', '超管密码'],
            '管理员账号': ['管理员账号', '管理员用户名', 'admin账号', '后台账号'],
            '管理员密码': ['管理员密码', 'admin密码', '后台密码'],
            '用户账号': ['用户账号', '用户名'],
            '用户密码': ['用户密码'],
            '升级用户账号': ['升级用户账号', '升级账号', '升级用户名'],
            '升级用户密码': ['升级用户密码', '升级密码'],
            '产品功能': ['产品功能', '功能描述'],
            '授权功能': ['授权功能', '授权内容'],
            '授权开始日期': ['授权开始日期', '授权开始'],
            '授权结束日期': ['授权结束日期', '授权结束'],
            '客户': ['客户', '客户名称', '联系人'],
            '部署的平台版本': ['部署的平台版本', '平台版本', '版本'],
            '平台访问地址': ['平台访问地址', '平台地址', '访问地址', 'URL'],
            '管理员信息': ['管理员信息', '账号信息'],
            '升级平台地址': ['升级平台地址', '升级平台信息', '升级信息'],
            '标准或定制': ['标准/定制', '标准或定制', '版本类型'],
            '日活': ['日活', '日活跃用户'],
        }

        if field_name in field_mappings:
            possible_names.extend(field_mappings[field_name])

        # 在sheet中搜索
        for row in sheet.iter_rows(max_row=50):  # 只搜索前50行
            for i, cell in enumerate(row):
                if cell.value:
                    cell_text = str(cell.value).strip()
                    # 检查是否匹配任何可能的字段名
                    for possible_name in possible_names:
                        if cell_text == possible_name or possible_name in cell_text:
                            # 找到字段名，查找对应的值
                            value = None

                            # 尝试右边的单元格
                            if i + 1 < len(row) and row[i + 1].value:
                                value = row[i + 1].value

                            # 如果右边没有值，尝试下一行同一列
                            if not value and cell.row + 1 <= sheet.max_row:
                                next_row_cell = sheet.cell(row=cell.row + 1, column=cell.column)
                                if next_row_cell.value:
                                    value = next_row_cell.value

                            if value:
                                return str(value).strip()

        return None
    
    def _extract_range_data(self, sheet, data_type: str, fields: Dict) -> List[Dict]:
        """提取range类型的数据（如服务器列表）"""
        range_data = []
        
        # 查找表格头部
        header_row = None
        header_mapping = {}
        
        for row_idx, row in enumerate(sheet.iter_rows(), 1):
            for cell in row:
                if cell.value and str(cell.value).strip() in fields:
                    header_row = row_idx
                    break
            if header_row:
                break
        
        if not header_row:
            return range_data
        
        # 建立列映射
        header_row_cells = list(sheet.iter_rows(min_row=header_row, max_row=header_row))[0]
        for col_idx, cell in enumerate(header_row_cells):
            if cell.value:
                field_value = str(cell.value).strip()
                for field_name in fields:
                    if field_value == field_name or field_value in self._get_field_aliases(field_name):
                        header_mapping[col_idx] = field_name
                        break
        
        # 提取数据行
        for row in sheet.iter_rows(min_row=header_row + 1):
            row_data = {}
            has_data = False
            
            for col_idx, cell in enumerate(row):
                if col_idx in header_mapping and cell.value:
                    field_name = header_mapping[col_idx]
                    row_data[field_name] = str(cell.value).strip()
                    has_data = True
            
            if has_data:
                range_data.append(row_data)
        
        return range_data
    
    def _extract_component_data(self, sheet, comp_name: str, fields: Dict) -> Dict:
        """提取组件数据"""
        comp_data = {'name': comp_name}
        
        for field_name in fields:
            value = self._find_field_value_in_sheet(sheet, f"{comp_name}.{field_name}")
            if value:
                comp_data[field_name] = value
        
        return comp_data if len(comp_data) > 1 else {}
    
    def _get_field_aliases(self, field_name: str) -> List[str]:
        """获取字段的别名列表"""
        aliases = {
            'IP地址': ['IP', 'ip', 'IP地址', '服务器IP'],
            '用途': ['用途', '服务器用途', '作用', '功能'],
            '系统发行版': ['系统发行版', '操作系统', 'OS', '系统版本'],
            '内存': ['内存', '内存大小', 'RAM', '内存容量'],
            'CPU': ['CPU', 'cpu', '处理器', 'CPU核数'],
            '磁盘': ['磁盘', '硬盘', '存储', '磁盘容量'],
        }
        
        return aliases.get(field_name, [field_name])

    def _find_component_table_in_sheet(self, sheet) -> Dict[str, Dict]:
        """在sheet中查找组件信息表格"""
        components = {}

        # 查找可能的组件表格标题
        component_headers = ['组件名称', '组件', '应用名称', '应用', '服务名称', '服务']
        port_headers = ['端口', 'port', '服务端口', '监听端口']
        version_headers = ['版本', 'version', '版本号']
        count_headers = ['数量', 'count', '实例数', '副本数']

        # 查找表格头部
        header_row = None
        header_mapping = {}

        for row_idx, row in enumerate(sheet.iter_rows(max_row=50), 1):
            for cell in row:
                if cell.value and str(cell.value).strip() in component_headers:
                    header_row = row_idx
                    break
            if header_row:
                break

        if not header_row:
            return components

        # 建立列映射
        header_row_cells = list(sheet.iter_rows(min_row=header_row, max_row=header_row))[0]
        for col_idx, cell in enumerate(header_row_cells):
            if cell.value:
                field_value = str(cell.value).strip()
                if field_value in component_headers:
                    header_mapping[col_idx] = 'name'
                elif field_value in port_headers:
                    header_mapping[col_idx] = 'port'
                elif field_value in version_headers:
                    header_mapping[col_idx] = 'version'
                elif field_value in count_headers:
                    header_mapping[col_idx] = 'count'

        # 提取数据行
        for row in sheet.iter_rows(min_row=header_row + 1):
            row_data = {}
            has_data = False

            for col_idx, cell in enumerate(row):
                if col_idx in header_mapping and cell.value:
                    field_name = header_mapping[col_idx]
                    row_data[field_name] = str(cell.value).strip()
                    has_data = True

            if has_data and row_data.get('name'):
                comp_name = row_data['name']
                components[comp_name] = {
                    'name': comp_name,
                    'port': row_data.get('port', ''),
                    'version': row_data.get('version', ''),
                    'count': row_data.get('count', '1'),
                    'default_port': row_data.get('port', '')
                }
            elif len(components) > 0:
                # 如果已经有数据行，遇到空行就停止
                break

        return components

    def _find_architecture_table(self, sheet) -> List[Dict]:
        """查找部署架构图表格"""
        architecture_data = []

        # 查找可能的架构图表格标题
        arch_keywords = ['部署架构', '系统架构', '架构图', '组件部署', '部署图']

        # 查找包含架构关键词的区域
        for row_idx, row in enumerate(sheet.iter_rows(max_row=100), 1):
            for cell in row:
                if cell.value:
                    cell_text = str(cell.value).strip()
                    for keyword in arch_keywords:
                        if keyword in cell_text:
                            # 找到架构图区域，尝试解析下面的表格
                            arch_data = self._parse_architecture_table_from_position(sheet, row_idx + 1)
                            if arch_data:
                                architecture_data.extend(arch_data)
                            break

        return architecture_data

    def _parse_architecture_table_from_position(self, sheet, start_row: int) -> List[Dict]:
        """从指定位置开始解析架构表格"""
        arch_data = []

        # 查找表格头部（组件分类、组件名称、端口等）
        header_keywords = {
            'category': ['组件分类', '分类', '类别'],
            'name': ['组件名称', '组件', '应用名称', '应用'],
            'port': ['端口', 'port', '服务端口'],
            'ip': ['IP地址', 'IP', 'ip']
        }

        header_row = None
        header_mapping = {}

        # 在接下来的10行内查找表格头
        for row_idx in range(start_row, min(start_row + 10, sheet.max_row + 1)):
            row = list(sheet.iter_rows(min_row=row_idx, max_row=row_idx))[0]
            found_headers = 0

            for col_idx, cell in enumerate(row):
                if cell.value:
                    cell_text = str(cell.value).strip()
                    for field_type, keywords in header_keywords.items():
                        if any(keyword in cell_text for keyword in keywords):
                            header_mapping[col_idx] = field_type
                            found_headers += 1
                            break

            if found_headers >= 2:  # 至少找到2个相关列
                header_row = row_idx
                break

        if not header_row:
            return arch_data

        # 提取数据行
        for row in sheet.iter_rows(min_row=header_row + 1, max_row=min(header_row + 50, sheet.max_row)):
            row_data = {}
            has_data = False

            for col_idx, cell in enumerate(row):
                if col_idx in header_mapping and cell.value:
                    field_name = header_mapping[col_idx]
                    row_data[field_name] = str(cell.value).strip()
                    has_data = True

            if has_data and (row_data.get('name') or row_data.get('category')):
                arch_data.append({
                    '组件分类': row_data.get('category', ''),
                    '组件名称': row_data.get('name', ''),
                    '端口': row_data.get('port', ''),
                    'IP地址': row_data.get('ip', '')
                })
            elif len(arch_data) > 0:
                # 如果已经有数据行，遇到空行就停止
                break

        return arch_data

    def _post_process_data(self, extracted_data: Dict[str, Any]):
        """后处理提取的数据"""
        # 确保必要字段存在
        basic_info = extracted_data['basic_info']
        
        # 设置表单类型
        if '公司名称' in basic_info:
            extracted_data['company_name'] = basic_info['公司名称']
        
        if '记录日期' in basic_info:
            extracted_data['record_date'] = basic_info['记录日期']
        
        # 统计服务器数量
        extracted_data['server_count'] = len(extracted_data['servers'])
        extracted_data['total_servers'] = extracted_data['server_count']
        
        # 验证必填字段（只提示警告，不阻止导入）
        required_fields = self._get_required_fields()
        for field in required_fields:
            if field not in basic_info or not basic_info[field]:
                extracted_data['warnings'].append(f"建议填写必填字段: {field}")
    
    def _get_required_fields(self) -> List[str]:
        """获取必填字段列表"""
        required_fields_map = {
            '安全测评': ['公司名称', '部署包版本', '记录日期'],
            '安全监测': ['公司名称', '前端版本', '后端版本', '记录日期'],
            '应用加固': ['公司名称', '客户', '记录日期']
        }
        
        return required_fields_map.get(self.form_type, ['公司名称', '记录日期'])
    
    def get_placeholder_info(self) -> Dict[str, Any]:
        """获取占位符信息"""
        return {
            'simple_placeholders': self.placeholders,
            'range_placeholders': self.range_placeholders,
            'component_placeholders': self.component_placeholders,
            'total_count': len(self.placeholders) + sum(len(fields) for fields in self.range_placeholders.values()) + sum(len(fields) for fields in self.component_placeholders.values())
        }
