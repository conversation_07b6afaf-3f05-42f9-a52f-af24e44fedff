<template>
  <div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>
            发现重复的表单记录
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        
        <div class="modal-body">
          <div class="alert alert-warning">
            <i class="bi bi-info-circle me-2"></i>
            <strong>检测到重复：</strong>已存在相同公司（{{ duplicateData.company_name }}）和表单类型（{{ duplicateData.form_type }}）的记录。
          </div>
          
          <!-- 现有记录列表 -->
          <div class="mb-4">
            <h6 class="mb-3">
              <i class="bi bi-clock-history me-2"></i>
              现有记录 ({{ duplicateData.total_count }} 条)
            </h6>
            
            <div class="table-responsive">
              <table class="table table-sm table-hover">
                <thead class="table-light">
                  <tr>
                    <th>记录日期</th>
                    <th>创建时间</th>
                    <th>创建人</th>
                    <th>文件名</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="record in duplicateData.existing_records" :key="record.id">
                    <td>{{ record.record_date }}</td>
                    <td>{{ formatDateTime(record.created_at) }}</td>
                    <td>{{ record.created_by }}</td>
                    <td class="text-truncate" style="max-width: 200px;" :title="record.excel_filename">
                      {{ record.excel_filename }}
                    </td>
                    <td>
                      <button 
                        class="btn btn-outline-primary btn-sm me-1" 
                        @click="editExisting(record.id)"
                        title="编辑此记录"
                      >
                        <i class="bi bi-pencil"></i>
                      </button>
                      <button 
                        class="btn btn-outline-info btn-sm" 
                        @click="viewExisting(record.id)"
                        title="查看详情"
                      >
                        <i class="bi bi-eye"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div v-if="duplicateData.total_count > 5" class="text-muted small">
              还有 {{ duplicateData.total_count - 5 }} 条记录未显示...
            </div>
          </div>
          
          <!-- 别名输入 -->
          <div v-if="showAliasInput" class="mb-4">
            <h6 class="mb-3">
              <i class="bi bi-tag me-2"></i>
              修改别名
            </h6>
            <div class="row">
              <div class="col-md-8">
                <input 
                  v-model="customAlias" 
                  type="text" 
                  class="form-control" 
                  placeholder="请输入新的别名（如：V2.0、更新版等）"
                  @keyup.enter="submitWithAlias"
                >
              </div>
              <div class="col-md-4">
                <button 
                  class="btn btn-success w-100" 
                  @click="submitWithAlias"
                  :disabled="!customAlias.trim()"
                >
                  <i class="bi bi-check-lg me-1"></i>
                  确认提交
                </button>
              </div>
            </div>
            <div class="form-text">
              新文件名将为：{{ generateAliasFilename() }}
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <div class="d-flex justify-content-between w-100">
            <div>
              <button type="button" class="btn btn-secondary" @click="$emit('close')">
                <i class="bi bi-x-lg me-1"></i>
                取消
              </button>
            </div>
            
            <div>
              <button 
                type="button" 
                class="btn btn-outline-warning me-2" 
                @click="toggleAliasInput"
              >
                <i class="bi bi-tag me-1"></i>
                {{ showAliasInput ? '取消别名' : '修改别名' }}
              </button>
              
              <button 
                type="button" 
                class="btn btn-warning me-2" 
                @click="forceOverwrite"
              >
                <i class="bi bi-arrow-repeat me-1"></i>
                强制覆盖
              </button>
              
              <button 
                type="button" 
                class="btn btn-primary" 
                @click="goToHistory"
              >
                <i class="bi bi-list-ul me-1"></i>
                查看历史记录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 重复表单提交处理模态框
 * 当检测到重复的表单提交时显示，提供多种处理选项
 */
export default {
  name: 'DuplicateSubmissionModal',
  
  props: {
    duplicateData: {
      type: Object,
      required: true
    }
  },
  
  emits: ['close', 'force-overwrite', 'submit-with-alias', 'edit-existing', 'view-existing', 'go-to-history'],
  
  data() {
    return {
      showAliasInput: false,
      customAlias: ''
    }
  },
  
  methods: {
    /**
     * 格式化日期时间
     */
    formatDateTime(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    },
    
    /**
     * 切换别名输入显示
     */
    toggleAliasInput() {
      this.showAliasInput = !this.showAliasInput
      if (!this.showAliasInput) {
        this.customAlias = ''
      }
    },
    
    /**
     * 生成带别名的文件名
     */
    generateAliasFilename() {
      if (!this.customAlias.trim()) {
        return this.duplicateData.expected_filename
      }
      
      const filename = this.duplicateData.expected_filename
      const lastDotIndex = filename.lastIndexOf('.')
      const nameWithoutExt = filename.substring(0, lastDotIndex)
      const extension = filename.substring(lastDotIndex)
      
      return `${nameWithoutExt}-${this.customAlias.trim()}${extension}`
    },
    
    /**
     * 使用别名提交
     */
    submitWithAlias() {
      if (!this.customAlias.trim()) {
        alert('请输入别名')
        return
      }
      
      this.$emit('submit-with-alias', {
        alias: this.customAlias.trim(),
        filename: this.generateAliasFilename()
      })
    },
    
    /**
     * 强制覆盖
     */
    forceOverwrite() {
      if (confirm('确定要强制覆盖吗？这将创建新的记录，但保留历史记录。')) {
        this.$emit('force-overwrite')
      }
    },
    
    /**
     * 编辑现有记录
     */
    editExisting(recordId) {
      this.$emit('edit-existing', recordId)
    },
    
    /**
     * 查看现有记录
     */
    viewExisting(recordId) {
      this.$emit('view-existing', recordId)
    },
    
    /**
     * 跳转到历史记录页面
     */
    goToHistory() {
      this.$emit('go-to-history')
    }
  }
}
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.table-responsive {
  max-height: 300px;
  overflow-y: auto;
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-text {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.5rem;
}

.alert {
  border-left: 4px solid #ffc107;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}
</style>
