<template>
  <div class="basic-info-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-bug me-2"></i>
                基本信息显示测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 表单类型选择 -->
              <div class="row mb-4">
                <div class="col-md-6">
                  <label class="form-label fw-bold">选择表单类型</label>
                  <select v-model="selectedFormType" class="form-select" @change="loadTestData">
                    <option value="">请选择表单类型</option>
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">调试信息</label>
                  <div class="d-flex gap-2">
                    <button 
                      class="btn btn-info btn-sm" 
                      @click="showDebugInfo = !showDebugInfo"
                    >
                      {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
                    </button>
                    <button 
                      class="btn btn-warning btn-sm" 
                      @click="refreshComponent"
                    >
                      刷新组件
                    </button>
                  </div>
                </div>
              </div>

              <!-- 调试信息 -->
              <div v-if="showDebugInfo && selectedFormType" class="row mb-4">
                <div class="col-12">
                  <div class="card bg-light">
                    <div class="card-header">
                      <h6 class="mb-0">调试信息 - {{ selectedFormType }}</h6>
                    </div>
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-4">
                          <h6 class="text-primary">基本字段配置 ({{ basicFieldConfig.length }})</h6>
                          <pre class="small">{{ JSON.stringify(basicFieldConfig, null, 2) }}</pre>
                        </div>
                        <div class="col-md-4">
                          <h6 class="text-success">客户字段配置 ({{ customerFieldConfig.length }})</h6>
                          <pre class="small">{{ JSON.stringify(customerFieldConfig, null, 2) }}</pre>
                        </div>
                        <div class="col-md-4">
                          <h6 class="text-warning">部署字段配置 ({{ deploymentFieldConfig.length }})</h6>
                          <pre class="small">{{ JSON.stringify(deploymentFieldConfig, null, 2) }}</pre>
                        </div>
                      </div>
                      <div class="row mt-3">
                        <div class="col-12">
                          <h6 class="text-danger">部署字段详细分析</h6>
                          <div class="alert alert-info">
                            <strong>当前表单类型:</strong> {{ selectedFormType }}<br>
                            <strong>部署字段总数:</strong> {{ deploymentFieldConfig.length }}<br>
                            <strong>过滤后的部署字段:</strong>
                            <ul class="mb-0">
                              <li v-for="field in filteredDeploymentFields" :key="field.id">
                                {{ field.id }} - formTypes: {{ field.formTypes?.join(', ') || '无限制' }}
                              </li>
                              <li v-if="filteredDeploymentFields.length === 0" class="text-muted">无匹配字段</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <div class="row mt-3">
                        <div class="col-md-6">
                          <h6 class="text-info">表单数据</h6>
                          <pre class="small">{{ JSON.stringify(testFormData, null, 2) }}</pre>
                        </div>
                        <div class="col-md-6">
                          <h6 class="text-secondary">基本信息值</h6>
                          <pre class="small">{{ JSON.stringify(basicInfoValues, null, 2) }}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 测试表单 -->
              <div v-if="selectedFormType" class="row">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">{{ selectedFormType }} - 基本信息测试</h6>
                    </div>
                    <div class="card-body">
                      <common-basic-info
                        v-model:company="testFormData.公司名称"
                        v-model:date="testFormData.记录日期"
                        v-model:editor="testFormData.编辑人"
                        v-model:field-values="basicInfoValues"
                        :basic-field-config="basicFieldConfig"
                        :customer-field-config="customerFieldConfig"
                        :deployment-field-config="deploymentFieldConfig"
                        :form-type="selectedFormType"
                        :key="`${selectedFormType}-${componentKey}`"
                      />

                      <!-- 调试信息显示 -->
                      <div class="mt-3 p-3 bg-light rounded">
                        <h6>实时调试信息</h6>
                        <p><strong>当前表单类型:</strong> {{ selectedFormType }}</p>
                        <p><strong>组件Key:</strong> {{ `${selectedFormType}-${componentKey}` }}</p>
                        <p><strong>部署字段配置数量:</strong> {{ deploymentFieldConfig.length }}</p>
                        <p><strong>过滤后的部署字段:</strong> {{ filteredDeploymentFields.length }}</p>
                        <div v-if="filteredDeploymentFields.length > 0">
                          <strong>应该显示的字段:</strong>
                          <ul>
                            <li v-for="field in filteredDeploymentFields" :key="field.id">
                              {{ field.id }} ({{ field.fields?.map(f => f.label).join(', ') }})
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonBasicInfo from '@/components/forms/common/CommonBasicInfo.vue'
import {
  getBasicInfoFieldsByFormType,
  getCustomerInfoFieldsByFormType,
  getDeploymentInfoFieldsByFormType
} from '@/config/formFields'
import { getInitialFormData } from '@/config/formDataConfig'

export default {
  name: 'BasicInfoTest',
  components: {
    CommonBasicInfo
  },
  data() {
    return {
      selectedFormType: '',
      showDebugInfo: false,
      componentKey: 0,
      testFormData: {},
      basicInfoValues: {},
      basicFieldConfig: [],
      customerFieldConfig: [],
      deploymentFieldConfig: []
    }
  },
  computed: {
    /**
     * 过滤后的部署字段（用于调试）
     */
    filteredDeploymentFields() {
      return this.deploymentFieldConfig.filter(field => {
        if (field.formTypes && Array.isArray(field.formTypes)) {
          return field.formTypes.includes(this.selectedFormType)
        }
        return true
      })
    }
  },
  methods: {
    /**
     * 加载测试数据
     */
    loadTestData() {
      if (!this.selectedFormType) return

      // 获取字段配置
      this.basicFieldConfig = getBasicInfoFieldsByFormType(this.selectedFormType)
      this.customerFieldConfig = getCustomerInfoFieldsByFormType(this.selectedFormType)
      this.deploymentFieldConfig = getDeploymentInfoFieldsByFormType(this.selectedFormType)

      // 获取初始表单数据
      this.testFormData = getInitialFormData(this.selectedFormType)

      // 设置基本信息值
      this.updateBasicInfoValues()

      console.log('加载测试数据:', {
        formType: this.selectedFormType,
        basicFieldConfig: this.basicFieldConfig,
        customerFieldConfig: this.customerFieldConfig,
        deploymentFieldConfig: this.deploymentFieldConfig,
        testFormData: this.testFormData
      })

      // 调试部署字段过滤
      console.log('部署字段过滤调试:')
      console.log('原始部署字段数量:', this.deploymentFieldConfig.length)
      this.deploymentFieldConfig.forEach((field, index) => {
        console.log(`字段 ${index}:`, {
          id: field.id,
          formTypes: field.formTypes,
          shouldShow: !field.formTypes || field.formTypes.includes(this.selectedFormType)
        })
      })
    },

    /**
     * 更新基本信息值
     */
    updateBasicInfoValues() {
      const values = {}

      // 根据表单类型设置不同的基本信息值
      if (this.selectedFormType === '安全测评') {
        values.customerId = this.testFormData.客户标识 || ''
        values.deploymentVersion = this.testFormData.部署包版本 || ''
      } else if (this.selectedFormType === '安全监测') {
        values.customerId = this.testFormData.客户标识 || ''
        values.dailyActive = this.testFormData.日活 || ''
        values.frontendVersion = this.testFormData.前端版本 || ''
        values.backendVersion = this.testFormData.后端版本 || ''
        values.standardOrCustom = this.testFormData.标准或定制 || '标准版'
      } else if (this.selectedFormType === '应用加固') {
        values.customerId = this.testFormData.客户标识 || ''
        values.customer = this.testFormData.客户 || ''
        values.platformVersion = this.testFormData.部署的平台版本 || ''
      }

      this.basicInfoValues = values
    },

    /**
     * 刷新组件
     */
    refreshComponent() {
      this.componentKey += 1
      this.loadTestData()
    }
  },
  watch: {
    // 监听基本信息值变化
    basicInfoValues: {
      handler(newValues) {
        console.log('基本信息值变化:', newValues)
        
        // 同步回表单数据
        if (this.selectedFormType === '安全测评') {
          this.testFormData.客户标识 = newValues.customerId || ''
          this.testFormData.部署包版本 = newValues.deploymentVersion || ''
        } else if (this.selectedFormType === '安全监测') {
          this.testFormData.客户标识 = newValues.customerId || ''
          this.testFormData.日活 = newValues.dailyActive || ''
          this.testFormData.前端版本 = newValues.frontendVersion || ''
          this.testFormData.后端版本 = newValues.backendVersion || ''
          this.testFormData.标准或定制 = newValues.standardOrCustom || '标准版'
        } else if (this.selectedFormType === '应用加固') {
          this.testFormData.客户标识 = newValues.customerId || ''
          this.testFormData.客户 = newValues.customer || ''
          this.testFormData.部署的平台版本 = newValues.platformVersion || ''
        }
      },
      deep: true
    }
  },
  mounted() {
    console.log('BasicInfoTest 组件已挂载')
  }
}
</script>

<style scoped>
.basic-info-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 2px solid #e9ecef;
}

pre {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #0dcaf0 !important; }
.text-secondary { color: #6c757d !important; }
</style>
