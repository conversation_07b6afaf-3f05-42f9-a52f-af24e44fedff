<template>
  <div class="modal fade" id="snapshotModal" tabindex="-1" aria-labelledby="snapshotModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="snapshotModalLabel">{{ title }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div v-if="mode === 'save'">
            <div class="mb-3">
              <label for="snapshotName" class="form-label">快照名称</label>
              <input
                type="text"
                class="form-control"
                id="snapshotName"
                v-model="snapshotName"
                placeholder="请输入快照名称"
              >
            </div>
          </div>
          <div v-else-if="mode === 'load'">
            <div v-if="snapshots.length === 0" class="alert alert-info">
              没有保存的快照
            </div>
            <div v-else class="list-group snapshot-list">
              <div
                v-for="(snapshot, index) in snapshots"
                :key="snapshot.id"
                class="list-group-item list-group-item-action d-flex justify-content-between align-items-center snapshot-item"
                :class="{ active: selectedSnapshotIndex === index }"
                @click="selectSnapshot(index)"
              >
                <div>
                  <div class="fw-bold">{{ snapshot.name }}</div>
                  <small class="text-muted">{{ snapshot.date }}</small>
                </div>
                <button
                  type="button"
                  class="btn btn-sm btn-outline-danger"
                  @click.stop="$emit('delete-snapshot', index)"
                >
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button
            v-if="mode === 'save'"
            type="button"
            class="btn btn-primary"
            @click="saveSnapshot"
            :disabled="!snapshotName"
          >
            保存
          </button>
          <button
            v-else-if="mode === 'load'"
            type="button"
            class="btn btn-primary"
            @click="loadSnapshot"
            :disabled="selectedSnapshotIndex === null || snapshots.length === 0"
          >
            加载
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SnapshotModal',
  props: {
    mode: {
      type: String,
      default: 'save',
      validator: value => ['save', 'load'].includes(value)
    },
    snapshots: {
      type: Array,
      default: () => []
    },
    defaultName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      snapshotName: this.defaultName,
      selectedSnapshotIndex: null
    }
  },
  computed: {
    title() {
      return this.mode === 'save' ? '保存表单快照' : '加载表单快照'
    }
  },
  methods: {
    selectSnapshot(index) {
      this.selectedSnapshotIndex = index
    },
    saveSnapshot() {
      if (this.snapshotName) {
        this.$emit('save-snapshot', this.snapshotName)
        this.snapshotName = ''
        this.hideModal()
      }
    },
    loadSnapshot() {
      if (this.selectedSnapshotIndex !== null) {
        this.$emit('load-snapshot', this.selectedSnapshotIndex)
        this.selectedSnapshotIndex = null
        this.hideModal()
      }
    },
    hideModal() {
      // 使用Bootstrap的方法隐藏模态框
      if (window.bootstrap) {
        const modalElement = document.getElementById('snapshotModal')
        const modal = bootstrap.Modal.getInstance(modalElement)
        if (modal) {
          modal.hide()
        }
      }
    },
    resetForm() {
      this.snapshotName = this.defaultName
      this.selectedSnapshotIndex = null
    },
    showModal() {
      this.resetForm()
      // 使用Bootstrap的方法显示模态框
      this.$nextTick(() => {
        try {
          const modalElement = document.getElementById('snapshotModal')
          if (!modalElement) {
            console.error('模态框元素未找到')
            return
          }

          // 确保Bootstrap已加载
          if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap未加载')
            // 尝试使用jQuery显示模态框（如果可用）
            if (window.jQuery) {
              window.jQuery('#snapshotModal').modal('show')
            } else {
              alert('无法显示模态框，请检查Bootstrap是否正确加载')
            }
            return
          }

          let modal = bootstrap.Modal.getInstance(modalElement)
          if (!modal) {
            console.log('创建新的模态框实例')
            modal = new bootstrap.Modal(modalElement)
          }
          modal.show()
          console.log('模态框已显示')
        } catch (error) {
          console.error('显示模态框时出错:', error)
          alert('保存快照失败，请重试')
        }
      })
    }
  }
}
</script>

<style scoped>
.snapshot-list {
  max-height: 300px;
  overflow-y: auto;
}

.list-group-item {
  transition: all 0.2s ease;
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

.list-group-item.active {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.snapshot-item {
  cursor: pointer;
  user-select: none;
}

.list-group-item .btn-outline-danger {
  opacity: 0.7;
  transition: all 0.2s ease;
}

.list-group-item:hover .btn-outline-danger {
  opacity: 1;
}

.list-group-item.active .btn-outline-danger {
  color: white;
  border-color: white;
}
</style>
