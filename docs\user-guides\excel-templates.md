# 📊 Excel模板使用指南

## 📋 概述

Excel模板是系统生成Excel文件的基础，通过占位符机制将表单数据渲染到Excel文件中。本指南将详细介绍模板的使用方法和占位符语法。

## 📁 模板文件管理

### 模板文件位置
- **存储目录**: `backend/excel_files/templates/`
- **命名规则**: 
  - 安全测评: `安全测评-运维信息登记模板.xlsx`
  - 安全监测: `安全监测-运维信息登记模板.xlsx`
  - 应用加固: `应用加固-运维信息登记模板.xlsx`

### 模板文件创建
1. **自动创建**: 系统首次运行时会自动创建默认模板
2. **手动创建**: 可以手动创建或修改模板文件
3. **模板导入**: 支持从外部导入模板文件

## 🏷️ 占位符语法

### 1. 普通占位符
**语法**: `{{字段名}}`
**作用**: 将表单数据中的同名字段渲染到对应单元格
**示例**: 
- `{{客户}}` → 表单中"客户"字段的值
- `{{项目名称}}` → 表单中"项目名称"字段的值

### 2. 条件占位符
**语法**: `{{#if 条件}}内容{{/if}}`
**作用**: 根据条件决定是否显示内容
**示例**:
```
{{#if 有数据库}}
数据库配置信息
{{/if}}
```

### 3. 循环占位符
**语法**: `{{#each 数组}}{{字段名}}{{/each}}`
**作用**: 遍历数组数据，重复渲染内容
**示例**:
```
{{#each 服务器列表}}
服务器IP: {{IP地址}}
{{/each}}
```

### 4. 组件占位符
**语法**: `{{组件名称_COUNT}}` 和 `{{组件名称_PORT}}`
**作用**: 显示组件的数量和端口信息
**示例**:
- `{{MySQL_COUNT}}` → MySQL组件的数量
- `{{MySQL_PORT}}` → MySQL组件的端口

### 5. 服务器占位符
**语法**: `{{服务器字段名}}`
**作用**: 显示服务器相关信息
**示例**:
- `{{服务器IP}}` → 服务器IP地址
- `{{操作系统}}` → 操作系统类型
- `{{SSH端口}}` → SSH端口号

### 6. 维护记录占位符
**语法**: `{{#each 维护记录}}{{字段名}}{{/each}}`
**作用**: 遍历维护记录，显示维护信息
**示例**:
```
{{#each 维护记录}}
维护时间: {{维护时间}}
维护人员: {{维护人员}}
维护内容: {{维护内容}}
{{/each}}
```

## 📊 部署架构图

### 架构图生成规则
系统会自动为配置了服务器信息的表单生成部署架构图sheet页面。

### 架构图格式
- **组件分类**: 按分类分组显示
- **组件名称**: 显示具体组件名称
- **端口**: 显示组件端口信息
- **IP列**: 在有部署的机器上打勾显示

### 架构图布局
- 按组件分类排序
- 组件分类列使用合并单元格
- 减少空行，优化布局
- 统一格式和样式

## 🎨 模板设计最佳实践

### 1. 布局设计
- **清晰的标题**: 使用明确的标题和副标题
- **合理的分组**: 按功能模块分组信息
- **统一的样式**: 保持字体、颜色、边框的一致性

### 2. 占位符使用
- **准确的字段名**: 确保占位符与表单字段名完全匹配
- **合理的位置**: 将占位符放在合适的单元格位置
- **条件判断**: 使用条件占位符处理可选信息

### 3. 数据验证
- **必填字段**: 标记必填字段，确保数据完整性
- **格式验证**: 对特殊格式字段进行验证
- **范围检查**: 对数值字段进行范围检查

## 🔧 模板编辑工具

### 在线编辑器
1. 访问系统管理页面
2. 选择"模板管理"功能
3. 在线编辑模板内容
4. 实时预览效果

### Excel编辑器
1. 下载现有模板文件
2. 使用Excel或WPS编辑
3. 上传修改后的模板
4. 测试模板效果

### 版本管理
- **版本控制**: 支持多个版本的模板
- **版本切换**: 可以动态切换模板版本
- **版本备份**: 自动备份历史版本

## 📥 模板导入导出

### 导出模板
1. 访问模板管理页面
2. 选择要导出的模板
3. 点击"导出"按钮
4. 下载模板文件

### 导入模板
1. 准备模板文件
2. 访问模板管理页面
3. 点击"导入"按钮
4. 选择文件并上传

### 模板验证
- **格式检查**: 验证文件格式是否正确
- **占位符检查**: 验证占位符语法
- **兼容性检查**: 确保与系统兼容

## 🔍 模板测试

### 测试流程
1. **创建测试数据**: 准备完整的测试表单数据
2. **生成测试文件**: 使用测试数据生成Excel文件
3. **检查结果**: 验证生成的Excel文件是否正确
4. **修复问题**: 根据测试结果修复模板问题

### 测试要点
- **占位符渲染**: 确保所有占位符正确渲染
- **格式保持**: 确保Excel格式不被破坏
- **数据完整**: 确保所有数据都正确显示
- **特殊情况**: 测试空值、特殊字符等情况

## 🚨 常见问题

### 占位符不生效
**原因**: 字段名不匹配或语法错误
**解决**: 检查字段名拼写和占位符语法

### 格式错乱
**原因**: 模板格式被破坏或占位符位置不当
**解决**: 重新设计模板布局，确保格式正确

### 数据缺失
**原因**: 表单数据不完整或字段映射错误
**解决**: 检查表单数据完整性和字段映射关系

### 中文乱码
**原因**: 编码问题或字体不支持
**解决**: 确保使用UTF-8编码和支持中文的字体

## 📚 高级功能

### 动态表格
使用循环占位符创建动态表格，根据数据量自动调整行数。

### 条件格式
结合条件占位符和Excel条件格式，实现动态样式。

### 公式计算
在模板中使用Excel公式，实现自动计算功能。

### 图表生成
在模板中预设图表，根据数据自动生成图表。

## 📝 模板维护

### 定期更新
- 根据业务需求更新模板
- 优化模板性能和效果
- 修复发现的问题

### 版本管理
- 记录模板变更历史
- 保持版本向后兼容
- 提供版本回滚功能

### 文档同步
- 更新模板文档
- 同步占位符说明
- 维护使用示例

## 📚 相关文档

- [表单管理指南](form-management.md)
- [组件管理指南](component-management.md)
- [系统管理功能](system-administration.md)
