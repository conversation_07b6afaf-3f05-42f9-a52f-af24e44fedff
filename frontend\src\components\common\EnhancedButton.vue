<template>
  <button
    :type="type"
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
    class="btn-enhanced"
  >
    <!-- 加载状态 -->
    <span v-if="loading" class="loading-spinner me-2"></span>
    
    <!-- 图标 -->
    <i v-if="icon && !loading" :class="icon" class="me-2"></i>
    
    <!-- 按钮文本 -->
    <span>{{ text }}</span>
    
    <!-- 右侧图标 -->
    <i v-if="iconRight" :class="iconRight" class="ms-2"></i>
  </button>
</template>

<script>
export default {
  name: 'EnhancedButton',
  props: {
    text: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'button',
      validator: value => ['button', 'submit', 'reset'].includes(value)
    },
    variant: {
      type: String,
      default: 'primary',
      validator: value => ['primary', 'secondary', 'success', 'warning', 'danger', 'info', 'light', 'dark'].includes(value)
    },
    size: {
      type: String,
      default: 'md',
      validator: value => ['sm', 'md', 'lg'].includes(value)
    },
    icon: {
      type: String,
      default: ''
    },
    iconRight: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    outline: {
      type: Boolean,
      default: false
    },
    block: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  computed: {
    buttonClasses() {
      const baseClass = this.outline ? 'btn-outline' : 'btn'
      const variantClass = `${baseClass}-${this.variant}`
      const enhancedClass = `${baseClass}-${this.variant}-enhanced`
      
      return [
        'btn',
        variantClass,
        enhancedClass,
        {
          [`btn-${this.size}`]: this.size !== 'md',
          'w-100': this.block,
          'disabled': this.disabled || this.loading
        }
      ]
    }
  },
  methods: {
    handleClick(event) {
      if (!this.disabled && !this.loading) {
        this.$emit('click', event)
      }
    }
  }
}
</script>

<style scoped>
.btn-enhanced {
  position: relative;
  overflow: hidden;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.btn-enhanced:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-enhanced .loading-spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

/* 按钮尺寸 */
.btn-enhanced.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-enhanced.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* 轮廓按钮样式 */
.btn-outline-primary-enhanced {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}

.btn-outline-primary-enhanced:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline-success-enhanced {
  border: 2px solid var(--success-color);
  color: var(--success-color);
  background: transparent;
}

.btn-outline-success-enhanced:hover {
  background: var(--success-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline-warning-enhanced {
  border: 2px solid var(--warning-color);
  color: #b45309;
  background: transparent;
}

.btn-outline-warning-enhanced:hover {
  background: var(--warning-color);
  color: var(--gray-800);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline-danger-enhanced {
  border: 2px solid var(--danger-color);
  color: var(--danger-color);
  background: transparent;
}

.btn-outline-danger-enhanced:hover {
  background: var(--danger-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 波纹效果 */
.btn-enhanced::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn-enhanced:active::after {
  width: 300px;
  height: 300px;
}
</style>
