<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5); z-index: 1060;">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-file-earmark-excel me-2 text-success"></i>
            选择Excel模板类型
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>

        <div class="modal-body">
          <div class="template-selection-container">
            <!-- 当前表单信息 -->
            <div class="current-form-info mb-4">
              <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                  <h6 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    当前表单信息
                  </h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <strong>公司名称:</strong><br>
                      <span class="text-primary">{{ formData?.company_name || '未知' }}</span>
                    </div>
                    <div class="col-md-4">
                      <strong>原始类型:</strong><br>
                      <span class="badge" :class="getFormTypeBadgeClass(formData?.form_type)">
                        {{ formData?.form_type || '未知' }}
                      </span>
                    </div>
                    <div class="col-md-4">
                      <strong>记录日期:</strong><br>
                      {{ formatDate(formData?.record_date) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 模板选择区域 -->
            <div class="template-options">
              <h6 class="mb-2">
                <i class="bi bi-collection me-2"></i>
                请选择要使用的Excel模板：
              </h6>
              <div class="alert alert-info mb-3">
                <i class="bi bi-info-circle me-2"></i>
                <strong>提示：</strong>您可以选择任意类型的模板重新生成Excel。
                如果选择与原表单不同类型的模板，系统会尽量映射相关字段。
              </div>

              <!-- 加载状态 -->
              <div v-if="loading" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载可用模板...</p>
              </div>

              <!-- 错误状态 -->
              <div v-else-if="error" class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                {{ error }}
              </div>

              <!-- 无模板状态 -->
              <div v-else-if="!availableTemplates.length" class="alert alert-warning">
                <div class="d-flex align-items-start">
                  <i class="bi bi-exclamation-triangle me-3 mt-1"></i>
                  <div>
                    <h6 class="alert-heading mb-2">暂无可用模板</h6>
                    <p class="mb-2">可能的原因：</p>
                    <ul class="mb-2">
                      <li>模板管理系统中没有上传任何模板</li>
                      <li>所有模板都处于"未激活"状态</li>
                      <li>API调用失败，无法获取模板数据</li>
                    </ul>
                    <p class="mb-0">
                      <strong>解决方案：</strong>
                      请前往 <a href="/template-manager" target="_blank">模板管理页面</a>
                      上传并激活模板，或者检查浏览器控制台的错误信息。
                    </p>
                  </div>
                </div>
              </div>

              <!-- 模板列表（按类型分组） -->
              <div v-else>
                <div
                  v-for="group in groupedTemplates"
                  :key="group.type"
                  class="template-group mb-4"
                >
                  <!-- 分组标题 -->
                  <div class="group-header mb-3">
                    <h6 class="mb-0 d-flex align-items-center">
                      <i :class="getTemplateIcon(group.type)"
                         :style="{ color: getTemplateColor(group.type) }"
                         class="me-2"></i>
                      {{ group.type }}
                      <span v-if="group.isCurrent" class="badge bg-primary ms-2">
                        <i class="bi bi-check-circle me-1"></i>当前类型
                      </span>
                      <span class="badge bg-secondary ms-2">{{ group.templates.length }}个模板</span>
                    </h6>
                  </div>

                  <!-- 该类型的模板列表 -->
                  <div class="row g-3">
                    <div
                      v-for="template in group.templates"
                      :key="template.id"
                      class="col-md-6 col-lg-4"
                    >
                      <div
                        class="template-card"
                        :class="{
                          'selected': selectedTemplateId === template.id,
                          'current': template.template_type === formData?.form_type
                        }"
                        @click="selectTemplate(template.id)"
                      >
                        <div class="template-icon">
                          <i :class="getTemplateIcon(template.template_type)"
                             :style="{ color: getTemplateColor(template.template_type) }"></i>
                        </div>
                        <div class="template-title">{{ template.alias || template.filename }}</div>
                        <div class="template-type">{{ template.template_type || '未知类型' }}</div>
                        <div class="template-description">{{ template.description || '无描述' }}</div>
                        <div class="template-meta">
                          <small class="text-muted">
                            <i class="bi bi-calendar me-1"></i>
                            {{ formatDate(template.last_modified || template.created_at) }}
                          </small>
                        </div>

                        <!-- 激活状态标识 -->
                        <div v-if="template.is_active" class="active-badge">
                          <i class="bi bi-star-fill me-1"></i>
                          激活
                        </div>
                        <div v-else class="inactive-badge">
                          <i class="bi bi-star me-1"></i>
                          未激活
                        </div>

                        <!-- 当前模板标识 -->
                        <div v-if="template.template_type === formData?.form_type" class="current-badge">
                          <i class="bi bi-check-circle-fill me-1"></i>
                          当前类型
                        </div>

                        <!-- 选中标识 -->
                        <div v-if="selectedTemplateId === template.id" class="selected-badge">
                          <i class="bi bi-check2-circle me-1"></i>
                          已选择
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 模板说明 -->
            <div v-if="selectedTemplateInfo" class="template-info mt-4">
              <div class="card border-info">
                <div class="card-header bg-info text-white">
                  <h6 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    模板详情
                  </h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <strong>模板名称:</strong><br>
                      <span class="text-muted">{{ selectedTemplateInfo.alias || selectedTemplateInfo.filename }}</span>
                    </div>
                    <div class="col-md-6">
                      <strong>模板类型:</strong><br>
                      <span class="text-muted">{{ selectedTemplateInfo.template_type }}</span>
                    </div>
                  </div>
                  <div class="row mt-2">
                    <div class="col-md-6">
                      <strong>文件名:</strong><br>
                      <span class="text-muted">{{ selectedTemplateInfo.filename }}</span>
                    </div>
                    <div class="col-md-6">
                      <strong>创建时间:</strong><br>
                      <span class="text-muted">{{ formatDate(selectedTemplateInfo.created_at) }}</span>
                    </div>
                  </div>
                  <div v-if="selectedTemplateInfo.description" class="mt-2">
                    <strong>描述:</strong><br>
                    <span class="text-muted">{{ selectedTemplateInfo.description }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 模板类型匹配提示 -->
            <div v-if="selectedTemplateInfo" class="alert alert-success mt-3">
              <div class="d-flex align-items-center">
                <i class="bi bi-check-circle me-2"></i>
                <span>
                  已选择 <strong>"{{ selectedTemplateInfo.template_type }}"</strong> 类型的模板，
                  与当前表单类型完全匹配。
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            <i class="bi bi-x-circle me-1"></i>
            取消
          </button>
          <button
            type="button"
            class="btn btn-success"
            :disabled="!selectedTemplateId || loading"
            @click="confirmSelection"
          >
            <i class="bi bi-check-circle me-1"></i>
            确认生成Excel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import excelApi from '@/api/excel'

/**
 * 模板选择模态框组件
 * 用于在重新生成Excel时选择模板类型
 */
export default {
  name: 'TemplateSelectionModal',
  
  props: {
    formData: {
      type: Object,
      required: true
    }
  },

  emits: ['close', 'confirm'],

  data() {
    return {
      selectedTemplate: null,
      selectedTemplateId: null, // 选中的模板ID
      availableTemplates: [], // 从后端获取的模板列表
      loading: false, // 加载状态
      error: null // 错误信息
    }
  },

  computed: {
    selectedTemplateInfo() {
      return this.availableTemplates.find(t => t.id === this.selectedTemplateId)
    },

    // 按类型分组的模板（现在只显示当前表单类型的模板）
    groupedTemplates() {
      const currentFormType = this.formData?.form_type

      if (!this.availableTemplates.length) {
        return []
      }

      // 由于现在只获取当前表单类型的模板，所以只会有一个组
      return [{
        type: currentFormType,
        isCurrent: true,
        templates: this.availableTemplates
      }]
    }
  },

  async mounted() {
    console.log('🎯 TemplateSelectionModal mounted')
    console.log('📋 接收到的formData:', this.formData)
    console.log('📋 formData.form_type:', this.formData?.form_type)
    console.log('📋 formData.company_name:', this.formData?.company_name)

    // 加载可用模板
    await this.loadAvailableTemplates()
  },

  methods: {
    /**
     * 加载可用模板
     */
    async loadAvailableTemplates() {
      this.loading = true
      this.error = null

      try {
        console.log('🔍 加载可用模板列表')
        console.log('📋 当前表单类型:', this.formData?.form_type)

        const currentFormType = this.formData?.form_type
        if (!currentFormType) {
          this.error = '无法确定表单类型'
          return
        }

        // 🎯 只获取当前表单类型的模板
        const response = await excelApi.getTemplatesByFormType(currentFormType)

        const templates = response.data.data || []
        console.log(`📋 获取到 ${currentFormType} 类型的模板:`, templates)

        this.availableTemplates = templates

        if (this.availableTemplates.length === 0) {
          console.warn(`❌ 没有找到 ${currentFormType} 类型的模板`)
          this.error = `没有找到 ${currentFormType} 类型的模板，请先在模板管理中上传相应的模板文件`
        }

        console.log('📋 最终可用模板列表:', this.availableTemplates)

        // 默认选择当前表单类型的激活模板，如果没有则选择第一个可用模板
        this.selectDefaultTemplate()

      } catch (error) {
        console.error('❌ 加载模板列表失败:', error)
        this.error = '加载模板列表失败，请稍后重试'

        // 如果加载失败，使用备用的硬编码模板
        this.loadFallbackTemplates()
      } finally {
        this.loading = false
      }
    },

    /**
     * 选择默认模板
     */
    selectDefaultTemplate() {
      if (!this.availableTemplates.length) {
        console.warn('⚠️ 没有可用的模板')
        return
      }

      // 由于现在只获取当前表单类型的模板，逻辑简化了
      // 1. 优先选择激活模板
      const activeTemplate = this.availableTemplates.find(template => template.is_active)
      if (activeTemplate) {
        this.selectedTemplateId = activeTemplate.id
        console.log('✅ 选择激活模板:', activeTemplate.alias || activeTemplate.filename)
        return
      }

      // 2. 如果没有激活模板，选择第一个可用模板
      this.selectedTemplateId = this.availableTemplates[0].id
      console.log('✅ 选择第一个可用模板:', this.availableTemplates[0].alias || this.availableTemplates[0].filename)
    },

    /**
     * 加载备用模板（当API失败时使用）
     */
    loadFallbackTemplates() {
      console.log('🔄 使用备用模板列表')

      const currentFormType = this.formData?.form_type || '安全测评'
      console.log('🔄 为表单类型创建备用模板:', currentFormType)

      // 只创建当前表单类型的备用模板
      const fallbackTemplateMap = {
        '安全测评': {
          id: 'fallback-assessment',
          template_type: '安全测评',
          alias: '安全测评默认模板',
          filename: 'security_assessment_template.xlsx',
          description: '安全测评项目专用模板（备用）',
          is_active: true,
          created_at: new Date().toISOString()
        },
        '安全监测': {
          id: 'fallback-monitoring',
          template_type: '安全监测',
          alias: '安全监测默认模板',
          filename: 'security_monitoring_template.xlsx',
          description: '安全监测项目专用模板（备用）',
          is_active: true,
          created_at: new Date().toISOString()
        },
        '应用加固': {
          id: 'fallback-hardening',
          template_type: '应用加固',
          alias: '应用加固默认模板',
          filename: 'app_hardening_template.xlsx',
          description: '应用加固项目专用模板（备用）',
          is_active: true,
          created_at: new Date().toISOString()
        }
      }

      const fallbackTemplate = fallbackTemplateMap[currentFormType]
      if (fallbackTemplate) {
        this.availableTemplates = [fallbackTemplate]
      } else {
        this.availableTemplates = []
      }

      this.selectDefaultTemplate()
    },

    /**
     * 选择模板
     */
    selectTemplate(templateId) {
      this.selectedTemplateId = templateId
      console.log('🎯 选择模板ID:', templateId)
    },

    /**
     * 确认选择
     */
    confirmSelection() {
      if (!this.selectedTemplateId || !this.selectedTemplateInfo) {
        console.warn('⚠️ 没有选择有效的模板')
        return
      }

      console.log('✅ 确认选择模板:', this.selectedTemplateInfo)

      this.$emit('confirm', {
        templateId: this.selectedTemplateId,
        templateType: this.selectedTemplateInfo.template_type,
        templateInfo: this.selectedTemplateInfo,
        filename: this.selectedTemplateInfo.filename
      })
    },

    /**
     * 获取表单类型徽章样式
     */
    getFormTypeBadgeClass(formType) {
      const badgeClasses = {
        '安全测评': 'bg-success',
        '安全监测': 'bg-primary', 
        '应用加固': 'bg-warning text-dark'
      }
      return badgeClasses[formType] || 'bg-secondary'
    },

    /**
     * 获取模板图标
     */
    getTemplateIcon(templateType) {
      const iconMap = {
        '安全测评': 'bi bi-shield-check',
        '安全监测': 'bi bi-eye',
        '应用加固': 'bi bi-shield-lock'
      }
      return iconMap[templateType] || 'bi bi-file-earmark-excel'
    },

    /**
     * 获取模板颜色
     */
    getTemplateColor(templateType) {
      const colorMap = {
        '安全测评': '#198754',
        '安全监测': '#0d6efd',
        '应用加固': '#fd7e14'
      }
      return colorMap[templateType] || '#6c757d'
    },

    /**
     * 格式化日期
     */
    formatDate(timestamp) {
      if (!timestamp) return '未知'

      // 处理时间戳（秒）
      const date = new Date(timestamp * 1000)

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '无效日期'
      }

      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.template-selection-container {
  max-height: 70vh;
  overflow-y: auto;
}

.template-card {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: #ffffff;
}

.template-card:hover {
  border-color: #0d6efd;
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
  transform: translateY(-2px);
}

.template-card.selected {
  border-color: #198754;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
  box-shadow: 0 4px 12px rgba(25, 135, 84, 0.2);
}

.template-card.current {
  border-color: #ffc107;
  background: linear-gradient(135deg, #fffdf0 0%, #fff3cd 100%);
}

.template-icon {
  font-size: 2.5rem;
  margin-bottom: 12px;
}

.template-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 4px;
}

.template-type {
  font-size: 0.85rem;
  font-weight: 500;
  color: #007bff;
  margin-bottom: 8px;
}

.template-description {
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
  margin-bottom: 8px;
}

.template-meta {
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.current-badge {
  position: absolute;
  top: 40px;
  left: 8px;
  background: #ffc107;
  color: #000;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.selected-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #198754;
  color: #fff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.active-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.inactive-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #6c757d;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* 模板分组样式 */
.template-group {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  background: #f8f9fa;
}

.group-header {
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 8px;
  margin-bottom: 16px !important;
}

.group-header h6 {
  color: #495057;
  font-weight: 600;
}

.template-group:last-child {
  margin-bottom: 0 !important;
}

.field-tags {
  max-height: 100px;
  overflow-y: auto;
}

.compatibility-warning {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-card {
    padding: 15px;
  }
  
  .template-icon {
    font-size: 2rem;
  }
  
  .template-title {
    font-size: 1rem;
  }
  
  .template-description {
    font-size: 0.8rem;
  }
}
</style>
