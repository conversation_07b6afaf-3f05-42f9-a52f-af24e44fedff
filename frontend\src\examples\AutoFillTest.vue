<template>
  <div class="auto-fill-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-magic me-2"></i>
                访问信息自动填充功能测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 表单类型选择 -->
              <div class="row mb-4">
                <div class="col-md-4">
                  <label class="form-label fw-bold">选择表单类型</label>
                  <select v-model="selectedFormType" class="form-select" @change="loadFormType">
                    <option value="">请选择表单类型</option>
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">操作</label>
                  <div class="d-flex gap-2">
                    <button 
                      class="btn btn-primary btn-sm" 
                      @click="addTestServer"
                      :disabled="!selectedFormType"
                    >
                      添加测试服务器
                    </button>
                    <button 
                      class="btn btn-warning btn-sm" 
                      @click="clearServers"
                    >
                      清空服务器
                    </button>
                  </div>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">自动填充状态</label>
                  <div class="d-flex gap-2">
                    <span v-if="autoFillCount > 0" class="badge bg-success">
                      已填充 {{ autoFillCount }} 个字段
                    </span>
                    <span v-else class="badge bg-secondary">
                      未触发自动填充
                    </span>
                  </div>
                </div>
              </div>

              <!-- 测试说明 -->
              <div class="alert alert-info mb-4">
                <h6 class="alert-heading">自动填充功能说明</h6>
                <p class="mb-2">此测试验证根据勾选的组件自动填充访问信息的功能：</p>
                <ul class="mb-0">
                  <li><strong>安全测评</strong>：front-ssp-admin → 管理员页面，front-ssp-user → 用户页面，luna → 升级页面，backend-ssp-user → 外部服务</li>
                  <li><strong>安全监测</strong>：web-service-nginx → 业务功能页面，init → init地址，kibana → kibana地址</li>
                  <li><strong>应用加固</strong>：secweb → 平台访问地址，luna → 升级平台地址</li>
                </ul>
              </div>

              <!-- 服务器信息模拟 -->
              <div v-if="selectedFormType" class="row mb-4">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">模拟服务器信息</h6>
                    </div>
                    <div class="card-body">
                      <div v-if="testFormData.服务器信息.length === 0" class="text-muted text-center py-3">
                        暂无服务器信息，点击"添加测试服务器"按钮添加
                      </div>
                      <div v-else>
                        <div v-for="(server, index) in testFormData.服务器信息" :key="index" class="mb-3">
                          <div class="card bg-light">
                            <div class="card-body">
                              <div class="row">
                                <div class="col-md-3">
                                  <strong>服务器 {{ index + 1 }}</strong><br>
                                  <small class="text-muted">{{ server.IP地址 }}</small>
                                </div>
                                <div class="col-md-6">
                                  <strong>部署组件:</strong><br>
                                  <span v-for="component in server.部署应用" :key="component" class="badge bg-primary me-1 mb-1">
                                    {{ component }}
                                  </span>
                                </div>
                                <div class="col-md-3">
                                  <strong>组件端口:</strong><br>
                                  <div v-for="(port, comp) in server.组件端口" :key="comp" class="small">
                                    {{ comp }}: {{ port }}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 访问信息测试 -->
              <div v-if="selectedFormType" class="row">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">{{ selectedFormType }} - 访问信息自动填充测试</h6>
                    </div>
                    <div class="card-body">
                      
                      <!-- 使用DynamicAccessInfoSection测试 -->
                      <dynamic-access-info-section
                        v-model="accessInfoData"
                        :field-config="accessFieldConfig"
                        :form-type="selectedFormType"
                        :server-list="testFormData.服务器信息"
                        :key="`access-info-${selectedFormType}-${refreshKey}`"
                      />

                      <!-- 自动填充结果分析 -->
                      <div class="mt-4">
                        <h6 class="text-info">自动填充结果分析</h6>
                        <div class="row">
                          <div class="col-md-6">
                            <div class="card bg-light">
                              <div class="card-body">
                                <h6 class="card-title">已填充字段</h6>
                                <div v-if="filledFields.length > 0">
                                  <div v-for="field in filledFields" :key="field.key" class="mb-2">
                                    <strong>{{ field.label }}:</strong>
                                    <div class="text-success small">{{ field.value }}</div>
                                  </div>
                                </div>
                                <div v-else class="text-muted">
                                  暂无自动填充的字段
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="card bg-light">
                              <div class="card-body">
                                <h6 class="card-title">未填充字段</h6>
                                <div v-if="emptyFields.length > 0">
                                  <div v-for="field in emptyFields" :key="field.key" class="mb-2">
                                    <strong>{{ field.label }}:</strong>
                                    <div class="text-muted small">等待自动填充或手动输入</div>
                                  </div>
                                </div>
                                <div v-else class="text-success">
                                  所有字段都已填充
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicAccessInfoSection from '@/components/forms/common/DynamicAccessInfoSection.vue'
import { getFormFieldConfig } from '@/config/formFieldConfig'
import { getInitialFormData, createNewServerItem } from '@/config/formDataConfig'

export default {
  name: 'AutoFillTest',
  components: {
    DynamicAccessInfoSection
  },
  data() {
    return {
      selectedFormType: '',
      refreshKey: 0,
      testFormData: {},
      accessInfoData: {},
      accessFieldConfig: {},
      autoFillCount: 0
    }
  },
  computed: {
    /**
     * 已填充的字段
     */
    filledFields() {
      const fields = []
      Object.keys(this.accessFieldConfig).forEach(key => {
        const field = this.accessFieldConfig[key]
        const value = this.accessInfoData[key]
        if (value && value.trim()) {
          fields.push({
            key,
            label: field.field,
            value
          })
        }
      })
      return fields
    },

    /**
     * 未填充的字段
     */
    emptyFields() {
      const fields = []
      Object.keys(this.accessFieldConfig).forEach(key => {
        const field = this.accessFieldConfig[key]
        const value = this.accessInfoData[key]
        if (!value || !value.trim()) {
          fields.push({
            key,
            label: field.field
          })
        }
      })
      return fields
    }
  },
  methods: {
    /**
     * 加载表单类型
     */
    loadFormType() {
      if (!this.selectedFormType) return

      console.log(`加载表单类型: ${this.selectedFormType}`)

      // 获取字段配置
      const config = getFormFieldConfig(this.selectedFormType)
      this.accessFieldConfig = config.access || {}

      // 获取初始表单数据
      this.testFormData = getInitialFormData(this.selectedFormType)

      // 初始化访问信息数据
      this.initializeAccessInfoData()

      // 强制刷新组件
      this.refreshKey += 1

      console.log('表单类型加载完成:', {
        formType: this.selectedFormType,
        accessFieldConfig: this.accessFieldConfig,
        testFormData: this.testFormData
      })
    },

    /**
     * 初始化访问信息数据
     */
    initializeAccessInfoData() {
      const data = {}
      Object.keys(this.accessFieldConfig).forEach(key => {
        const field = this.accessFieldConfig[key]
        data[key] = field.default || ''
      })
      this.accessInfoData = data
    },

    /**
     * 添加测试服务器
     */
    addTestServer() {
      if (!this.selectedFormType) return

      const server = createNewServerItem()
      
      // 根据表单类型添加对应的组件
      if (this.selectedFormType === '安全测评') {
        server.IP地址 = `192.168.1.${100 + this.testFormData.服务器信息.length}`
        server.部署应用 = ['front-ssp-admin', 'front-ssp-user', 'backend-ssp-user', 'luna']
        server.组件端口 = {
          'front-ssp-admin': '8080',
          'front-ssp-user': '8081',
          'backend-ssp-user': '8082',
          'luna': '9001'
        }
        console.log('添加安全测评测试服务器:', server)
      } else if (this.selectedFormType === '安全监测') {
        server.IP地址 = `192.168.1.${100 + this.testFormData.服务器信息.length}`
        server.部署应用 = ['web-service-nginx', 'init', 'kibana']
        server.组件端口 = {
          'web-service-nginx': '443',
          'init': '8181',
          'kibana': '5601'
        }
      } else if (this.selectedFormType === '应用加固') {
        server.IP地址 = `192.168.1.${100 + this.testFormData.服务器信息.length}`
        server.部署应用 = ['secweb', 'luna']
        server.组件端口 = {
          'secweb': '8000',
          'luna': '9001'
        }
      }

      this.testFormData.服务器信息.push(server)
      console.log('添加测试服务器:', server)
    },

    /**
     * 清空服务器
     */
    clearServers() {
      this.testFormData.服务器信息 = []
      this.initializeAccessInfoData()
      this.autoFillCount = 0
      console.log('清空服务器信息')
    }
  },
  watch: {
    // 监听访问信息数据变化
    accessInfoData: {
      handler(newData) {
        // 计算自动填充的字段数量
        this.autoFillCount = Object.values(newData).filter(value => 
          value && value.trim()
        ).length
      },
      deep: true
    }
  },
  mounted() {
    console.log('AutoFillTest 组件已挂载')
  }
}
</script>

<style scoped>
.auto-fill-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 2px solid #e9ecef;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.badge {
  font-size: 0.75rem;
}

.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #0dcaf0 !important; }
.text-danger { color: #dc3545 !important; }
</style>
