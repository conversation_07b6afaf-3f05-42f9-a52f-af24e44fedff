<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content shadow-lg border-0">
        <div class="modal-header bg-gradient-warning text-dark border-0">
          <h5 class="modal-title fw-bold">
            <i class="bi bi-shield-lock me-2"></i>
            修改密码
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body p-4">
          <form @submit.prevent="handleSubmit">
            <!-- 当前密码 -->
            <div class="mb-3">
              <label class="form-label fw-semibold">
                <i class="bi bi-key me-1"></i>当前密码 <span class="text-danger">*</span>
              </label>
              <div class="input-group">
                <input 
                  :type="showOldPassword ? 'text' : 'password'" 
                  class="form-control" 
                  v-model="form.oldPassword"
                  placeholder="请输入当前密码"
                  required
                  ref="oldPasswordInput"
                >
                <button 
                  type="button" 
                  class="btn btn-outline-secondary"
                  @click="showOldPassword = !showOldPassword"
                >
                  <i :class="showOldPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                </button>
              </div>
            </div>

            <!-- 新密码 -->
            <div class="mb-3">
              <label class="form-label fw-semibold">
                <i class="bi bi-shield-plus me-1"></i>新密码 <span class="text-danger">*</span>
              </label>
              <div class="input-group">
                <input 
                  :type="showNewPassword ? 'text' : 'password'" 
                  class="form-control" 
                  v-model="form.newPassword"
                  placeholder="请输入新密码"
                  required
                  minlength="6"
                >
                <button 
                  type="button" 
                  class="btn btn-outline-secondary"
                  @click="showNewPassword = !showNewPassword"
                >
                  <i :class="showNewPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                </button>
              </div>
              <small class="text-muted">密码长度至少6位</small>
            </div>

            <!-- 确认新密码 -->
            <div class="mb-4">
              <label class="form-label fw-semibold">
                <i class="bi bi-shield-check me-1"></i>确认新密码 <span class="text-danger">*</span>
              </label>
              <div class="input-group">
                <input 
                  :type="showConfirmPassword ? 'text' : 'password'" 
                  class="form-control" 
                  v-model="form.confirmPassword"
                  placeholder="请再次输入新密码"
                  required
                  :class="{ 'is-invalid': form.confirmPassword && form.newPassword !== form.confirmPassword }"
                >
                <button 
                  type="button" 
                  class="btn btn-outline-secondary"
                  @click="showConfirmPassword = !showConfirmPassword"
                >
                  <i :class="showConfirmPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                </button>
              </div>
              <div v-if="form.confirmPassword && form.newPassword !== form.confirmPassword" 
                   class="invalid-feedback">
                两次输入的密码不一致
              </div>
            </div>

            <!-- 密码强度提示 -->
            <div class="alert alert-info border-0 bg-light">
              <h6 class="alert-heading">
                <i class="bi bi-info-circle me-1"></i>密码安全建议
              </h6>
              <ul class="mb-0 small">
                <li>密码长度至少6位字符</li>
                <li>建议包含大小写字母、数字和特殊字符</li>
                <li>避免使用常见密码或个人信息</li>
                <li>定期更换密码以保证账户安全</li>
              </ul>
            </div>
          </form>
        </div>
        <div class="modal-footer border-0 pt-0">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button 
            type="button" 
            class="btn btn-warning" 
            @click="handleSubmit"
            :disabled="saving || !isFormValid"
          >
            <span v-if="saving" class="spinner-border spinner-border-sm me-2"></span>
            <i v-else class="bi bi-shield-check me-1"></i>
            {{ saving ? '修改中...' : '确认修改' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChangePasswordModal',
  emits: ['close', 'success', 'error'],
  data() {
    return {
      form: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      saving: false,
      showOldPassword: false,
      showNewPassword: false,
      showConfirmPassword: false
    }
  },
  computed: {
    isFormValid() {
      return this.form.oldPassword && 
             this.form.newPassword && 
             this.form.confirmPassword &&
             this.form.newPassword === this.form.confirmPassword &&
             this.form.newPassword.length >= 6
    }
  },
  mounted() {
    // 自动聚焦到第一个输入框
    this.$nextTick(() => {
      this.$refs.oldPasswordInput?.focus()
    })
  },
  methods: {
    async handleSubmit() {
      if (!this.isFormValid) {
        return
      }

      this.saving = true
      try {
        const API_BASE_URL = process.env.NODE_ENV === 'production'
          ? (process.env.VUE_APP_API_BASE_URL || '/api')
          : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

        const response = await fetch(`${API_BASE_URL}/auth/change-password`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.$store.state.token}`
          },
          body: JSON.stringify({
            old_password: this.form.oldPassword,
            new_password: this.form.newPassword
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            this.$emit('success', '密码修改成功')
            this.$emit('close')
          } else {
            this.$emit('error', result.message || '密码修改失败')
          }
        } else {
          const errorResult = await response.json()
          this.$emit('error', errorResult.message || '密码修改失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        this.$emit('error', '网络错误，请稍后重试')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.bg-gradient-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.modal-content {
  border-radius: 1rem;
  overflow: hidden;
}

.form-control:focus {
  border-color: #ffc107;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.btn {
  transition: all 0.3s ease;
}

.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.alert {
  border-radius: 0.75rem;
}

.input-group .btn {
  border-color: #ced4da;
}

.input-group .btn:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
}

/* 密码强度指示器样式 */
.password-strength {
  height: 4px;
  border-radius: 2px;
  margin-top: 0.5rem;
  transition: all 0.3s ease;
}

.strength-weak {
  background-color: #dc3545;
  width: 33%;
}

.strength-medium {
  background-color: #ffc107;
  width: 66%;
}

.strength-strong {
  background-color: #28a745;
  width: 100%;
}
</style>
