/**
 * 性能监控工具
 */

// 性能计时器
export class PerformanceTimer {
  constructor(name) {
    this.name = name
    this.startTime = null
    this.endTime = null
  }

  start() {
    this.startTime = performance.now()
    console.log(`⏱️ [${this.name}] 开始计时`)
    return this
  }

  end() {
    this.endTime = performance.now()
    const duration = this.endTime - this.startTime
    console.log(`⏱️ [${this.name}] 耗时: ${duration.toFixed(2)}ms`)
    return duration
  }
}

// 防抖函数
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 延迟加载
export function lazyLoad(callback, delay = 100) {
  return new Promise(resolve => {
    setTimeout(() => {
      const result = callback()
      resolve(result)
    }, delay)
  })
}

// 批量处理
export function batchProcess(items, batchSize = 100, processor) {
  return new Promise(async (resolve) => {
    const results = []
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize)
      const batchResults = await processor(batch)
      results.push(...batchResults)
      
      // 让出控制权，避免阻塞UI
      if (i + batchSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, 0))
      }
    }
    resolve(results)
  })
}

// 内存使用监控
export function checkMemoryUsage() {
  if (performance.memory) {
    const memory = performance.memory
    console.log('📊 内存使用情况:')
    console.log(`  已使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
    console.log(`  总分配: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
    console.log(`  限制: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`)
    return memory
  }
  return null
}

// 页面性能指标
export function getPagePerformance() {
  const navigation = performance.getEntriesByType('navigation')[0]
  if (navigation) {
    console.log('📈 页面性能指标:')
    console.log(`  DNS查询: ${navigation.domainLookupEnd - navigation.domainLookupStart}ms`)
    console.log(`  TCP连接: ${navigation.connectEnd - navigation.connectStart}ms`)
    console.log(`  请求响应: ${navigation.responseEnd - navigation.requestStart}ms`)
    console.log(`  DOM解析: ${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`)
    console.log(`  页面加载: ${navigation.loadEventEnd - navigation.loadEventStart}ms`)
    return navigation
  }
  return null
}

// 组件渲染性能监控
export function measureComponentRender(componentName) {
  return {
    beforeMount() {
      this._renderTimer = new PerformanceTimer(`${componentName} 渲染`)
      this._renderTimer.start()
    },
    mounted() {
      if (this._renderTimer) {
        this._renderTimer.end()
      }
    }
  }
}

// 网络请求性能监控
export function measureApiCall(url, options = {}) {
  const timer = new PerformanceTimer(`API: ${url}`)
  timer.start()
  
  return fetch(url, options)
    .then(response => {
      timer.end()
      return response
    })
    .catch(error => {
      timer.end()
      throw error
    })
}

// 图片懒加载
export function lazyLoadImage(img, src) {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.src = src
        observer.unobserve(entry.target)
      }
    })
  })
  observer.observe(img)
}

// 虚拟滚动辅助函数
export function calculateVisibleItems(scrollTop, itemHeight, containerHeight, totalItems) {
  const startIndex = Math.floor(scrollTop / itemHeight)
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    totalItems - 1
  )
  return { startIndex, endIndex }
}

// 性能优化建议
export function getPerformanceTips() {
  const tips = []
  
  // 检查内存使用
  const memory = checkMemoryUsage()
  if (memory && memory.usedJSHeapSize > 50 * 1024 * 1024) {
    tips.push('内存使用较高，建议检查是否有内存泄漏')
  }
  
  // 检查页面性能
  const navigation = getPagePerformance()
  if (navigation && navigation.loadEventEnd - navigation.navigationStart > 3000) {
    tips.push('页面加载时间较长，建议优化资源加载')
  }
  
  return tips
}
