<template>
  <div 
    :class="cardClasses"
    class="card-enhanced fade-in"
    :style="cardStyle"
  >
    <!-- 卡片头部 -->
    <div v-if="$slots.header || title" class="card-header">
      <div class="d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
          <i v-if="icon" :class="icon" class="me-2"></i>
          <h5 v-if="title" class="mb-0">{{ title }}</h5>
          <slot name="header"></slot>
        </div>
        <div v-if="$slots.actions" class="card-actions">
          <slot name="actions"></slot>
        </div>
      </div>
    </div>
    
    <!-- 卡片主体 -->
    <div class="card-body">
      <slot></slot>
    </div>
    
    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="card-footer bg-transparent border-top-0">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnhancedCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'security-monitoring', 'security-testing', 'app-hardening'].includes(value)
    },
    hoverable: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    cardClasses() {
      return [
        {
          'hover-lift': this.hoverable,
          [`card-${this.variant}`]: this.variant !== 'default'
        }
      ]
    },
    cardStyle() {
      return this.loading ? { opacity: 0.7 } : {}
    }
  }
}
</script>

<style scoped>
.card-actions .btn {
  margin-left: 0.5rem;
}

.card-actions .btn:first-child {
  margin-left: 0;
}

/* 加载状态 */
.card-enhanced[style*="opacity: 0.7"] {
  pointer-events: none;
  position: relative;
}

.card-enhanced[style*="opacity: 0.7"]::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--gray-200);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  z-index: 10;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
