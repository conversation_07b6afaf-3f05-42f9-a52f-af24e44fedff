#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
基于 app.sql 文件重新生成数据库初始化

功能说明：
1. 读取 app.sql 文件并执行 SQL 语句
2. 初始化所有数据库表和数据
3. 支持 MySQL 数据库
4. 支持开发环境和生产环境
5. 支持重新创建数据库（删除现有数据库并重新创建）
6. 自动验证数据库表结构和 AUTO_INCREMENT 配置

使用方法：
- 生产环境初始化: python init_db.py --env production
- 开发环境初始化: python init_db.py --env development
- 重新创建生产环境: python init_db.py --env production --recreate
- 重新创建开发环境: python init_db.py --env development --recreate
- 默认环境: python init_db.py (使用生产环境)

注意事项：
- --recreate 参数会删除现有数据库并重新创建，请谨慎使用
- 重新创建操作会要求用户确认，避免误操作
- 脚本会自动验证表结构和数据完整性
"""

import os
import sys
import re
import argparse
from datetime import datetime
import pymysql
from urllib.parse import quote_plus

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

BASE_DIR = os.path.abspath(os.path.dirname(__file__))

# MySQL数据库配置
MYSQL_HOST = '************'
MYSQL_PORT = 3306
MYSQL_USER = 'junguangchen'
MYSQL_PASSWORD = '1qaz@WSX'

# 环境配置
DATABASE_CONFIG = {
    'production': 'export_excel_prod',    # 生产环境数据库
    'development': 'export_excel_dev',    # 开发环境数据库
    'default': 'export_excel_prod'        # 默认使用生产环境
}

def read_sql_file():
    """读取 app.sql 文件内容"""
    sql_file_path = os.path.join(BASE_DIR, 'app.sql')

    if not os.path.exists(sql_file_path):
        print(f"错误: 找不到 app.sql 文件: {sql_file_path}")
        return None

    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"成功读取 app.sql 文件，大小: {len(content)} 字符")
        return content
    except Exception as e:
        print(f"读取 app.sql 文件失败: {str(e)}")
        return None

def parse_sql_statements(sql_content):
    """解析 SQL 语句"""
    if not sql_content:
        return []

    # 移除注释和空行
    lines = sql_content.split('\n')
    cleaned_lines = []

    for line in lines:
        line = line.strip()
        # 跳过空行和注释
        if line and not line.startswith('--') and not line.startswith('#'):
            cleaned_lines.append(line)

    # 重新组合成完整的 SQL 内容
    cleaned_content = ' '.join(cleaned_lines)

    # 按分号分割 SQL 语句
    statements = []
    current_statement = ""
    in_string = False
    escape_next = False

    for char in cleaned_content:
        if escape_next:
            current_statement += char
            escape_next = False
            continue

        if char == '\\':
            escape_next = True
            current_statement += char
            continue

        if char == "'" and not escape_next:
            in_string = not in_string

        current_statement += char

        if char == ';' and not in_string:
            stmt = current_statement.strip()
            if stmt and stmt != ';':
                statements.append(stmt[:-1])  # 移除末尾的分号
            current_statement = ""

    # 添加最后一个语句（如果有的话）
    if current_statement.strip():
        statements.append(current_statement.strip())

    print(f"解析出 {len(statements)} 条 SQL 语句")
    return statements

def connect_to_mysql(database=None):
    """连接到 MySQL 数据库"""
    try:
        # 连接到 MySQL 服务器
        connection_params = {
            'host': MYSQL_HOST,
            'port': MYSQL_PORT,
            'user': MYSQL_USER,
            'password': MYSQL_PASSWORD,
            'charset': 'utf8mb4',
            'autocommit': False
        }

        if database:
            connection_params['database'] = database

        connection = pymysql.connect(**connection_params)

        if database:
            print(f"成功连接到 MySQL 数据库: {MYSQL_HOST}:{MYSQL_PORT}/{database}")
        else:
            print(f"成功连接到 MySQL 服务器: {MYSQL_HOST}:{MYSQL_PORT}")
        return connection
    except Exception as e:
        print(f"连接 MySQL 失败: {str(e)}")
        return None

def recreate_database(database_name):
    """重新创建数据库（删除现有数据库并重新创建）"""
    print(f"🗑️ 准备重新创建数据库: {database_name}")
    print("⚠️ 警告：这将删除所有现有数据！")

    # 连接到 MySQL 服务器（不指定数据库）
    connection = connect_to_mysql()
    if not connection:
        return False

    try:
        with connection.cursor() as cursor:
            # 删除现有数据库
            print(f"🗑️ 删除现有数据库 {database_name}...")
            cursor.execute(f"DROP DATABASE IF EXISTS {database_name}")

            # 创建新数据库
            print(f"🆕 创建新数据库 {database_name}...")
            cursor.execute(f"CREATE DATABASE {database_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")

            connection.commit()
            print(f"✅ 数据库 {database_name} 重新创建成功")

        return True

    except Exception as e:
        print(f"❌ 重新创建数据库失败: {str(e)}")
        return False
    finally:
        connection.close()

def verify_database_tables(database_name):
    """验证数据库表结构"""
    print("\n🔍 验证数据库表结构...")

    connection = connect_to_mysql(database_name)
    if not connection:
        return False

    try:
        with connection.cursor() as cursor:
            # 检查关键表
            tables_to_check = ['excel_file', 'user', 'form_submission', 'component_config']

            for table in tables_to_check:
                try:
                    # 检查表是否存在
                    cursor.execute(f"SHOW CREATE TABLE {table}")
                    result = cursor.fetchone()

                    if result:
                        create_sql = result[1]
                        if 'AUTO_INCREMENT' in create_sql:
                            print(f"✅ {table} 表 AUTO_INCREMENT 配置正确")
                        else:
                            print(f"⚠️ {table} 表 AUTO_INCREMENT 配置缺失")

                        # 检查记录数
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        print(f"📊 {table} 表有 {count} 条记录")
                    else:
                        print(f"❌ {table} 表不存在")

                except Exception as e:
                    print(f"❌ 检查表 {table} 失败: {e}")

        return True

    except Exception as e:
        print(f"❌ 验证数据库表失败: {str(e)}")
        return False
    finally:
        connection.close()

def execute_sql_statements(connection, statements, database_name=None):
    """执行 SQL 语句"""
    cursor = connection.cursor()
    executed_count = 0
    error_count = 0

    try:
        # 如果指定了数据库，先切换到该数据库
        if database_name:
            cursor.execute(f"USE {database_name}")
            print(f"切换到数据库: {database_name}")

        for i, statement in enumerate(statements):
            statement = statement.strip()
            if not statement:
                continue

            try:
                # 显示执行进度
                if i % 50 == 0 or i < 10:  # 前10条和每50条显示一次
                    print(f"执行语句 {i+1}/{len(statements)}: {statement[:80]}...")

                cursor.execute(statement)
                executed_count += 1

                # 每执行20条语句提交一次
                if executed_count % 20 == 0:
                    connection.commit()

            except Exception as e:
                error_msg = str(e)
                # 忽略一些常见的无害错误
                if any(ignore in error_msg.lower() for ignore in [
                    'table already exists',
                    'database already exists',
                    'duplicate entry',
                    'column already exists'
                ]):
                    print(f"  跳过: {error_msg}")
                else:
                    print(f"  错误: {error_msg}")
                    error_count += 1

        # 最终提交
        connection.commit()
        print(f"✅ SQL 执行完成: 成功 {executed_count} 条，错误 {error_count} 条")

        return executed_count, error_count

    except Exception as e:
        print(f"❌ 执行 SQL 语句时发生错误: {str(e)}")
        connection.rollback()
        return executed_count, error_count
    finally:
        cursor.close()

def create_directories():
    """创建必要的目录"""
    directories = [
        os.path.join(BASE_DIR, 'uploads'),
        os.path.join(BASE_DIR, 'excel_files'),
        os.path.join(BASE_DIR, 'excel_files', 'templates'),
        os.path.join(BASE_DIR, 'excel_files', 'generated')
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"确保目录存在: {directory}")

def init_database(environment='production', recreate=False):
    """初始化数据库"""
    # 获取数据库名称
    database_name = DATABASE_CONFIG.get(environment, DATABASE_CONFIG['default'])

    print("="*60)
    if recreate:
        print(f"开始重新创建数据库 - 环境: {environment}")
    else:
        print(f"开始初始化数据库 - 环境: {environment}")
    print(f"数据库名称: {database_name}")
    print("="*60)

    # 创建必要的目录
    create_directories()

    # 如果需要重新创建数据库
    if recreate:
        if not recreate_database(database_name):
            return False

    # 读取 SQL 文件
    sql_content = read_sql_file()
    if not sql_content:
        return False

    # 解析 SQL 语句
    statements = parse_sql_statements(sql_content)
    if not statements:
        print("没有找到有效的 SQL 语句")
        return False

    # 连接到指定数据库
    connection = connect_to_mysql(database_name)
    if not connection:
        return False

    try:
        # 执行 SQL 语句
        success_count, error_count = execute_sql_statements(connection, statements, database_name)

        # 验证数据库表结构
        verify_database_tables(database_name)

        print("="*60)
        if recreate:
            print("🎉 数据库重新创建完成！")
        else:
            print("✅ 数据库初始化完成！")
        print(f"MySQL 数据库: {MYSQL_HOST}:{MYSQL_PORT}/{database_name}")
        print(f"执行结果: 成功 {success_count} 条，错误 {error_count} 条")
        print("="*60)

        return True

    finally:
        connection.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库初始化脚本')
    parser.add_argument(
        '--env',
        choices=['development', 'production'],
        default='production',
        help='指定环境 (development/production，默认: production)'
    )
    parser.add_argument(
        '--recreate',
        action='store_true',
        help='重新创建数据库（删除现有数据库并重新创建）'
    )
    parser.add_argument(
        '--force',
        action='store_true',
        help='强制执行，跳过确认提示（用于自动化部署）'
    )

    args = parser.parse_args()

    # 确认操作
    database_name = DATABASE_CONFIG.get(args.env, DATABASE_CONFIG['default'])

    if args.recreate:
        print(f"⚠️  即将重新创建 {args.env} 环境的数据库: {database_name}")
        print("⚠️  警告：这将删除所有现有数据！")
        if not args.force:
            confirm = input("确认继续吗？(y/N): ")
            if confirm.lower() not in ['y', 'yes']:
                print("❌ 操作已取消")
                return
        else:
            print("🤖 自动化模式：跳过确认提示")
    else:
        print(f"⚠️  即将初始化 {args.env} 环境的数据库: {database_name}")
        if not args.force:
            confirm = input("确认继续吗？(y/N): ")
            if confirm.lower() not in ['y', 'yes']:
                print("❌ 操作已取消")
                return
        else:
            print("🤖 自动化模式：跳过确认提示")

    try:
        success = init_database(args.env, recreate=args.recreate)
        if success:
            if args.recreate:
                print("\n🎉 数据库重新创建成功！")
            else:
                print("\n✅ 数据库初始化成功！")
            print(f"\n环境: {args.env}")
            print(f"数据库: {database_name}")
            print("\n默认管理员账户:")
            print("  用户名: admin")
            print("  密码: admin123")
            print("  请首次登录后修改密码！")
        else:
            if args.recreate:
                print("\n❌ 数据库重新创建失败！")
            else:
                print("\n❌ 数据库初始化失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生未知错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()