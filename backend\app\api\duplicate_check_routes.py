"""
重复提交检测API路由
提供重复提交检测和管理功能
"""

from flask import Blueprint, jsonify, request, current_app
from app.auth.decorators import permission_required, get_current_user
from app.utils.cache_utils import DuplicateCheckCacheManager
from app.models.models import FormSubmission
from app import db
import traceback
from datetime import datetime

# 创建重复提交检测蓝图
duplicate_check_bp = Blueprint('duplicate_check', __name__, url_prefix='/api/duplicate-check')


@duplicate_check_bp.route('/check', methods=['POST'])
@permission_required('form.submit')
def check_duplicate_submission():
    """
    检查重复提交
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400
        
        company_name = data.get('company_name', '').strip()
        form_type = data.get('form_type', '').strip()
        
        if not company_name or not form_type:
            return jsonify({
                'status': 'error',
                'message': '公司名称和表单类型不能为空'
            }), 400
        
        # 检查缓存中的重复提交
        cached_submission_id = DuplicateCheckCacheManager.check_duplicate(company_name, form_type)
        
        if cached_submission_id:
            # 从数据库获取详细信息
            existing_submission = FormSubmission.query.get(cached_submission_id)
            if existing_submission:
                current_app.logger.warning(f"检测到重复提交: company={company_name}, form_type={form_type}, existing_id={cached_submission_id}")
                return jsonify({
                    'status': 'warning',
                    'message': '检测到重复提交',
                    'data': {
                        'is_duplicate': True,
                        'existing_submission': {
                            'id': existing_submission.id,
                            'company_name': existing_submission.company_name,
                            'form_type': existing_submission.form_type,
                            'created_at': existing_submission.created_at.isoformat(),
                            'submitter': existing_submission.submitter
                        }
                    }
                })
        
        # 检查数据库中的历史提交（作为备用检查）
        existing_submission = FormSubmission.query.filter_by(
            company_name=company_name,
            form_type=form_type
        ).order_by(FormSubmission.created_at.desc()).first()
        
        if existing_submission:
            # 如果数据库中存在但缓存中没有，更新缓存
            DuplicateCheckCacheManager.mark_submission(company_name, form_type, existing_submission.id)
            
            current_app.logger.warning(f"检测到历史重复提交: company={company_name}, form_type={form_type}, existing_id={existing_submission.id}")
            return jsonify({
                'status': 'warning',
                'message': '检测到历史重复提交',
                'data': {
                    'is_duplicate': True,
                    'existing_submission': {
                        'id': existing_submission.id,
                        'company_name': existing_submission.company_name,
                        'form_type': existing_submission.form_type,
                        'created_at': existing_submission.created_at.isoformat(),
                        'submitter': existing_submission.submitter
                    }
                }
            })
        
        # 没有重复提交
        current_app.logger.debug(f"重复检测通过: company={company_name}, form_type={form_type}")
        return jsonify({
            'status': 'success',
            'message': '未检测到重复提交',
            'data': {
                'is_duplicate': False,
                'existing_submission': None
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"检查重复提交失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'检查重复提交失败: {str(e)}'
        }), 500


@duplicate_check_bp.route('/mark', methods=['POST'])
@permission_required('form.submit')
def mark_submission():
    """
    标记提交记录（在成功提交后调用）
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400
        
        company_name = data.get('company_name', '').strip()
        form_type = data.get('form_type', '').strip()
        submission_id = data.get('submission_id')
        
        if not company_name or not form_type or not submission_id:
            return jsonify({
                'status': 'error',
                'message': '公司名称、表单类型和提交ID不能为空'
            }), 400
        
        # 标记提交记录
        success = DuplicateCheckCacheManager.mark_submission(company_name, form_type, submission_id)
        
        if success:
            current_app.logger.info(f"标记提交记录成功: company={company_name}, form_type={form_type}, id={submission_id}")
            return jsonify({
                'status': 'success',
                'message': '提交记录标记成功',
                'data': {
                    'submission_id': submission_id,
                    'company_name': company_name,
                    'form_type': form_type
                }
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '提交记录标记失败'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"标记提交记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'标记提交记录失败: {str(e)}'
        }), 500


@duplicate_check_bp.route('/clear', methods=['POST'])
@permission_required('form.duplicate.manage')
def clear_duplicate_check():
    """
    清除重复检测记录
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400
        
        company_name = data.get('company_name', '').strip()
        form_type = data.get('form_type', '').strip()
        
        if not company_name or not form_type:
            return jsonify({
                'status': 'error',
                'message': '公司名称和表单类型不能为空'
            }), 400
        
        # 清除重复检测记录
        success = DuplicateCheckCacheManager.clear_duplicate_check(company_name, form_type)
        
        if success:
            current_app.logger.info(f"清除重复检测记录: company={company_name}, form_type={form_type}, operator={current_user.username}")
            return jsonify({
                'status': 'success',
                'message': '重复检测记录清除成功'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '重复检测记录清除失败'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"清除重复检测记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'清除重复检测记录失败: {str(e)}'
        }), 500


@duplicate_check_bp.route('/stats', methods=['GET'])
@permission_required('form.duplicate.view')
def get_duplicate_check_stats():
    """
    获取重复检测统计信息
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        # 获取重复检测相关的缓存键
        from app.utils.cache_utils import cache
        
        try:
            duplicate_keys = cache.cache._write_client.keys('duplicate_check:*')
            
            # 统计信息
            stats = {
                'total_checks': len(duplicate_keys),
                'active_submissions': len(duplicate_keys),
                'cache_status': 'connected'
            }
            
            # 获取最近的检测记录（示例）
            recent_checks = []
            for key in duplicate_keys[:10]:  # 只取前10个
                try:
                    key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                    ttl = cache.cache._write_client.ttl(key)
                    recent_checks.append({
                        'key': key_str,
                        'ttl': ttl
                    })
                except:
                    continue
            
            stats['recent_checks'] = recent_checks
            
            return jsonify({
                'status': 'success',
                'message': '获取重复检测统计成功',
                'data': stats
            })
            
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'获取重复检测统计失败: {str(e)}'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"获取重复检测统计失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取重复检测统计失败: {str(e)}'
        }), 500


@duplicate_check_bp.route('/health', methods=['GET'])
def duplicate_check_health():
    """
    重复检测系统健康检查（无需权限）
    """
    try:
        # 测试缓存连接
        from app.utils.cache_utils import cache
        cache.cache._write_client.ping()
        
        return jsonify({
            'status': 'success',
            'data': {
                'duplicate_check_system': 'Redis',
                'cache_status': 'connected',
                'features': [
                    'duplicate_check',
                    'submission_mark',
                    'duplicate_clear',
                    'duplicate_stats'
                ]
            }
        })
    except Exception as e:
        current_app.logger.error(f"重复检测健康检查失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'重复检测系统异常: {str(e)}'
        }), 500
