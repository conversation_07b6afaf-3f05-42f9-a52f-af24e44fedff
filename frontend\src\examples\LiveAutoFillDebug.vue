<template>
  <div class="live-auto-fill-debug">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-activity me-2"></i>
                实时自动填充调试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 控制面板 -->
              <div class="row mb-4">
                <div class="col-md-6">
                  <label class="form-label fw-bold">表单类型</label>
                  <select v-model="formType" class="form-select">
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">调试操作</label>
                  <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm" @click="addServerAndWatch">
                      添加服务器并观察
                    </button>
                    <button class="btn btn-success btn-sm" @click="manualTrigger">
                      手动触发填充
                    </button>
                    <button class="btn btn-warning btn-sm" @click="clearAll">
                      清空重置
                    </button>
                  </div>
                </div>
              </div>

              <!-- 实时日志 -->
              <div class="row mb-4">
                <div class="col-12">
                  <h6>实时调试日志</h6>
                  <div class="card bg-dark text-light">
                    <div class="card-body" style="height: 200px; overflow-y: auto;" ref="logContainer">
                      <div v-for="(log, index) in debugLogs" :key="index" class="mb-1">
                        <span class="text-muted">{{ log.timestamp }}</span>
                        <span :class="log.type === 'error' ? 'text-danger' : log.type === 'success' ? 'text-success' : log.type === 'warning' ? 'text-warning' : 'text-info'">
                          {{ log.message }}
                        </span>
                      </div>
                      <div v-if="debugLogs.length === 0" class="text-muted">
                        等待调试信息...
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 表单测试区域 -->
              <div class="row mb-4">
                <div class="col-12">
                  <h6>表单测试区域</h6>
                  <div class="card">
                    <div class="card-body">
                      <!-- 使用BaseForm进行测试 -->
                      <base-form
                        v-model="testFormData"
                        :form-type="formType"
                        :component-groups="componentGroups"
                        :key="`form-${formType}-${refreshKey}`"
                        @update:modelValue="onFormDataChange"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 数据状态监控 -->
              <div class="row">
                <div class="col-md-6">
                  <h6>服务器信息状态</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div class="mb-2">
                        <strong>服务器数量:</strong> {{ testFormData.服务器信息?.length || 0 }}
                      </div>
                      <div v-if="testFormData.服务器信息?.length > 0">
                        <div v-for="(server, index) in testFormData.服务器信息" :key="index" class="mb-2 p-2 border rounded">
                          <div><strong>服务器 {{ index + 1 }}:</strong> {{ server.IP地址 }}</div>
                          <div><strong>组件:</strong> {{ server.部署应用?.join(', ') || '无' }}</div>
                          <div><strong>端口:</strong> 
                            <span v-for="(port, comp) in server.组件端口" :key="comp" class="me-2">
                              {{ comp }}:{{ port }}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div v-else class="text-muted">
                        暂无服务器信息
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-6">
                  <h6>访问信息状态</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div v-if="formType === '安全测评'">
                        <div class="mb-1"><strong>管理员页面IP:</strong> {{ testFormData.管理员页面IP || '(空)' }}</div>
                        <div class="mb-1"><strong>用户页面IP:</strong> {{ testFormData.用户页面IP || '(空)' }}</div>
                        <div class="mb-1"><strong>升级页面IP:</strong> {{ testFormData.升级页面IP || '(空)' }}</div>
                        <div class="mb-1"><strong>对外服务端口:</strong> {{ testFormData.对外服务端口 || '(空)' }}</div>
                      </div>
                      <div v-else-if="formType === '安全监测'">
                        <div class="mb-1"><strong>业务功能页面地址:</strong> {{ testFormData.业务功能页面地址 || '(空)' }}</div>
                        <div class="mb-1"><strong>init地址:</strong> {{ testFormData.init地址 || '(空)' }}</div>
                        <div class="mb-1"><strong>kibana地址:</strong> {{ testFormData.kibana地址 || '(空)' }}</div>
                      </div>
                      <div v-else-if="formType === '应用加固'">
                        <div class="mb-1"><strong>平台访问地址:</strong> {{ testFormData.平台访问地址 || '(空)' }}</div>
                        <div class="mb-1"><strong>升级平台地址:</strong> {{ testFormData.升级平台地址 || '(空)' }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseForm from '@/components/forms/common/BaseForm.vue'
import { getInitialFormData, createNewServerItem } from '@/config/formDataConfig'

export default {
  name: 'LiveAutoFillDebug',
  components: {
    BaseForm
  },
  data() {
    return {
      formType: '安全测评',
      testFormData: {},
      componentGroups: {},
      refreshKey: 0,
      debugLogs: []
    }
  },
  methods: {
    /**
     * 添加调试日志
     */
    addLog(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString()
      this.debugLogs.push({
        timestamp,
        message,
        type
      })
      
      // 限制日志数量
      if (this.debugLogs.length > 50) {
        this.debugLogs.shift()
      }
      
      // 自动滚动到底部
      this.$nextTick(() => {
        const container = this.$refs.logContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    /**
     * 添加服务器并观察自动填充
     */
    addServerAndWatch() {
      this.addLog(`开始添加${this.formType}测试服务器...`, 'info')
      
      const server = createNewServerItem()
      server.IP地址 = `192.168.1.${100 + this.testFormData.服务器信息.length}`
      
      // 根据表单类型添加对应组件
      if (this.formType === '安全测评') {
        server.部署应用 = ['front-ssp-admin', 'front-ssp-user', 'luna', 'backend-ssp-user']
        server.组件端口 = {
          'front-ssp-admin': '8080',
          'front-ssp-user': '8081',
          'luna': '9001',
          'backend-ssp-user': '8082'
        }
        this.addLog('添加安全测评组件: front-ssp-admin, front-ssp-user, luna, backend-ssp-user', 'info')
      } else if (this.formType === '安全监测') {
        server.部署应用 = ['web-service-nginx', 'init', 'kibana']
        server.组件端口 = {
          'web-service-nginx': '443',
          'init': '8181',
          'kibana': '5601'
        }
        this.addLog('添加安全监测组件: web-service-nginx, init, kibana', 'info')
      } else if (this.formType === '应用加固') {
        server.部署应用 = ['secweb', 'luna']
        server.组件端口 = {
          'secweb': '8000',
          'luna': '9001'
        }
        this.addLog('添加应用加固组件: secweb, luna', 'info')
      }
      
      this.testFormData.服务器信息.push(server)
      this.addLog(`服务器已添加: ${server.IP地址}`, 'success')
      
      // 等待一下再检查自动填充结果
      setTimeout(() => {
        this.checkAutoFillResults()
      }, 1000)
    },

    /**
     * 检查自动填充结果
     */
    checkAutoFillResults() {
      this.addLog('检查自动填充结果...', 'info')
      
      let filledCount = 0
      let expectedFields = []
      
      if (this.formType === '安全测评') {
        expectedFields = ['管理员页面IP', '用户页面IP', '升级页面IP', '对外服务端口']
      } else if (this.formType === '安全监测') {
        expectedFields = ['业务功能页面地址', 'init地址', 'kibana地址']
      } else if (this.formType === '应用加固') {
        expectedFields = ['平台访问地址', '升级平台地址']
      }
      
      expectedFields.forEach(field => {
        if (this.testFormData[field]) {
          filledCount++
          this.addLog(`✅ ${field}: ${this.testFormData[field]}`, 'success')
        } else {
          this.addLog(`❌ ${field}: 未填充`, 'error')
        }
      })
      
      if (filledCount === expectedFields.length) {
        this.addLog(`🎉 自动填充成功！已填充 ${filledCount}/${expectedFields.length} 个字段`, 'success')
      } else {
        this.addLog(`⚠️ 自动填充不完整：已填充 ${filledCount}/${expectedFields.length} 个字段`, 'warning')
      }
    },

    /**
     * 手动触发填充
     */
    manualTrigger() {
      this.addLog('手动触发自动填充...', 'info')
      
      // 通过刷新key强制重新渲染组件
      this.refreshKey += 1
      
      setTimeout(() => {
        this.checkAutoFillResults()
      }, 500)
    },

    /**
     * 表单数据变化监听
     */
    onFormDataChange(newData) {
      // 检查访问信息字段的变化
      const accessFields = {
        '安全测评': ['管理员页面IP', '用户页面IP', '升级页面IP', '对外服务端口'],
        '安全监测': ['业务功能页面地址', 'init地址', 'kibana地址'],
        '应用加固': ['平台访问地址', '升级平台地址']
      }
      
      const currentFields = accessFields[this.formType] || []
      currentFields.forEach(field => {
        if (newData[field] !== this.testFormData[field]) {
          this.addLog(`🔄 字段变化 ${field}: "${this.testFormData[field] || ''}" → "${newData[field] || ''}"`, 'info')
        }
      })
      
      this.testFormData = newData
    },

    /**
     * 清空重置
     */
    clearAll() {
      this.testFormData = getInitialFormData(this.formType)
      this.debugLogs = []
      this.refreshKey += 1
      this.addLog('数据已清空重置', 'info')
    },

    /**
     * 初始化表单数据
     */
    initFormData() {
      this.testFormData = getInitialFormData(this.formType)
      this.addLog(`初始化${this.formType}表单数据`, 'info')
    }
  },
  watch: {
    formType() {
      this.initFormData()
      this.debugLogs = []
      this.refreshKey += 1
    }
  },
  mounted() {
    this.initFormData()
    this.addLog('实时自动填充调试工具已启动', 'success')
    
    // 加载组件分组数据
    this.componentGroups = {
      testing: {
        'aimrsk-engine': [
          { name: 'front-ssp-admin', description: '前端管理界面', port: '8080' },
          { name: 'front-ssp-user', description: '前端用户界面', port: '8081' },
          { name: 'backend-ssp-user', description: '后端用户服务', port: '8082' },
          { name: 'luna', description: 'Luna升级组件', port: '9001' }
        ]
      },
      security: {
        'security-monitor': [
          { name: 'web-service-nginx', description: 'Web服务', port: '443' },
          { name: 'init', description: '初始化服务', port: '8181' },
          { name: 'kibana', description: 'Kibana服务', port: '5601' }
        ]
      },
      hardening: {
        'hardening-platform': [
          { name: 'secweb', description: '安全Web平台', port: '8000' },
          { name: 'luna', description: 'Luna升级组件', port: '9001' }
        ]
      }
    }
  }
}
</script>

<style scoped>
.live-auto-fill-debug {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.text-success { color: #198754 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #0dcaf0 !important; }
.text-muted { color: #6c757d !important; }
</style>
