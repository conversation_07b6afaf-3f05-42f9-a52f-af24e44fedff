import { ref, nextTick } from 'vue'

/**
 * 统一的表单方法 Composable
 * 提取所有表单组件的公共方法，消除代码重复
 * @param {string} formType - 表单类型
 * @returns {Object} 表单方法对象
 */
export function useFormMethods(formType) {
  const refreshKey = ref(0)
  const toastRef = ref(null)

  /**
   * 展开所有部分
   * 统一的展开逻辑，适用于所有表单类型
   */
  const expandAllSections = () => {
    console.log(`${formType}Form: 展开所有部分`)
    
    // 查找所有折叠的卡片头部（.collapsed类表示已折叠）
    const collapsedHeaders = document.querySelectorAll('.card-header.collapsed')
    console.log('找到折叠的卡片头部:', collapsedHeaders.length)
    
    // 点击所有折叠的卡片头部来展开
    collapsedHeaders.forEach((header, index) => {
      console.log(`点击展开第${index + 1}个折叠的卡片`)
      header.click()
    })

    // 备用方案：处理Bootstrap Collapse组件
    const collapsedElements = document.querySelectorAll('.collapse:not(.show)')
    if (window.bootstrap && window.bootstrap.Collapse && collapsedElements.length > 0) {
      console.log('处理Bootstrap Collapse组件:', collapsedElements.length)
      collapsedElements.forEach(element => {
        const collapseInstance = window.bootstrap.Collapse.getInstance(element) ||
                                new window.bootstrap.Collapse(element, { toggle: false })
        if (!element.classList.contains('show')) {
          collapseInstance.show()
        }
      })
    }
  }

  /**
   * 折叠所有部分
   * 统一的折叠逻辑，适用于所有表单类型
   */
  const collapseAllSections = () => {
    console.log(`${formType}Form: 折叠所有部分`)
    
    // 查找所有展开的卡片头部（没有.collapsed类表示已展开）
    const expandedHeaders = document.querySelectorAll('.card-header:not(.collapsed)')
    console.log('找到展开的卡片头部:', expandedHeaders.length)
    
    // 点击所有展开的卡片头部来折叠
    expandedHeaders.forEach((header, index) => {
      console.log(`点击折叠第${index + 1}个展开的卡片`)
      header.click()
    })

    // 备用方案：处理Bootstrap Collapse组件
    const expandedElements = document.querySelectorAll('.collapse.show')
    if (window.bootstrap && window.bootstrap.Collapse && expandedElements.length > 0) {
      console.log('处理Bootstrap Collapse组件:', expandedElements.length)
      expandedElements.forEach(element => {
        const collapseInstance = window.bootstrap.Collapse.getInstance(element) ||
                                new window.bootstrap.Collapse(element, { toggle: false })
        if (element.classList.contains('show')) {
          collapseInstance.hide()
        }
      })
    }
  }

  /**
   * 刷新所有部分组件
   * 通过改变key值强制重新渲染组件
   */
  const refreshSections = () => {
    refreshKey.value += 1
    console.log(`${formType}Form: 刷新所有部分，refreshKey: ${refreshKey.value}`)
  }

  /**
   * 显示Toast通知
   * 统一的通知显示方法
   * @param {string} message - 通知消息
   * @param {string} title - 通知标题
   * @param {string} type - 通知类型 (info|success|warning|error)
   */
  const showToast = (message, title = '提示', type = 'info') => {
    console.log(`Toast [${type}] ${title}: ${message}`)
    
    // 如果有Toast组件引用，使用组件显示
    if (toastRef.value && typeof toastRef.value.showToast === 'function') {
      toastRef.value.showToast(message, title, type)
    } else {
      // 降级处理：使用浏览器原生通知
      if (type === 'error') {
        alert(`错误: ${message}`)
      } else if (type === 'warning') {
        alert(`警告: ${message}`)
      } else if (type === 'success') {
        // 成功消息可以使用console.log，避免过多弹窗
        console.log(`成功: ${message}`)
      } else {
        console.log(`${title}: ${message}`)
      }
    }
  }

  /**
   * 处理sections更新
   * 悬浮球导航sections更新时的回调
   * @param {Array} sections - 更新后的sections列表
   */
  const handleSectionsUpdated = (sections) => {
    console.log(`${formType}Form: sections已更新:`, sections)
    // 这里可以添加额外的处理逻辑，比如更新本地状态等
  }

  /**
   * 处理架构变化（主要用于安全监测表单）
   * @param {Object} event - 架构变化事件对象
   */
  const onArchitectureChanged = (event) => {
    if (formType === '安全监测') {
      const { serverIndex, architecture } = event
      console.log(`服务器 #${serverIndex + 1} 架构变更为: ${architecture}`)
      
      // 这里可以添加特定的架构变化处理逻辑
      // 例如：根据架构类型更新kibana认证信息等
    }
  }

  /**
   * 手动刷新悬浮球sections
   * @param {Object} floatingBallRef - 悬浮球组件引用
   */
  const refreshSidebarSections = (floatingBallRef) => {
    if (floatingBallRef && typeof floatingBallRef.refreshSections === 'function') {
      floatingBallRef.refreshSections()
      console.log(`${formType}Form: 手动刷新悬浮球sections`)
    }
  }

  /**
   * 设置Toast组件引用
   * @param {Object} ref - Toast组件引用
   */
  const setToastRef = (ref) => {
    toastRef.value = ref
  }

  /**
   * 滚动到指定部分
   * @param {string} sectionId - 部分ID
   */
  const scrollToSection = (sectionId) => {
    nextTick(() => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        })
        console.log(`${formType}Form: 滚动到部分 ${sectionId}`)
      }
    })
  }

  /**
   * 检查所有必填字段是否已填写
   * @param {Object} formData - 表单数据
   * @param {Array} requiredFields - 必填字段列表
   * @returns {Object} 检查结果
   */
  const checkRequiredFields = (formData, requiredFields) => {
    const missingFields = requiredFields.filter(field => {
      const value = formData[field]
      return !value || (typeof value === 'string' && !value.trim())
    })

    return {
      isValid: missingFields.length === 0,
      missingFields,
      message: missingFields.length > 0 
        ? `请填写以下必填字段: ${missingFields.join(', ')}`
        : '所有必填字段已填写'
    }
  }

  /**
   * 表单数据变化处理
   * @param {Object} newData - 新的表单数据
   * @param {Object} oldData - 旧的表单数据
   */
  const onFormDataChange = (newData, oldData) => {
    console.log(`${formType}Form: 表单数据发生变化`)
    // 这里可以添加数据变化的处理逻辑
    // 例如：自动保存、数据验证等
  }

  return {
    refreshKey,
    expandAllSections,
    collapseAllSections,
    refreshSections,
    showToast,
    handleSectionsUpdated,
    onArchitectureChanged,
    refreshSidebarSections,
    setToastRef,
    scrollToSection,
    checkRequiredFields,
    onFormDataChange
  }
}
