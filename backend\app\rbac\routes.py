from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.rbac import bp
from app.models.auth_models import User, Role, Permission, UserGroup, user_roles, role_permissions, user_groups, group_roles
from app.auth.decorators import admin_required, permission_required, login_required
from app import db
from sqlalchemy import or_


@bp.route('/users', methods=['GET'])
@permission_required('user.view')
def get_users():
    """获取用户列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '').strip()
        
        query = User.query
        
        # 搜索过滤
        if search:
            query = query.filter(or_(
                User.username.contains(search),
                User.email.contains(search),
                User.real_name.contains(search),
                User.department.contains(search)
            ))
        
        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        users = pagination.items
        
        return jsonify({
            'status': 'success',
            'data': {
                'users': [user.to_dict() for user in users],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户列表错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '获取用户列表失败'
        }), 500


@bp.route('/users/<int:user_id>', methods=['GET'])
@permission_required('user.view')
def get_user(user_id):
    """获取用户详情"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404
        
        return jsonify({
            'status': 'success',
            'data': {
                'user': user.to_dict(include_permissions=True)
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户详情错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '获取用户详情失败'
        }), 500


@bp.route('/users', methods=['POST'])
@permission_required('user.create')
def create_user():
    """创建用户"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供用户信息'
            }), 400
        
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        
        if not username or not email or not password:
            return jsonify({
                'status': 'error',
                'message': '用户名、邮箱和密码不能为空'
            }), 400
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            return jsonify({
                'status': 'error',
                'message': '用户名已存在'
            }), 400
        
        # 检查邮箱是否已存在
        if User.query.filter_by(email=email).first():
            return jsonify({
                'status': 'error',
                'message': '邮箱已被注册'
            }), 400
        
        # 创建用户
        user = User(
            username=username,
            email=email,
            real_name=data.get('real_name', '').strip(),
            phone=data.get('phone', '').strip(),
            department=data.get('department', '').strip(),
            position=data.get('position', '').strip(),
            is_active=data.get('is_active', True)
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.flush()  # 获取用户ID
        
        # 分配角色
        role_ids = data.get('role_ids', [])
        if role_ids:
            roles = Role.query.filter(Role.id.in_(role_ids)).all()
            user.roles = roles
        
        # 分配用户组
        group_ids = data.get('group_ids', [])
        if group_ids:
            groups = UserGroup.query.filter(UserGroup.id.in_(group_ids)).all()
            user.groups = groups
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '用户创建成功',
            'data': {
                'user': user.to_dict()
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建用户错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '创建用户失败'
        }), 500


@bp.route('/users/<int:user_id>', methods=['PUT'])
@permission_required('user.edit')
def update_user(user_id):
    """更新用户"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404
        
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供更新信息'
            }), 400
        
        # 更新基本信息
        if 'real_name' in data:
            user.real_name = data['real_name'].strip()
        if 'phone' in data:
            user.phone = data['phone'].strip()
        if 'department' in data:
            user.department = data['department'].strip()
        if 'position' in data:
            user.position = data['position'].strip()
        if 'is_active' in data:
            user.is_active = data['is_active']
        
        # 更新邮箱（需要验证唯一性）
        if 'email' in data:
            new_email = data['email'].strip()
            if new_email != user.email:
                if User.query.filter_by(email=new_email).first():
                    return jsonify({
                        'status': 'error',
                        'message': '邮箱已被使用'
                    }), 400
                user.email = new_email
        
        # 更新角色
        if 'role_ids' in data:
            role_ids = data['role_ids']
            roles = Role.query.filter(Role.id.in_(role_ids)).all() if role_ids else []
            user.roles = roles
        
        # 更新用户组
        if 'group_ids' in data:
            group_ids = data['group_ids']
            groups = UserGroup.query.filter(UserGroup.id.in_(group_ids)).all() if group_ids else []
            user.groups = groups
        
        db.session.commit()

        # 清除用户缓存，确保返回最新数据
        from app.utils.cache_utils import UserCacheManager
        UserCacheManager.clear_user_cache(user.id)

        return jsonify({
            'status': 'success',
            'message': '用户更新成功',
            'data': {
                'user': user.to_dict()
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '更新用户失败'
        }), 500


@bp.route('/users/<int:user_id>', methods=['DELETE'])
@permission_required('user.delete')
def delete_user(user_id):
    """删除用户"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404
        
        # 不能删除管理员
        if user.is_admin:
            return jsonify({
                'status': 'error',
                'message': '不能删除管理员用户'
            }), 400
        
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '用户删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除用户错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '删除用户失败'
        }), 500


@bp.route('/roles', methods=['GET'])
@permission_required('role.view')
def get_roles():
    """获取角色列表"""
    try:
        roles = Role.query.order_by(Role.created_at.desc()).all()
        
        return jsonify({
            'status': 'success',
            'data': {
                'roles': [role.to_dict() for role in roles]
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取角色列表错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '获取角色列表失败'
        }), 500


@bp.route('/roles', methods=['POST'])
@permission_required('role.create')
def create_role():
    """创建角色"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供角色信息'
            }), 400
        
        code = data.get('code', '').strip()
        name = data.get('name', '').strip()
        
        if not code or not name:
            return jsonify({
                'status': 'error',
                'message': '角色代码和名称不能为空'
            }), 400
        
        # 检查角色代码是否已存在
        if Role.query.filter_by(code=code).first():
            return jsonify({
                'status': 'error',
                'message': '角色代码已存在'
            }), 400
        
        # 创建角色
        role = Role(
            code=code,
            name=name,
            description=data.get('description', '').strip(),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(role)
        db.session.flush()  # 获取角色ID
        
        # 分配权限
        permission_ids = data.get('permission_ids', [])
        if permission_ids:
            permissions = Permission.query.filter(Permission.id.in_(permission_ids)).all()
            role.permissions = permissions
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '角色创建成功',
            'data': {
                'role': role.to_dict()
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建角色错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '创建角色失败'
        }), 500


@bp.route('/roles/<int:role_id>', methods=['PUT'])
@permission_required('role.edit')
def update_role(role_id):
    """更新角色"""
    try:
        role = Role.query.get(role_id)
        if not role:
            return jsonify({
                'status': 'error',
                'message': '角色不存在'
            }), 404

        # 系统角色不能修改
        if role.is_system:
            return jsonify({
                'status': 'error',
                'message': '系统角色不能修改'
            }), 400

        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供更新信息'
            }), 400

        # 更新基本信息
        if 'name' in data:
            role.name = data['name'].strip()
        if 'description' in data:
            role.description = data['description'].strip()
        if 'is_active' in data:
            role.is_active = data['is_active']

        # 更新权限
        if 'permission_ids' in data:
            permission_ids = data['permission_ids']
            permissions = Permission.query.filter(Permission.id.in_(permission_ids)).all() if permission_ids else []
            role.permissions = permissions

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '角色更新成功',
            'data': {
                'role': role.to_dict()
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新角色错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '更新角色失败'
        }), 500


@bp.route('/roles/<int:role_id>', methods=['DELETE'])
@permission_required('role.delete')
def delete_role(role_id):
    """删除角色"""
    try:
        role = Role.query.get(role_id)
        if not role:
            return jsonify({
                'status': 'error',
                'message': '角色不存在'
            }), 404

        # 系统角色不能删除
        if role.is_system:
            return jsonify({
                'status': 'error',
                'message': '系统角色不能删除'
            }), 400

        # 检查是否有用户使用此角色
        if role.users:
            return jsonify({
                'status': 'error',
                'message': '该角色正在被用户使用，无法删除'
            }), 400

        db.session.delete(role)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '角色删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除角色错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '删除角色失败'
        }), 500


@bp.route('/permissions', methods=['GET'])
@permission_required('permission.view')
def get_permissions():
    """获取权限列表"""
    try:
        permissions = Permission.query.order_by(Permission.module, Permission.code).all()

        # 按模块分组
        grouped_permissions = {}
        for permission in permissions:
            module = permission.module
            if module not in grouped_permissions:
                grouped_permissions[module] = []
            grouped_permissions[module].append(permission.to_dict())

        return jsonify({
            'status': 'success',
            'data': {
                'permissions': [permission.to_dict() for permission in permissions],
                'grouped_permissions': grouped_permissions
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取权限列表错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '获取权限列表失败'
        }), 500


@bp.route('/permissions', methods=['POST'])
@permission_required('permission.create')
def create_permission():
    """创建权限"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供权限信息'
            }), 400

        code = data.get('code', '').strip()
        name = data.get('name', '').strip()
        module = data.get('module', '').strip()

        if not code or not name or not module:
            return jsonify({
                'status': 'error',
                'message': '权限代码、名称和模块不能为空'
            }), 400

        # 检查权限代码是否已存在
        if Permission.query.filter_by(code=code).first():
            return jsonify({
                'status': 'error',
                'message': '权限代码已存在'
            }), 400

        # 创建权限
        permission = Permission(
            code=code,
            name=name,
            description=data.get('description', '').strip(),
            module=module,
            is_active=data.get('is_active', True)
        )

        db.session.add(permission)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '权限创建成功',
            'data': {
                'permission': permission.to_dict()
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建权限错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '创建权限失败'
        }), 500


@bp.route('/groups', methods=['GET'])
@permission_required('group.view')
def get_groups():
    """获取用户组列表"""
    try:
        # 检查是否需要包含用户信息
        include_users = request.args.get('include_users', 'false').lower() == 'true'

        groups = UserGroup.query.order_by(UserGroup.created_at.desc()).all()

        return jsonify({
            'status': 'success',
            'data': {
                'groups': [group.to_dict(include_users=include_users) for group in groups]
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取用户组列表错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '获取用户组列表失败'
        }), 500


@bp.route('/groups', methods=['POST'])
@permission_required('group.create')
def create_group():
    """创建用户组"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供用户组信息'
            }), 400

        code = data.get('code', '').strip()
        name = data.get('name', '').strip()

        if not code or not name:
            return jsonify({
                'status': 'error',
                'message': '用户组代码和名称不能为空'
            }), 400

        # 检查用户组代码是否已存在
        if UserGroup.query.filter_by(code=code).first():
            return jsonify({
                'status': 'error',
                'message': '用户组代码已存在'
            }), 400

        # 创建用户组
        group = UserGroup(
            code=code,
            name=name,
            description=data.get('description', '').strip(),
            is_active=data.get('is_active', True)
        )

        db.session.add(group)
        db.session.flush()  # 获取用户组ID

        # 分配角色
        role_ids = data.get('role_ids', [])
        if role_ids:
            roles = Role.query.filter(Role.id.in_(role_ids)).all()
            group.roles = roles

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '用户组创建成功',
            'data': {
                'group': group.to_dict()
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建用户组错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '创建用户组失败'
        }), 500


@bp.route('/groups/<int:group_id>', methods=['PUT'])
@permission_required('group.edit')
def update_group(group_id):
    """更新用户组"""
    try:
        group = UserGroup.query.get(group_id)
        if not group:
            return jsonify({
                'status': 'error',
                'message': '用户组不存在'
            }), 404

        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供更新信息'
            }), 400

        # 更新基本信息
        if 'name' in data:
            group.name = data['name'].strip()
        if 'description' in data:
            group.description = data['description'].strip()
        if 'is_active' in data:
            group.is_active = data['is_active']

        # 更新角色
        if 'role_ids' in data:
            role_ids = data['role_ids']
            roles = Role.query.filter(Role.id.in_(role_ids)).all() if role_ids else []
            group.roles = roles

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '用户组更新成功',
            'data': {
                'group': group.to_dict()
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户组错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '更新用户组失败'
        }), 500


@bp.route('/groups/<int:group_id>', methods=['DELETE'])
@permission_required('group.delete')
def delete_group(group_id):
    """删除用户组"""
    try:
        group = UserGroup.query.get(group_id)
        if not group:
            return jsonify({
                'status': 'error',
                'message': '用户组不存在'
            }), 404

        # 检查是否有用户使用此用户组
        if group.users:
            return jsonify({
                'status': 'error',
                'message': '该用户组正在被用户使用，无法删除'
            }), 400

        db.session.delete(group)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '用户组删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除用户组错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '删除用户组失败'
        }), 500


@bp.route('/groups/<int:group_id>/members', methods=['PUT'])
@permission_required('group.edit')
def update_group_members(group_id):
    """更新用户组成员"""
    try:
        group = UserGroup.query.get(group_id)
        if not group:
            return jsonify({
                'status': 'error',
                'message': '用户组不存在'
            }), 404

        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供成员信息'
            }), 400

        # 获取用户ID列表
        user_ids = data.get('user_ids', [])

        # 获取当前用户组的所有成员（用于清除缓存）
        old_users = list(group.users)  # 复制当前成员列表

        # 验证用户是否存在
        users = User.query.filter(User.id.in_(user_ids)).all() if user_ids else []

        # 更新用户组成员
        group.users = users

        db.session.commit()

        # 清除所有相关用户的缓存（包括新增的和移除的）
        from app.utils.cache_utils import UserCacheManager

        # 清除旧成员的缓存
        for user in old_users:
            UserCacheManager.clear_user_cache(user.id)

        # 清除新成员的缓存
        for user in users:
            UserCacheManager.clear_user_cache(user.id)

        return jsonify({
            'status': 'success',
            'message': '用户组成员更新成功',
            'data': {
                'group': group.to_dict(include_users=True)
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户组成员错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '更新用户组成员失败'
        }), 500


@bp.route('/debug/user-permissions', methods=['GET'])
@jwt_required()
def debug_user_permissions():
    """调试用户权限（临时API）"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(int(user_id))

        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404

        # 获取用户所有权限
        all_permissions = user.get_all_permissions()

        # 检查特定权限
        has_group_edit = user.has_permission('group.edit')

        return jsonify({
            'status': 'success',
            'data': {
                'user_id': user.id,
                'username': user.username,
                'is_admin': user.is_admin,
                'has_group_edit': has_group_edit,
                'all_permissions': list(all_permissions),
                'roles': [role.to_dict() for role in user.roles],
                'groups': [group.to_dict() for group in user.groups]
            }
        })

    except Exception as e:
        current_app.logger.error(f"调试用户权限错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'调试失败: {str(e)}'
        }), 500


@bp.route('/permissions/<int:permission_id>', methods=['PUT'])
@permission_required('permission.edit')
def update_permission(permission_id):
    """更新权限"""
    try:
        permission = Permission.query.get(permission_id)
        if not permission:
            return jsonify({
                'status': 'error',
                'message': '权限不存在'
            }), 404

        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供更新信息'
            }), 400

        # 更新基本信息
        if 'name' in data:
            permission.name = data['name'].strip()
        if 'description' in data:
            permission.description = data['description'].strip()
        if 'is_active' in data:
            permission.is_active = data['is_active']

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '权限更新成功',
            'data': {
                'permission': permission.to_dict()
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新权限错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '更新权限失败'
        }), 500
