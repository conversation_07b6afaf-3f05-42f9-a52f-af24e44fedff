# 🔐 用户权限管理指南

## 📋 概述

本指南介绍了梆梆安全-运维信息登记平台的用户权限管理系统，包括用户管理、角色管理、权限配置等功能。

## 🏗️ RBAC权限模型

### 权限架构
```
用户 (User) ←→ 角色 (Role) ←→ 权限 (Permission)
     ↓              ↓              ↓
   用户组          角色组         权限组
  (Group)      (Role Group)   (Permission Group)
```

### 核心概念
- **用户 (User)**: 系统使用者，可以分配多个角色
- **角色 (Role)**: 权限的集合，如管理员、操作员等
- **权限 (Permission)**: 具体的操作权限，如查看、编辑、删除等
- **用户组 (Group)**: 用户的组织结构，便于批量管理

## 👥 用户管理

### 用户信息
- **基本信息**: 用户名、邮箱、姓名、电话
- **状态管理**: 启用/禁用、锁定/解锁
- **登录信息**: 最后登录时间、登录次数
- **安全设置**: 密码策略、双因子认证

### 用户操作

#### 创建用户
1. 访问"用户管理"页面
2. 点击"新增用户"按钮
3. 填写用户基本信息：
   - 用户名（必填，唯一）
   - 邮箱（必填，唯一）
   - 姓名（必填）
   - 初始密码（必填）
4. 分配角色和用户组
5. 点击"保存"完成创建

#### 编辑用户
1. 在用户列表中找到目标用户
2. 点击"编辑"按钮
3. 修改用户信息
4. 调整角色分配
5. 保存修改

#### 用户状态管理
```javascript
// 启用/禁用用户
PUT /users/{userId}/status
{
  "status": "active" | "inactive"
}

// 锁定/解锁用户
PUT /users/{userId}/lock
{
  "locked": true | false
}

// 重置密码
POST /users/{userId}/reset-password
{
  "newPassword": "new_password"
}
```

## 🎭 角色管理

### 预定义角色
- **超级管理员**: 拥有所有权限
- **系统管理员**: 系统配置和用户管理权限
- **业务管理员**: 业务数据管理权限
- **普通用户**: 基本的查看和操作权限

### 角色权限

#### 超级管理员权限
- ✅ 用户管理：增删改查
- ✅ 角色管理：增删改查
- ✅ 权限管理：增删改查
- ✅ 系统配置：所有配置项
- ✅ 数据管理：所有数据操作
- ✅ 日志查看：所有系统日志

#### 系统管理员权限
- ✅ 用户管理：增删改查（除超级管理员）
- ✅ 角色管理：查看和编辑（除超级管理员角色）
- ✅ 组件管理：增删改查
- ✅ 模板管理：增删改查
- ✅ 系统监控：查看系统状态
- ❌ 系统配置：核心配置项

#### 业务管理员权限
- ✅ 表单管理：增删改查
- ✅ Excel生成：生成和下载
- ✅ 历史记录：查看和管理
- ✅ 组件配置：查看和使用
- ❌ 用户管理：无权限
- ❌ 系统配置：无权限

#### 普通用户权限
- ✅ 表单填写：创建和编辑自己的表单
- ✅ Excel下载：下载自己生成的文件
- ✅ 历史查看：查看自己的操作记录
- ❌ 用户管理：无权限
- ❌ 系统管理：无权限

### 自定义角色

#### 创建角色
1. 访问"角色管理"页面
2. 点击"新增角色"按钮
3. 填写角色信息：
   - 角色名称（必填，唯一）
   - 角色描述（可选）
   - 角色类型（系统角色/业务角色）
4. 选择权限：
   - 从权限列表中勾选需要的权限
   - 可以按模块分组选择
5. 保存角色

#### 权限分配
```javascript
// 角色权限配置示例
{
  "roleName": "表单管理员",
  "description": "负责表单管理的角色",
  "permissions": [
    "form.create",      // 创建表单
    "form.read",        // 查看表单
    "form.update",      // 更新表单
    "form.delete",      // 删除表单
    "excel.generate",   // 生成Excel
    "excel.download",   // 下载Excel
    "component.read"    // 查看组件
  ]
}
```

## 🏢 用户组管理

### 组织架构
```
公司
├── 技术部
│   ├── 开发组
│   ├── 测试组
│   └── 运维组
├── 产品部
│   ├── 产品组
│   └── 设计组
└── 管理部
    ├── 人事组
    └── 财务组
```

### 用户组功能
- **层级管理**: 支持多级组织结构
- **批量操作**: 批量分配角色和权限
- **继承权限**: 子组可以继承父组权限
- **数据隔离**: 基于用户组的数据访问控制

### 用户组操作

#### 创建用户组
1. 访问"用户组管理"页面
2. 点击"新增用户组"按钮
3. 填写组信息：
   - 组名称（必填）
   - 父级组（可选）
   - 组描述（可选）
4. 设置组权限
5. 添加组成员
6. 保存用户组

#### 用户组权限继承
```javascript
// 权限继承示例
{
  "parentGroup": "技术部",
  "childGroup": "开发组",
  "inheritedPermissions": [
    "system.read",      // 从技术部继承
    "log.read"          // 从技术部继承
  ],
  "ownPermissions": [
    "code.write",       // 开发组特有
    "deploy.execute"    // 开发组特有
  ]
}
```

## 🔒 权限控制

### 页面级权限
```javascript
// 路由守卫
router.beforeEach((to, from, next) => {
  const requiredPermissions = to.meta.permissions;
  const userPermissions = store.getters.userPermissions;
  
  if (hasPermissions(userPermissions, requiredPermissions)) {
    next();
  } else {
    next('/403'); // 无权限页面
  }
});

// 路由配置
{
  path: '/users',
  component: UserManagement,
  meta: {
    permissions: ['user.read']
  }
}
```

### 功能级权限
```vue
<template>
  <div>
    <!-- 基于权限显示按钮 -->
    <button v-if="hasPermission('user.create')" @click="createUser">
      新增用户
    </button>
    
    <!-- 基于角色显示内容 -->
    <div v-if="hasRole('admin')">
      管理员专用功能
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    hasPermission(permission) {
      return this.$store.getters.hasPermission(permission);
    },
    hasRole(role) {
      return this.$store.getters.hasRole(role);
    }
  }
};
</script>
```

### 数据级权限
```javascript
// 基于用户组的数据过滤
app.get('/api/forms', authenticate, (req, res) => {
  const userGroup = req.user.group;
  const query = {
    // 只能查看本组或子组的数据
    group: { $in: getUserGroupHierarchy(userGroup) }
  };
  
  Form.find(query).then(forms => {
    res.json(forms);
  });
});
```

## 🔐 安全策略

### 密码策略
- **最小长度**: 8位字符
- **复杂度要求**: 包含大小写字母、数字、特殊字符
- **历史密码**: 不能重复使用最近5次密码
- **有效期**: 90天强制更换
- **锁定策略**: 连续5次错误锁定账户

### 会话管理
- **会话超时**: 30分钟无操作自动登出
- **并发控制**: 同一用户最多3个并发会话
- **强制登出**: 管理员可强制用户登出
- **设备绑定**: 可选择绑定设备登录

### 审计日志
```javascript
// 用户操作日志
{
  "userId": "user123",
  "action": "user.create",
  "resource": "user456",
  "timestamp": "2024-01-01T10:00:00Z",
  "ip": "*************",
  "userAgent": "Mozilla/5.0...",
  "result": "success"
}

// 权限变更日志
{
  "operatorId": "admin123",
  "targetUserId": "user456",
  "action": "permission.grant",
  "permission": "form.delete",
  "timestamp": "2024-01-01T10:00:00Z",
  "reason": "业务需要"
}
```

## 🛠️ 权限配置

### 权限列表
```javascript
// 系统权限定义
const PERMISSIONS = {
  // 用户管理
  'user.create': '创建用户',
  'user.read': '查看用户',
  'user.update': '更新用户',
  'user.delete': '删除用户',
  
  // 角色管理
  'role.create': '创建角色',
  'role.read': '查看角色',
  'role.update': '更新角色',
  'role.delete': '删除角色',
  
  // 表单管理
  'form.create': '创建表单',
  'form.read': '查看表单',
  'form.update': '更新表单',
  'form.delete': '删除表单',
  
  // Excel管理
  'excel.generate': '生成Excel',
  'excel.download': '下载Excel',
  'excel.template': '管理模板',
  
  // 组件管理
  'component.create': '创建组件',
  'component.read': '查看组件',
  'component.update': '更新组件',
  'component.delete': '删除组件',
  
  // 系统管理
  'system.config': '系统配置',
  'system.monitor': '系统监控',
  'system.log': '查看日志'
};
```

### 权限检查工具
```javascript
// 权限检查函数
function hasPermission(userPermissions, requiredPermission) {
  return userPermissions.includes(requiredPermission);
}

function hasAnyPermission(userPermissions, requiredPermissions) {
  return requiredPermissions.some(permission => 
    userPermissions.includes(permission)
  );
}

function hasAllPermissions(userPermissions, requiredPermissions) {
  return requiredPermissions.every(permission => 
    userPermissions.includes(permission)
  );
}
```

## 📊 权限报告

### 用户权限报告
- 用户权限分布统计
- 角色使用情况分析
- 权限变更历史记录
- 异常权限检测报告

### 安全审计报告
- 登录失败统计
- 权限提升记录
- 敏感操作日志
- 安全事件分析

## 🚨 常见问题

### 权限不生效
1. 检查用户角色分配
2. 确认角色权限配置
3. 验证权限缓存更新
4. 检查前端权限判断逻辑

### 无法登录
1. 检查用户状态（是否被禁用/锁定）
2. 验证密码是否正确
3. 检查账户是否过期
4. 确认网络连接正常

### 权限过多/过少
1. 检查角色权限配置
2. 确认用户组权限继承
3. 验证权限分配逻辑
4. 联系管理员调整权限

## 📚 相关文档

- [系统管理功能](system-administration.md)
- [API接口文档](../backend/api-documentation.md)
- [安全配置指南](../operations/security.md)
- [故障排除指南](../backend/troubleshooting.md)
