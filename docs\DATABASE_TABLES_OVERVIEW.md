# 🗄️ 数据库表结构详解

## 📋 概述

梆梆安全-运维信息登记平台使用MySQL数据库存储所有数据。历史数据页面显示的数据主要来自 **`form_submission`** 表，这是系统的核心数据表。

## 🎯 核心数据表

### 1. 📊 `form_submission` - 表单提交记录表

这是**历史数据页面的主要数据源**，存储所有表单提交的完整信息。

#### 表结构
```sql
CREATE TABLE `form_submission` (
  `id` int NOT NULL AUTO_INCREMENT,                    -- 主键ID
  `company_name` varchar(200) NOT NULL,                -- 公司名称
  `form_type` varchar(50) NOT NULL,                    -- 表单类型
  `record_date` date NOT NULL,                         -- 记录日期
  `form_data` text NOT NULL,                           -- 完整表单数据(JSON)
  `excel_filename` varchar(255) DEFAULT NULL,          -- 生成的Excel文件名
  `excel_filepath` varchar(500) DEFAULT NULL,          -- Excel文件路径
  `server_count` int DEFAULT NULL,                     -- 服务器数量
  `component_count` int DEFAULT NULL,                  -- 组件数量
  `created_at` datetime DEFAULT NULL,                  -- 提交时间 ⭐
  `updated_at` datetime DEFAULT NULL,                  -- 更新时间 ⭐
  `created_by` varchar(100) DEFAULT NULL,              -- 创建者
  `ip_address` varchar(45) DEFAULT NULL,               -- 提交者IP
  `user_agent` varchar(500) DEFAULT NULL,              -- 用户代理
  `status` varchar(20) DEFAULT NULL,                   -- 状态
  `error_message` text,                                -- 错误信息
  PRIMARY KEY (`id`),
  KEY `ix_form_submission_company_name` (`company_name`),
  KEY `ix_form_submission_created_at` (`created_at`),
  KEY `ix_form_submission_form_type` (`form_type`),
  KEY `ix_form_submission_record_date` (`record_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
```

#### 字段说明

| 字段名 | 类型 | 说明 | 历史页面显示 |
|--------|------|------|-------------|
| `id` | int | 主键，自增ID | ❌ |
| `company_name` | varchar(200) | 公司名称，支持别名格式 | ✅ 公司名称列 |
| `form_type` | varchar(50) | 表单类型：安全监测/安全测评/应用加固 | ✅ 表单类型列 |
| `record_date` | date | 表单记录的日期 | ✅ 记录日期列 |
| `form_data` | text | 完整的表单数据，JSON格式存储 | ❌ (详情中显示) |
| `excel_filename` | varchar(255) | 生成的Excel文件名 | ❌ |
| `excel_filepath` | varchar(500) | Excel文件的完整路径 | ❌ |
| `server_count` | int | 服务器数量统计 | ✅ 服务器数列 |
| `component_count` | int | 组件数量统计 | ✅ 组件数列 |
| **`created_at`** | datetime | **提交时间** | ✅ **提交时间列** |
| **`updated_at`** | datetime | **更新时间** | ✅ **更新时间列** |
| `created_by` | varchar(100) | 创建者/编辑人 | ✅ 编辑人列 |
| `ip_address` | varchar(45) | 提交者IP地址 | ❌ |
| `user_agent` | varchar(500) | 浏览器用户代理 | ❌ |
| `status` | varchar(20) | 处理状态：success/failed/processing | ✅ 状态列 |
| `error_message` | text | 错误信息（如果有） | ❌ |

### 2. 📝 `form_submission_edit` - 表单编辑历史表

记录每次表单编辑的详细历史，与 `form_submission` 表关联。

#### 表结构
```sql
CREATE TABLE `form_submission_edit` (
  `id` int NOT NULL AUTO_INCREMENT,                    -- 主键ID
  `submission_id` int NOT NULL,                        -- 关联的表单提交记录ID
  `edit_type` varchar(50) NOT NULL,                    -- 编辑类型
  `edit_description` varchar(500) DEFAULT NULL,        -- 编辑描述
  `old_data` text,                                     -- 编辑前数据
  `new_data` text,                                     -- 编辑后数据
  `changed_fields` text,                               -- 变更字段列表
  `edited_by` varchar(100) DEFAULT NULL,               -- 编辑者
  `edit_reason` varchar(500) DEFAULT NULL,             -- 编辑原因
  `created_at` datetime DEFAULT NULL,                  -- 编辑时间
  `ip_address` varchar(45) DEFAULT NULL,               -- 编辑者IP
  `user_agent` varchar(500) DEFAULT NULL,              -- 用户代理
  PRIMARY KEY (`id`),
  KEY `ix_form_submission_edit_submission_id` (`submission_id`),
  KEY `ix_form_submission_edit_created_at` (`created_at`),
  CONSTRAINT `form_submission_edit_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `form_submission` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
```

## 🏢 数据库环境

### 数据库配置

| 环境 | 数据库名 | 主机 | 用户名 | 说明 |
|------|----------|------|--------|------|
| **生产环境** | `export_excel` | ************ | junguangchen | 正式环境数据 |
| **开发环境** | `export_excel_dev` | ************ | junguangchen | 开发测试数据 |

### 连接信息
```bash
# 连接生产数据库
mysql -h ************ -u junguangchen -p export_excel

# 连接开发数据库  
mysql -h ************ -u junguangchen -p export_excel_dev
```

## 📊 数据查询示例

### 查看历史数据页面的数据
```sql
-- 查看最近的表单提交记录
SELECT 
    id,
    company_name,
    form_type,
    record_date,
    server_count,
    component_count,
    created_at,
    updated_at,
    created_by,
    status
FROM form_submission 
ORDER BY created_at DESC 
LIMIT 10;
```

### 查看编辑历史
```sql
-- 查看某个表单的编辑历史
SELECT 
    e.id,
    e.edit_type,
    e.edit_description,
    e.edited_by,
    e.created_at as edit_time,
    s.company_name
FROM form_submission_edit e
JOIN form_submission s ON e.submission_id = s.id
WHERE s.id = 1
ORDER BY e.created_at DESC;
```

### 统计数据
```sql
-- 统计各表单类型的数量
SELECT 
    form_type,
    COUNT(*) as count,
    MAX(created_at) as latest_submission
FROM form_submission 
GROUP BY form_type;

-- 统计编辑频率
SELECT 
    s.company_name,
    s.form_type,
    COUNT(e.id) as edit_count
FROM form_submission s
LEFT JOIN form_submission_edit e ON s.id = e.submission_id
GROUP BY s.id, s.company_name, s.form_type
HAVING edit_count > 0
ORDER BY edit_count DESC;
```

## 🔍 相关辅助表

### 3. 👥 `user` - 用户表
存储系统用户信息，`created_by` 字段关联此表。

### 4. 📋 `form_type` - 表单类型表
定义支持的表单类型：
- 安全测评
- 安全监测  
- 应用加固

### 5. 🧩 `component_config` - 组件配置表
存储可选择的组件信息，影响 `component_count` 统计。

### 6. 📁 `excel_file` - Excel文件表
存储生成的Excel文件信息，与 `excel_filename` 关联。

## 🔧 数据维护

### 备份命令
```bash
# 备份生产数据库
mysqldump -h ************ -u junguangchen -p export_excel > backup_prod_$(date +%Y%m%d).sql

# 备份开发数据库
mysqldump -h ************ -u junguangchen -p export_excel_dev > backup_dev_$(date +%Y%m%d).sql
```

### 数据清理
```sql
-- 清理超过6个月的编辑历史（谨慎操作）
DELETE FROM form_submission_edit 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);

-- 查看数据库大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'export_excel'
ORDER BY (data_length + index_length) DESC;
```

## 📝 总结

历史数据页面的数据主要来源：

1. **主表**: `form_submission` - 存储所有表单提交记录
2. **辅助表**: `form_submission_edit` - 存储编辑历史
3. **关联表**: `user`, `form_type`, `component_config` 等

**重点字段**:
- `created_at` → 提交时间列 🟢
- `updated_at` → 更新时间列 🟠  
- `company_name` → 公司名称列
- `form_type` → 表单类型列
- `server_count` → 服务器数列
- `component_count` → 组件数列
- `status` → 状态列

这些数据通过后端API (`/excel/form_submissions`) 提供给前端，在历史数据页面中展示。
