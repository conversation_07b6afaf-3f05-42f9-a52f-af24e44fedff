<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-bar-chart me-2"></i>
            表单提交统计信息
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>

        <div class="modal-body">
          <div v-if="loading" class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载统计信息...</p>
          </div>

          <div v-else-if="statistics">
            <!-- 总体统计 -->
            <div class="row mb-4">
              <div class="col-12">
                <div class="card bg-primary text-white">
                  <div class="card-body text-center">
                    <h2 class="display-4 mb-0">{{ statistics.total_submissions }}</h2>
                    <p class="mb-0">总提交数</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 按表单类型统计 -->
            <div class="row mb-4">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h6 class="card-title mb-0">
                      <i class="bi bi-pie-chart me-2"></i>
                      按表单类型统计
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div
                        v-for="stat in statistics.form_type_stats"
                        :key="stat.form_type"
                        class="col-md-4 mb-3"
                      >
                        <div class="card" :class="getFormTypeCardClass(stat.form_type)">
                          <div class="card-body text-center">
                            <h4 class="mb-1">{{ stat.count }}</h4>
                            <p class="mb-0">{{ stat.form_type }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 按公司统计（Top 10） -->
            <div class="row mb-4">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h6 class="card-title mb-0">
                      <i class="bi bi-building me-2"></i>
                      公司提交排行榜 (Top 10)
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-sm">
                        <thead>
                          <tr>
                            <th width="50">排名</th>
                            <th>公司名称</th>
                            <th width="100">提交次数</th>
                            <th width="150">占比</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(stat, index) in statistics.company_stats" :key="stat.company_name">
                            <td>
                              <span class="badge" :class="getRankBadgeClass(index + 1)">
                                {{ index + 1 }}
                              </span>
                            </td>
                            <td>{{ stat.company_name }}</td>
                            <td>
                              <span class="badge bg-primary">{{ stat.count }}</span>
                            </td>
                            <td>
                              <div class="progress" style="height: 20px;">
                                <div
                                  class="progress-bar"
                                  :style="{ width: getPercentage(stat.count) + '%' }"
                                >
                                  {{ getPercentage(stat.count) }}%
                                </div>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 最近30天提交趋势 -->
            <div class="row">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h6 class="card-title mb-0">
                      <i class="bi bi-graph-up me-2"></i>
                      最近30天提交趋势
                    </h6>
                  </div>
                  <div class="card-body">
                    <div v-if="statistics.recent_trend.length > 0">
                      <!-- 简单的趋势图表 -->
                      <div class="trend-chart">
                        <div
                          v-for="trend in statistics.recent_trend"
                          :key="trend.date"
                          class="trend-bar"
                          :style="{ height: getTrendBarHeight(trend.count) + '%' }"
                          :title="`${trend.date}: ${trend.count}次提交`"
                        >
                          <small class="trend-label">{{ formatTrendDate(trend.date) }}</small>
                          <div class="trend-value">{{ trend.count }}</div>
                        </div>
                      </div>

                      <!-- 趋势统计 -->
                      <div class="row mt-3">
                        <div class="col-md-4 text-center">
                          <div class="border rounded p-2">
                            <strong>{{ getTotalRecentSubmissions() }}</strong>
                            <br>
                            <small class="text-muted">30天总计</small>
                          </div>
                        </div>
                        <div class="col-md-4 text-center">
                          <div class="border rounded p-2">
                            <strong>{{ getAverageDaily() }}</strong>
                            <br>
                            <small class="text-muted">日均提交</small>
                          </div>
                        </div>
                        <div class="col-md-4 text-center">
                          <div class="border rounded p-2">
                            <strong>{{ getMaxDaily() }}</strong>
                            <br>
                            <small class="text-muted">单日最高</small>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else class="text-center text-muted">
                      <i class="bi bi-graph-down display-4"></i>
                      <p class="mt-2">最近30天暂无提交数据</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
          <button type="button" class="btn btn-primary" @click="refreshStatistics">
            <i class="bi bi-arrow-clockwise me-1"></i>
            刷新数据
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

// 统一的API基础URL配置
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

/**
 * 统计信息模态框组件
 * 显示表单提交的统计信息和趋势分析
 */
export default {
  name: 'StatisticsModal',
  emits: ['close'],
  data() {
    return {
      loading: false,
      statistics: null
    }
  },

  mounted() {
    this.loadStatistics()
  },

  methods: {
    /**
     * 加载统计信息
     */
    async loadStatistics() {
      this.loading = true
      try {
        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/statistics`)
        const result = await response.json()

        if (result.status === 'success') {
          this.statistics = result.data
        } else {
          console.error('加载统计信息失败:', result.message)
        }
      } catch (error) {
        console.error('加载统计信息失败:', error)
      } finally {
        this.loading = false
      }
    },

    /**
     * 刷新统计信息
     */
    refreshStatistics() {
      this.loadStatistics()
    },

    /**
     * 获取表单类型卡片样式
     */
    getFormTypeCardClass(formType) {
      const classes = {
        '安全监测': 'border-primary',
        '安全测评': 'border-success',
        '应用加固': 'border-warning'
      }
      return classes[formType] || 'border-secondary'
    },

    /**
     * 获取排名徽章样式
     */
    getRankBadgeClass(rank) {
      if (rank === 1) return 'bg-warning text-dark'
      if (rank === 2) return 'bg-secondary'
      if (rank === 3) return 'bg-info'
      return 'bg-light text-dark'
    },

    /**
     * 计算百分比
     */
    getPercentage(count) {
      if (!this.statistics || !this.statistics.total_submissions) return 0
      return Math.round((count / this.statistics.total_submissions) * 100)
    },

    /**
     * 获取趋势柱状图高度
     */
    getTrendBarHeight(count) {
      if (!this.statistics || !this.statistics.recent_trend || !this.statistics.recent_trend.length) return 0
      const maxCount = Math.max(...this.statistics.recent_trend.map(t => t.count))
      return maxCount > 0 ? (count / maxCount) * 100 : 0
    },

    /**
     * 格式化趋势日期
     */
    formatTrendDate(dateString) {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}/${date.getDate()}`
    },

    /**
     * 获取最近30天总提交数
     */
    getTotalRecentSubmissions() {
      if (!this.statistics || !this.statistics.recent_trend || !this.statistics.recent_trend.length) return 0
      return this.statistics.recent_trend.reduce((sum, trend) => sum + trend.count, 0)
    },

    /**
     * 获取日均提交数
     */
    getAverageDaily() {
      const total = this.getTotalRecentSubmissions()
      const days = (this.statistics && this.statistics.recent_trend && this.statistics.recent_trend.length) || 1
      return Math.round(total / days * 10) / 10
    },

    /**
     * 获取单日最高提交数
     */
    getMaxDaily() {
      if (!this.statistics || !this.statistics.recent_trend || !this.statistics.recent_trend.length) return 0
      return Math.max(...this.statistics.recent_trend.map(t => t.count))
    }
  }
}
</script>

<style scoped>
.modal {
  z-index: 1060;
}

.card {
  border: 1px solid #dee2e6;
}

.trend-chart {
  display: flex;
  align-items: flex-end;
  height: 200px;
  gap: 2px;
  padding: 10px 0;
  border-bottom: 1px solid #dee2e6;
}

.trend-bar {
  flex: 1;
  background: linear-gradient(to top, #007bff, #66b3ff);
  border-radius: 2px 2px 0 0;
  position: relative;
  min-height: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.trend-bar:hover {
  background: linear-gradient(to top, #0056b3, #4da6ff);
  transform: scale(1.05);
}

.trend-label {
  position: absolute;
  bottom: -20px;
  font-size: 0.7rem;
  color: #6c757d;
  white-space: nowrap;
}

.trend-value {
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}

.progress {
  border-radius: 10px;
}

.progress-bar {
  border-radius: 10px;
  background: linear-gradient(45deg, #007bff, #66b3ff);
}
</style>
