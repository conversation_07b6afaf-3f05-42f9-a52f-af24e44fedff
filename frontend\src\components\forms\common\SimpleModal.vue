<template>
  <div
    v-if="show"
    class="simple-modal-overlay"
    @click.self="closeModal"
    data-safe-modal="true"
    data-modal-type="simple"
  >
    <div class="simple-modal-container" :class="modalSizeClass">
      <div class="simple-modal-header">
        <h5 class="simple-modal-title">{{ title }}</h5>
        <button type="button" class="simple-modal-close" @click="closeModal">&times;</button>
      </div>
      <div class="simple-modal-body">
        <slot></slot>
      </div>
      <div class="simple-modal-footer" v-if="$slots['footer-buttons']">
        <slot name="footer-buttons"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { setupResponsiveListener, getViewportInfo } from '@/utils/responsiveModal'

export default {
  name: 'SimpleModal',
  props: {
    title: {
      type: String,
      default: '模态框'
    },
    show: {
      type: Boolean,
      default: false
    },
    modalSize: {
      type: String,
      default: 'md',
      validator: value => ['sm', 'md', 'lg', 'xl', 'xxl'].includes(value)
    },
    safeModal: {
      type: Boolean,
      default: false
    },
    responsive: {
      type: Boolean,
      default: true
    },
    responsiveOptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      responsiveCleanup: null,
      viewportInfo: {}
    }
  },
  computed: {
    modalSizeClass() {
      return `modal-size-${this.modalSize}`
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.registerActiveModal()
        this.preventBodyScroll()
        this.setupResponsive()
        console.log('🔓 SimpleModal 已显示:', this.title)
      } else {
        this.unregisterActiveModal()
        this.restoreBodyScroll()
        this.cleanupResponsive()
        console.log('🔒 SimpleModal 已隐藏:', this.title)
      }
    },

    modalSize() {
      if (this.show && this.responsive) {
        this.$nextTick(() => {
          this.setupResponsive()
        })
      }
    }
  },
  methods: {
    closeModal() {
      console.log('🔒 关闭 SimpleModal:', this.title)
      this.$emit('update:show', false)
    },

    registerActiveModal() {
      if (this.safeModal) {
        window.activeModals = window.activeModals || new Set()
        window.activeModals.add(`simple-modal-${this.title}`)
        console.log('📝 注册安全模态框:', this.title)
      }
    },

    unregisterActiveModal() {
      if (this.safeModal && window.activeModals) {
        window.activeModals.delete(`simple-modal-${this.title}`)
        console.log('🗑️ 注销安全模态框:', this.title)
      }
    },

    preventBodyScroll() {
      document.body.style.overflow = 'hidden'
      document.body.classList.add('modal-open')
    },

    restoreBodyScroll() {
      // 检查是否还有其他活跃的模态框
      const hasOtherModals = document.querySelectorAll('.simple-modal-overlay').length > 1 ||
                            document.querySelectorAll('.modal.show').length > 0

      if (!hasOtherModals) {
        document.body.style.overflow = ''
        document.body.classList.remove('modal-open')
      }
    },

    setupResponsive() {
      if (!this.responsive) return

      this.$nextTick(() => {
        const container = this.$el?.querySelector('.simple-modal-container')
        if (container) {
          this.cleanupResponsive()
          this.responsiveCleanup = setupResponsiveListener(
            container,
            this.modalSize,
            this.responsiveOptions
          )
          this.updateViewportInfo()
          console.log('📱 响应式模态框已设置:', this.modalSize)
        }
      })
    },

    cleanupResponsive() {
      if (this.responsiveCleanup) {
        this.responsiveCleanup()
        this.responsiveCleanup = null
      }
    },

    updateViewportInfo() {
      this.viewportInfo = getViewportInfo()
    }
  },

  mounted() {
    this.updateViewportInfo()
    if (this.show) {
      this.registerActiveModal()
      this.preventBodyScroll()
      this.setupResponsive()
    }
  },

  beforeUnmount() {
    this.unregisterActiveModal()
    this.restoreBodyScroll()
    this.cleanupResponsive()
  }
}
</script>

<style scoped>
.simple-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999999 !important; /* 确保在最上层 */
  pointer-events: auto;
}

.simple-modal-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 95vw;
  height: 90vh;
  max-width: none;
  max-height: none;
  overflow: hidden;
  animation: modal-appear 0.3s ease;
  display: flex;
  flex-direction: column;
}

/* 响应式尺寸 - 基于视口宽度 */
.simple-modal-container.modal-size-sm {
  width: min(90vw, 400px);
  height: min(80vh, 500px);
}

.simple-modal-container.modal-size-md {
  width: min(90vw, 600px);
  height: min(85vh, 600px);
}

.simple-modal-container.modal-size-lg {
  width: min(95vw, 900px);
  height: min(90vh, 700px);
}

.simple-modal-container.modal-size-xl {
  width: min(98vw, 1200px);
  height: min(95vh, 800px);
}

.simple-modal-container.modal-size-xxl {
  width: min(98vw, 1400px);
  height: min(95vh, 900px);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .simple-modal-container {
    width: 100vw !important;
    height: 100vh !important;
    border-radius: 0;
    margin: 0;
  }

  .simple-modal-container.modal-size-sm,
  .simple-modal-container.modal-size-md,
  .simple-modal-container.modal-size-lg,
  .simple-modal-container.modal-size-xl,
  .simple-modal-container.modal-size-xxl {
    width: 100vw !important;
    height: 100vh !important;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .simple-modal-container.modal-size-sm {
    width: 80vw;
    height: 70vh;
  }

  .simple-modal-container.modal-size-md {
    width: 85vw;
    height: 75vh;
  }

  .simple-modal-container.modal-size-lg {
    width: 90vw;
    height: 80vh;
  }

  .simple-modal-container.modal-size-xl,
  .simple-modal-container.modal-size-xxl {
    width: 95vw;
    height: 85vh;
  }
}

@keyframes modal-appear {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.simple-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.simple-modal-title {
  margin: 0;
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  font-weight: 600;
  color: #2c3e50;
}

.simple-modal-close {
  background: none;
  border: none;
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  cursor: pointer;
  padding: 0.25rem;
  color: #6c757d;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
}

.simple-modal-close:hover {
  color: #343a40;
  background-color: #e9ecef;
}

.simple-modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.simple-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
  flex-shrink: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .simple-modal-header {
    padding: 0.75rem 1rem;
    border-radius: 0;
  }

  .simple-modal-body {
    padding: 1rem;
  }

  .simple-modal-footer {
    padding: 0.75rem 1rem;
    border-radius: 0;
  }

  .simple-modal-title {
    font-size: 1rem;
  }

  .simple-modal-close {
    font-size: 1.2rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .simple-modal-header {
    padding: 1.25rem 2rem;
  }

  .simple-modal-body {
    padding: 2rem;
  }

  .simple-modal-footer {
    padding: 1.25rem 2rem;
  }
}
</style>
