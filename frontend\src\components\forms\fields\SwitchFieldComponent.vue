<template>
  <div class="switch-field-component">
    <div class="form-check form-switch">
      <input 
        :id="id"
        type="checkbox" 
        :checked="value"
        :disabled="field.is_readonly"
        class="form-check-input"
        @change="handleChange"
      >
      <label :for="id" class="form-check-label">
        {{ switchLabel }}
      </label>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SwitchFieldComponent',
  props: {
    id: String,
    field: {
      type: Object,
      required: true
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:value', 'field-change'],
  computed: {
    switchLabel() {
      const options = this.field.field_options || {}
      return this.value ? (options.onLabel || '开启') : (options.offLabel || '关闭')
    }
  },
  methods: {
    handleChange(event) {
      const newValue = event.target.checked
      this.$emit('update:value', newValue)
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: newValue,
        event: 'change'
      })
    }
  }
}
</script>

<style scoped>
.form-check-input:disabled {
  opacity: 0.5;
}
</style>
