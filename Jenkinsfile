// 企业运维信息管理平台 Jenkins CI/CD 流水线
// 项目: export_excel - 智能化运维文档生成与管理系统
// 功能: 提供运维信息登记、Excel文档自动生成、模板管理等企业级功能
// 作者: junguang.chen
// 更新时间: 2025-01-06

pipeline {
    agent any
    
    environment {
        // 项目配置
        PROJECT_NAME = 'export_excel'
        DEPLOY_PATH = '/opt/export_excel'
        // 端口配置（从参数获取，如果参数为空则使用默认值）
        NGINX_PORT = "${params.NGINX_PORT ?: '9999'}"
        BACKEND_PORT = "${params.BACKEND_PORT ?: '5000'}"
        
        // 数据库配置
        MYSQL_HOST = '************'
        MYSQL_PORT = '3306'
        MYSQL_USER = 'junguangchen'
        MYSQL_PASSWORD = '1qaz@WSX'
        MYSQL_DATABASE = 'export_excel'
        
        // Redis配置
        REDIS_HOST = '************'
        REDIS_PORT = '6379'
        REDIS_PASSWORD = '1qaz@WSX'
        REDIS_DB = '0'
        
        // 环境变量
        FLASK_ENV = 'production'
        NODE_ENV = 'production'
    }
    
    parameters {
        choice(
            name: 'DEPLOY_ENV',
            choices: ['production', 'development'],
            description: '🌍 部署环境选择 - production: 生产环境 | development: 开发测试环境'
        )
        booleanParam(
            name: 'FORCE_REBUILD',
            defaultValue: false,
            description: '🔄 强制重新构建 - 清除缓存并重新编译前端和后端应用'
        )
        booleanParam(
            name: 'FRONTEND_UPDATED',
            defaultValue: true,
            description: '前端代码是否有更新'
        )
        booleanParam(
            name: 'RESTART_SERVICES',
            defaultValue: true,
            description: '🚀 重启系统服务 - 重启后端应用服务和Nginx代理服务'
        )
        booleanParam(
            name: 'INIT_DATABASE',
            defaultValue: false,
            description: '🗄️ 初始化数据库 - 执行数据库结构创建和初始数据导入（首次部署或结构变更时启用）'
        )
        string(
            name: 'NGINX_PORT',
            defaultValue: '9999',
            description: '🌐 Nginx前端端口 - 用户访问系统的Web端口（默认: 9999）'
        )
        string(
            name: 'BACKEND_PORT',
            defaultValue: '5000',
            description: '⚙️ 后端API端口 - Flask应用服务监听端口（默认: 5000）'
        )
    }
    
    stages {
        stage('环境检查') {
            steps {
                script {
                    echo "开始部署 ${PROJECT_NAME} 项目"
                    echo "部署环境: ${params.DEPLOY_ENV}"
                    echo "目标路径: ${DEPLOY_PATH}"
                    
                    sh '''
                        echo "检查系统依赖..."
                        node --version || echo "Node.js 未安装"
                        python3 --version || echo "Python3 未安装"
                        nginx -v || echo "Nginx 未安装"
                        
                        echo "系统依赖检查完成"
                    '''
                }
            }
        }
        
        stage('代码检出') {
            steps {
                script {
                    echo "检出代码到工作目录..."
                    
                    if (params.FORCE_REBUILD) {
                        sh 'rm -rf ./*'
                    }
                    
                    checkout scm
                    
                    sh '''
                        echo "项目结构:"
                        ls -la
                        echo "前端目录:"
                        ls -la frontend/ || echo "前端目录不存在"
                        echo "后端目录:"
                        ls -la backend/ || echo "后端目录不存在"
                    '''
                }
            }
        }
        
        stage('构建应用') {
            parallel {
                stage('前端构建') {
                    steps {
                        dir('frontend') {
                            sh '''
                                echo "=== 前端构建开始 ==="
                                echo "部署环境: ${DEPLOY_ENV}"
                                echo "强制重建: ${FORCE_REBUILD}"
                                echo "前端更新: ${FRONTEND_UPDATED}"

                                # 显示环境信息
                                echo "=== 环境信息 ==="
                                echo "当前用户: $(whoami)"
                                echo "用户ID: $(id -u)"
                                echo "是否为root: $([ $(id -u) -eq 0 ] && echo '是' || echo '否')"
                                echo "当前目录: $(pwd)"
                                echo "Node.js版本: $(node --version)"
                                echo "npm版本: $(npm --version)"

                                # 创建环境配置文件
                                if [ "${DEPLOY_ENV}" = "production" ]; then
                                    echo "创建生产环境配置文件..."
                                    cat > .env.production << EOF
VUE_APP_API_BASE_URL=/api
VUE_APP_TITLE=企业运维信息管理平台
VUE_APP_VERSION=1.0.0
NODE_ENV=production
EOF
                                    # 同时创建.env文件确保构建时能读取到
                                    cat > .env << EOF
VUE_APP_API_BASE_URL=/api
VUE_APP_TITLE=企业运维信息管理平台
VUE_APP_VERSION=1.0.0
NODE_ENV=production
EOF
                                    echo "✅ 生产环境配置文件创建完成"
                                fi

                                # 判断是否需要重新构建
                                if [ "${FORCE_REBUILD}" = "true" ] || [ "${FRONTEND_UPDATED}" = "true" ] || [ ! -d "dist" ]; then
                                    echo "需要重新构建前端"

                                    # 清理构建目录
                                    echo "清理构建目录..."
                                    rm -rf dist/

                                    # 如果强制重建，清理依赖
                                    if [ "${FORCE_REBUILD}" = "true" ]; then
                                        echo "⚠️ 强制重建：清理依赖..."
                                        sudo rm -rf node_modules/ package-lock.json
                                    fi

                                    # 设置npm配置
                                    echo "设置npm配置..."
                                    npm config set registry https://registry.npmmirror.com
                                    echo "npm registry已设置为: $(npm config get registry)"

                                    # 安装依赖
                                    echo "=== 安装依赖 ==="
                                    echo "开始执行 npm install..."
                                    sudo npm install

                                    # 构建项目
                                    echo "=== 构建项目 ==="
                                    echo "开始执行 npm run build..."
                                    sudo npm run build

                                    # 修复文件权限给jenkins用户
                                    echo "修复文件权限..."
                                    sudo chown -R jenkins:jenkins . 2>/dev/null || true

                                    echo "✅ 构建完成"
                                else
                                    echo "使用现有构建结果"
                                fi

                                # 验证构建结果
                                if [ ! -d "dist" ]; then
                                    echo "❌ 前端构建失败，dist目录不存在"
                                    exit 1
                                fi

                                echo "✅ 前端构建成功"
                                echo "构建结果: $(du -sh dist/)"
                                echo "构建文件列表:"
                                ls -la dist/ | head -5

                                echo "=== 前端构建完成 ==="
                            '''
                        }
                    }
                }
                
                stage('后端准备') {
                    steps {
                        dir('backend') {
                            sh '''
                                echo "准备后端应用..."

                                # 检查requirements.txt
                                if [ ! -f "requirements.txt" ]; then
                                    echo "未找到requirements.txt"
                                    exit 1
                                fi

                                # 检查uv是否可用
                                UV_PATH=""
                                for path in "/usr/local/python3/bin/uv" "/usr/local/bin/uv" "$HOME/.cargo/bin/uv" "$(which uv 2>/dev/null)"; do
                                    if [ -x "$path" ]; then
                                        UV_PATH="$path"
                                        echo "找到uv: $UV_PATH"
                                        break
                                    fi
                                done

                                # 检查是否需要强制重建后端虚拟环境
                                if [ "${FORCE_REBUILD}" = "true" ]; then
                                    echo "⚠️ 强制重建：清理后端虚拟环境..."
                                    sudo rm -rf .venv
                                fi

                                # 智能处理虚拟环境和依赖
                                if [ -n "$UV_PATH" ] && [ -f "pyproject.toml" ]; then
                                    echo "使用uv sync智能同步依赖..."

                                    # 设置uv环境变量，增加超时时间和重试次数
                                    export UV_HTTP_TIMEOUT=120
                                    export UV_CONCURRENT_DOWNLOADS=1

                                    # 清理可能的缓存问题
                                    echo "清理uv缓存..."
                                    sudo -E $UV_PATH cache clean || true

                                    # uv sync 会智能处理：如果.venv不存在则创建，如果依赖有变化则更新
                                    echo "执行uv sync（超时时间：120秒）..."
                                    sudo -E $UV_PATH sync --no-binary=:none: || {
                                        echo "uv sync失败，尝试使用pip方式..."
                                        # 如果uv sync失败，回退到pip方式
                                        sudo python3 -m venv .venv
                                        sudo .venv/bin/pip install --upgrade pip
                                        sudo .venv/bin/pip install -r requirements.txt --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn --timeout 120
                                        sudo .venv/bin/pip install gunicorn --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn --timeout 120
                                    }
                                    echo "✅ 依赖安装完成"
                                elif [ -n "$UV_PATH" ] && [ -f "requirements.txt" ]; then
                                    echo "使用uv处理requirements.txt..."
                                    # 检查虚拟环境是否存在
                                    if [ ! -d ".venv" ]; then
                                        echo "创建新的虚拟环境..."
                                        sudo $UV_PATH venv .venv
                                    else
                                        echo "虚拟环境已存在，检查依赖更新..."
                                    fi
                                    # 使用uv pip install，强制使用预编译包
                                    sudo $UV_PATH pip install -r requirements.txt --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple
                                    sudo $UV_PATH pip install gunicorn --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple
                                else
                                    echo "使用pip处理依赖..."
                                    # 检查虚拟环境是否存在
                                    if [ ! -d ".venv" ]; then
                                        echo "创建新的虚拟环境..."
                                        sudo python3 -m venv .venv
                                        sudo .venv/bin/python -m pip install --upgrade pip
                                    else
                                        echo "虚拟环境已存在，检查依赖更新..."
                                    fi
                                    # 使用pip安装，强制使用预编译包
                                    sudo .venv/bin/pip install -r requirements.txt --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn
                                    sudo .venv/bin/pip install gunicorn --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn
                                fi

                                # 修复虚拟环境权限给jenkins用户
                                echo "修复虚拟环境权限..."
                                sudo chown -R jenkins:jenkins .venv 2>/dev/null || true

                                # 验证关键依赖（使用绝对路径）
                                echo "验证关键依赖..."
                                .venv/bin/python -c "
import sys
print('Python路径:', sys.executable)
modules = ['flask', 'psutil', 'pymysql', 'redis', 'pandas', 'openpyxl']
for module in modules:
    try:
        __import__(module)
        print(f'✓ {module}: 已安装')
    except ImportError:
        print(f'✗ {module}: 未安装')
        exit(1)
"

                                echo "后端准备完成"
                            '''
                        }
                    }
                }
            }
        }
        
        stage('数据库初始化') {
            when {
                expression { params.INIT_DATABASE }
            }
            steps {
                dir('backend') {
                    sh '''
                        echo "初始化数据库..."
                        
                        # 确定Python可执行文件
                        if [ -f ".venv/bin/python" ]; then
                            PYTHON_CMD=".venv/bin/python"
                            echo "使用uv虚拟环境: $PYTHON_CMD"
                        elif [ -f "venv/bin/python" ]; then
                            PYTHON_CMD="venv/bin/python"
                            echo "使用pip虚拟环境: $PYTHON_CMD"
                        else
                            PYTHON_CMD="python3"
                            echo "使用系统Python: $PYTHON_CMD"
                        fi
                        
                        # 创建环境配置文件
                        sudo tee .env > /dev/null << EOF
MYSQL_HOST=${MYSQL_HOST}
MYSQL_PORT=${MYSQL_PORT}
MYSQL_USER=${MYSQL_USER}
MYSQL_PASSWORD=${MYSQL_PASSWORD}
MYSQL_DATABASE=${MYSQL_DATABASE}
REDIS_HOST=${REDIS_HOST}
REDIS_PORT=${REDIS_PORT}
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_DB=${REDIS_DB}
FLASK_ENV=${DEPLOY_ENV}
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
UPLOAD_FOLDER=uploads
EXCEL_TEMPLATES_FOLDER=excel_files/templates
EXCEL_GENERATED_FOLDER=excel_files/generated
EOF

                        sudo chmod 600 .env
                        sudo chown jenkins:jenkins .env
                        
                        # 测试数据库连接
                        $PYTHON_CMD -c "
import pymysql
try:
    conn = pymysql.connect(host='${MYSQL_HOST}', port=${MYSQL_PORT}, user='${MYSQL_USER}', password='${MYSQL_PASSWORD}')
    print('数据库连接成功')
    conn.close()
except Exception as e:
    print(f'数据库连接失败: {e}')
    exit(1)
"

                        # 初始化数据库（使用--force跳过交互式确认）
                        if [ "${DEPLOY_ENV}" = "production" ]; then
                            $PYTHON_CMD init_db.py --env production --force || echo "数据库初始化可能失败，但继续部署"
                        else
                            $PYTHON_CMD init_db.py --env development --force || echo "数据库初始化可能失败，但继续部署"
                        fi
                        
                        echo "数据库初始化完成"
                    '''
                }
            }
        }

        stage('应用部署') {
            steps {
                script {
                    echo "开始部署应用到 ${DEPLOY_PATH}..."

                    sh '''
                        # 创建部署目录
                        sudo mkdir -p ${DEPLOY_PATH}
                        sudo chown -R $USER:$USER ${DEPLOY_PATH}

                        # 备份现有部署并保存重要目录内容
                        BACKUP_DIR=""
                        if [ -d "${DEPLOY_PATH}/current" ]; then
                            echo "备份现有部署..."
                            BACKUP_DIR="${DEPLOY_PATH}/backup-$(date +%Y%m%d-%H%M%S)"
                            sudo mv ${DEPLOY_PATH}/current ${BACKUP_DIR} || true
                            echo "备份目录: ${BACKUP_DIR}"
                        fi

                        # 创建新的部署目录
                        mkdir -p ${DEPLOY_PATH}/current

                        # 复制应用文件（不包括虚拟环境）
                        echo "复制应用文件..."
                        sudo cp -r frontend/dist ${DEPLOY_PATH}/current/frontend-dist

                        # 复制后端文件，但排除虚拟环境
                        echo "复制后端文件..."
                        sudo rsync -av --exclude='.venv' --exclude='venv' --exclude='__pycache__' --exclude='*.pyc' backend/ ${DEPLOY_PATH}/current/backend/

                        sudo cp nginx.conf ${DEPLOY_PATH}/current/ || echo "nginx.conf不存在，跳过"

                        # 创建必要的目录
                        sudo mkdir -p ${DEPLOY_PATH}/current/logs
                        sudo mkdir -p ${DEPLOY_PATH}/current/backend/excel_files/templates
                        sudo mkdir -p ${DEPLOY_PATH}/current/backend/excel_files/generated

                        # 还原上个版本的重要目录内容
                        if [ -n "${BACKUP_DIR}" ] && [ -d "${BACKUP_DIR}" ]; then
                            echo "还原上个版本的重要目录内容..."

                            # 还原Excel模板文件
                            if [ -d "${BACKUP_DIR}/backend/excel_files/templates" ]; then
                                echo "还原Excel模板文件..."
                                sudo cp -r ${BACKUP_DIR}/backend/excel_files/templates/* ${DEPLOY_PATH}/current/backend/excel_files/templates/ 2>/dev/null || echo "模板目录为空或不存在"
                            fi

                            # 还原生成的Excel文件
                            if [ -d "${BACKUP_DIR}/backend/excel_files/generated" ]; then
                                echo "还原生成的Excel文件..."
                                sudo cp -r ${BACKUP_DIR}/backend/excel_files/generated/* ${DEPLOY_PATH}/current/backend/excel_files/generated/ 2>/dev/null || echo "生成文件目录为空或不存在"
                            fi

                            # 还原日志文件
                            if [ -d "${BACKUP_DIR}/logs" ]; then
                                echo "还原日志文件..."
                                sudo cp -r ${BACKUP_DIR}/logs/* ${DEPLOY_PATH}/current/logs/ 2>/dev/null || echo "日志目录为空或不存在"
                            fi

                            echo "✅ 重要目录内容还原完成"
                        else
                            echo "⚠️ 没有找到备份目录，跳过内容还原"
                        fi

                        # 设置基本权限
                        sudo chmod 755 ${DEPLOY_PATH}/current/backend/excel_files

                        # 修复所有文件权限给jenkins用户
                        echo "修复部署文件权限..."
                        sudo chown -R jenkins:jenkins ${DEPLOY_PATH}/current

                        echo "应用文件部署完成"
                    '''
                }
            }
        }

        stage('重建虚拟环境') {
            steps {
                script {
                    echo "在部署目录重建虚拟环境..."

                    sh '''
                        cd ${DEPLOY_PATH}/current/backend

                        # 检查uv是否可用
                        UV_PATH=""
                        for path in "/usr/local/python3/bin/uv" "/usr/local/bin/uv" "$HOME/.cargo/bin/uv" "$(which uv 2>/dev/null)"; do
                            if [ -x "$path" ]; then
                                UV_PATH="$path"
                                echo "找到uv: $UV_PATH"
                                break
                            fi
                        done

                        # 在部署目录智能处理虚拟环境
                        if [ -n "$UV_PATH" ] && [ -f "pyproject.toml" ]; then
                            echo "在部署目录使用uv sync..."

                            # 设置uv环境变量
                            export UV_HTTP_TIMEOUT=120
                            export UV_CONCURRENT_DOWNLOADS=1

                            # uv sync 会智能处理虚拟环境和依赖
                            sudo -E $UV_PATH sync --no-binary=:none: || {
                                echo "uv sync失败，使用pip方式..."
                                sudo python3 -m venv .venv
                                sudo .venv/bin/pip install --upgrade pip
                                sudo .venv/bin/pip install -r requirements.txt --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn --timeout 120
                                sudo .venv/bin/pip install gunicorn --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn --timeout 120
                            }
                            echo "✅ 部署目录依赖安装完成"
                        elif [ -n "$UV_PATH" ] && [ -f "requirements.txt" ]; then
                            echo "在部署目录使用uv处理requirements.txt..."
                            if [ ! -d ".venv" ]; then
                                echo "在部署目录创建新的虚拟环境..."
                                sudo $UV_PATH venv .venv
                            else
                                echo "部署目录虚拟环境已存在，更新依赖..."
                            fi
                            sudo $UV_PATH pip install -r requirements.txt --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple
                            sudo $UV_PATH pip install gunicorn --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple
                        else
                            echo "在部署目录使用pip处理依赖..."
                            if [ ! -d ".venv" ]; then
                                echo "在部署目录创建新的虚拟环境..."
                                sudo python3 -m venv .venv
                                sudo .venv/bin/python -m pip install --upgrade pip
                            else
                                echo "部署目录虚拟环境已存在，更新依赖..."
                            fi
                            sudo .venv/bin/pip install -r requirements.txt --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn
                            sudo .venv/bin/pip install gunicorn --only-binary=:all: --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn
                        fi

                        # 修复虚拟环境权限给jenkins用户
                        echo "修复虚拟环境权限..."
                        sudo chown -R jenkins:jenkins .venv 2>/dev/null || true

                        # 验证虚拟环境
                        echo "验证虚拟环境..."
                        echo "Python路径: $(pwd)/.venv/bin/python"
                        echo "虚拟环境目录: $(pwd)/.venv"

                        # 检查虚拟环境文件
                        if [ ! -f ".venv/bin/python" ]; then
                            echo "错误: 虚拟环境Python不存在"
                            exit 1
                        fi

                        # 验证关键依赖（使用绝对路径）
                        echo "验证关键依赖..."
                        .venv/bin/python -c "
import sys
print('Python可执行文件:', sys.executable)
print('site-packages路径:', [p for p in sys.path if 'site-packages' in p])

modules = ['flask', 'psutil', 'pymysql', 'redis', 'pandas', 'openpyxl', 'gunicorn']
failed = []
for module in modules:
    try:
        __import__(module)
        print(f'✓ {module}: 已安装')
    except ImportError as e:
        print(f'✗ {module}: 未安装 - {e}')
        failed.append(module)

if failed:
    print(f'缺失模块: {failed}')
    exit(1)
else:
    print('所有依赖验证通过')
"

                        # 测试应用导入（使用绝对路径）
                        echo "测试应用导入..."
                        .venv/bin/python -c "
import sys
sys.path.insert(0, '.')
try:
    from run import app
    print('✓ 应用导入成功')
    with app.app_context():
        print('✓ 应用上下文创建成功')
except Exception as e:
    print(f'✗ 应用测试失败: {e}')
    import traceback
    traceback.print_exc()
    exit(1)
"

                        # 设置正确的权限
                        echo "设置虚拟环境权限..."
                        sudo chown -R jenkins:jenkins .venv

                        # 安全地设置可执行文件权限，避免符号链接权限错误
                        echo "设置可执行文件权限..."
                        find .venv/bin -type f -name "python*" -exec chmod +x {} \\; 2>/dev/null || true
                        find .venv/bin -type f -name "pip*" -exec chmod +x {} \\; 2>/dev/null || true
                        find .venv/bin -type f -name "gunicorn*" -exec chmod +x {} \\; 2>/dev/null || true
                        find .venv/bin -type f -name "flask*" -exec chmod +x {} \\; 2>/dev/null || true

                        # 设置激活脚本权限
                        [ -f .venv/bin/activate ] && chmod +x .venv/bin/activate 2>/dev/null || true

                        # 验证关键可执行文件
                        if [ ! -x .venv/bin/python ]; then
                            echo "⚠️ Python可执行文件权限可能有问题"
                            ls -la .venv/bin/python* || true
                        fi

                        if [ ! -x .venv/bin/gunicorn ]; then
                            echo "⚠️ Gunicorn可执行文件权限可能有问题"
                            ls -la .venv/bin/gunicorn* || true
                        fi

                        echo "虚拟环境重建完成"
                    '''
                }
            }
        }

        stage('配置服务') {
            steps {
                sh '''
                    echo "配置服务..."

                    # 配置Nginx
                    echo "配置Nginx..."
                    if [ -f "${DEPLOY_PATH}/current/nginx.conf" ]; then
                        # 更新nginx.conf中的路径
                        sed -i "s|/opt/export_excel/frontend/dist|${DEPLOY_PATH}/current/frontend-dist|g" ${DEPLOY_PATH}/current/nginx.conf
                        sed -i "s|/opt/export_excel/backend/excel_files|${DEPLOY_PATH}/current/backend/excel_files|g" ${DEPLOY_PATH}/current/nginx.conf

                        # 更新端口配置
                        sed -i "s|listen [0-9]*;|listen ${NGINX_PORT};|g" ${DEPLOY_PATH}/current/nginx.conf
                        sed -i "s|proxy_pass http://127.0.0.1:[0-9]*|proxy_pass http://127.0.0.1:${BACKEND_PORT}|g" ${DEPLOY_PATH}/current/nginx.conf

                        echo "✅ 已更新Nginx配置："
                        echo "  前端端口: ${NGINX_PORT}"
                        echo "  后端端口: ${BACKEND_PORT}"

                        # 部署Nginx配置
                        sudo cp ${DEPLOY_PATH}/current/nginx.conf /etc/nginx/conf.d/export-excel.conf

                        # 测试Nginx配置
                        if sudo nginx -t; then
                            echo "✅ Nginx配置测试通过"
                        else
                            echo "❌ Nginx配置测试失败"
                            exit 1
                        fi
                    else
                        echo "⚠️ nginx.conf文件不存在，跳过Nginx配置"
                    fi

                    echo "配置后端服务..."

                    # 验证虚拟环境存在
                    if [ ! -f "${DEPLOY_PATH}/current/backend/.venv/bin/gunicorn" ]; then
                        echo "错误: 虚拟环境中的gunicorn不存在"
                        echo "检查虚拟环境..."
                        ls -la ${DEPLOY_PATH}/current/backend/.venv/bin/ || echo "虚拟环境bin目录不存在"
                        exit 1
                    fi

                    GUNICORN_PATH="${DEPLOY_PATH}/current/backend/.venv/bin/gunicorn"
                    PYTHON_PATH="${DEPLOY_PATH}/current/backend/.venv/bin/python"

                    echo "使用Gunicorn路径: $GUNICORN_PATH"
                    echo "使用Python路径: $PYTHON_PATH"

                    # 测试gunicorn配置
                    echo "测试gunicorn配置..."
                    cd ${DEPLOY_PATH}/current/backend
                    if ! $GUNICORN_PATH --check-config run:app; then
                        echo "gunicorn配置测试失败"
                        exit 1
                    fi
                    echo "gunicorn配置测试通过"

                    # 创建systemd服务文件
                    sudo tee /etc/systemd/system/export-excel-backend.service > /dev/null << EOF
[Unit]
Description=企业运维信息管理平台后端服务 - 智能化运维文档生成与管理系统
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=jenkins
Group=jenkins
WorkingDirectory=${DEPLOY_PATH}/current/backend
Environment=PATH=${DEPLOY_PATH}/current/backend/.venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=${DEPLOY_PATH}/current/backend
Environment=FLASK_ENV=production

ExecStart=$GUNICORN_PATH \\
    --bind 0.0.0.0:${BACKEND_PORT} \\
    --workers 2 \\
    --timeout 60 \\
    --log-level info \\
    --access-logfile ${DEPLOY_PATH}/current/logs/gunicorn-access.log \\
    --error-logfile ${DEPLOY_PATH}/current/logs/gunicorn-error.log \\
    run:app

Restart=always
RestartSec=5
KillMode=mixed
TimeoutStopSec=30

StandardOutput=append:${DEPLOY_PATH}/current/logs/backend.log
StandardError=append:${DEPLOY_PATH}/current/logs/backend-error.log

[Install]
WantedBy=multi-user.target
EOF

                    # 重新加载systemd配置
                    sudo systemctl daemon-reload

                    echo "后端服务配置完成"
                '''
            }
        }

        stage('启动服务') {
            when {
                expression { params.RESTART_SERVICES }
            }
            steps {
                sh '''
                    echo "启动服务..."

                    # 停止现有服务
                    sudo systemctl stop export-excel-backend || true
                    sleep 5

                    # 启动后端服务
                    sudo systemctl start export-excel-backend
                    sudo systemctl enable export-excel-backend

                    # 启动和配置Nginx
                    echo "配置Nginx服务..."
                    if sudo systemctl is-active --quiet nginx; then
                        echo "Nginx正在运行，重新加载配置..."
                        sudo systemctl reload nginx
                    else
                        echo "启动Nginx服务..."
                        sudo systemctl start nginx
                        sudo systemctl enable nginx
                    fi

                    # 验证Nginx状态
                    if sudo systemctl is-active --quiet nginx; then
                        echo "✅ Nginx服务运行正常"
                    else
                        echo "❌ Nginx服务启动失败"
                        sudo systemctl status nginx --no-pager -l
                    fi

                    sleep 10
                    echo "服务启动完成"
                '''
            }
        }

        stage('部署验证') {
            steps {
                script {
                    echo "验证部署结果..."

                    sh '''
                        echo "检查服务状态..."

                        # 检查后端服务状态
                        if sudo systemctl is-active --quiet export-excel-backend; then
                            echo "后端服务运行正常"
                        else
                            echo "后端服务未运行"
                            sudo systemctl status export-excel-backend || true
                        fi

                        # 检查Nginx服务状态
                        if sudo systemctl is-active --quiet nginx; then
                            echo "Nginx服务运行正常"
                        else
                            echo "Nginx服务未运行"
                            sudo systemctl status nginx || true
                        fi

                        # 检查端口监听
                        if netstat -tuln | grep -q ":${BACKEND_PORT} "; then
                            echo "后端端口 ${BACKEND_PORT} 监听正常"
                        else
                            echo "后端端口 ${BACKEND_PORT} 未监听"
                        fi

                        if netstat -tuln | grep -q ":${NGINX_PORT} "; then
                            echo "Nginx端口 ${NGINX_PORT} 监听正常"
                        else
                            echo "Nginx端口 ${NGINX_PORT} 未监听"
                        fi

                        echo "部署验证完成！"
                        echo ""
                        echo "部署信息:"
                        echo "  环境: ${DEPLOY_ENV}"
                        echo "  项目目录: ${DEPLOY_PATH}/current"
                        echo "  前端访问: http://服务器IP:${NGINX_PORT}"
                        echo "  后端端口: ${BACKEND_PORT}"
                        echo "  Nginx端口: ${NGINX_PORT}"
                        echo ""

                        # 如果前端有更新，提示清理浏览器缓存
                        if [ "${FRONTEND_UPDATED}" = "true" ]; then
                            echo "⚠️  前端更新提示:"
                            echo "  前端代码已更新，如果页面显示异常，请："
                            echo "  1. 强制刷新浏览器 (Ctrl+F5 或 Cmd+Shift+R)"
                            echo "  2. 清理浏览器缓存"
                            echo "  3. 或使用无痕/隐私模式访问"
                            echo ""
                        fi

                        echo "服务管理:"
                        echo "  查看后端状态: sudo systemctl status export-excel-backend"
                        echo "  查看Nginx状态: sudo systemctl status nginx"
                        echo "  查看后端日志: tail -f ${DEPLOY_PATH}/current/logs/backend.log"
                        echo "  重启后端: sudo systemctl restart export-excel-backend"
                        echo "  重启Nginx: sudo systemctl restart nginx"
                        echo "  测试Nginx配置: sudo nginx -t"
                    '''
                }
            }
        }
    }

    post {
        always {
            echo "清理工作空间..."
            sh '''
                npm cache clean --force 2>/dev/null || true
                find . -type d -name "__pycache__" -exec rm -rf {} \\+ 2>/dev/null || true
                find . -type f -name "*.pyc" -delete 2>/dev/null || true
            '''
        }

        success {
            echo "流水线执行成功！"
        }

        failure {
            echo "流水线执行失败！"
        }
    }
}
