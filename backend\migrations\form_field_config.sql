-- 表单字段配置相关数据库表

-- 1. 表单字段分组表
CREATE TABLE IF NOT EXISTS form_field_groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    form_type VARCHAR(50) NOT NULL,
    group_name VARCHAR(100) NOT NULL,
    group_label VARCHAR(100) NOT NULL,
    group_description TEXT,
    display_order INTEGER DEFAULT 0,
    is_collapsible BOOLEAN DEFAULT TRUE,
    is_expanded_by_default BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(form_type, group_name)
);

-- 2. 表单字段配置表
CREATE TABLE IF NOT EXISTS form_field_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    form_type VARCHAR(50) NOT NULL,
    group_id INTEGER,
    field_name VARCHAR(100) NOT NULL,
    field_label VARCHAR(100) NOT NULL,
    field_type VARCHAR(50) NOT NULL, -- text, textarea, select, checkbox, radio, date, number, email, url, password, file
    field_description TEXT,
    placeholder VARCHAR(200),
    default_value TEXT,
    is_required BOOLEAN DEFAULT FALSE,
    is_readonly BOOLEAN DEFAULT FALSE,
    is_auto_fill BOOLEAN DEFAULT FALSE,
    display_order INTEGER DEFAULT 0,
    validation_rules TEXT, -- JSON格式的验证规则
    field_options TEXT, -- JSON格式的选项（用于select, radio, checkbox）
    css_classes VARCHAR(200),
    grid_columns INTEGER DEFAULT 12, -- Bootstrap栅格列数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES form_field_groups(id) ON DELETE CASCADE,
    UNIQUE(form_type, field_name)
);

-- 3. 表单配置版本表
CREATE TABLE IF NOT EXISTS form_config_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    form_type VARCHAR(50) NOT NULL,
    version_name VARCHAR(100) NOT NULL,
    version_description TEXT,
    config_data TEXT NOT NULL, -- JSON格式的完整配置
    is_active BOOLEAN DEFAULT FALSE,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(form_type, version_name)
);

-- 4. 字段类型定义表（预定义的字段类型）
CREATE TABLE IF NOT EXISTS field_type_definitions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type_name VARCHAR(50) UNIQUE NOT NULL,
    type_label VARCHAR(100) NOT NULL,
    type_description TEXT,
    default_validation TEXT, -- JSON格式的默认验证规则
    has_options BOOLEAN DEFAULT FALSE, -- 是否支持选项配置
    icon_class VARCHAR(100), -- 图标CSS类
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入预定义的字段类型
INSERT OR IGNORE INTO field_type_definitions (type_name, type_label, type_description, default_validation, has_options, icon_class) VALUES
('text', '单行文本', '普通的文本输入框', '{"maxLength": 255}', FALSE, 'bi-input-cursor-text'),
('textarea', '多行文本', '多行文本输入框', '{"maxLength": 1000}', FALSE, 'bi-textarea'),
('select', '下拉选择', '下拉选择框', '{}', TRUE, 'bi-menu-button-wide'),
('checkbox', '复选框', '多选复选框', '{}', TRUE, 'bi-check-square'),
('radio', '单选框', '单选按钮组', '{}', TRUE, 'bi-record-circle'),
('date', '日期选择', '日期选择器', '{}', FALSE, 'bi-calendar-date'),
('datetime', '日期时间', '日期时间选择器', '{}', FALSE, 'bi-calendar-event'),
('number', '数字输入', '数字输入框', '{"min": 0}', FALSE, 'bi-123'),
('email', '邮箱地址', '邮箱输入框', '{}', FALSE, 'bi-envelope'),
('url', '网址链接', 'URL输入框', '{}', FALSE, 'bi-link'),
('password', '密码输入', '密码输入框', '{"minLength": 6}', FALSE, 'bi-key'),
('file', '文件上传', '文件上传组件', '{}', FALSE, 'bi-file-earmark-arrow-up'),
('switch', '开关切换', '开关切换组件', '{}', FALSE, 'bi-toggle-on'),
('slider', '滑块输入', '数值滑块', '{"min": 0, "max": 100}', FALSE, 'bi-sliders'),
('color', '颜色选择', '颜色选择器', '{}', FALSE, 'bi-palette');

-- 插入默认的表单分组（基于现有表单结构）
INSERT OR IGNORE INTO form_field_groups (form_type, group_name, group_label, group_description, display_order) VALUES
-- 安全测评表单分组
('安全测评', 'basic_info', '基本信息', '项目的基本信息', 1),
('安全测评', 'customer_info', '客户信息', '客户相关信息', 2),
('安全测评', 'version_info', '版本信息', '系统版本相关信息', 3),
('安全测评', 'server_info', '服务器信息', '服务器部署信息', 4),
('安全测评', 'maintenance_records', '维护记录', '系统维护记录', 5),

-- 安全监测表单分组
('安全监测', 'basic_info', '基本信息', '项目的基本信息', 1),
('安全监测', 'customer_info', '客户信息', '客户相关信息', 2),
('安全监测', 'access_info', '访问信息', '系统访问相关信息', 3),
('安全监测', 'network_config', '网络配置', '网络配置信息', 4),
('安全监测', 'server_info', '服务器信息', '服务器部署信息', 5),
('安全监测', 'maintenance_records', '维护记录', '系统维护记录', 6),

-- 应用加固表单分组
('应用加固', 'basic_info', '基本信息', '项目的基本信息', 1),
('应用加固', 'customer_info', '客户信息', '客户相关信息', 2),
('应用加固', 'access_info', '访问信息', '系统访问相关信息', 3),
('应用加固', 'server_info', '服务器信息', '服务器部署信息', 4),
('应用加固', 'maintenance_records', '维护记录', '系统维护记录', 5);
