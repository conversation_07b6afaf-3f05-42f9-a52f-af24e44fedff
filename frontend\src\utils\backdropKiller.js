/**
 * 强力遮罩清理工具 - 彻底解决Bootstrap模态框遮罩问题
 */

/**
 * 强制清理所有遮罩层
 */
function killAllBackdrops(force = false) {
  console.log('💀 启动强力遮罩清理器', force ? '(强制模式)' : '')

  try {
    // 检查是否有活跃的安全模态框
    const activeModals = document.querySelectorAll('.modal[data-modal-active="true"]')
    const safeModals = document.querySelectorAll('.modal[data-safe-modal="true"].show')

    if (!force && (activeModals.length > 0 || safeModals.length > 0)) {
      console.log('检测到活跃的安全模态框，跳过清理')
      return
    }

    // 1. 移除遮罩元素（但保护安全遮罩和安全模态框）
    const selectors = [
      '.modal-backdrop:not([data-safe-backdrop="true"])',
      '.modal-backdrop.show:not([data-safe-backdrop="true"])',
      '.modal-backdrop.fade:not([data-safe-backdrop="true"])',
      '.modal-backdrop.fade.show:not([data-safe-backdrop="true"])',
      '[class*="backdrop"]:not([data-safe-backdrop="true"]):not([data-safe-modal="true"])',
      '[class*="overlay"]:not([data-safe-backdrop="true"]):not([data-safe-modal="true"])'
    ]

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector)
      elements.forEach(el => {
        console.log(`移除遮罩元素: ${selector}`)
        el.remove()
      })
    })

    // 2. 隐藏非安全模态框
    const modals = document.querySelectorAll('.modal:not([data-safe-modal="true"])')
    modals.forEach(modal => {
      modal.classList.remove('show', 'fade')
      modal.style.display = 'none'
      modal.setAttribute('aria-hidden', 'true')
      modal.removeAttribute('aria-modal')
      modal.removeAttribute('role')
    })

    // 3. 清理非安全模态框的Bootstrap实例
    if (window.bootstrap && window.bootstrap.Modal) {
      modals.forEach(modal => {
        const instance = window.bootstrap.Modal.getInstance(modal)
        if (instance) {
          try {
            instance.dispose()
          } catch (e) {
            console.warn('清理Bootstrap实例失败:', e)
          }
        }
      })
    }

    // 4. 只有在没有活跃模态框时才重置body状态
    if (force || activeModals.length === 0) {
      document.body.classList.remove('modal-open')
      document.body.style.overflow = ''
      document.body.style.paddingRight = ''
      document.body.style.marginRight = ''
      document.body.style.position = ''

      // 5. 清理可能的内联样式
      document.documentElement.style.overflow = ''
      document.documentElement.style.paddingRight = ''
    }

    // 6. 移除可能的事件监听器
    const events = ['click', 'keydown', 'keyup']
    events.forEach(event => {
      document.removeEventListener(event, handleBackdropClick, true)
    })

    console.log('✅ 强力遮罩清理完成')
  } catch (error) {
    console.error('强力遮罩清理失败:', error)
  }
}

/**
 * 处理遮罩点击事件
 */
function handleBackdropClick(event) {
  if (event.target && event.target.classList.contains('modal-backdrop')) {
    event.preventDefault()
    event.stopPropagation()
    killAllBackdrops()
  }
}

/**
 * 监听页面变化，自动清理遮罩
 */
function setupAutoCleanup() {
  // 跟踪活跃的模态框
  window.activeModals = window.activeModals || new Set()

  // 监听DOM变化（减少频繁触发）
  let cleanupTimeout = null
  const observer = new MutationObserver((mutations) => {
    let hasBackdropChanges = false

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1 && node.classList &&
              node.classList.contains('modal-backdrop')) {
            hasBackdropChanges = true
          }
        })
      }
    })

    // 只有在检测到遮罩层变化时才处理
    if (hasBackdropChanges) {
      // 清除之前的定时器，避免重复执行
      if (cleanupTimeout) {
        clearTimeout(cleanupTimeout)
      }

      // 检查是否有活跃的模态框
      const activeModals = document.querySelectorAll('.modal.show')
      const safeModals = document.querySelectorAll('.modal[data-safe-modal="true"]')

      // 如果有活跃的模态框或安全模态框，跳过清理
      if (activeModals.length > 0 || safeModals.length > 0 || window.activeModals.size > 0) {
        console.log('检测到活跃模态框，跳过自动清理')
        return
      }

      console.log('检测到新的遮罩层，准备清理')
      cleanupTimeout = setTimeout(() => {
        // 再次检查是否有活跃模态框
        const stillActiveModals = document.querySelectorAll('.modal.show')
        if (stillActiveModals.length === 0) {
          killAllBackdrops()
        }
        cleanupTimeout = null
      }, 1000) // 增加延迟时间到1秒
    }
  })

  observer.observe(document.body, {
    childList: true,
    subtree: false // 只监听直接子元素变化，减少监听范围
  })

  // 定期清理（大幅减少频率，避免影响悬浮球）
  setInterval(() => {
    const backdrops = document.querySelectorAll('.modal-backdrop')
    const activeModals = document.querySelectorAll('.modal.show')

    // 只有在没有活跃模态框时才清理
    if (backdrops.length > 0 && activeModals.length === 0 && window.activeModals.size === 0) {
      console.log(`定期清理：发现 ${backdrops.length} 个遮罩层，无活跃模态框`)
      killAllBackdrops()
    }
  }, 120000) // 增加到2分钟，大幅减少频率
}

/**
 * 安全显示模态框
 */
function safeShowModal(modalId, options = {}) {
  console.log(`🔧 安全显示模态框: ${modalId}`)

  // 注册活跃模态框
  window.activeModals = window.activeModals || new Set()
  window.activeModals.add(modalId)

  // 先清理其他遮罩，但不清理当前要显示的模态框
  const otherBackdrops = document.querySelectorAll('.modal-backdrop:not([data-modal-id="' + modalId + '"])')
  otherBackdrops.forEach(backdrop => backdrop.remove())

  setTimeout(() => {
    const modalElement = document.getElementById(modalId)
    if (!modalElement) {
      console.error(`模态框元素未找到: ${modalId}`)
      window.activeModals.delete(modalId)
      return
    }

    try {
      // 标记为安全模态框
      modalElement.setAttribute('data-safe-modal', 'true')
      modalElement.setAttribute('data-modal-active', 'true')

      // 创建Bootstrap实例
      if (window.bootstrap && window.bootstrap.Modal) {
        const modal = new window.bootstrap.Modal(modalElement, {
          backdrop: options.backdrop !== false ? 'static' : false,
          keyboard: true,
          ...options
        })

        // 监听模态框关闭事件
        modalElement.addEventListener('hidden.bs.modal', () => {
          window.activeModals.delete(modalId)
          modalElement.removeAttribute('data-modal-active')
          console.log(`模态框 ${modalId} 已关闭，从活跃列表移除`)
        }, { once: true })

        modal.show()
      } else {
        // 手动显示
        modalElement.style.display = 'block'
        modalElement.classList.add('show')
        modalElement.setAttribute('aria-modal', 'true')
        modalElement.removeAttribute('aria-hidden')
        document.body.classList.add('modal-open')

        // 手动添加遮罩
        if (options.backdrop !== false) {
          createCustomBackdrop(modalId)
        }
      }

      console.log(`✅ 模态框 ${modalId} 显示成功`)
    } catch (error) {
      console.error(`显示模态框 ${modalId} 失败:`, error)
      window.activeModals.delete(modalId)
    }
  }, 50)
}

/**
 * 创建自定义遮罩
 */
function createCustomBackdrop(modalId) {
  const backdrop = document.createElement('div')
  backdrop.className = 'custom-modal-backdrop'
  backdrop.setAttribute('data-modal-id', modalId)
  backdrop.setAttribute('data-safe-backdrop', 'true')
  backdrop.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1055;
    pointer-events: auto;
  `

  backdrop.addEventListener('click', () => {
    const modal = document.getElementById(modalId)
    if (modal) {
      const bsModal = window.bootstrap.Modal.getInstance(modal)
      if (bsModal) {
        bsModal.hide()
      } else {
        modal.style.display = 'none'
        modal.classList.remove('show')
        backdrop.remove()
        document.body.classList.remove('modal-open')
        window.activeModals.delete(modalId)
      }
    }
  })

  document.body.appendChild(backdrop)
}

/**
 * 初始化遮罩清理器
 */
function initBackdropKiller() {
  console.log('🚀 初始化强力遮罩清理器')
  
  // 立即清理一次
  killAllBackdrops()
  
  // 设置自动清理
  setupAutoCleanup()
  
  // 页面可见性变化时清理（减少频率）
  let visibilityTimeout = null
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      // 延迟清理，避免频繁触发
      if (visibilityTimeout) clearTimeout(visibilityTimeout)
      visibilityTimeout = setTimeout(() => {
        const activeModals = document.querySelectorAll('.modal.show')
        if (activeModals.length === 0) {
          killAllBackdrops()
        }
      }, 2000)
    }
  })

  // 窗口焦点变化时清理（减少频率）
  let focusTimeout = null
  window.addEventListener('focus', () => {
    if (focusTimeout) clearTimeout(focusTimeout)
    focusTimeout = setTimeout(() => {
      const activeModals = document.querySelectorAll('.modal.show')
      if (activeModals.length === 0) {
        killAllBackdrops()
      }
    }, 1000)
  })
  
  // 暴露到全局
  window.backdropKiller = {
    kill: killAllBackdrops,
    safeShow: safeShowModal,
    init: initBackdropKiller
  }
  
  console.log('✅ 强力遮罩清理器初始化完成')
}

// 自动初始化
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initBackdropKiller)
  } else {
    initBackdropKiller()
  }
}

export {
  killAllBackdrops,
  safeShowModal,
  initBackdropKiller,
  setupAutoCleanup
}

export default {
  kill: killAllBackdrops,
  safeShow: safeShowModal,
  init: initBackdropKiller,
  setupAutoCleanup
}
