<template>
  <!-- 表单预览模态框 -->
  <div class="modal fade" id="formPreviewModal" tabindex="-1" aria-labelledby="formPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="formPreviewModalLabel">
            <i class="bi bi-eye me-2"></i>
            表单预览 - {{ formType }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div v-if="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在生成表单预览...</p>
          </div>

          <div v-else-if="previewGroups.length > 0" class="form-preview">
            <!-- 表单头部信息 -->
            <div class="alert alert-info mb-4">
              <i class="bi bi-info-circle me-2"></i>
              <strong>预览说明：</strong>这是根据当前字段配置生成的表单预览，实际表单可能会有所不同。
            </div>

            <!-- 表单内容 -->
            <form class="preview-form">
              <div v-for="group in previewGroups" :key="group.id" class="form-group-preview mb-4">
                <!-- 分组标题 -->
                <div class="group-header mb-3">
                  <h5 class="group-title">
                    <i class="bi bi-folder me-2"></i>
                    {{ group.group_label }}
                    <span class="badge bg-secondary ms-2">{{ group.fields.length }} 个字段</span>
                  </h5>
                  <p v-if="group.group_description" class="group-description text-muted">
                    {{ group.group_description }}
                  </p>
                </div>

                <!-- 字段列表 -->
                <div class="row">
                  <div 
                    v-for="field in group.fields" 
                    :key="field.id" 
                    :class="`col-md-${field.grid_columns || 12}`"
                    class="mb-3"
                  >
                    <div class="field-preview">
                      <!-- 字段标签 -->
                      <label class="form-label">
                        {{ field.field_label }}
                        <span v-if="field.is_required" class="text-danger">*</span>
                        <span v-if="field.is_readonly" class="badge bg-secondary ms-1">只读</span>
                        <span v-if="field.is_auto_fill" class="badge bg-info ms-1">自动填充</span>
                      </label>

                      <!-- 字段描述 -->
                      <div v-if="field.field_description" class="field-description text-muted small mb-2">
                        {{ field.field_description }}
                      </div>

                      <!-- 字段输入控件 -->
                      <div class="field-input">
                        <!-- 文本输入 -->
                        <input 
                          v-if="field.field_type === 'text' || field.field_type === 'email' || field.field_type === 'url'"
                          type="text"
                          class="form-control"
                          :class="field.css_classes"
                          :placeholder="field.placeholder"
                          :value="field.default_value"
                          :readonly="field.is_readonly"
                          :required="field.is_required"
                          disabled
                        >

                        <!-- 密码输入 -->
                        <input 
                          v-else-if="field.field_type === 'password'"
                          type="password"
                          class="form-control"
                          :class="field.css_classes"
                          :placeholder="field.placeholder"
                          :readonly="field.is_readonly"
                          :required="field.is_required"
                          disabled
                        >

                        <!-- 数字输入 -->
                        <input 
                          v-else-if="field.field_type === 'number'"
                          type="number"
                          class="form-control"
                          :class="field.css_classes"
                          :placeholder="field.placeholder"
                          :value="field.default_value"
                          :readonly="field.is_readonly"
                          :required="field.is_required"
                          disabled
                        >

                        <!-- 多行文本 -->
                        <textarea 
                          v-else-if="field.field_type === 'textarea'"
                          class="form-control"
                          :class="field.css_classes"
                          :placeholder="field.placeholder"
                          :readonly="field.is_readonly"
                          :required="field.is_required"
                          rows="3"
                          disabled
                        >{{ field.default_value }}</textarea>

                        <!-- 下拉选择 -->
                        <select 
                          v-else-if="field.field_type === 'select'"
                          class="form-select"
                          :class="field.css_classes"
                          :required="field.is_required"
                          disabled
                        >
                          <option value="">{{ field.placeholder || '请选择...' }}</option>
                          <option 
                            v-for="(label, value) in field.field_options" 
                            :key="value" 
                            :value="value"
                            :selected="value === field.default_value"
                          >
                            {{ label }}
                          </option>
                        </select>

                        <!-- 单选框 -->
                        <div v-else-if="field.field_type === 'radio'" class="radio-group">
                          <div 
                            v-for="(label, value) in field.field_options" 
                            :key="value"
                            class="form-check"
                          >
                            <input 
                              class="form-check-input" 
                              type="radio" 
                              :name="`radio_${field.field_name}`"
                              :value="value"
                              :checked="value === field.default_value"
                              disabled
                            >
                            <label class="form-check-label">
                              {{ label }}
                            </label>
                          </div>
                        </div>

                        <!-- 复选框 -->
                        <div v-else-if="field.field_type === 'checkbox'" class="checkbox-group">
                          <div 
                            v-for="(label, value) in field.field_options" 
                            :key="value"
                            class="form-check"
                          >
                            <input 
                              class="form-check-input" 
                              type="checkbox" 
                              :value="value"
                              disabled
                            >
                            <label class="form-check-label">
                              {{ label }}
                            </label>
                          </div>
                        </div>

                        <!-- 日期选择 -->
                        <input 
                          v-else-if="field.field_type === 'date'"
                          type="date"
                          class="form-control"
                          :class="field.css_classes"
                          :value="field.default_value"
                          :readonly="field.is_readonly"
                          :required="field.is_required"
                          disabled
                        >

                        <!-- 日期时间选择 -->
                        <input 
                          v-else-if="field.field_type === 'datetime'"
                          type="datetime-local"
                          class="form-control"
                          :class="field.css_classes"
                          :value="field.default_value"
                          :readonly="field.is_readonly"
                          :required="field.is_required"
                          disabled
                        >

                        <!-- 文件上传 -->
                        <input 
                          v-else-if="field.field_type === 'file'"
                          type="file"
                          class="form-control"
                          :class="field.css_classes"
                          :required="field.is_required"
                          disabled
                        >

                        <!-- 开关切换 -->
                        <div v-else-if="field.field_type === 'switch'" class="form-check form-switch">
                          <input 
                            class="form-check-input" 
                            type="checkbox" 
                            :checked="field.default_value === 'true' || field.default_value === true"
                            disabled
                          >
                          <label class="form-check-label">
                            {{ field.field_label }}
                          </label>
                        </div>

                        <!-- 颜色选择 -->
                        <input 
                          v-else-if="field.field_type === 'color'"
                          type="color"
                          class="form-control form-control-color"
                          :class="field.css_classes"
                          :value="field.default_value || '#000000'"
                          disabled
                        >

                        <!-- 滑块 -->
                        <input 
                          v-else-if="field.field_type === 'slider'"
                          type="range"
                          class="form-range"
                          :class="field.css_classes"
                          :value="field.default_value || 50"
                          disabled
                        >

                        <!-- 未知类型 -->
                        <div v-else class="alert alert-warning">
                          <i class="bi bi-exclamation-triangle me-2"></i>
                          未知字段类型: {{ field.field_type }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>

          <div v-else class="text-center py-5">
            <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无字段配置</h5>
            <p class="text-muted">请先配置字段分组和字段</p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>关闭
          </button>
          <button type="button" class="btn btn-primary" @click="exportPreview">
            <i class="bi bi-download me-1"></i>导出预览
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FormPreviewModal',
  props: {
    formType: {
      type: String,
      default: ''
    },
    fieldGroups: {
      type: Array,
      default: () => []
    },
    fieldConfigs: {
      type: Array,
      default: () => []
    }
  },
  emits: ['show-toast'],
  data() {
    return {
      loading: false,
      previewGroups: []
    }
  },
  methods: {
    show() {
      this.loading = true
      this.generatePreview()

      // 清理可能存在的模态框实例和遮罩
      this.cleanupModal()

      // 使用强力遮罩清理器
      if (window.backdropKiller) {
        window.backdropKiller.kill()
        setTimeout(() => {
          window.backdropKiller.safeShow('formPreviewModal', {
            backdrop: false,
            keyboard: true
          })
        }, 100)
      } else {
        // 备用方案
        const modalElement = document.getElementById('formPreviewModal')
        if (modalElement) {
          setTimeout(() => {
            modalElement.style.display = 'block'
            modalElement.classList.add('show')
            modalElement.setAttribute('aria-modal', 'true')
            modalElement.removeAttribute('aria-hidden')
            document.body.classList.add('modal-open')
          }, 100)
        }
      }
    },

    cleanupModal() {
      try {
        // 清理可能存在的模态框实例
        const modalElement = document.getElementById('formPreviewModal')
        if (modalElement) {
          const existingModal = window.bootstrap.Modal.getInstance(modalElement)
          if (existingModal) {
            existingModal.dispose()
          }
        }

        // 清理遮罩层
        const backdrops = document.querySelectorAll('.modal-backdrop')
        backdrops.forEach(backdrop => {
          if (backdrop.parentNode) {
            backdrop.parentNode.removeChild(backdrop)
          }
        })

        // 重置body样式
        document.body.classList.remove('modal-open')
        document.body.style.overflow = ''
        document.body.style.paddingRight = ''
      } catch (error) {
        console.warn('清理模态框时出错:', error)
      }
    },

    generatePreview() {
      try {
        // 组织预览数据
        this.previewGroups = this.fieldGroups.map(group => {
          const groupFields = this.fieldConfigs
            .filter(field => field.group_id === group.id)
            .sort((a, b) => (a.display_order || 0) - (b.display_order || 0))
          
          return {
            ...group,
            fields: groupFields
          }
        }).filter(group => group.fields.length > 0)
        
        this.loading = false
      } catch (error) {
        console.error('生成表单预览失败:', error)
        this.$emit('show-toast', '生成表单预览失败', '错误', 'error')
        this.loading = false
      }
    },

    exportPreview() {
      // 导出预览配置
      const config = {
        formType: this.formType,
        groups: this.previewGroups,
        exportTime: new Date().toISOString(),
        version: '1.0'
      }
      
      const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${this.formType}-字段配置-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      this.$emit('show-toast', '配置导出成功', '成功', 'success')
    }
  },

  beforeUnmount() {
    // 组件销毁前清理模态框
    this.cleanupModal()
  }
}
</script>

<style scoped>
/* 确保模态框层级正确 */
:deep(.modal) {
  z-index: 1060 !important;
}

:deep(.modal-backdrop) {
  z-index: 1055 !important;
}

/* 修复模态框显示问题 */
:deep(.modal.show) {
  display: block !important;
}

:deep(.modal-dialog) {
  margin: 1.75rem auto;
  pointer-events: auto;
}

:deep(.modal-content) {
  pointer-events: auto;
}

.form-preview {
  max-height: 70vh;
  overflow-y: auto;
}

.form-group-preview {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  background-color: #f8f9fa;
}

.group-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.group-title {
  color: #495057;
  margin-bottom: 0.5rem;
}

.group-description {
  margin-bottom: 0;
  font-size: 0.9rem;
}

.field-preview {
  background-color: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.field-description {
  font-style: italic;
}

.radio-group .form-check,
.checkbox-group .form-check {
  margin-bottom: 0.5rem;
}

.form-control:disabled,
.form-select:disabled,
.form-check-input:disabled {
  background-color: #f8f9fa;
  opacity: 0.8;
}

.badge {
  font-size: 0.75em;
}

.text-danger {
  color: #dc3545 !important;
}
</style>
