/**
 * 通用模态框修复工具
 * 用于解决Bootstrap模态框的遮罩层级和显示问题
 */

/**
 * 清理模态框状态的通用方法
 * @param {string} modalId - 模态框ID
 */
export function cleanupModal(modalId) {
  try {
    // 清理可能存在的模态框实例
    const modalElement = document.getElementById(modalId)
    if (modalElement) {
      const existingModal = window.bootstrap.Modal.getInstance(modalElement)
      if (existingModal) {
        existingModal.dispose()
      }
    }

    // 清理遮罩层
    const backdrops = document.querySelectorAll('.modal-backdrop')
    backdrops.forEach(backdrop => {
      if (backdrop.parentNode) {
        backdrop.parentNode.removeChild(backdrop)
      }
    })

    // 重置body样式
    document.body.classList.remove('modal-open')
    document.body.style.overflow = ''
    document.body.style.paddingRight = ''
  } catch (error) {
    console.warn(`清理模态框 ${modalId} 时出错:`, error)
  }
}

/**
 * 安全显示模态框的通用方法
 * @param {string} modalId - 模态框ID
 * @param {Object} options - Bootstrap模态框选项
 * @param {Function} onShown - 显示完成回调
 * @param {Function} onHidden - 隐藏完成回调
 */
export function safeShowModal(modalId, options = {}, onShown = null, onHidden = null) {
  // 清理可能存在的模态框实例和遮罩
  cleanupModal(modalId)
  
  // 显示模态框
  const modalElement = document.getElementById(modalId)
  if (modalElement) {
    setTimeout(() => {
      const defaultOptions = {
        backdrop: 'static',
        keyboard: false
      }
      
      const finalOptions = { ...defaultOptions, ...options }
      const modal = new window.bootstrap.Modal(modalElement, finalOptions)
      
      // 监听模态框事件
      if (onShown) {
        modalElement.addEventListener('shown.bs.modal', () => {
          console.log(`模态框 ${modalId} 已显示`)
          onShown()
        }, { once: true })
      }
      
      if (onHidden) {
        modalElement.addEventListener('hidden.bs.modal', () => {
          console.log(`模态框 ${modalId} 已隐藏`)
          cleanupModal(modalId)
          onHidden()
        }, { once: true })
      } else {
        // 默认隐藏时清理
        modalElement.addEventListener('hidden.bs.modal', () => {
          cleanupModal(modalId)
        }, { once: true })
      }
      
      modal.show()
    }, 100)
  }
}

/**
 * 为Vue组件添加模态框修复方法的Mixin
 */
export const modalFixerMixin = {
  methods: {
    cleanupModal(modalId) {
      cleanupModal(modalId)
    },
    
    safeShowModal(modalId, options = {}, onShown = null, onHidden = null) {
      safeShowModal(modalId, options, onShown, onHidden)
    }
  },
  
  beforeUnmount() {
    // 组件销毁前清理所有可能的模态框
    const modals = document.querySelectorAll('.modal')
    modals.forEach(modal => {
      if (modal.id) {
        cleanupModal(modal.id)
      }
    })
  }
}

/**
 * 通用的模态框CSS修复样式
 */
export const modalFixerStyles = `
/* 确保模态框层级正确 */
.modal {
  z-index: 1060 !important;
}

.modal-backdrop {
  z-index: 1055 !important;
}

/* 修复模态框显示问题 */
.modal.show {
  display: block !important;
}

.modal-dialog {
  margin: 1.75rem auto;
  pointer-events: auto;
}

.modal-content {
  pointer-events: auto;
}
`

/**
 * 批量修复页面中所有模态框
 */
export function fixAllModalsOnPage() {
  console.log('🔧 批量修复页面中的所有模态框')
  
  // 添加全局样式
  const styleId = 'modal-fixer-styles'
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = modalFixerStyles
    document.head.appendChild(style)
  }
  
  // 清理所有遗留的遮罩
  const backdrops = document.querySelectorAll('.modal-backdrop')
  backdrops.forEach(backdrop => backdrop.remove())
  
  // 重置body状态
  document.body.classList.remove('modal-open')
  document.body.style.overflow = ''
  document.body.style.paddingRight = ''
  
  console.log('✅ 模态框修复完成')
}

/**
 * 在页面加载时自动修复模态框
 */
if (typeof window !== 'undefined') {
  // 页面加载完成后自动修复
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixAllModalsOnPage)
  } else {
    fixAllModalsOnPage()
  }
  
  // 暴露到全局（开发环境）
  if (process.env.NODE_ENV === 'development') {
    window.modalFixer = {
      cleanup: cleanupModal,
      safeShow: safeShowModal,
      fixAll: fixAllModalsOnPage,
      styles: modalFixerStyles
    }
    console.log('🔧 模态框修复工具已加载到 window.modalFixer')
  }
}

export default {
  cleanupModal,
  safeShowModal,
  modalFixerMixin,
  modalFixerStyles,
  fixAllModalsOnPage
}
