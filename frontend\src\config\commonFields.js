/**
 * 公共字段配置定义
 * 用于在不同表单类型间复用相同的字段配置
 */

/**
 * 基础字段定义 - 所有表单都需要的核心字段
 */
export const COMMON_BASIC_FIELDS = {
  // 公司名称字段
  companyName: {
    field_name: 'companyName',
    field_label: '公司名称',
    field_type: 'text',
    is_required: true,
    placeholder: '请输入公司名称',
    field_description: '客户公司的名称',
    icon: 'bi-building',
    validation_rules: {
      minLength: 2,
      maxLength: 100
    }
  },

  // 记录日期字段
  recordDate: {
    field_name: 'recordDate',
    field_label: '记录日期',
    field_type: 'date',
    is_required: true,
    placeholder: '',
    field_description: '表单记录的日期',
    icon: 'bi-calendar-event',
    is_auto_fill: true,
    default_value: () => new Date().toISOString().split('T')[0]
  },

  // 客户字段
  customer: {
    field_name: 'customer',
    field_label: '客户',
    field_type: 'text',
    is_required: true,
    placeholder: '请输入客户名称',
    field_description: '客户名称或标识',
    icon: 'bi-person-badge',
    validation_rules: {
      minLength: 1,
      maxLength: 50
    }
  }
}

/**
 * 访问信息字段定义
 */
export const COMMON_ACCESS_FIELDS = {
  // 平台地址字段
  platformUrl: {
    field_name: 'platformUrl',
    field_label: '平台地址',
    field_type: 'url',
    is_required: true,
    placeholder: '请输入平台访问地址',
    field_description: '系统平台的访问地址',
    icon: 'bi-globe',
    validation_rules: {
      pattern: '^https?://.+',
      message: '请输入有效的URL地址'
    }
  },

  // 平台版本字段
  platformVersion: {
    field_name: 'platformVersion',
    field_label: '部署的平台版本',
    field_type: 'text',
    is_required: false,
    placeholder: '请输入平台版本',
    field_description: '当前部署的平台版本号',
    icon: 'bi-tag',
    validation_rules: {
      maxLength: 50
    }
  }
}

/**
 * 服务器信息字段定义
 */
export const COMMON_SERVER_FIELDS = {
  // 服务器IP地址
  serverIp: {
    field_name: 'serverIp',
    field_label: '服务器IP地址',
    field_type: 'text',
    is_required: false,
    placeholder: '请输入服务器IP地址',
    field_description: '服务器的IP地址',
    icon: 'bi-hdd-network',
    validation_rules: {
      pattern: '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
      message: '请输入有效的IP地址'
    }
  },

  // 服务器操作系统
  serverOs: {
    field_name: 'serverOs',
    field_label: '服务器操作系统',
    field_type: 'text',
    is_required: false,
    placeholder: '请输入操作系统信息',
    field_description: '服务器的操作系统版本',
    icon: 'bi-pc-display',
    validation_rules: {
      maxLength: 100
    }
  },

  // 服务器配置
  serverConfig: {
    field_name: 'serverConfig',
    field_label: '服务器配置',
    field_type: 'text',
    is_required: false,
    placeholder: '请输入服务器配置信息',
    field_description: '服务器的硬件配置信息',
    icon: 'bi-cpu',
    validation_rules: {
      maxLength: 200
    }
  },

  // SSH端口
  sshPort: {
    field_name: 'sshPort',
    field_label: 'SSH端口',
    field_type: 'number',
    is_required: false,
    placeholder: '22',
    default_value: '22',
    field_description: 'SSH连接端口',
    icon: 'bi-terminal',
    validation_rules: {
      min: 1,
      max: 65535
    }
  }
}

/**
 * 维护记录字段定义
 */
export const COMMON_MAINTENANCE_FIELDS = {
  // 维护日期
  maintenanceDate: {
    field_name: 'maintenanceDate',
    field_label: '维护日期',
    field_type: 'date',
    is_required: false,
    placeholder: '',
    field_description: '系统维护的日期',
    icon: 'bi-calendar-check'
  },

  // 维护内容
  maintenanceContent: {
    field_name: 'maintenanceContent',
    field_label: '维护内容',
    field_type: 'textarea',
    is_required: false,
    placeholder: '请输入维护内容描述',
    field_description: '本次维护的具体内容',
    icon: 'bi-journal-text',
    validation_rules: {
      maxLength: 1000
    }
  },

  // 维护人员
  maintenanceBy: {
    field_name: 'maintenanceBy',
    field_label: '维护人员',
    field_type: 'text',
    is_required: false,
    placeholder: '请输入维护人员姓名',
    field_description: '执行维护的人员',
    icon: 'bi-person-gear',
    validation_rules: {
      maxLength: 50
    }
  }
}

/**
 * 标准分组定义
 */
export const STANDARD_GROUPS = {
  basic_info: {
    group_name: 'basic_info',
    group_label: '基本信息',
    group_description: '项目的基本信息',
    display_order: 1,
    is_collapsible: true,
    is_expanded_by_default: true,
    default_fields: ['companyName', 'recordDate']
  },

  customer_info: {
    group_name: 'customer_info',
    group_label: '客户信息',
    group_description: '客户相关信息',
    display_order: 2,
    is_collapsible: true,
    is_expanded_by_default: true,
    default_fields: ['customer']
  },

  access_info: {
    group_name: 'access_info',
    group_label: '访问信息',
    group_description: '系统访问相关信息',
    display_order: 3,
    is_collapsible: true,
    is_expanded_by_default: true,
    default_fields: ['platformUrl', 'platformVersion']
  },

  server_info: {
    group_name: 'server_info',
    group_label: '服务器信息',
    group_description: '服务器部署信息',
    display_order: 4,
    is_collapsible: true,
    is_expanded_by_default: false,
    default_fields: ['serverIp', 'serverOs', 'serverConfig', 'sshPort'] // 包含所有服务器相关字段
  },

  maintenance_record: {
    group_name: 'maintenance_record',
    group_label: '维护记录',
    group_description: '系统维护记录',
    display_order: 5,
    is_collapsible: true,
    is_expanded_by_default: false,
    default_fields: ['maintenanceDate', 'maintenanceContent', 'maintenanceBy'] // 包含所有维护相关字段
  }
}

/**
 * 获取所有公共字段定义
 */
export const getAllCommonFields = () => {
  return {
    ...COMMON_BASIC_FIELDS,
    ...COMMON_ACCESS_FIELDS,
    ...COMMON_SERVER_FIELDS,
    ...COMMON_MAINTENANCE_FIELDS
  }
}

/**
 * 根据字段名获取公共字段定义
 * @param {string} fieldName - 字段名
 * @returns {Object|null} - 字段定义对象
 */
export const getCommonField = (fieldName) => {
  const allFields = getAllCommonFields()
  return allFields[fieldName] || null
}

/**
 * 获取标准分组配置
 * @param {string} groupName - 分组名
 * @returns {Object|null} - 分组配置对象
 */
export const getStandardGroup = (groupName) => {
  return STANDARD_GROUPS[groupName] || null
}

/**
 * 获取所有标准分组
 * @returns {Array} - 标准分组数组
 */
export const getAllStandardGroups = () => {
  return Object.values(STANDARD_GROUPS)
}
