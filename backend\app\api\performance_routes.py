"""
性能监控API路由
提供系统性能监控、统计和报告功能
"""

from flask import Blueprint, jsonify, request
from flask_jwt_extended import get_jwt_identity
from app.auth.decorators import permission_required, login_required
from app.models.auth_models import User
from app.utils.performance_monitor import (
    performance_monitor,
    get_performance_report,
    clear_performance_data
)
import traceback
from datetime import datetime

# 创建性能监控蓝图
performance_bp = Blueprint('performance', __name__, url_prefix='/api/performance')


@performance_bp.route('/stats', methods=['GET'])
@permission_required('system.view')
def get_performance_stats():
    """获取性能统计信息"""
    try:
        stats = performance_monitor.get_stats()
        
        return jsonify({
            'status': 'success',
            'message': '获取性能统计成功',
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取性能统计失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/report', methods=['GET'])
@permission_required('system.view')
def get_performance_report_api():
    """获取详细性能报告"""
    try:
        report = get_performance_report()
        
        return jsonify({
            'status': 'success',
            'message': '获取性能报告成功',
            'data': report
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取性能报告失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/slow-queries', methods=['GET'])
@permission_required('system.view')
def get_slow_queries():
    """获取慢查询列表"""
    try:
        limit = request.args.get('limit', 10, type=int)
        hours = request.args.get('hours', 1, type=int)  # 默认只显示最近1小时

        # 计算时间过滤
        from datetime import datetime, timedelta
        cutoff_time = datetime.now() - timedelta(hours=hours)

        # 过滤最近的慢查询
        recent_queries = []
        for query in performance_monitor.slow_queries:
            query_time = query.get('timestamp')
            if query_time and query_time > cutoff_time:
                recent_queries.append(query)

        # 只取最新的几条
        slow_queries = recent_queries[-limit:] if recent_queries else []

        # 格式化慢查询数据
        formatted_queries = []
        for query in slow_queries:
            formatted_queries.append({
                'sql': query.get('sql', ''),
                'duration': query.get('duration', 0),
                'timestamp': query.get('timestamp', '').isoformat() if hasattr(query.get('timestamp', ''), 'isoformat') else str(query.get('timestamp', '')),
                'parameters': query.get('parameters', ''),
                'type': query.get('type', 'unknown')
            })

        return jsonify({
            'status': 'success',
            'message': f'获取最近{hours}小时的慢查询列表成功',
            'data': {
                'queries': formatted_queries,
                'total': len(formatted_queries),
                'total_all_time': len(performance_monitor.slow_queries),
                'hours_filter': hours
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取慢查询列表失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/cache-stats', methods=['GET'])
@permission_required('system.view')
def get_cache_performance():
    """获取缓存性能统计"""
    try:
        cache_stats = performance_monitor._get_cache_stats()

        # 计算总体统计
        total_hits = sum(stat['hits'] for stat in cache_stats.values())
        total_misses = sum(stat['misses'] for stat in cache_stats.values())
        total_requests = total_hits + total_misses
        overall_hit_rate = total_hits / total_requests if total_requests > 0 else 0

        return jsonify({
            'status': 'success',
            'message': '获取缓存性能统计成功',
            'data': {
                'cache_types': cache_stats,
                'summary': {
                    'total_hits': total_hits,
                    'total_misses': total_misses,
                    'total_requests': total_requests,
                    'overall_hit_rate': overall_hit_rate
                }
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取缓存性能统计失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/request-stats', methods=['GET'])
@permission_required('system.view')
def get_request_performance():
    """获取请求性能统计"""
    try:
        hours = request.args.get('hours', 1, type=int)
        since_time = datetime.now().timestamp() - (hours * 3600)
        
        request_stats = performance_monitor._get_request_stats(since_time)
        
        # 计算总体统计
        total_requests = sum(stat['count'] for stat in request_stats.values())
        avg_duration = sum(stat['avg_duration'] * stat['count'] for stat in request_stats.values()) / total_requests if total_requests > 0 else 0
        
        return jsonify({
            'status': 'success',
            'message': f'获取最近{hours}小时请求性能统计成功',
            'data': {
                'endpoints': request_stats,
                'summary': {
                    'total_requests': total_requests,
                    'avg_duration': avg_duration,
                    'time_range_hours': hours
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取请求性能统计失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/system-resources', methods=['GET'])
@permission_required('system.view')
def get_system_resources():
    """获取系统资源使用情况"""
    try:
        system_stats = performance_monitor._get_system_stats()
        
        return jsonify({
            'status': 'success',
            'message': '获取系统资源统计成功',
            'data': system_stats,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取系统资源统计失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/clear-data', methods=['POST'])
@permission_required('system.config')
def clear_performance_data_api():
    """清理性能监控数据"""
    try:
        clear_performance_data()

        return jsonify({
            'status': 'success',
            'message': '性能监控数据清理成功',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'清理性能监控数据失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/clear-slow-queries', methods=['POST'])
@permission_required('system.config')
def clear_slow_queries_api():
    """清理慢查询数据"""
    try:
        performance_monitor.slow_queries.clear()

        return jsonify({
            'status': 'success',
            'message': '慢查询数据清理成功',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'清理慢查询数据失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/health-check', methods=['GET'])
def health_check():
    """系统健康检查"""
    try:
        stats = performance_monitor.get_stats()
        
        # 健康检查指标
        health_status = 'healthy'
        issues = []
        
        # 检查系统资源
        system = stats.get('system', {})
        if system.get('cpu_percent', 0) > 80:
            health_status = 'warning'
            issues.append('CPU使用率过高')
        
        if system.get('memory_percent', 0) > 85:
            health_status = 'warning'
            issues.append('内存使用率过高')
        
        if system.get('disk_percent', 0) > 90:
            health_status = 'critical'
            issues.append('磁盘空间不足')
        
        # 检查慢查询
        if len(performance_monitor.slow_queries) > 10:
            health_status = 'warning'
            issues.append('慢查询过多')
        
        # 检查错误率
        error_count = sum(performance_monitor.error_counts.values())
        if error_count > 50:
            health_status = 'critical'
            issues.append('错误率过高')
        
        return jsonify({
            'status': 'success',
            'data': {
                'health_status': health_status,
                'issues': issues,
                'timestamp': datetime.now().isoformat(),
                'uptime': 'N/A',  # 可以添加应用启动时间计算
                'version': '1.0.0'  # 可以从配置中获取
            }
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'健康检查失败: {str(e)}',
            'data': {
                'health_status': 'critical',
                'issues': ['系统监控异常'],
                'timestamp': datetime.now().isoformat()
            }
        }), 500


@performance_bp.route('/excel-stats', methods=['GET'])
@permission_required('system.view')
def get_excel_generation_stats():
    """获取Excel生成性能统计"""
    try:
        # 获取Excel生成统计
        excel_stats = getattr(performance_monitor, 'excel_stats', {})

        # 获取模板缓存统计
        from app.excel.utils import get_template_cache_stats
        template_cache_stats = get_template_cache_stats()

        return jsonify({
            'status': 'success',
            'message': '获取Excel生成性能统计成功',
            'data': {
                'excel_generation': excel_stats,
                'template_cache': template_cache_stats,
                'timestamp': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取Excel生成性能统计失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/template-cache', methods=['GET'])
@permission_required('system.view')
def get_template_cache_info():
    """获取模板缓存详细信息"""
    try:
        from app.excel.utils import get_template_cache_stats
        cache_stats = get_template_cache_stats()

        # 获取缓存命中率
        cache_performance = performance_monitor._get_cache_stats()
        template_cache_performance = cache_performance.get('template', {})

        return jsonify({
            'status': 'success',
            'message': '获取模板缓存信息成功',
            'data': {
                'cache_info': cache_stats,
                'performance': template_cache_performance,
                'timestamp': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取模板缓存信息失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/clear-template-cache', methods=['POST'])
@permission_required('system.config')
def clear_template_cache_api():
    """清空模板缓存"""
    try:
        from app.excel.utils import clear_template_cache
        clear_template_cache()

        return jsonify({
            'status': 'success',
            'message': '模板缓存清空成功',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'清空模板缓存失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@performance_bp.route('/recommendations', methods=['GET'])
@permission_required('system.view')
def get_performance_recommendations():
    """获取性能优化建议"""
    try:
        stats = performance_monitor.get_stats()
        recommendations = []

        # 基于统计数据生成建议
        system = stats.get('system', {})
        cache_stats = stats.get('cache', {})

        # CPU建议
        if system.get('cpu_percent', 0) > 70:
            recommendations.append({
                'type': 'system',
                'priority': 'high',
                'title': 'CPU使用率过高',
                'description': '考虑优化代码逻辑或增加服务器资源',
                'action': '检查慢查询和耗时操作'
            })

        # 内存建议
        if system.get('memory_percent', 0) > 80:
            recommendations.append({
                'type': 'system',
                'priority': 'high',
                'title': '内存使用率过高',
                'description': '考虑增加内存或优化缓存策略',
                'action': '检查内存泄漏和缓存配置'
            })

        # 缓存建议
        for cache_type, cache_stat in cache_stats.items():
            if cache_stat.get('hit_rate', 0) < 0.7:
                recommendations.append({
                    'type': 'cache',
                    'priority': 'medium',
                    'title': f'{cache_type}缓存命中率低',
                    'description': f'当前命中率: {cache_stat.get("hit_rate", 0):.2%}',
                    'action': '考虑调整缓存策略或增加缓存时间'
                })

        # 模板缓存特殊建议
        template_cache_stat = cache_stats.get('template', {})
        if template_cache_stat and template_cache_stat.get('hit_rate', 0) < 0.8:
            recommendations.append({
                'type': 'excel',
                'priority': 'medium',
                'title': 'Excel模板缓存命中率低',
                'description': f'当前命中率: {template_cache_stat.get("hit_rate", 0):.2%}',
                'action': '检查模板文件是否频繁修改，考虑预热缓存'
            })

        # Excel生成性能建议
        excel_stats = getattr(performance_monitor, 'excel_stats', {})
        if excel_stats:
            avg_duration = excel_stats.get('total_duration', 0) / max(excel_stats.get('total_generated', 1), 1)
            if avg_duration > 2.0:  # 超过2秒
                recommendations.append({
                    'type': 'excel',
                    'priority': 'high',
                    'title': 'Excel生成速度较慢',
                    'description': f'平均生成时间: {avg_duration:.2f}秒',
                    'action': '检查模板复杂度和数据量，考虑优化模板或异步处理'
                })

        # 慢查询建议
        if len(performance_monitor.slow_queries) > 5:
            recommendations.append({
                'type': 'database',
                'priority': 'high',
                'title': '存在多个慢查询',
                'description': f'发现{len(performance_monitor.slow_queries)}个慢查询',
                'action': '优化SQL查询或添加索引'
            })

        return jsonify({
            'status': 'success',
            'message': '获取性能优化建议成功',
            'data': {
                'recommendations': recommendations,
                'total': len(recommendations),
                'timestamp': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取性能优化建议失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


# ==================== 基础监控端点 (只需登录) ====================

@performance_bp.route('/basic-stats', methods=['GET'])
@login_required
def get_basic_stats():
    """获取基础性能统计 (只需登录权限)"""
    try:
        system_stats = performance_monitor._get_system_stats()

        # 简化的统计信息
        basic_stats = {
            'system': {
                'cpu_percent': system_stats.get('cpu_percent', 0),
                'memory_percent': system_stats.get('memory_percent', 0),
                'disk_percent': system_stats.get('disk_percent', 0)
            },
            'requests': {
                'total_endpoints': len(performance_monitor.request_times),
                'slow_queries_count': len(performance_monitor.slow_queries)
            },
            'timestamp': datetime.now().isoformat()
        }

        return jsonify({
            'status': 'success',
            'message': '获取基础性能统计成功',
            'data': basic_stats
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取基础性能统计失败: {str(e)}'
        }), 500


@performance_bp.route('/simple-health', methods=['GET'])
@login_required
def simple_health_check():
    """简单健康检查 (只需登录权限)"""
    try:
        system_stats = performance_monitor._get_system_stats()

        # 简单的健康状态判断
        health_status = 'healthy'
        if system_stats.get('cpu_percent', 0) > 80 or system_stats.get('memory_percent', 0) > 85:
            health_status = 'warning'

        return jsonify({
            'status': 'success',
            'data': {
                'health_status': health_status,
                'cpu_percent': system_stats.get('cpu_percent', 0),
                'memory_percent': system_stats.get('memory_percent', 0),
                'timestamp': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'健康检查失败: {str(e)}'
        }), 500



# ==================== 公开端点 (无需认证) ====================

@performance_bp.route('/public-health', methods=['GET'])
def public_health_check():
    """公开健康检查 (无需认证)"""
    try:
        # 基础的系统状态检查
        import time
        current_time = datetime.now().isoformat()

        return jsonify({
            'status': 'success',
            'data': {
                'health_status': 'healthy',
                'timestamp': current_time,
                'version': '1.0.0',
                'service': 'performance-monitor'
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'公开健康检查失败: {str(e)}',
            'data': {
                'health_status': 'critical',
                'timestamp': datetime.now().isoformat()
            }
        }), 500


@performance_bp.route('/debug-permissions', methods=['GET'])
@login_required
def debug_user_permissions():
    """调试用户权限（临时接口）"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(int(user_id))

        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404

        # 获取用户的所有权限
        all_permissions = user.get_all_permissions()

        # 检查特定权限
        system_view = user.has_permission('system.view')
        system_config = user.has_permission('system.config')

        return jsonify({
            'status': 'success',
            'data': {
                'user_id': user.id,
                'username': user.username,
                'is_admin': user.is_admin,
                'is_active': user.is_active,
                'roles': [{'id': r.id, 'code': r.code, 'name': r.name} for r in user.roles],
                'groups': [{'id': g.id, 'code': g.code, 'name': g.name} for g in user.groups],
                'all_permissions': all_permissions,
                'has_system_view': system_view,
                'has_system_config': system_config,
                'permission_count': len(all_permissions)
            }
        })
    except Exception as e:
        import traceback
        return jsonify({
            'status': 'error',
            'message': f'调试权限失败: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500

