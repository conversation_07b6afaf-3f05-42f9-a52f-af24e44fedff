<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-xl">
      <div class="modal-content shadow-lg border-0">
        <div class="modal-header bg-gradient-success text-white border-0">
          <h5 class="modal-title fw-bold">
            <i class="bi bi-shield-check me-2"></i>
            {{ isEdit ? '编辑角色权限' : '创建新角色' }}
          </h5>
          <button type="button" class="btn-close btn-close-white" @click="$emit('close')"></button>
        </div>
        <div class="modal-body p-4">
          <form @submit.prevent="handleSubmit">
            <div class="row">
              <!-- 基本信息卡片 -->
              <div class="col-md-5">
                <div class="card border-0 shadow-sm h-100">
                  <div class="card-header bg-light border-0">
                    <h6 class="card-title mb-0 text-success">
                      <i class="bi bi-info-circle me-2"></i>基本信息
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-tag me-1"></i>角色名称 <span class="text-danger">*</span>
                      </label>
                      <input
                        type="text"
                        class="form-control"
                        v-model="form.name"
                        placeholder="请输入角色名称"
                        required
                        :disabled="form.is_system"
                      >
                    </div>

                    <div class="mb-3">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-code me-1"></i>角色代码 <span class="text-danger">*</span>
                      </label>
                      <input
                        type="text"
                        class="form-control"
                        v-model="form.code"
                        placeholder="请输入角色代码"
                        :disabled="isEdit || form.is_system"
                        required
                        pattern="[a-zA-Z0-9_.-]+"
                        title="只能包含字母、数字、下划线、点和短横线"
                      >
                      <small class="text-muted">只能包含字母、数字、下划线、点和短横线</small>
                      <small v-if="isEdit && !form.is_system" class="text-muted d-block">编辑时不能修改角色代码</small>
                    </div>

                    <div class="mb-3">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-file-text me-1"></i>角色描述
                      </label>
                      <textarea
                        class="form-control"
                        rows="3"
                        v-model="form.description"
                        placeholder="请输入角色描述"
                        :disabled="form.is_system"
                      ></textarea>
                    </div>

                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="isActive"
                          v-model="form.is_active"
                          :disabled="form.is_system"
                        >
                        <label class="form-check-label fw-semibold" for="isActive">
                          <i class="bi bi-toggle-on me-1"></i>启用角色
                        </label>
                      </div>
                      <small class="text-muted">禁用后该角色将无法使用</small>
                    </div>

                    <div v-if="form.is_system" class="alert alert-warning border-0">
                      <i class="bi bi-shield-lock me-2"></i>
                      <strong>系统角色说明：</strong><br>
                      系统角色的基本信息受保护，只能调整权限配置。
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 权限配置卡片 -->
              <div class="col-md-7">
                <div class="card border-0 shadow-sm h-100">
                  <div class="card-header bg-light border-0">
                    <div class="d-flex justify-content-between align-items-center">
                      <h6 class="card-title mb-0 text-success">
                        <i class="bi bi-shield-check me-2"></i>权限配置
                      </h6>
                      <div>
                        <button type="button" class="btn btn-sm btn-outline-success me-1" @click="selectAllPermissions">
                          <i class="bi bi-check-all me-1"></i>全选
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" @click="clearAllPermissions">
                          <i class="bi bi-x-circle me-1"></i>清空
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="permissions-container border rounded-3 p-3 bg-light" style="max-height: 450px; overflow-y: auto;">
                      <div v-for="(permissions, module) in groupedPermissions" :key="module" class="mb-4">
                        <div class="module-header d-flex justify-content-between align-items-center mb-3 p-2 bg-white rounded shadow-sm">
                          <h6 class="text-primary mb-0 fw-bold">
                            <i class="bi bi-folder2-open me-2"></i>
                            {{ module }} 模块
                          </h6>
                          <div>
                            <button
                              type="button"
                              class="btn btn-xs btn-outline-primary me-1"
                              @click="selectModulePermissions(module)"
                              title="选择该模块所有权限"
                            >
                              <i class="bi bi-check-square"></i>
                            </button>
                            <button
                              type="button"
                              class="btn btn-xs btn-outline-secondary"
                              @click="clearModulePermissions(module)"
                              title="清空该模块所有权限"
                            >
                              <i class="bi bi-square"></i>
                            </button>
                          </div>
                        </div>

                        <div class="row g-2">
                          <div v-for="permission in permissions" :key="permission.id" class="col-12">
                            <div class="permission-item p-2 border rounded bg-white shadow-sm">
                              <div class="form-check">
                                <input
                                  class="form-check-input"
                                  type="checkbox"
                                  :id="`permission-${permission.id}`"
                                  :value="permission.id"
                                  v-model="form.permission_ids"
                                >
                                <label class="form-check-label w-100" :for="`permission-${permission.id}`">
                                  <div class="d-flex justify-content-between align-items-center">
                                    <div class="flex-grow-1">
                                      <div class="fw-semibold">{{ permission.name }}</div>
                                      <small class="text-muted">
                                        <code class="bg-light px-1 rounded">{{ permission.code }}</code>
                                        <span v-if="permission.description" class="ms-1">{{ permission.description }}</span>
                                      </small>
                                    </div>
                                    <div class="permission-badges">
                                      <span v-if="!permission.is_active" class="badge bg-warning text-dark">禁用</span>
                                    </div>
                                  </div>
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div v-if="!permissions.length" class="text-center py-5">
                        <i class="bi bi-shield-x fs-1 text-muted d-block mb-2"></i>
                        <p class="text-muted mb-0">暂无可用权限</p>
                      </div>
                    </div>

                    <div class="mt-3 p-2 bg-info bg-opacity-10 rounded">
                      <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle text-info me-2"></i>
                        <span class="fw-semibold text-info">
                          已选择 {{ form.permission_ids.length }} 个权限
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer bg-light border-0 p-4">
          <button type="button" class="btn btn-outline-secondary px-4" @click="$emit('close')">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button type="button" class="btn btn-success px-4" @click="handleSubmit" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-2"></span>
            <i v-else class="bi bi-check-circle me-1"></i>
            {{ saving ? '保存中...' : (isEdit ? '保存更改' : '创建角色') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RoleEditModal',
  props: {
    role: {
      type: Object,
      default: null
    },
    permissions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'save'],
  data() {
    return {
      form: {
        name: '',
        code: '',
        description: '',
        is_active: true,
        is_system: false,
        permission_ids: []
      },
      saving: false
    }
  },
  computed: {
    isEdit() {
      return !!this.role
    },
    
    groupedPermissions() {
      const grouped = {}
      this.permissions.forEach(permission => {
        const module = permission.module || '其他'
        if (!grouped[module]) {
          grouped[module] = []
        }
        grouped[module].push(permission)
      })
      
      // 按模块名排序
      const sortedGrouped = {}
      Object.keys(grouped).sort().forEach(key => {
        sortedGrouped[key] = grouped[key].sort((a, b) => a.name.localeCompare(b.name))
      })
      
      return sortedGrouped
    }
  },
  mounted() {
    if (this.role) {
      this.form = {
        ...this.role,
        permission_ids: this.role.permissions ? this.role.permissions.map(p => p.id) : []
      }
    }
  },
  methods: {
    async handleSubmit() {
      if (this.saving) return
      
      this.saving = true
      try {
        const roleData = { ...this.form }
        this.$emit('save', roleData)
      } finally {
        this.saving = false
      }
    },
    
    selectAllPermissions() {
      this.form.permission_ids = this.permissions.map(p => p.id)
    },
    
    clearAllPermissions() {
      this.form.permission_ids = []
    },
    
    selectModulePermissions(module) {
      const modulePermissions = this.groupedPermissions[module] || []
      const moduleIds = modulePermissions.map(p => p.id)
      
      // 添加该模块的权限ID（去重）
      this.form.permission_ids = [...new Set([...this.form.permission_ids, ...moduleIds])]
    },
    
    clearModulePermissions(module) {
      const modulePermissions = this.groupedPermissions[module] || []
      const moduleIds = modulePermissions.map(p => p.id)
      
      // 移除该模块的权限ID
      this.form.permission_ids = this.form.permission_ids.filter(id => !moduleIds.includes(id))
    }
  }
}
</script>

<style scoped>
.modal {
  z-index: 1060;
}

.bg-gradient-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
}

.form-check {
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
}

.form-check:hover {
  background-color: rgba(40, 167, 69, 0.05);
  border-radius: 4px;
}

.form-check-label {
  width: 100%;
  cursor: pointer;
}

.permission-item {
  transition: all 0.2s ease;
}

.permission-item:hover {
  background-color: rgba(40, 167, 69, 0.05) !important;
  border-color: #28a745 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.1) !important;
}

.module-header {
  transition: all 0.2s ease;
}

.module-header:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.permissions-container::-webkit-scrollbar {
  width: 8px;
}

.permissions-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.permissions-container::-webkit-scrollbar-thumb {
  background: #28a745;
  border-radius: 4px;
}

.permissions-container::-webkit-scrollbar-thumb:hover {
  background: #20c997;
}

.btn-xs {
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  border-radius: 0.2rem;
}

.form-control:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.form-check-input:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

code {
  background-color: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  border: 1px solid #e9ecef;
}

.badge {
  font-size: 0.75em;
}

.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-content {
  border-radius: 15px;
  overflow: hidden;
}

.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

@media (max-width: 768px) {
  .modal-dialog {
    margin: 0.5rem;
  }

  .permissions-container {
    max-height: 300px !important;
  }
}
</style>
