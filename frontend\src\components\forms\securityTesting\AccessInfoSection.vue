<template>
  <collapsible-card card-class="border-info" storage-key="access-info-section">
    <template #header>
      <i class="bi bi-door-open me-2"></i>访问信息
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-primary">管理员页面: {{ adminPageIP || '未配置' }}</span>
        <span class="badge bg-success">用户页面: {{ userPageIP || '未配置' }}</span>
        <span class="badge bg-info">升级页面: {{ upgradePageIP || '未配置' }}</span>
        <span class="badge bg-secondary">升级用户: {{ upgradeAccount || '未配置' }}</span>
      </div>
    </template>

    <!-- 管理员页面子部分 -->
    <div class="access-subsection mb-4">
      <div class="subsection-header">
        <h6 class="subsection-title">
          <i class="bi bi-person-gear me-2 text-primary"></i>
          管理员页面
        </h6>
      </div>
      <div class="subsection-content">
        <!-- 管理员页面IP -->
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="adminPageIP"
            v-model="adminPageIP"
            placeholder="例如：*************"
            @input="updateAdminPageIP"
            :readonly="autoFilledFields.adminPage"
            :class="{'bg-light': autoFilledFields.adminPage}"
          >
          <label for="adminPageIP">管理员页面IP <span class="text-danger">*</span></label>
          <small v-if="autoFilledFields.adminPage" class="form-text text-muted">
            <i class="bi bi-info-circle"></i> 自动填充自frontend-ssp-admin组件
          </small>
        </div>

        <!-- 账号信息 -->
        <div class="row g-3">
          <!-- 超级管理员账号 -->
          <div class="col-md-6">
            <div class="account-card">
              <div class="account-card-header">
                <i class="bi bi-shield-fill-check me-1 text-danger"></i>
                <span class="fw-bold">超级管理员</span>
              </div>
              <div class="account-card-body">
                <div class="input-group mb-2">
                  <span class="input-group-text">账号</span>
                  <input
                    type="text"
                    class="form-control"
                    v-model="superAdminAccount"
                    @input="updateSuperAdminAccount"
                    placeholder="超级管理员账号"
                  >
                </div>
                <div class="input-group">
                  <span class="input-group-text">密码</span>
                  <input
                    :type="passwordVisibility.superAdmin ? 'text' : 'password'"
                    class="form-control"
                    v-model="superAdminPassword"
                    @input="updateSuperAdminPassword"
                    placeholder="超级管理员密码"
                  >
                  <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('superAdmin')">
                    <i :class="passwordVisibility.superAdmin ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 管理员账号 -->
          <div class="col-md-6">
            <div class="account-card">
              <div class="account-card-header">
                <i class="bi bi-person-badge me-1 text-warning"></i>
                <span class="fw-bold">管理员</span>
              </div>
              <div class="account-card-body">
                <div class="input-group mb-2">
                  <span class="input-group-text">账号</span>
                  <input
                    type="text"
                    class="form-control"
                    v-model="adminAccount"
                    @input="updateAdminAccount"
                    placeholder="管理员账号"
                  >
                </div>
                <div class="input-group">
                  <span class="input-group-text">密码</span>
                  <input
                    :type="passwordVisibility.admin ? 'text' : 'password'"
                    class="form-control"
                    v-model="adminPassword"
                    @input="updateAdminPassword"
                    placeholder="管理员密码"
                  >
                  <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('admin')">
                    <i :class="passwordVisibility.admin ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户页面子部分 -->
    <div class="access-subsection mb-4">
      <div class="subsection-header">
        <h6 class="subsection-title">
          <i class="bi bi-people me-2 text-success"></i>
          用户页面
        </h6>
      </div>
      <div class="subsection-content">
        <!-- 用户页面IP -->
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="userPageIP"
            v-model="userPageIP"
            placeholder="例如：*************"
            @input="updateUserPageIP"
            :readonly="autoFilledFields.userPage"
            :class="{'bg-light': autoFilledFields.userPage}"
          >
          <label for="userPageIP">用户页面IP <span class="text-danger">*</span></label>
          <small v-if="autoFilledFields.userPage" class="form-text text-muted">
            <i class="bi bi-info-circle"></i> 自动填充自frontend-ssp-user组件
          </small>
        </div>

        <!-- 用户账号 -->
        <div class="row g-3">
          <div class="col-md-6">
            <div class="account-card">
              <div class="account-card-header">
                <i class="bi bi-person me-1 text-success"></i>
                <span class="fw-bold">用户账号</span>
              </div>
              <div class="account-card-body">
                <div class="input-group mb-2">
                  <span class="input-group-text">账号</span>
                  <input
                    type="text"
                    class="form-control"
                    v-model="userAccount"
                    @input="updateUserAccount"
                    placeholder="用户账号"
                  >
                </div>
                <div class="input-group">
                  <span class="input-group-text">密码</span>
                  <input
                    :type="passwordVisibility.user ? 'text' : 'password'"
                    class="form-control"
                    v-model="userPassword"
                    @input="updateUserPassword"
                    placeholder="用户密码"
                  >
                  <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('user')">
                    <i :class="passwordVisibility.user ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 对外服务端口 -->
          <div class="col-md-6">
            <div class="form-floating">
              <input
                type="text"
                class="form-control"
                id="externalServicePort"
                v-model="externalServicePort"
                placeholder="例如：*************:9120"
                @input="updateExternalServicePort"
                :readonly="autoFilledFields.externalService"
                :class="{'bg-light': autoFilledFields.externalService}"
              >
              <label for="externalServicePort">对外服务端口</label>
              <small v-if="autoFilledFields.externalService" class="form-text text-muted">
                <i class="bi bi-info-circle"></i> 自动填充自backend-ssp-user组件
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 升级页面子部分 -->
    <div class="access-subsection">
      <div class="subsection-header">
        <h6 class="subsection-title">
          <i class="bi bi-arrow-up-circle me-2 text-info"></i>
          升级页面
        </h6>
      </div>
      <div class="subsection-content">
        <!-- 升级页面地址 -->
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="upgradePageIP"
            v-model="upgradePageIP"
            placeholder="例如：http://*************:8080"
            @input="updateUpgradePageIP"
            :readonly="autoFilledFields.upgradePage"
            :class="{'bg-light': autoFilledFields.upgradePage}"
          >
          <label for="upgradePageIP">升级页面地址 <span class="text-danger">*</span></label>
          <small v-if="autoFilledFields.upgradePage" class="form-text text-muted">
            <i class="bi bi-info-circle"></i> 自动填充自luna组件的IP和端口
          </small>
        </div>

        <!-- 升级用户账号 -->
        <div class="row g-3 mb-3">
          <div class="col-md-6">
            <div class="account-card">
              <div class="account-card-header">
                <i class="bi bi-arrow-up-circle me-1 text-info"></i>
                <span class="fw-bold">升级用户</span>
              </div>
              <div class="account-card-body">
                <div class="input-group mb-2">
                  <span class="input-group-text">账号</span>
                  <input
                    type="text"
                    class="form-control"
                    v-model="upgradeAccount"
                    @input="updateUpgradeAccount"
                    placeholder="升级用户账号"
                  >
                </div>
                <div class="input-group">
                  <span class="input-group-text">密码</span>
                  <input
                    :type="passwordVisibility.upgrade ? 'text' : 'password'"
                    class="form-control"
                    v-model="upgradePassword"
                    @input="updateUpgradePassword"
                    placeholder="升级用户密码"
                  >
                  <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('upgrade')">
                    <i :class="passwordVisibility.upgrade ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  </collapsible-card>
</template>

<script>
/**
 * 安全测评访问信息部分组件
 * 用于填写各种访问信息和账号密码
 */
import CollapsibleCard from '../common/CollapsibleCard.vue'

export default {
  name: 'AccessInfoSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 管理员页面IP
    adminPage: {
      type: String,
      default: ''
    },
    // 超级管理员账号
    superAdmin: {
      type: String,
      default: '<EMAIL>'
    },
    // 超级管理员密码
    superAdminPwd: {
      type: String,
      default: 'everisk@!QAZ2wsx'
    },
    // 管理员账号
    admin: {
      type: String,
      default: 'admin'
    },
    // 管理员密码
    adminPwd: {
      type: String,
      default: 'everisk@!QAZ2wsx'
    },
    // 用户账号
    user: {
      type: String,
      default: 'user'
    },
    // 用户密码
    userPwd: {
      type: String,
      default: 'everisk@!QAZ2wsx'
    },
    // 用户页面IP
    userPage: {
      type: String,
      default: ''
    },
    // 升级页面地址
    upgradePage: {
      type: String,
      default: ''
    },
    // 升级用户账号
    upgradeUser: {
      type: String,
      default: 'upgrader'
    },
    // 升级用户密码
    upgradeUserPwd: {
      type: String,
      default: 'upgrader@abc#2020'
    },
    // 对外服务端口
    externalService: {
      type: String,
      default: ''
    },
    // 服务器信息列表，用于自动填充地址
    serverList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      adminPageIP: this.adminPage,
      superAdminAccount: this.superAdmin,
      superAdminPassword: this.superAdminPwd,
      adminAccount: this.admin,
      adminPassword: this.adminPwd,
      userAccount: this.user,
      userPassword: this.userPwd,
      userPageIP: this.userPage,
      upgradePageIP: this.upgradePage,
      upgradeAccount: this.upgradeUser,
      upgradePassword: this.upgradeUserPwd,
      externalServicePort: this.externalService,
      passwordVisibility: {
        superAdmin: false,
        admin: false,
        user: false,
        upgrade: false
      },
      // 自动填充字段标记
      autoFilledFields: {
        adminPage: false,
        userPage: false,
        upgradePage: false,
        externalService: false
      },
      // 跟踪用户是否手动编辑过字段
      userEditedFields: {
        superAdminAccount: false,
        superAdminPassword: false,
        adminAccount: false,
        adminPassword: false,
        userAccount: false,
        userPassword: false,
        upgradeAccount: false,
        upgradePassword: false
      }
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    adminPage(newVal) {
      this.adminPageIP = newVal
    },
    superAdmin(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.superAdminAccount) {
        this.superAdminAccount = newVal
      }
    },
    superAdminPwd(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.superAdminPassword) {
        this.superAdminPassword = newVal
      }
    },
    admin(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.adminAccount) {
        this.adminAccount = newVal
      }
    },
    adminPwd(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.adminPassword) {
        this.adminPassword = newVal
      }
    },
    user(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.userAccount) {
        this.userAccount = newVal
      }
    },
    userPwd(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.userPassword) {
        this.userPassword = newVal
      }
    },
    userPage(newVal) {
      this.userPageIP = newVal
    },
    upgradePage(newVal) {
      this.upgradePageIP = newVal
    },
    upgradeUser(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.upgradeAccount) {
        this.upgradeAccount = newVal
      }
    },
    upgradeUserPwd(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.upgradePassword) {
        this.upgradePassword = newVal
      }
    },
    externalService(newVal) {
      this.externalServicePort = newVal
    },
    // 监听服务器列表变化，自动更新各个地址
    serverList: {
      handler() {
        this.autoFillAdminPageIP(true)
        this.autoFillUserPageIP(true)
        this.autoFillUpgradePageIP(true)
        this.autoFillExternalServicePort(true)
      },
      deep: true
    }
  },
  mounted() {
    // 组件挂载时自动填充
    this.autoFillAdminPageIP(true)
    this.autoFillUserPageIP(true)
    this.autoFillUpgradePageIP(true)
    this.autoFillExternalServicePort(true)
  },
  methods: {
    /**
     * 切换密码可见性
     * @param {String} field - 密码字段名称
     */
    togglePasswordVisibility(field) {
      this.passwordVisibility[field] = !this.passwordVisibility[field]
    },

    /**
     * 自动填充管理员页面IP地址
     * 使用front-ssp-admin组件所在的IP地址和端口
     * @param {Boolean} force - 是否强制更新，即使已有值
     */
    autoFillAdminPageIP(force = false) {
      // 如果已经有值且不是强制更新，不自动填充
      if (this.adminPageIP && !force) return

      // 查找包含front-ssp-admin组件的服务器
      const adminServer = this.findServerWithComponent('front-ssp-admin')
      if (adminServer) {
        const ip = adminServer.IP地址
        const port = adminServer.组件端口['front-ssp-admin'] || this.getComponentDefaultPort('front-ssp-admin')
        this.adminPageIP = `${ip}:${port.split('/')[0]}`
        this.autoFilledFields.adminPage = true
        this.updateAdminPageIP()
      } else {
        // 如果没有找到组件，清空字段
        if (force && this.adminPageIP && this.autoFilledFields.adminPage) {
          this.adminPageIP = ''
          this.autoFilledFields.adminPage = false
          this.updateAdminPageIP()
        }
      }
    },

    /**
     * 自动填充用户页面IP地址
     * 使用front-ssp-user组件所在的IP地址和端口
     * @param {Boolean} force - 是否强制更新，即使已有值
     */
    autoFillUserPageIP(force = false) {
      // 如果已经有值且不是强制更新，不自动填充
      if (this.userPageIP && !force) return

      // 查找包含front-ssp-user组件的服务器
      const userServer = this.findServerWithComponent('front-ssp-user')
      if (userServer) {
        const ip = userServer.IP地址
        const port = userServer.组件端口['front-ssp-user'] || this.getComponentDefaultPort('front-ssp-user')
        this.userPageIP = `${ip}:${port.split('/')[0]}`
        this.autoFilledFields.userPage = true
        this.updateUserPageIP()
      } else {
        // 如果没有找到组件，清空字段
        if (force && this.userPageIP && this.autoFilledFields.userPage) {
          this.userPageIP = ''
          this.autoFilledFields.userPage = false
          this.updateUserPageIP()
        }
      }
    },

    /**
     * 自动填充升级页面地址
     * 使用luna组件所在的IP地址和端口
     * @param {Boolean} force - 是否强制更新，即使已有值
     */
    autoFillUpgradePageIP(force = false) {
      // 如果已经有值且不是强制更新，不自动填充
      if (this.upgradePageIP && !force) return

      // 查找包含luna组件的服务器
      const upgradeServer = this.findServerWithComponent('luna')
      if (upgradeServer) {
        const ip = upgradeServer.IP地址
        const port = upgradeServer.组件端口['luna'] || this.getComponentDefaultPort('luna')
        this.upgradePageIP = `http://${ip}:${port.split('/')[0]}`
        this.autoFilledFields.upgradePage = true
        this.updateUpgradePageIP()
      } else {
        // 如果没有找到组件，清空字段
        if (force && this.upgradePageIP && this.autoFilledFields.upgradePage) {
          this.upgradePageIP = ''
          this.autoFilledFields.upgradePage = false
          this.updateUpgradePageIP()
        }
      }
    },

    /**
     * 查找包含指定组件的服务器
     * @param {String} componentName - 组件名称
     * @returns {Object|null} - 包含该组件的服务器对象或null
     */
    findServerWithComponent(componentName) {
      if (!this.serverList || this.serverList.length === 0) {
        return null
      }

      // 查找包含指定组件的服务器
      for (const server of this.serverList) {
        if (server.部署应用 && server.部署应用.includes(componentName) && server.IP地址) {
          return server
        }
      }

      return null
    },

    /**
     * 获取组件的默认端口
     * @param {String} componentName - 组件名称
     * @returns {String} - 组件的默认端口
     */
    getComponentDefaultPort(componentName) {
      // 默认端口映射
      const defaultPorts = {
        'front-ssp-admin': '8200/tcp',
        'front-ssp-user': '8100/tcp',
        'luna': '9001/tcp',
        'backend-ssp-user': '9120/tcp'
      }
      return defaultPorts[componentName] || '无'
    },

    /**
     * 自动填充对外服务端口
     * 使用backend-ssp-user组件所在的IP地址和端口
     * @param {Boolean} force - 是否强制更新，即使已有值
     */
    autoFillExternalServicePort(force = false) {
      // 如果已经有值且不是强制更新，不自动填充
      if (this.externalServicePort && !force) return

      // 查找包含backend-ssp-user组件的服务器
      const serviceServer = this.findServerWithComponent('backend-ssp-user')
      if (serviceServer) {
        const ip = serviceServer.IP地址
        const port = serviceServer.组件端口['backend-ssp-user'] || this.getComponentDefaultPort('backend-ssp-user')
        this.externalServicePort = `${ip}:${port.split('/')[0]}`
        this.autoFilledFields.externalService = true
        this.updateExternalServicePort()
      } else {
        // 如果没有找到组件，清空字段
        if (force && this.externalServicePort && this.autoFilledFields.externalService) {
          this.externalServicePort = ''
          this.autoFilledFields.externalService = false
          this.updateExternalServicePort()
        }
      }
    },

    // 更新方法
    updateAdminPageIP() {
      this.$emit('update:adminPage', this.adminPageIP)
    },
    updateSuperAdminAccount() {
      // 标记为用户手动编辑
      this.userEditedFields.superAdminAccount = true
      this.$emit('update:superAdmin', this.superAdminAccount)
    },
    updateSuperAdminPassword() {
      // 标记为用户手动编辑
      this.userEditedFields.superAdminPassword = true
      this.$emit('update:superAdminPwd', this.superAdminPassword)
    },
    updateAdminAccount() {
      // 标记为用户手动编辑
      this.userEditedFields.adminAccount = true
      this.$emit('update:admin', this.adminAccount)
    },
    updateAdminPassword() {
      // 标记为用户手动编辑
      this.userEditedFields.adminPassword = true
      this.$emit('update:adminPwd', this.adminPassword)
    },
    updateUserAccount() {
      // 标记为用户手动编辑
      this.userEditedFields.userAccount = true
      this.$emit('update:user', this.userAccount)
    },
    updateUserPassword() {
      // 标记为用户手动编辑
      this.userEditedFields.userPassword = true
      this.$emit('update:userPwd', this.userPassword)
    },
    updateUserPageIP() {
      this.$emit('update:userPage', this.userPageIP)
    },
    updateUpgradePageIP() {
      this.$emit('update:upgradePage', this.upgradePageIP)
    },
    updateUpgradeAccount() {
      // 标记为用户手动编辑
      this.userEditedFields.upgradeAccount = true
      this.$emit('update:upgradeUser', this.upgradeAccount)
    },
    updateUpgradePassword() {
      // 标记为用户手动编辑
      this.userEditedFields.upgradePassword = true
      this.$emit('update:upgradeUserPwd', this.upgradePassword)
    },
    updateExternalServicePort() {
      this.$emit('update:externalService', this.externalServicePort)
    }
  }
}
</script>

<style scoped>

/* 访问信息子部分样式 */
.access-subsection {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #dee2e6;
  transition: all 0.3s ease;
}

.access-subsection:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.subsection-header {
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 0.75rem;
  margin-bottom: 1rem;
}

.subsection-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
}

.subsection-content {
  padding-top: 0.5rem;
}

/* 账号卡片样式 */
.account-card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
  transition: all 0.3s ease;
}

.account-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.account-card-header {
  background: #f8f9fa;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.9rem;
}

.account-card-body {
  padding: 1rem;
}

/* 信息说明样式 */
.info-section {
  background: #e7f3ff;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #17a2b8;
  margin-top: 1rem;
}

.info-content p {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

/* 输入组样式 */
.input-group-text {
  min-width: 80px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 表单控件样式 */
.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .access-subsection {
    padding: 1rem;
  }

  .account-card-body {
    padding: 0.75rem;
  }

  .input-group-text {
    min-width: 70px;
    font-size: 0.8rem;
  }
}
</style>
