[project]
name = "export-excel-backend"
version = "1.0.0"
description = "Export Excel Backend Application"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Web框架核心
    "Flask==2.3.3",
    "Werkzeug==2.3.7",
    "Jinja2==3.1.2",
    "MarkupSafe==2.1.3",
    "itsdangerous==2.1.2",
    "click==8.1.7",
    "blinker==1.6.3",

    # 认证与安全
    "Flask-JWT-Extended==4.5.3",
    "Flask-Bcrypt==1.0.1",
    "PyJWT==2.8.0",
    "bcrypt==4.0.1",
    "cryptography==41.0.7",

    # 数据库相关
    "Flask-SQLAlchemy==3.0.5",
    "SQLAlchemy==1.4.46",
    "Flask-Migrate==4.0.5",
    "alembic==1.12.1",
    "PyMySQL==1.1.0",

    # 数据处理与Excel
    "pandas==2.1.4",
    "numpy==1.26.4",
    "openpyxl==3.1.2",
    "python-dateutil==2.8.2",
    "pytz==2023.3",
    "six==1.16.0",

    # 缓存与性能
    "Flask-Caching==2.1.0",
    "redis==5.0.1",
    "psutil==5.9.6",

    # HTTP与网络
    "Flask-Cors==4.0.0",
    "requests==2.31.0",
    "urllib3==2.0.7",
    "certifi==2023.11.17",

    # 图像处理
    "Pillow==10.1.0",

    # 表单处理
    "Flask-WTF==1.1.1",
    "WTForms==3.1.1",

    # 工具与实用程序
    "python-dotenv==1.0.1",
    "watchdog==3.0.0",

    # 生产环境部署
    "gunicorn==21.2.0",

    # 其他依赖
    "colorama==0.4.6",
    "Mako==1.3.0",
    "typing_extensions==4.8.0",
]

[project.optional-dependencies]
dev = [
    # 测试框架
    "pytest>=7.0.0",
    "pytest-flask>=1.2.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",

    # 代码质量
    "black>=22.0.0",
    "flake8>=5.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "pylint>=2.17.0",

    # 安全检查
    "bandit>=1.7.0",
    "safety>=2.3.0",

    # 开发工具
    "ipython>=8.10.0",
    "pre-commit>=3.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.uv]
# 生产环境不安装开发依赖
dev-dependencies = []

# uv配置优化 - 使用正确的配置格式
[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true

[[tool.uv.index]]
name = "douban"
url = "http://pypi.douban.com/simple"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
