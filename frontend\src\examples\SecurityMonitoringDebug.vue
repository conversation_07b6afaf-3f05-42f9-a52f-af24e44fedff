<template>
  <div class="security-monitoring-debug">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-bug me-2"></i>
                安全监测表单数据流调试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 操作面板 -->
              <div class="row mb-4">
                <div class="col-md-8">
                  <button class="btn btn-primary me-2" @click="fillTestData">
                    填充测试数据
                  </button>
                  <button class="btn btn-success me-2" @click="validateAndSubmit">
                    验证并模拟提交
                  </button>
                  <button class="btn btn-warning me-2" @click="clearData">
                    清空数据
                  </button>
                  <button class="btn btn-info" @click="exportData">
                    导出数据
                  </button>
                </div>
                <div class="col-md-4">
                  <div class="alert alert-info mb-0 py-2">
                    <strong>状态：</strong>
                    <span :class="isDataValid ? 'text-success' : 'text-danger'">
                      {{ isDataValid ? '数据完整' : '数据不完整' }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 表单 -->
              <div class="row mb-4">
                <div class="col-12">
                  <h6>安全监测表单</h6>
                  <div class="card">
                    <div class="card-body">
                      <security-monitoring-form
                        v-model="testFormData"
                        :component-groups="componentGroups"
                        :key="`form-${refreshKey}`"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 数据检查面板 -->
              <div class="row">
                <div class="col-md-4">
                  <h6>必填字段检查</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div v-for="field in requiredFields" :key="field" class="mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                          <strong>{{ field }}:</strong>
                          <span v-if="testFormData[field]" class="badge bg-success">
                            ✓
                          </span>
                          <span v-else class="badge bg-danger">
                            ✗
                          </span>
                        </div>
                        <div class="small text-muted">
                          {{ testFormData[field] || '(未填写)' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-4">
                  <h6>提交数据预览</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div class="mb-2">
                        <strong>文档后缀:</strong> {{ testFormData.文档后缀 }}
                      </div>
                      <div class="mb-2">
                        <strong>公司名称:</strong> {{ testFormData.公司名称 }}
                      </div>
                      <div class="mb-2">
                        <strong>前端版本:</strong> 
                        <span :class="testFormData.前端版本 ? 'text-success' : 'text-danger'">
                          {{ testFormData.前端版本 || '(空)' }}
                        </span>
                      </div>
                      <div class="mb-2">
                        <strong>后端版本:</strong> 
                        <span :class="testFormData.后端版本 ? 'text-success' : 'text-danger'">
                          {{ testFormData.后端版本 || '(空)' }}
                        </span>
                      </div>
                      <div class="mb-2">
                        <strong>记录日期:</strong> {{ testFormData.记录日期 }}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-4">
                  <h6>验证结果</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div v-if="validationResult">
                        <div v-if="validationResult.success" class="text-success">
                          <i class="bi bi-check-circle"></i>
                          验证通过，可以提交
                        </div>
                        <div v-else class="text-danger">
                          <i class="bi bi-x-circle"></i>
                          验证失败
                          <div class="mt-2">
                            <strong>缺少字段:</strong>
                            <ul class="mb-0">
                              <li v-for="field in validationResult.missingFields" :key="field">
                                {{ field }}
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <div v-else class="text-muted">
                        点击"验证并模拟提交"查看结果
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 完整数据展示 -->
              <div class="row mt-4">
                <div class="col-12">
                  <h6>完整表单数据 (JSON)</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <pre class="small">{{ JSON.stringify(testFormData, null, 2) }}</pre>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 提交结果 -->
              <div v-if="submitResult" class="row mt-4">
                <div class="col-12">
                  <h6>模拟提交结果</h6>
                  <div class="alert" :class="submitResult.success ? 'alert-success' : 'alert-danger'">
                    <div class="d-flex justify-content-between align-items-start">
                      <div>
                        <strong>{{ submitResult.success ? '✅ 提交成功' : '❌ 提交失败' }}</strong>
                        <div class="mt-2">{{ submitResult.message }}</div>
                        <div v-if="submitResult.details" class="mt-2 small">
                          <strong>详细信息:</strong>
                          <pre>{{ JSON.stringify(submitResult.details, null, 2) }}</pre>
                        </div>
                      </div>
                      <button class="btn btn-sm btn-outline-secondary" @click="submitResult = null">
                        关闭
                      </button>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SecurityMonitoringForm from '@/components/forms/securityMonitoring/SecurityMonitoringForm.vue'
import { getInitialFormData } from '@/config/formDataConfig'

export default {
  name: 'SecurityMonitoringDebug',
  components: {
    SecurityMonitoringForm
  },
  data() {
    return {
      testFormData: {},
      componentGroups: {},
      refreshKey: 0,
      validationResult: null,
      submitResult: null,
      requiredFields: ['公司名称', '前端版本', '后端版本', '记录日期']
    }
  },
  computed: {
    isDataValid() {
      return this.requiredFields.every(field => this.testFormData[field])
    }
  },
  methods: {
    /**
     * 填充测试数据
     */
    fillTestData() {
      // 获取初始数据
      this.testFormData = getInitialFormData('安全监测')
      
      // 填充测试值
      this.testFormData.公司名称 = '测试公司有限公司'
      this.testFormData.前端版本 = 'V5.1.2sp2'
      this.testFormData.后端版本 = 'V5.1.2sp2'
      this.testFormData.记录日期 = new Date().toISOString().split('T')[0]
      this.testFormData.客户标识 = 'TEST001'
      this.testFormData.日活 = '10000'
      this.testFormData.标准或定制 = '标准版'
      
      console.log('填充测试数据完成:', this.testFormData)
      this.refreshKey += 1
    },

    /**
     * 验证并模拟提交
     */
    async validateAndSubmit() {
      try {
        console.log('开始验证数据...')
        
        // 前端验证
        const missingFields = this.requiredFields.filter(field => !this.testFormData[field])
        
        if (missingFields.length > 0) {
          this.validationResult = {
            success: false,
            missingFields
          }
          
          this.submitResult = {
            success: false,
            message: `前端验证失败：缺少必要字段 ${missingFields.join(', ')}`,
            details: {
              missingFields,
              currentData: this.requiredFields.reduce((obj, field) => {
                obj[field] = this.testFormData[field] || null
                return obj
              }, {})
            }
          }
          return
        }

        this.validationResult = {
          success: true,
          missingFields: []
        }

        // 模拟后端提交
        console.log('模拟提交到后端...')
        
        const submitData = {
          ...this.testFormData,
          文档后缀: '安全监测'
        }

        // 模拟后端验证逻辑
        const backendRequiredFields = ['公司名称', '前端版本', '后端版本', '记录日期']
        const backendMissingFields = backendRequiredFields.filter(field => !submitData[field])
        
        if (backendMissingFields.length > 0) {
          this.submitResult = {
            success: false,
            message: `后端验证失败：缺少必要字段 ${backendMissingFields.join(', ')}`,
            details: {
              backendMissingFields,
              submitData: backendRequiredFields.reduce((obj, field) => {
                obj[field] = submitData[field] || null
                return obj
              }, {})
            }
          }
        } else {
          this.submitResult = {
            success: true,
            message: '验证通过！数据可以正常提交生成Excel',
            details: {
              submittedFields: backendRequiredFields.reduce((obj, field) => {
                obj[field] = submitData[field]
                return obj
              }, {}),
              formType: submitData.文档后缀
            }
          }
        }
        
        console.log('验证结果:', this.submitResult)
        
      } catch (error) {
        console.error('验证过程出错:', error)
        this.submitResult = {
          success: false,
          message: `验证过程出错: ${error.message}`,
          details: { error: error.toString() }
        }
      }
    },

    /**
     * 清空数据
     */
    clearData() {
      this.testFormData = getInitialFormData('安全监测')
      this.validationResult = null
      this.submitResult = null
      this.refreshKey += 1
      console.log('数据已清空')
    },

    /**
     * 导出数据
     */
    exportData() {
      const dataStr = JSON.stringify(this.testFormData, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `安全监测表单数据_${new Date().toISOString().split('T')[0]}.json`
      link.click()
      URL.revokeObjectURL(url)
      console.log('数据已导出')
    }
  },
  watch: {
    testFormData: {
      handler(newData) {
        console.log('表单数据变化:', {
          前端版本: newData.前端版本,
          后端版本: newData.后端版本,
          公司名称: newData.公司名称,
          记录日期: newData.记录日期,
          文档后缀: newData.文档后缀
        })
      },
      deep: true
    }
  },
  mounted() {
    console.log('SecurityMonitoringDebug 组件已挂载')
    this.testFormData = getInitialFormData('安全监测')
    
    // 加载组件分组数据
    this.componentGroups = {
      security: {
        'security-monitor': [
          { name: 'web-service-nginx', description: 'Web服务', port: '443' },
          { name: 'init', description: '初始化服务', port: '8181' },
          { name: 'kibana', description: 'Kibana服务', port: '5601' }
        ]
      }
    }
  }
}
</script>

<style scoped>
.security-monitoring-debug {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
}

pre {
  max-height: 400px;
  overflow-y: auto;
  font-size: 0.75rem;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
}

.text-success { color: #198754 !important; }
.text-danger { color: #dc3545 !important; }
.text-muted { color: #6c757d !important; }

.badge {
  font-size: 0.75rem;
}
</style>
