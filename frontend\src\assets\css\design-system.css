/**
 * 梆梆安全-运维信息登记平台 设计系统
 * 统一的设计语言和视觉规范
 */

/* ==================== CSS 变量定义 ==================== */
:root {
  /* 主色调 */
  --primary-color: #007bff;
  --primary-light: #66b3ff;
  --primary-dark: #0056b3;
  --primary-gradient: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  
  /* 功能色彩 */
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  
  /* 中性色彩 */
  --gray-50: #f8f9fa;
  --gray-100: #e9ecef;
  --gray-200: #dee2e6;
  --gray-300: #ced4da;
  --gray-400: #adb5bd;
  --gray-500: #6c757d;
  --gray-600: #495057;
  --gray-700: #343a40;
  --gray-800: #212529;
  --gray-900: #000000;
  
  /* 表单类型主题色 */
  --security-monitoring: #007bff;    /* 安全监测 - 蓝色 */
  --security-testing: #28a745;       /* 安全测评 - 绿色 */
  --app-hardening: #ff9800;          /* 应用加固 - 橙色 */
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  
  /* 圆角系统 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* 间距系统 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* 字体系统 */
  --font-family-base: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* 动画系统 */
  --transition-fast: 0.15s ease-in-out;
  --transition-base: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ==================== 全局基础样式 ==================== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-base);
  line-height: 1.6;
  color: var(--gray-700);
  background-color: #f5f7fa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==================== 卡片组件增强 ==================== */
.card-enhanced {
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
  background: white;
  overflow: hidden;
}

.card-enhanced:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-enhanced .card-header {
  background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--spacing-lg);
  font-weight: 600;
  color: var(--gray-700);
}

.card-enhanced .card-body {
  padding: var(--spacing-lg);
}

/* 表单类型特定卡片样式 */
.card-security-monitoring {
  border-left: 4px solid var(--security-monitoring);
}

.card-security-monitoring .card-header {
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, white 100%);
  color: var(--security-monitoring);
}

.card-security-testing {
  border-left: 4px solid var(--security-testing);
}

.card-security-testing .card-header {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, white 100%);
  color: var(--security-testing);
}

.card-app-hardening {
  border-left: 4px solid var(--app-hardening);
}

.card-app-hardening .card-header {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.1) 0%, white 100%);
  color: var(--app-hardening);
}

/* ==================== 按钮系统增强 ==================== */
.btn-enhanced {
  border-radius: var(--radius-md);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all var(--transition-base);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-base);
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-primary-enhanced {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-success-enhanced {
  background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-warning-enhanced {
  background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
  color: var(--gray-800);
  box-shadow: var(--shadow-sm);
}

.btn-danger-enhanced {
  background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
  color: white;
  box-shadow: var(--shadow-sm);
}

/* ==================== 表单增强样式 ==================== */
.form-enhanced .form-control {
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  transition: all var(--transition-base);
  background-color: white;
}

.form-enhanced .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
  background-color: white;
}

.form-enhanced .form-control:hover:not(:focus) {
  border-color: var(--gray-300);
}

/* 浮动标签增强 */
.form-floating-enhanced {
  position: relative;
}

.form-floating-enhanced .form-control {
  height: calc(3.5rem + 2px);
  padding: 1rem 0.75rem;
}

.form-floating-enhanced label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  padding: 1rem 0.75rem;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity var(--transition-base), transform var(--transition-base);
  color: var(--gray-500);
  font-weight: 500;
}

.form-floating-enhanced .form-control:focus ~ label,
.form-floating-enhanced .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.75;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  color: var(--primary-color);
  font-weight: 600;
}

/* ==================== 导航栏增强 ==================== */
/* 移除所有导航栏增强样式，回退到原始状态 */

/* ==================== 下拉菜单增强 ==================== */
.dropdown-menu-enhanced {
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-sm);
  background: white;
  backdrop-filter: blur(10px);
}

.dropdown-menu-enhanced .dropdown-item {
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  transition: all var(--transition-base);
  font-weight: 500;
}

.dropdown-menu-enhanced .dropdown-item:hover {
  background-color: var(--gray-50);
  transform: translateX(4px);
  color: var(--primary-color);
}

/* ==================== 状态指示器 ==================== */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-warning {
  background-color: rgba(255, 193, 7, 0.1);
  color: #b45309;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-info {
  background-color: rgba(23, 162, 184, 0.1);
  color: var(--info-color);
  border: 1px solid rgba(23, 162, 184, 0.2);
}

/* ==================== 加载动画 ==================== */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-200);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-skeleton {
  background: linear-gradient(90deg, var(--gray-100) 25%, var(--gray-200) 50%, var(--gray-100) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ==================== 响应式优化 ==================== */
@media (max-width: 768px) {
  :root {
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
  }
  
  .card-enhanced {
    margin-bottom: var(--spacing-md);
    border-radius: var(--radius-md);
  }
  
  .card-enhanced .card-header,
  .card-enhanced .card-body {
    padding: var(--spacing-md);
  }
  
  .btn-enhanced {
    padding: 0.625rem 1.25rem;
    font-size: var(--font-size-sm);
  }
  
  .navbar-enhanced .nav-link {
    padding: 0.5rem 0.75rem !important;
  }
}

/* ==================== 工具类 ==================== */
.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
  transition: transform var(--transition-base);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
