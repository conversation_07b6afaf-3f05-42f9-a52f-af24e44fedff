<template>
  <collapsible-card card-class="border-dark" storage-key="remarks-section">
    <template #header>
      <i class="bi bi-pencil-square me-2"></i>备注信息
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-dark">{{ remarks ? '已填写备注' : '未填写备注' }}</span>
        <span v-if="remarks" class="text-muted small text-truncate" style="max-width: 300px;">{{ remarks }}</span>
      </div>
    </template>
    <div class="form-floating">
      <textarea
        class="form-control"
        :id="id"
        v-model="remarks"
        style="height: 100px"
        placeholder="请输入备注信息（可选）"
        @input="updateRemarks"
      ></textarea>
      <label :for="id">备注信息</label>
    </div>
  </collapsible-card>
</template>

<script>
/**
 * 备注信息组件
 * 用于填写备注信息
 */
import CollapsibleCard from './CollapsibleCard.vue'

export default {
  name: 'RemarksSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 备注内容
    modelValue: {
      type: String,
      default: ''
    },
    // 组件ID，用于区分不同表单中的备注组件
    id: {
      type: String,
      default: 'remarks'
    }
  },
  data() {
    return {
      remarks: this.modelValue
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    modelValue(newVal) {
      this.remarks = newVal
    }
  },
  methods: {
    /**
     * 更新备注内容
     * 向父组件发送update:modelValue事件
     */
    updateRemarks() {
      this.$emit('update:modelValue', this.remarks)
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
</style>
