import { ref, computed, watch } from 'vue'
import { getFormFieldConfig, getFormValidationRules } from '@/config/formFieldConfig'

/**
 * 统一的表单数据处理 Composable
 * 提供统一的数据绑定、字段映射和验证逻辑
 * @param {string} formType - 表单类型
 * @param {Object} initialData - 初始数据
 * @returns {Object} 表单数据处理对象
 */
export function useFormData(formType, initialData) {
  const formData = ref({ ...initialData })
  const fieldConfig = computed(() => getFormFieldConfig(formType))
  
  /**
   * 统一的基本信息字段映射
   * 将配置中的字段映射到表单数据
   */
  const basicInfoValues = computed({
    get() {
      const config = fieldConfig.value.basic || {}
      const values = {}

      // 处理新配置格式的字段映射
      Object.keys(config).forEach(key => {
        const fieldName = config[key].field
        values[key] = formData.value[fieldName] || config[key].default || ''
      })

      // 统一的反向映射规则 - 从表单数据映射到组件数据
      const reverseMappings = {
        // 安全测评字段反向映射
        '客户标识': 'customerId',
        '部署包版本': 'deploymentVersion',

        // 安全监测字段反向映射
        '前端版本': 'frontendVersion',
        '后端版本': 'backendVersion',
        '日活': 'dailyActive',
        '标准或定制': 'standardOrCustom',

        // 应用加固字段反向映射
        '客户': 'customer',
        '部署的平台版本': 'platformVersion',

        // 通用字段反向映射
        '公司名称': 'companyName',
        '记录日期': 'recordDate',
        '编辑人': 'editor',
        '版本信息': 'versionInfo'
      }

      // 自动处理所有反向映射
      Object.keys(reverseMappings).forEach(chineseField => {
        const englishKey = reverseMappings[chineseField]
        if (!values[englishKey] && formData.value[chineseField]) {
          values[englishKey] = formData.value[chineseField]
          console.log(`反向映射: ${chineseField} -> ${englishKey} = ${formData.value[chineseField]}`)
        }
      })

      return values
    },
    set(newValues) {
      const config = fieldConfig.value.basic || {}

      Object.keys(newValues).forEach(key => {
        const fieldName = config[key]?.field
        if (fieldName) {
          formData.value[fieldName] = newValues[key]
        } else {
          // 处理直接字段映射 - 统一的字段映射规则
          const fieldMappings = {
            // 安全测评字段映射
            'customerId': '客户标识',
            'deploymentVersion': '部署包版本',

            // 安全监测字段映射
            'frontendVersion': '前端版本',
            'backendVersion': '后端版本',
            'dailyActive': '日活',
            'standardOrCustom': '标准或定制',

            // 应用加固字段映射
            'customer': '客户',
            'platformVersion': '部署的平台版本',

            // 通用字段映射
            'companyName': '公司名称',
            'recordDate': '记录日期',
            'editor': '编辑人',
            'versionInfo': '版本信息'
          }

          if (fieldMappings[key]) {
            formData.value[fieldMappings[key]] = newValues[key]
            console.log(`字段映射: ${key} -> ${fieldMappings[key]} = ${newValues[key]}`)
          } else {
            console.warn(`未找到字段映射: ${key}`)
          }
        }
      })
    }
  })

  /**
   * 统一的访问信息数据映射
   * 将配置中的访问信息字段映射到表单数据
   */
  const accessInfoData = computed({
    get() {
      const config = fieldConfig.value.access || {}
      const data = {}
      
      Object.keys(config).forEach(key => {
        const fieldName = config[key].field
        const defaultValue = config[key].default || ''
        
        // 处理对象类型的字段（如升级用户配置）
        if (config[key].type === 'object') {
          data[key] = formData.value[fieldName] || defaultValue
        } else {
          data[key] = formData.value[fieldName] || defaultValue
        }
      })
      
      return data
    },
    set(newData) {
      const config = fieldConfig.value.access || {}
      
      Object.keys(newData).forEach(key => {
        const fieldName = config[key]?.field
        if (fieldName) {
          formData.value[fieldName] = newData[key]
        }
      })
    }
  })

  /**
   * 统一的表单验证
   * 根据配置的验证规则进行表单验证
   * @returns {boolean} 验证是否通过
   * @throws {Error} 验证失败时抛出错误
   */
  const validateForm = () => {
    const rules = getFormValidationRules(formType)
    const missingFields = rules.required.filter(field => {
      const value = formData.value[field]
      return !value || (typeof value === 'string' && !value.trim())
    })
    
    if (missingFields.length > 0) {
      throw new Error(`请填写以下必填字段: ${missingFields.join(', ')}`)
    }
    return true
  }

  /**
   * 获取字段的默认值
   * @param {string} fieldKey - 字段键
   * @param {string} section - 字段所属部分（basic/access）
   * @returns {any} 默认值
   */
  const getFieldDefault = (fieldKey, section = 'basic') => {
    const config = fieldConfig.value[section]?.[fieldKey]
    return config?.default || ''
  }

  /**
   * 检查字段是否必填
   * @param {string} fieldName - 字段名称
   * @returns {boolean} 是否必填
   */
  const isFieldRequired = (fieldName) => {
    const rules = getFormValidationRules(formType)
    return rules.required.includes(fieldName)
  }

  /**
   * 重置表单数据到初始状态
   */
  const resetFormData = () => {
    // 保留基本信息
    const basicInfo = {
      公司名称: formData.value.公司名称,
      文档后缀: formData.value.文档后缀,
      编辑人: formData.value.编辑人,
      记录日期: formData.value.记录日期
    }
    
    // 重置为初始数据
    formData.value = { ...initialData, ...basicInfo }
  }

  /**
   * 更新表单数据
   * @param {Object} newData - 新的表单数据
   */
  const updateFormData = (newData) => {
    formData.value = { ...formData.value, ...newData }
  }

  // 监听外部数据变化，同步更新内部数据
  watch(() => initialData, (newData) => {
    if (newData && typeof newData === 'object') {
      formData.value = { ...newData }
    }
  }, { deep: true })

  return {
    formData,
    fieldConfig,
    basicInfoValues,
    accessInfoData,
    validateForm,
    getFieldDefault,
    isFieldRequired,
    resetFormData,
    updateFormData
  }
}

/**
 * 表单字段映射工具函数
 * 用于处理不同表单类型之间的字段映射
 */
export const FIELD_MAPPING = {
  // 公司名称相关字段
  companyName: {
    '安全测评': '公司名称',
    '安全监测': '公司名称', 
    '应用加固': ['公司名称', '客户'] // 应用加固有两个相关字段
  },
  
  // 版本信息相关字段
  version: {
    '安全测评': '部署包版本',
    '安全监测': ['前端版本', '后端版本'],
    '应用加固': '部署的平台版本'
  }
}

/**
 * 获取字段映射
 * @param {string} formType - 表单类型
 * @param {string} fieldType - 字段类型
 * @returns {string|Array} 映射的字段名
 */
export function getFieldMapping(formType, fieldType) {
  return FIELD_MAPPING[fieldType]?.[formType] || null
}
