/**
 * 表单字段调试工具
 * 用于调试表单字段配置和过滤逻辑
 */

import {
  getBasicInfoFieldsByFormType,
  getCustomerInfoFieldsByFormType,
  getDeploymentInfoFieldsByFormType,
  deploymentInfoFields
} from '@/config/formFields'

/**
 * 调试表单字段配置
 * @param {string} formType - 表单类型
 * @returns {Object} 调试信息
 */
export function debugFormFields(formType) {
  console.log(`\n=== 调试表单字段配置: ${formType} ===`)
  
  // 获取原始配置
  const rawDeploymentFields = deploymentInfoFields
  console.log('原始部署字段配置:', rawDeploymentFields)
  
  // 获取过滤后的配置
  const basicFields = getBasicInfoFieldsByFormType(formType)
  const customerFields = getCustomerInfoFieldsByFormType(formType)
  const deploymentFields = getDeploymentInfoFieldsByFormType(formType)
  
  console.log('基本字段配置:', basicFields)
  console.log('客户字段配置:', customerFields)
  console.log('部署字段配置:', deploymentFields)
  
  // 分析部署字段过滤
  console.log('\n=== 部署字段过滤分析 ===')
  rawDeploymentFields.forEach((field, index) => {
    const isVisible = !field.formTypes || field.formTypes.includes(formType)
    console.log(`字段 ${index}: ${field.id}`)
    console.log(`  formTypes: ${field.formTypes ? field.formTypes.join(', ') : '无限制'}`)
    console.log(`  当前表单类型: ${formType}`)
    console.log(`  是否显示: ${isVisible}`)
    console.log('---')
  })
  
  return {
    formType,
    rawDeploymentFields,
    basicFields,
    customerFields,
    deploymentFields,
    analysis: {
      basicCount: basicFields.length,
      customerCount: customerFields.length,
      deploymentCount: deploymentFields.length,
      rawDeploymentCount: rawDeploymentFields.length
    }
  }
}

/**
 * 检查字段配置问题
 * @param {string} formType - 表单类型
 * @returns {Array} 问题列表
 */
export function checkFieldConfigIssues(formType) {
  const issues = []
  
  try {
    const deploymentFields = getDeploymentInfoFieldsByFormType(formType)
    
    // 检查是否有部署字段
    if (deploymentFields.length === 0) {
      issues.push({
        type: 'warning',
        message: `表单类型 "${formType}" 没有配置部署字段`
      })
    }
    
    // 检查字段配置格式
    deploymentFields.forEach((field, index) => {
      if (!field.id) {
        issues.push({
          type: 'error',
          message: `部署字段 ${index} 缺少 id 属性`
        })
      }
      
      if (!field.formTypes) {
        issues.push({
          type: 'info',
          message: `部署字段 ${field.id} 没有 formTypes 限制，将在所有表单中显示`
        })
      }
      
      if (field.type === 'row' && (!field.fields || field.fields.length === 0)) {
        issues.push({
          type: 'error',
          message: `行字段 ${field.id} 没有子字段配置`
        })
      }
    })
    
  } catch (error) {
    issues.push({
      type: 'error',
      message: `获取字段配置时发生错误: ${error.message}`
    })
  }
  
  return issues
}

/**
 * 生成字段配置报告
 * @param {Array} formTypes - 表单类型列表
 * @returns {string} 报告内容
 */
export function generateFieldConfigReport(formTypes = ['安全测评', '安全监测', '应用加固']) {
  let report = '\n=== 表单字段配置报告 ===\n\n'
  
  formTypes.forEach(formType => {
    report += `📋 ${formType}\n`
    
    const debugInfo = debugFormFields(formType)
    const issues = checkFieldConfigIssues(formType)
    
    report += `  基本字段: ${debugInfo.analysis.basicCount}\n`
    report += `  客户字段: ${debugInfo.analysis.customerCount}\n`
    report += `  部署字段: ${debugInfo.analysis.deploymentCount}\n`
    
    if (issues.length > 0) {
      report += '  问题:\n'
      issues.forEach(issue => {
        const icon = issue.type === 'error' ? '❌' : issue.type === 'warning' ? '⚠️' : 'ℹ️'
        report += `    ${icon} ${issue.message}\n`
      })
    } else {
      report += '  ✅ 无问题\n'
    }
    
    report += '\n'
  })
  
  return report
}

/**
 * 修复部署字段配置问题
 * @param {string} formType - 表单类型
 * @returns {Object} 修复建议
 */
export function suggestFieldConfigFix(formType) {
  const suggestions = []
  
  const deploymentFields = getDeploymentInfoFieldsByFormType(formType)
  
  if (deploymentFields.length === 0) {
    if (formType === '安全测评') {
      suggestions.push({
        type: 'add',
        message: '建议添加"部署包版本"字段',
        config: {
          id: 'row_deployment_version',
          type: 'row',
          formTypes: ['安全测评'],
          fields: [{
            id: 'deploymentVersion',
            type: 'text',
            label: '部署包版本',
            placeholder: '请输入部署包版本',
            required: true,
            columnClass: 'col-md-12'
          }]
        }
      })
    } else if (formType === '安全监测') {
      suggestions.push({
        type: 'add',
        message: '建议添加版本信息字段',
        config: {
          id: 'row_version_info',
          type: 'row',
          formTypes: ['安全监测'],
          fields: [
            {
              id: 'frontendVersion',
              type: 'text',
              label: '前端版本',
              placeholder: '例如：V5.1.2sp2',
              required: true,
              columnClass: 'col-md-4'
            },
            {
              id: 'backendVersion',
              type: 'text',
              label: '后端版本',
              placeholder: '例如：V5.1.2sp2',
              required: true,
              columnClass: 'col-md-4'
            }
          ]
        }
      })
    } else if (formType === '应用加固') {
      suggestions.push({
        type: 'add',
        message: '建议添加"部署的平台版本"字段',
        config: {
          id: 'row_platform_version',
          type: 'row',
          formTypes: ['应用加固'],
          fields: [{
            id: 'platformVersion',
            type: 'text',
            label: '部署的平台版本',
            placeholder: '请输入部署的平台版本',
            required: false,
            columnClass: 'col-md-12'
          }]
        }
      })
    }
  }
  
  return {
    formType,
    hasIssues: suggestions.length > 0,
    suggestions
  }
}

/**
 * 快速调试所有表单类型
 */
export function quickDebugAllFormTypes() {
  const formTypes = ['安全测评', '安全监测', '应用加固']
  
  console.log('\n🔍 快速调试所有表单类型')
  
  formTypes.forEach(formType => {
    debugFormFields(formType)
    const issues = checkFieldConfigIssues(formType)
    const fixes = suggestFieldConfigFix(formType)
    
    if (issues.length > 0 || fixes.hasIssues) {
      console.log(`\n⚠️ ${formType} 存在问题:`)
      issues.forEach(issue => console.log(`  - ${issue.message}`))
      fixes.suggestions.forEach(suggestion => console.log(`  - ${suggestion.message}`))
    } else {
      console.log(`\n✅ ${formType} 配置正常`)
    }
  })
  
  return generateFieldConfigReport(formTypes)
}
