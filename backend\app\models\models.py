from datetime import datetime, timezone, timedelta
from app import db
import json

# 定义北京时区
BEIJING_TZ = timezone(timedelta(hours=8))

def get_beijing_time():
    """获取北京时间"""
    return datetime.now(BEIJING_TZ)

class ExcelFile(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)
    history_records = db.relationship('HistoryRecord', backref='excel_file', lazy=True)

    def to_dict(self):
        """将模型转换为字典，方便JSON序列化"""
        return {
            'id': self.id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class HistoryRecord(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    excel_file_id = db.Column(db.Integer, db.ForeignKey('excel_file.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # create, update, delete
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    user = db.Column(db.String(100))

    def to_dict(self):
        """将模型转换为字典，方便JSON序列化"""
        return {
            'id': self.id,
            'excel_file_id': self.excel_file_id,
            'action': self.action,
            'description': self.description,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'user': self.user
        }

class TemplateVersion(db.Model):
    """模板版本表，用于存储模板文件的版本信息"""
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False, unique=True)
    template_type = db.Column(db.String(50), nullable=False)  # 安全测评、安全监测、应用加固
    is_active = db.Column(db.Boolean, default=False)  # 是否为当前活动模板
    alias = db.Column(db.String(100), nullable=True)  # 模板别名
    form_type = db.Column(db.String(50), nullable=True)  # 关联的表单类型
    is_backup = db.Column(db.Boolean, default=False)  # 兼容旧版本
    backup_of = db.Column(db.String(255), nullable=True)  # 兼容旧版本
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    last_modified = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

    def to_dict(self):
        """将模型转换为字典，方便JSON序列化"""
        return {
            'id': self.id,
            'filename': self.filename,
            'template_type': self.template_type,
            'is_active': self.is_active,
            'alias': self.alias,
            'form_type': self.form_type,
            'is_backup': self.is_backup,
            'backup_of': self.backup_of,
            'created_at': self.created_at.isoformat(),
            'last_modified': self.last_modified.isoformat()
        }

class ComponentCategory(db.Model):
    """组件分类表，用于管理组件分类信息"""
    __tablename__ = 'component_category'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), nullable=False, unique=True)  # 分类键名，如'base', 'database'
    display_name = db.Column(db.String(100), nullable=False)     # 显示名称，如'基础组件'
    icon = db.Column(db.String(50), nullable=True)               # 图标类名，如'bi-box'
    color = db.Column(db.String(20), nullable=True)              # 颜色，如'bg-primary'
    order = db.Column(db.Integer, default=0)                     # 排序权重
    form_type = db.Column(db.String(50), nullable=True)          # 支持的表单类型(单一)
    form_types = db.Column(db.Text, nullable=True)               # 适用表单类型(JSON格式,兼容旧版)
    is_active = db.Column(db.Boolean, default=True)              # 是否启用
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

    # 关联的组件
    components = db.relationship('ComponentConfig', backref='category_info', lazy=True)

    def set_form_types(self, form_types_list):
        """设置表单类型(兼容旧版)"""
        self.form_types = json.dumps(form_types_list, ensure_ascii=False) if form_types_list else None
        # 同时设置新的单一表单类型字段
        if form_types_list and len(form_types_list) > 0:
            self.form_type = form_types_list[0]  # 取第一个作为主要表单类型

    def get_form_types(self):
        """获取表单类型列表(兼容旧版)"""
        # 优先使用新的单一表单类型字段
        if self.form_type:
            return [self.form_type]

        # 兼容旧版的多表单类型字段
        try:
            return json.loads(self.form_types) if self.form_types else []
        except json.JSONDecodeError:
            return []

    def set_form_type(self, form_type):
        """设置单一表单类型(新版)"""
        self.form_type = form_type
        # 同时更新旧版字段以保持兼容性
        self.form_types = json.dumps([form_type], ensure_ascii=False) if form_type else None

    def get_form_type(self):
        """获取单一表单类型(新版)"""
        return self.form_type

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'key': self.key,
            'display_name': self.display_name,
            'icon': self.icon,
            'color': self.color,
            'order': self.order,
            'form_type': self.get_form_type(),
            'form_types': self.get_form_types(),  # 兼容旧版
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


class ComponentConfig(db.Model):
    """组件配置表，用于存储不同表单类型的组件信息"""
    __tablename__ = 'component_config'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 组件名称
    display_name = db.Column(db.String(100), nullable=False)  # 显示名称
    version = db.Column(db.String(100), nullable=True)  # 版本信息
    default_port = db.Column(db.String(20), nullable=True)  # 默认端口
    description = db.Column(db.Text, nullable=True)  # 描述
    category_key = db.Column(db.String(50), db.ForeignKey('component_category.key'), nullable=False)  # 分类键名
    form_type = db.Column(db.String(50), nullable=False)  # 表单类型
    protocol = db.Column(db.String(10), nullable=True)  # 协议，如'http', 'https'
    alias = db.Column(db.String(100), nullable=True)  # 组件别名
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    order = db.Column(db.Integer, default=0)  # 在分类中的排序
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

    def to_dict(self):
        """将模型转换为字典，方便JSON序列化"""
        return {
            'id': self.id,
            'name': self.name,
            'display_name': self.display_name,
            'version': self.version,
            'default_port': self.default_port,
            'description': self.description,
            'category_key': self.category_key,
            'form_type': self.form_type,
            'protocol': self.protocol,
            'alias': self.alias,
            'is_active': self.is_active,
            'order': self.order,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            # 包含分类信息
            'category_info': self.category_info.to_dict() if self.category_info else None
        }


class FormSubmission(db.Model):
    """表单提交记录表，用于存储每次表单提交的完整数据"""
    __tablename__ = 'form_submission'

    id = db.Column(db.Integer, primary_key=True)

    # 基本信息
    company_name = db.Column(db.String(200), nullable=False, index=True)  # 公司名称
    form_type = db.Column(db.String(50), nullable=False, index=True)  # 表单类型：安全监测、安全测评、应用加固
    record_date = db.Column(db.Date, nullable=False, index=True)  # 记录日期

    # 表单数据（JSON格式存储完整的表单内容）
    form_data = db.Column(db.Text, nullable=False)  # 完整的表单数据JSON

    # 生成的文件信息
    excel_filename = db.Column(db.String(255), nullable=True)  # 生成的Excel文件名
    excel_filepath = db.Column(db.String(500), nullable=True)  # Excel文件路径

    # 服务器统计信息
    server_count = db.Column(db.Integer, default=0)  # 服务器数量
    component_count = db.Column(db.Integer, default=0)  # 组件数量

    # 时间戳
    created_at = db.Column(db.DateTime, default=get_beijing_time, index=True)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

    # 用户信息（可扩展）
    created_by = db.Column(db.String(100), nullable=True)  # 创建者
    ip_address = db.Column(db.String(45), nullable=True)  # 提交者IP地址
    user_agent = db.Column(db.String(500), nullable=True)  # 用户代理

    # 状态信息
    status = db.Column(db.String(20), default='success')  # 状态：success, failed, processing
    error_message = db.Column(db.Text, nullable=True)  # 错误信息（如果有）

    def set_form_data(self, data):
        """设置表单数据（自动转换为JSON）"""
        self.form_data = json.dumps(data, ensure_ascii=False, indent=2)

    def get_form_data(self):
        """获取表单数据（自动从JSON转换）"""
        try:
            return json.loads(self.form_data) if self.form_data else {}
        except json.JSONDecodeError:
            return {}

    def calculate_statistics(self, form_data=None):
        """计算并更新统计信息"""
        if form_data is None:
            form_data = self.get_form_data()

        # 计算服务器数量
        servers = form_data.get('服务器信息', [])
        self.server_count = len(servers) if isinstance(servers, list) else 0

        # 计算组件数量（唯一组件数量）
        unique_components = set()

        # 方法1：从部署应用字段获取
        deployed_apps = form_data.get('部署应用', [])
        if isinstance(deployed_apps, list):
            unique_components.update(deployed_apps)

        # 方法2：从selectedComponentDetails获取
        selected_details = form_data.get('selectedComponentDetails', {})
        if isinstance(selected_details, dict):
            unique_components.update(selected_details.keys())

        # 方法3：从服务器信息中收集（作为备选）
        if not unique_components and isinstance(servers, list):
            for server in servers:
                if isinstance(server, dict):
                    components = server.get('部署应用', [])
                    if isinstance(components, list):
                        unique_components.update(components)
                    elif isinstance(components, str):
                        # 处理字符串格式的组件列表
                        comp_list = [comp.strip() for comp in components.replace('\n', ',').split(',') if comp.strip()]
                        unique_components.update(comp_list)

        self.component_count = len(unique_components)

    def to_dict(self, include_form_data=False):
        """将模型转换为字典，方便JSON序列化"""
        result = {
            'id': self.id,
            'company_name': self.company_name,
            'form_type': self.form_type,
            'record_date': self.record_date.isoformat() if self.record_date else None,
            'excel_filename': self.excel_filename,
            'excel_filepath': self.excel_filepath,
            'server_count': self.server_count,
            'component_count': self.component_count,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'created_by': self.created_by,
            'ip_address': self.ip_address,
            'status': self.status,
            'error_message': self.error_message
        }

        # 可选择是否包含完整的表单数据
        if include_form_data:
            result['form_data'] = self.get_form_data()

        return result

    def to_summary_dict(self):
        """返回摘要信息，用于列表显示"""
        form_data = self.get_form_data()

        # 提取关键信息作为摘要
        summary = {
            'basic_info': {
                'customer_id': form_data.get('客户编号', ''),
                'daily_active': form_data.get('日活跃用户数', ''),
                'version_info': {
                    'frontend': form_data.get('前端版本', ''),
                    'backend': form_data.get('后端版本', '')
                }
            },
            'servers': []
        }

        # 服务器摘要信息
        servers = form_data.get('服务器信息', [])
        if isinstance(servers, list):
            for server in servers:
                if isinstance(server, dict):
                    server_summary = {
                        'ip': server.get('IP地址', ''),
                        'purpose': server.get('服务器用途', ''),
                        'system': server.get('系统发行版', ''),
                        'components': len(server.get('部署应用', [])) if isinstance(server.get('部署应用'), list) else 0
                    }
                    summary['servers'].append(server_summary)

        # 获取基本字典数据
        result = self.to_dict()

        # 从表单数据中提取编辑人信息
        editor = form_data.get('编辑人', '') or self.created_by or '未知'
        result['editor'] = editor

        return {
            **result,
            'summary': summary
        }

    def __repr__(self):
        return f'<FormSubmission {self.id}: {self.company_name} - {self.form_type}>'


class FormSubmissionEdit(db.Model):
    """表单提交编辑记录表，用于记录每次编辑的历史"""
    __tablename__ = 'form_submission_edit'

    id = db.Column(db.Integer, primary_key=True)

    # 关联的表单提交记录
    submission_id = db.Column(db.Integer, db.ForeignKey('form_submission.id'), nullable=False, index=True)
    submission = db.relationship('FormSubmission', backref=db.backref('edit_history', lazy=True, cascade='all, delete-orphan'))

    # 编辑信息
    edit_type = db.Column(db.String(50), nullable=False)  # 编辑类型：update, restore, regenerate
    edit_description = db.Column(db.String(500), nullable=True)  # 编辑描述

    # 编辑前后的数据
    old_data = db.Column(db.Text, nullable=True)  # 编辑前的数据（JSON格式）
    new_data = db.Column(db.Text, nullable=True)  # 编辑后的数据（JSON格式）

    # 变更字段记录
    changed_fields = db.Column(db.Text, nullable=True)  # 变更的字段列表（JSON格式）

    # 编辑者信息
    edited_by = db.Column(db.String(100), nullable=True)  # 编辑者
    edit_reason = db.Column(db.String(500), nullable=True)  # 编辑原因

    # 时间戳
    created_at = db.Column(db.DateTime, default=get_beijing_time, index=True)

    # 用户信息
    ip_address = db.Column(db.String(45), nullable=True)  # 编辑者IP地址
    user_agent = db.Column(db.String(500), nullable=True)  # 用户代理

    def set_old_data(self, data):
        """设置编辑前数据"""
        self.old_data = json.dumps(data, ensure_ascii=False, indent=2) if data else None

    def get_old_data(self):
        """获取编辑前数据"""
        try:
            return json.loads(self.old_data) if self.old_data else {}
        except json.JSONDecodeError:
            return {}

    def set_new_data(self, data):
        """设置编辑后数据"""
        self.new_data = json.dumps(data, ensure_ascii=False, indent=2) if data else None

    def get_new_data(self):
        """获取编辑后数据"""
        try:
            return json.loads(self.new_data) if self.new_data else {}
        except json.JSONDecodeError:
            return {}

    def set_changed_fields(self, fields):
        """设置变更字段列表"""
        self.changed_fields = json.dumps(fields, ensure_ascii=False) if fields else None

    def get_changed_fields(self):
        """获取变更字段列表"""
        try:
            return json.loads(self.changed_fields) if self.changed_fields else []
        except json.JSONDecodeError:
            return []

    def calculate_changes(self, old_data, new_data):
        """计算数据变更"""
        changes = []

        def compare_dict(old_dict, new_dict, prefix=''):
            for key in set(list(old_dict.keys()) + list(new_dict.keys())):
                old_val = old_dict.get(key)
                new_val = new_dict.get(key)
                field_name = f"{prefix}.{key}" if prefix else key

                if old_val != new_val:
                    if isinstance(old_val, dict) and isinstance(new_val, dict):
                        compare_dict(old_val, new_val, field_name)
                    else:
                        changes.append({
                            'field': field_name,
                            'old_value': old_val,
                            'new_value': new_val
                        })

        if isinstance(old_data, dict) and isinstance(new_data, dict):
            compare_dict(old_data, new_data)

        self.set_changed_fields(changes)
        return changes

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'submission_id': self.submission_id,
            'edit_type': self.edit_type,
            'edit_description': self.edit_description,
            'changed_fields': self.get_changed_fields(),
            'edited_by': self.edited_by,
            'edit_reason': self.edit_reason,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'ip_address': self.ip_address
        }

    def __repr__(self):
        return f'<FormSubmissionEdit {self.id}: {self.edit_type} on submission {self.submission_id}>'


class FormType(db.Model):
    """表单类型表，用于管理动态表单类型"""
    __tablename__ = 'form_type'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True, index=True)  # 表单类型名称
    display_name = db.Column(db.String(100), nullable=False)  # 显示名称
    description = db.Column(db.Text, nullable=True)  # 描述
    is_default = db.Column(db.Boolean, default=False)  # 是否为默认表单类型
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    order = db.Column(db.Integer, default=0)  # 排序权重
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'is_default': self.is_default,
            'is_active': self.is_active,
            'order': self.order,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    def __repr__(self):
        return f'<FormType {self.id}: {self.name}>'