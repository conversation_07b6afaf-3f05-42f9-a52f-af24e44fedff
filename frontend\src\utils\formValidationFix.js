/**
 * 表单验证修复工具
 * 统一处理所有表单类型的验证逻辑，确保前后端一致
 */

/**
 * 所有表单类型的必填字段配置
 * 与后端 routes.py 中的验证逻辑保持一致
 */
export const FORM_REQUIRED_FIELDS = {
  '安全测评': ['公司名称', '部署包版本', '管理员页面IP', '用户页面IP', '记录日期'],
  '安全监测': ['公司名称', '前端版本', '后端版本', '记录日期'],
  '应用加固': ['公司名称', '客户', '记录日期'],
  // 通用表单类型
  'default': ['公司名称', '版本信息', '管理员页面IP', '用户页面IP', '记录日期']
}

/**
 * 字段映射配置
 * 统一管理前端组件字段key与后端期望字段名的映射关系
 */
export const FIELD_MAPPINGS = {
  // 正向映射：组件字段key -> 表单数据字段名
  forward: {
    // 安全测评字段
    'customerId': '客户标识',
    'deploymentVersion': '部署包版本',
    
    // 安全监测字段
    'frontendVersion': '前端版本',
    'backendVersion': '后端版本',
    'dailyActive': '日活',
    'standardOrCustom': '标准或定制',
    
    // 应用加固字段
    'customer': '客户',
    'platformVersion': '部署的平台版本',
    
    // 通用字段
    'companyName': '公司名称',
    'recordDate': '记录日期',
    'editor': '编辑人',
    'versionInfo': '版本信息',
    
    // 访问信息字段
    'adminPageIP': '管理员页面IP',
    'userPageIP': '用户页面IP',
    'upgradePageIP': '升级页面IP',
    'externalServicePort': '对外服务端口',
    'businessPageAddress': '业务功能页面地址',
    'initAddress': 'init地址',
    'kibanaAddress': 'kibana地址',
    'platformAccessUrl': '平台访问地址',
    'upgradeInfo': '升级平台地址'
  },
  
  // 反向映射：表单数据字段名 -> 组件字段key
  reverse: {
    '客户标识': 'customerId',
    '部署包版本': 'deploymentVersion',
    '前端版本': 'frontendVersion',
    '后端版本': 'backendVersion',
    '日活': 'dailyActive',
    '标准或定制': 'standardOrCustom',
    '客户': 'customer',
    '部署的平台版本': 'platformVersion',
    '公司名称': 'companyName',
    '记录日期': 'recordDate',
    '编辑人': 'editor',
    '版本信息': 'versionInfo',
    '管理员页面IP': 'adminPageIP',
    '用户页面IP': 'userPageIP',
    '升级页面IP': 'upgradePageIP',
    '对外服务端口': 'externalServicePort',
    '业务功能页面地址': 'businessPageAddress',
    'init地址': 'initAddress',
    'kibana地址': 'kibanaAddress',
    '平台访问地址': 'platformAccessUrl',
    '升级平台地址': 'upgradeInfo'
  }
}

/**
 * 验证表单数据
 * @param {Object} formData - 表单数据
 * @param {string} formType - 表单类型
 * @returns {Object} 验证结果
 */
export function validateFormData(formData, formType) {
  const requiredFields = FORM_REQUIRED_FIELDS[formType] || FORM_REQUIRED_FIELDS.default
  const missingFields = []
  const warnings = []
  
  // 检查必填字段
  requiredFields.forEach(field => {
    const value = formData[field]
    if (!value || (typeof value === 'string' && !value.trim())) {
      missingFields.push(field)
    }
  })
  
  // 检查字段映射一致性
  Object.keys(FIELD_MAPPINGS.forward).forEach(key => {
    const chineseField = FIELD_MAPPINGS.forward[key]
    if (formData[key] && formData[chineseField] && formData[key] !== formData[chineseField]) {
      warnings.push(`字段映射不一致: ${key}(${formData[key]}) vs ${chineseField}(${formData[chineseField]})`)
    }
  })
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings,
    requiredFields
  }
}

/**
 * 修复表单数据映射
 * 确保所有字段都正确映射到期望的字段名
 * @param {Object} formData - 表单数据
 * @returns {Object} 修复后的表单数据
 */
export function fixFormDataMapping(formData) {
  const fixedData = { ...formData }
  
  // 应用正向映射
  Object.keys(FIELD_MAPPINGS.forward).forEach(key => {
    const chineseField = FIELD_MAPPINGS.forward[key]
    if (fixedData[key] && !fixedData[chineseField]) {
      fixedData[chineseField] = fixedData[key]
      console.log(`修复映射: ${key} -> ${chineseField} = ${fixedData[key]}`)
    }
  })
  
  return fixedData
}

/**
 * 获取表单类型的必填字段
 * @param {string} formType - 表单类型
 * @returns {Array} 必填字段数组
 */
export function getRequiredFields(formType) {
  return FORM_REQUIRED_FIELDS[formType] || FORM_REQUIRED_FIELDS.default
}

/**
 * 检查字段是否为必填
 * @param {string} fieldName - 字段名
 * @param {string} formType - 表单类型
 * @returns {boolean} 是否为必填字段
 */
export function isRequiredField(fieldName, formType) {
  const requiredFields = getRequiredFields(formType)
  return requiredFields.includes(fieldName)
}

/**
 * 生成表单验证报告
 * @param {Object} formData - 表单数据
 * @param {string} formType - 表单类型
 * @returns {Object} 详细的验证报告
 */
export function generateValidationReport(formData, formType) {
  const validation = validateFormData(formData, formType)
  const requiredFields = getRequiredFields(formType)
  
  const report = {
    formType,
    totalRequiredFields: requiredFields.length,
    filledRequiredFields: requiredFields.filter(field => 
      formData[field] && formData[field].toString().trim()
    ).length,
    missingFields: validation.missingFields,
    warnings: validation.warnings,
    isValid: validation.isValid,
    completionRate: Math.round(
      ((requiredFields.length - validation.missingFields.length) / requiredFields.length) * 100
    ),
    fieldDetails: requiredFields.map(field => ({
      fieldName: field,
      value: formData[field] || '',
      isFilled: !!(formData[field] && formData[field].toString().trim()),
      isRequired: true
    }))
  }
  
  return report
}

/**
 * 模拟后端验证
 * 使用与后端相同的验证逻辑
 * @param {Object} formData - 表单数据
 * @returns {Object} 后端验证结果
 */
export function simulateBackendValidation(formData) {
  const docSuffix = formData.文档后缀 || '安全测评'
  let requiredFields = []
  
  // 与后端 routes.py 第948-957行保持一致
  if (docSuffix === '安全监测') {
    requiredFields = ['公司名称', '前端版本', '后端版本', '记录日期']
  } else if (docSuffix === '应用加固') {
    requiredFields = ['公司名称', '客户', '记录日期']
  } else if (docSuffix === '安全测评') {
    requiredFields = ['公司名称', '部署包版本', '管理员页面IP', '用户页面IP', '记录日期']
  } else {
    requiredFields = ['公司名称', '版本信息', '管理员页面IP', '用户页面IP', '记录日期']
  }
  
  const missingFields = requiredFields.filter(field => !formData[field])
  
  return {
    success: missingFields.length === 0,
    message: missingFields.length === 0 
      ? '后端验证通过' 
      : `缺少必要字段: ${missingFields.join(', ')}`,
    missingFields,
    requiredFields,
    formType: docSuffix
  }
}

/**
 * 导出验证报告为JSON
 * @param {Object} report - 验证报告
 * @returns {string} JSON字符串
 */
export function exportValidationReport(report) {
  return JSON.stringify(report, null, 2)
}

/**
 * 打印验证报告到控制台
 * @param {Object} report - 验证报告
 */
export function logValidationReport(report) {
  console.group(`📋 表单验证报告 - ${report.formType}`)
  console.log(`✅ 完成率: ${report.completionRate}%`)
  console.log(`📊 已填写: ${report.filledRequiredFields}/${report.totalRequiredFields}`)
  
  if (report.missingFields.length > 0) {
    console.warn(`❌ 缺少字段:`, report.missingFields)
  }
  
  if (report.warnings.length > 0) {
    console.warn(`⚠️ 警告:`, report.warnings)
  }
  
  console.table(report.fieldDetails)
  console.groupEnd()
}
