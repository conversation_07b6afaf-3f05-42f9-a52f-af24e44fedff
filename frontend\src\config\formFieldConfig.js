/**
 * 统一的表单字段配置
 * 通过配置驱动表单渲染和验证
 */

/**
 * 表单字段配置对象
 * 每个表单类型包含：basic(基本信息)、access(访问信息)、validation(验证规则)
 */
export const FORM_FIELD_CONFIGS = {
  '安全测评': {
    basic: {
      customerId: { 
        field: '客户标识', 
        required: true,
        type: 'text',
        placeholder: '请输入客户标识'
      },
      deploymentVersion: { 
        field: '部署包版本', 
        required: true,
        type: 'text',
        placeholder: '请输入部署包版本'
      }
    },
    access: {
      adminPageIP: { 
        field: '管理员页面IP', 
        type: 'text', 
        required: true,
        placeholder: '请输入管理员页面IP',
        columnClass: 'col-md-6'
      },
      superAdminAccount: {
        field: '超级管理员账号',
        type: 'text',
        default: 'root',
        placeholder: '超级管理员账号',
        columnClass: 'col-md-6'
      },
      superAdminPassword: {
        field: '超级管理员密码',
        type: 'password',
        default: 'Cqmyg.Ysds5',
        showToggle: true,
        columnClass: 'col-md-6'
      },
      adminAccount: {
        field: '管理员账号',
        type: 'text',
        default: 'admin',
        columnClass: 'col-md-6'
      },
      adminPassword: {
        field: '管理员密码',
        type: 'password',
        default: '1qaz@WSX',
        showToggle: true,
        columnClass: 'col-md-6'
      },
      userAccount: { 
        field: '用户账号', 
        type: 'text',
        placeholder: '请输入用户账号',
        columnClass: 'col-md-6'
      },
      userPassword: { 
        field: '用户密码', 
        type: 'password',
        placeholder: '请输入用户密码',
        showToggle: true,
        columnClass: 'col-md-6'
      },
      userPageIP: { 
        field: '用户页面IP', 
        type: 'text', 
        required: true,
        placeholder: '请输入用户页面IP',
        columnClass: 'col-md-6'
      },
      upgradePageIP: { 
        field: '升级页面IP', 
        type: 'text',
        placeholder: '请输入升级页面IP',
        columnClass: 'col-md-6'
      },
      upgradeAccount: { 
        field: '升级用户账号', 
        type: 'text', 
        default: 'upgrader',
        columnClass: 'col-md-6'
      },
      upgradePassword: { 
        field: '升级用户密码', 
        type: 'password', 
        default: 'upgrader@abc#2020',
        showToggle: true,
        columnClass: 'col-md-6'
      },
      externalServicePort: { 
        field: '对外服务端口', 
        type: 'text',
        placeholder: '请输入对外服务端口',
        columnClass: 'col-md-6'
      }
    },
    validation: {
      required: ['公司名称', '部署包版本', '管理员页面IP', '用户页面IP', '记录日期']
    }
  },
  
  '安全监测': {
    basic: {
      customerId: { 
        field: '客户标识', 
        required: true,
        type: 'text',
        placeholder: '请输入客户标识'
      },
      dailyActive: { 
        field: '日活', 
        type: 'number',
        placeholder: '请输入日活数量'
      },
      frontendVersion: { 
        field: '前端版本', 
        required: true,
        type: 'text',
        placeholder: '请输入前端版本'
      },
      backendVersion: { 
        field: '后端版本', 
        required: true,
        type: 'text',
        placeholder: '请输入后端版本'
      },
      standardOrCustom: { 
        field: '标准或定制', 
        type: 'select',
        options: ['标准版', '定制版'],
        default: '标准版'
      }
    },
    access: {
      businessPageAddress: { 
        field: '业务功能页面地址', 
        type: 'text',
        placeholder: '请输入业务功能页面地址',
        columnClass: 'col-12'
      },
      superAdminAccount: {
        field: '超级管理员账号',
        type: 'text',
        default: '<EMAIL>',
        placeholder: '请输入超级管理员账号',
        columnClass: 'col-md-6'
      },
      superAdminPassword: {
        field: '超级管理员密码',
        type: 'password',
        showToggle: true,
        default: 'Bangcle@2024',
        placeholder: '请输入超级管理员密码',
        columnClass: 'col-md-6'
      },
      customerAdminAccount: {
        field: '客户管理员账号',
        type: 'text',
        default: '',
        placeholder: '请输入客户管理员账号',
        columnClass: 'col-md-6'
      },
      customerAdminPassword: {
        field: '客户管理员密码',
        type: 'password',
        showToggle: true,
        default: 'everisk@!QAZ2wsx',
        placeholder: '请输入客户管理员密码',
        columnClass: 'col-md-6'
      },
      initAddress: { 
        field: 'init地址', 
        type: 'text',
        placeholder: '请输入init地址',
        columnClass: 'col-md-6'
      },
      initUser: {
        field: 'init用户名',
        type: 'text',
        default: 'prometheus_user',
        placeholder: '请输入init用户名',
        columnClass: 'col-md-6'
      },
      initPassword: {
        field: 'init密码',
        type: 'password',
        showToggle: true,
        default: 'prometheus_pass',
        placeholder: '请输入init密码',
        columnClass: 'col-md-6'
      },
      kibanaAddress: { 
        field: 'kibana地址', 
        type: 'text',
        placeholder: '请输入kibana地址',
        columnClass: 'col-md-6'
      },
      kibanaAccount: {
        field: 'kibana账号',
        type: 'text',
        default: 'elastic',
        placeholder: '请输入kibana账号',
        columnClass: 'col-md-6'
      },
      kibanaPassword: {
        field: 'kibana密码',
        type: 'password',
        showToggle: true,
        default: 'bangcle123',
        placeholder: '请输入kibana密码',
        columnClass: 'col-md-6'
      }
    },
    validation: {
      required: ['公司名称', '前端版本', '后端版本', '记录日期']
    }
  },
  
  '应用加固': {
    basic: {
      customerId: { 
        field: '客户标识', 
        required: true,
        type: 'text',
        placeholder: '请输入客户标识'
      },
      customer: { 
        field: '客户', 
        required: true,
        type: 'text',
        placeholder: '请输入客户名称'
      },
      platformVersion: { 
        field: '部署的平台版本', 
        required: true,
        type: 'text',
        placeholder: '请输入部署的平台版本'
      }
    },
    access: {
      platformAccessUrl: {
        field: '平台访问地址',
        type: 'text',
        placeholder: '请输入平台访问地址',
        columnClass: 'col-12'
      },
      platformUserAccount: {
        field: '平台用户账号',
        type: 'text',
        placeholder: '请输入平台用户账号',
        columnClass: 'col-md-6'
      },
      platformUserPassword: {
        field: '平台用户密码',
        type: 'password',
        showToggle: true,
        placeholder: '请输入平台用户密码',
        columnClass: 'col-md-6'
      },
      adminAccount: {
        field: '管理员账号',
        type: 'text',
        default: 'admin',
        placeholder: '请输入管理员账号',
        columnClass: 'col-md-6'
      },
      adminPassword: {
        field: '管理员密码',
        type: 'password',
        default: 'admin@secneo',
        showToggle: true,
        placeholder: '请输入管理员密码',
        columnClass: 'col-md-6'
      },
      superAdminAccount: {
        field: '超级管理员账号',
        type: 'text',
        default: 'sadmin',
        placeholder: '请输入超级管理员账号',
        columnClass: 'col-md-6'
      },
      superAdminPassword: {
        field: '超级管理员密码',
        type: 'password',
        default: 'admin@secneo#2017',
        showToggle: true,
        placeholder: '请输入超级管理员密码',
        columnClass: 'col-md-6'
      },
      upgradeInfo: {
        field: '升级平台地址',
        type: 'text',
        placeholder: '请输入升级平台地址',
        columnClass: 'col-12'
      },
      upgradeAccount: {
        field: '升级用户账号',
        type: 'text',
        default: 'upgrader',
        placeholder: '请输入升级用户账号',
        columnClass: 'col-md-6'
      },
      upgradePassword: {
        field: '升级用户密码',
        type: 'password',
        default: 'upgrader@abc#2020',
        showToggle: true,
        placeholder: '请输入升级用户密码',
        columnClass: 'col-md-6'
      }
    },
    validation: {
      required: ['公司名称', '客户', '记录日期']
    }
  }
}

/**
 * 获取表单字段配置
 * @param {string} formType - 表单类型
 * @returns {Object} 字段配置对象
 */
export function getFormFieldConfig(formType) {
  return FORM_FIELD_CONFIGS[formType] || {}
}

/**
 * 获取表单验证规则
 * @param {string} formType - 表单类型
 * @returns {Object} 验证规则对象
 */
export function getFormValidationRules(formType) {
  const config = FORM_FIELD_CONFIGS[formType]
  return config?.validation || { required: [] }
}

/**
 * 获取基本信息字段配置
 * @param {string} formType - 表单类型
 * @returns {Object} 基本信息字段配置
 */
export function getBasicFieldConfig(formType) {
  const config = getFormFieldConfig(formType)
  return config.basic || {}
}

/**
 * 获取访问信息字段配置
 * @param {string} formType - 表单类型
 * @returns {Object} 访问信息字段配置
 */
export function getAccessFieldConfig(formType) {
  const config = getFormFieldConfig(formType)
  return config.access || {}
}
