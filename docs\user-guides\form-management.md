# 📝 表单管理指南

## 📋 概述

本指南将帮助您了解如何使用梆梆安全-运维信息登记平台的表单管理功能。

## 🎯 表单类型

系统支持三种表单类型：

### 1. 🔒 安全测评表单
- **用途**: 安全测评项目的运维信息登记
- **特点**: 包含详细的安全组件配置
- **组件分类**: 基础、数据库、工具、服务、引擎、前端、后端

### 2. 📊 安全监测表单
- **用途**: 安全监测项目的运维信息登记
- **特点**: 专注于监测相关组件
- **组件分类**: 监测工具、数据采集、分析引擎

### 3. 🛡️ 应用加固表单
- **用途**: 应用加固项目的运维信息登记
- **特点**: 自动填充访问信息，简化操作
- **组件分类**: 加固工具、防护组件

## 📝 表单填写流程

### 1. 选择表单类型
1. 访问表单填写页面
2. 在顶部选择对应的表单类型
3. 系统会自动加载对应的表单结构

### 2. 填写基本信息
**必填字段:**
- 客户公司名称
- 项目名称
- 记录日期

**可选字段:**
- 项目描述
- 联系人信息

### 3. 配置访问信息
**包含字段:**
- 访问地址
- 访问端口
- 访问协议
- 认证信息

### 4. 选择部署组件
1. 在组件选择区域，按分类浏览可用组件
2. 点击组件卡片进行选择/取消选择
3. 已选择的组件会显示在右侧列表中
4. 可以为每个组件配置端口信息

### 5. 配置服务器信息
**服务器基本信息:**
- 服务器IP地址
- 操作系统类型
- 系统版本
- 硬件配置

**运维信息:**
- SSH端口
- Root密码（支持显示/隐藏）
- 运维用户信息

### 6. 添加维护记录
**维护记录包含:**
- 维护时间
- 维护人员
- 维护类型
- 任务链接
- 维护内容

**操作方式:**
- 点击"添加维护记录"按钮
- 填写维护信息
- 支持添加多条维护记录

## 💾 表单操作功能

### 1. 保存草稿
- 点击"保存草稿"按钮
- 表单数据会保存到本地存储
- 下次访问时可以恢复数据

### 2. 预览数据
- 点击"预览数据"按钮
- 查看当前填写的表单数据
- 确认信息无误后再提交

### 3. 重置表单
- 点击"重置表单"按钮
- 清空所有已填写的数据
- 恢复到初始状态

### 4. 提交表单
- 点击"提交表单"按钮
- 系统会验证必填字段
- 提交成功后生成Excel文件

### 5. 生成表单
- 点击"生成表单"按钮
- 基于当前数据生成Excel文件
- 支持重复生成

## 📊 表单快照功能

### 保存快照
1. 填写表单数据
2. 点击"保存快照"按钮
3. 输入快照名称
4. 快照保存到本地存储

### 加载快照
1. 点击"加载快照"按钮
2. 选择要加载的快照
3. 确认加载操作
4. 表单数据会被快照数据覆盖

### 管理快照
- 查看所有已保存的快照
- 删除不需要的快照
- 重命名快照

## 🔍 重复提交检测

### 检测机制
- 系统会检测相同公司名称和表单类型的提交
- 如果检测到重复，会弹出提示对话框

### 处理选项
1. **强制创建新记录**: 创建新的表单记录
2. **下载现有文件**: 下载之前生成的Excel文件
3. **取消操作**: 返回表单继续编辑

## 📋 表单验证规则

### 必填字段验证
- 客户公司名称
- 项目名称
- 记录日期

### 格式验证
- 邮箱格式验证
- IP地址格式验证
- 端口号范围验证
- 日期格式验证

### 业务逻辑验证
- 组件端口冲突检测
- 服务器IP重复检测
- 表单数据完整性检查

## 🎨 表单样式特性

### 浮动标签
- 输入框获得焦点时标签上浮
- 提供更好的用户体验

### 可折叠分组
- 表单按功能分组
- 支持展开/折叠操作
- 减少页面复杂度

### 导航侧边栏
- 显示表单各个分组
- 支持快速跳转
- 跟随页面滚动

### 必填字段指示
- 必填字段标有红色星号
- 未填写时显示提示信息

## 🔧 高级功能

### 自动填充
- 应用加固表单支持访问信息自动填充
- 基于表单类型预设默认值

### 动态组件加载
- 根据表单类型动态加载对应组件
- 组件配置实时生效

### 表单导航
- 侧边栏导航支持移动
- 包含保存表单功能
- 随页面滚动固定显示

## 📚 相关文档

- [组件管理指南](component-management.md)
- [Excel模板使用](excel-templates.md)
- [系统管理功能](system-administration.md)
