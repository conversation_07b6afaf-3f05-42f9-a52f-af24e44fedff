<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-xl">
      <div class="modal-content shadow-lg border-0">
        <div class="modal-header bg-gradient-primary text-white border-0">
          <h5 class="modal-title fw-bold">
            <i class="bi bi-person-circle me-2"></i>
            {{ isEdit ? '编辑用户信息' : '创建新用户' }}
          </h5>
          <button type="button" class="btn-close btn-close-white" @click="$emit('close')"></button>
        </div>
        <div class="modal-body p-4">
          <form @submit.prevent="handleSubmit">
            <div class="row g-4">
              <!-- 基本信息卡片 -->
              <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                  <div class="card-header bg-light border-0">
                    <h6 class="card-title mb-0 text-primary">
                      <i class="bi bi-person-badge me-2"></i>基本信息
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-person me-1"></i>用户名 <span class="text-danger">*</span>
                      </label>
                      <div class="input-group">
                        <span class="input-group-text bg-light border-end-0">
                          <i class="bi bi-at text-muted"></i>
                        </span>
                        <input
                          type="text"
                          class="form-control border-start-0 ps-0"
                          v-model="form.username"
                          :disabled="isEdit"
                          placeholder="请输入用户名"
                          required
                        >
                      </div>
                      <small v-if="isEdit" class="text-muted">编辑时不能修改用户名</small>
                    </div>

                    <div class="mb-3">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-envelope me-1"></i>邮箱地址 <span class="text-danger">*</span>
                      </label>
                      <div class="input-group">
                        <span class="input-group-text bg-light border-end-0">
                          <i class="bi bi-envelope text-muted"></i>
                        </span>
                        <input
                          type="email"
                          class="form-control border-start-0 ps-0"
                          v-model="form.email"
                          placeholder="请输入邮箱地址"
                          required
                        >
                      </div>
                    </div>

                    <div class="mb-3" v-if="!isEdit">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-key me-1"></i>登录密码 <span class="text-danger">*</span>
                      </label>
                      <div class="input-group">
                        <span class="input-group-text bg-light border-end-0">
                          <i class="bi bi-lock text-muted"></i>
                        </span>
                        <input
                          type="password"
                          class="form-control border-start-0 ps-0"
                          v-model="form.password"
                          placeholder="请输入登录密码"
                          required
                        >
                      </div>
                    </div>

                    <div class="mb-3">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-person-badge me-1"></i>真实姓名
                      </label>
                      <input
                        type="text"
                        class="form-control"
                        v-model="form.real_name"
                        placeholder="请输入真实姓名"
                      >
                    </div>

                    <div class="mb-0">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-telephone me-1"></i>手机号码
                      </label>
                      <input
                        type="tel"
                        class="form-control"
                        v-model="form.phone"
                        placeholder="请输入手机号码"
                      >
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 组织信息和权限卡片 -->
              <div class="col-md-6">
                <!-- 组织信息卡片 -->
                <div class="card border-0 shadow-sm mb-3">
                  <div class="card-header bg-light border-0">
                    <h6 class="card-title mb-0 text-primary">
                      <i class="bi bi-building me-2"></i>组织信息
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-diagram-3 me-1"></i>所属部门
                      </label>
                      <input
                        type="text"
                        class="form-control"
                        v-model="form.department"
                        placeholder="请输入所属部门"
                      >
                    </div>

                    <div class="mb-0">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-briefcase me-1"></i>职位
                      </label>
                      <input
                        type="text"
                        class="form-control"
                        v-model="form.position"
                        placeholder="请输入职位"
                      >
                    </div>
                  </div>
                </div>

                <!-- 权限设置卡片 -->
                <div class="card border-0 shadow-sm">
                  <div class="card-header bg-light border-0">
                    <h6 class="card-title mb-0 text-primary">
                      <i class="bi bi-shield-check me-2"></i>权限设置
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="isActive"
                          v-model="form.is_active"
                        >
                        <label class="form-check-label fw-semibold" for="isActive">
                          <i class="bi bi-person-check me-1"></i>启用用户
                        </label>
                      </div>
                      <small class="text-muted">禁用后用户将无法登录系统</small>
                    </div>

                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="isAdmin"
                          v-model="form.is_admin"
                        >
                        <label class="form-check-label fw-semibold" for="isAdmin">
                          <i class="bi bi-shield-fill-check me-1"></i>系统管理员
                        </label>
                      </div>
                      <small class="text-muted">管理员拥有系统所有权限</small>
                    </div>

                    <div class="mb-3">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-person-gear me-1"></i>分配角色
                      </label>
                      <div class="role-transfer-container">
                        <div class="row g-3">
                          <!-- 可选角色 -->
                          <div class="col-5">
                            <div class="transfer-panel">
                              <div class="panel-header">
                                <small class="text-muted fw-semibold">可选角色</small>
                                <span class="badge bg-secondary">{{ availableRoles.length }}</span>
                              </div>
                              <div class="panel-body">
                                <div v-if="availableRoles.length === 0" class="text-muted text-center py-3">
                                  <i class="bi bi-info-circle"></i>
                                  <div>暂无可选角色</div>
                                </div>
                                <div v-else>
                                  <div
                                    v-for="role in availableRoles"
                                    :key="role.id"
                                    class="transfer-item"
                                    @click="addRole(role)"
                                  >
                                    <div class="d-flex align-items-center">
                                      <div class="flex-grow-1">
                                        <div class="fw-semibold">{{ role.name }}</div>
                                        <small class="text-muted">{{ role.description || '无描述' }}</small>
                                      </div>
                                      <div class="transfer-actions">
                                        <span v-if="role.is_system" class="badge bg-warning text-dark me-2">系统</span>
                                        <i class="bi bi-plus-circle text-primary"></i>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 操作按钮 -->
                          <div class="col-2 d-flex flex-column justify-content-center align-items-center">
                            <button
                              type="button"
                              class="btn btn-outline-primary btn-sm mb-2"
                              @click="addAllRoles"
                              :disabled="availableRoles.length === 0"
                              title="添加全部"
                            >
                              <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button
                              type="button"
                              class="btn btn-outline-secondary btn-sm"
                              @click="removeAllRoles"
                              :disabled="selectedRoles.length === 0"
                              title="移除全部"
                            >
                              <i class="bi bi-chevron-double-left"></i>
                            </button>
                          </div>

                          <!-- 已选角色 -->
                          <div class="col-5">
                            <div class="transfer-panel">
                              <div class="panel-header">
                                <small class="text-muted fw-semibold">已选角色</small>
                                <span class="badge bg-primary">{{ selectedRoles.length }}</span>
                              </div>
                              <div class="panel-body">
                                <div v-if="selectedRoles.length === 0" class="text-muted text-center py-3">
                                  <i class="bi bi-info-circle"></i>
                                  <div>暂无已选角色</div>
                                </div>
                                <div v-else>
                                  <div
                                    v-for="role in selectedRoles"
                                    :key="role.id"
                                    class="transfer-item selected"
                                    @click="removeRole(role)"
                                  >
                                    <div class="d-flex align-items-center">
                                      <div class="flex-grow-1">
                                        <div class="fw-semibold">{{ role.name }}</div>
                                        <small class="text-muted">{{ role.description || '无描述' }}</small>
                                      </div>
                                      <div class="transfer-actions">
                                        <span v-if="role.is_system" class="badge bg-warning text-dark me-2">系统</span>
                                        <i class="bi bi-dash-circle text-danger"></i>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="mb-0">
                      <label class="form-label fw-semibold">
                        <i class="bi bi-people me-1"></i>加入用户组
                      </label>
                      <div class="group-transfer-container">
                        <div class="row g-3">
                          <!-- 可选用户组 -->
                          <div class="col-5">
                            <div class="transfer-panel">
                              <div class="panel-header">
                                <small class="text-muted fw-semibold">可选用户组</small>
                                <span class="badge bg-secondary">{{ availableGroups.length }}</span>
                              </div>
                              <div class="panel-body">
                                <div v-if="availableGroups.length === 0" class="text-muted text-center py-3">
                                  <i class="bi bi-info-circle"></i>
                                  <div>暂无可选用户组</div>
                                </div>
                                <div v-else>
                                  <div
                                    v-for="group in availableGroups"
                                    :key="group.id"
                                    class="transfer-item"
                                    @click="addGroup(group)"
                                  >
                                    <div class="d-flex align-items-center">
                                      <div class="flex-grow-1">
                                        <div class="fw-semibold">{{ group.name }}</div>
                                        <small class="text-muted">{{ group.description || '无描述' }}</small>
                                      </div>
                                      <div class="transfer-actions">
                                        <span class="badge bg-info me-2">{{ group.user_count || 0 }}人</span>
                                        <i class="bi bi-plus-circle text-primary"></i>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 操作按钮 -->
                          <div class="col-2 d-flex flex-column justify-content-center align-items-center">
                            <button
                              type="button"
                              class="btn btn-outline-primary btn-sm mb-2"
                              @click="addAllGroups"
                              :disabled="availableGroups.length === 0"
                              title="添加全部"
                            >
                              <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button
                              type="button"
                              class="btn btn-outline-secondary btn-sm"
                              @click="removeAllGroups"
                              :disabled="selectedGroups.length === 0"
                              title="移除全部"
                            >
                              <i class="bi bi-chevron-double-left"></i>
                            </button>
                          </div>

                          <!-- 已选用户组 -->
                          <div class="col-5">
                            <div class="transfer-panel">
                              <div class="panel-header">
                                <small class="text-muted fw-semibold">已选用户组</small>
                                <span class="badge bg-primary">{{ selectedGroups.length }}</span>
                              </div>
                              <div class="panel-body">
                                <div v-if="selectedGroups.length === 0" class="text-muted text-center py-3">
                                  <i class="bi bi-info-circle"></i>
                                  <div>暂无已选用户组</div>
                                </div>
                                <div v-else>
                                  <div
                                    v-for="group in selectedGroups"
                                    :key="group.id"
                                    class="transfer-item selected"
                                    @click="removeGroup(group)"
                                  >
                                    <div class="d-flex align-items-center">
                                      <div class="flex-grow-1">
                                        <div class="fw-semibold">{{ group.name }}</div>
                                        <small class="text-muted">{{ group.description || '无描述' }}</small>
                                      </div>
                                      <div class="transfer-actions">
                                        <span class="badge bg-info me-2">{{ group.user_count || 0 }}人</span>
                                        <i class="bi bi-dash-circle text-danger"></i>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer bg-light border-0 p-4">
          <button type="button" class="btn btn-outline-secondary px-4" @click="$emit('close')">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button type="button" class="btn btn-primary px-4" @click="handleSubmit" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-2"></span>
            <i v-else class="bi bi-check-circle me-1"></i>
            {{ saving ? '保存中...' : (isEdit ? '保存更改' : '创建用户') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserEditModal',
  props: {
    user: {
      type: Object,
      default: null
    },
    roles: {
      type: Array,
      default: () => []
    },
    groups: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'save'],
  data() {
    return {
      form: {
        username: '',
        email: '',
        password: '',
        real_name: '',
        phone: '',
        department: '',
        position: '',
        is_active: true,
        is_admin: false,
        role_ids: [],
        group_ids: []
      },
      saving: false
    }
  },
  computed: {
    isEdit() {
      return !!this.user
    },

    // 可选角色（未被选中的角色）
    availableRoles() {
      return this.roles.filter(role => !this.form.role_ids.includes(role.id))
    },

    // 已选角色
    selectedRoles() {
      return this.roles.filter(role => this.form.role_ids.includes(role.id))
    },

    // 可选用户组（未被选中的用户组）
    availableGroups() {
      return this.groups.filter(group => !this.form.group_ids.includes(group.id))
    },

    // 已选用户组
    selectedGroups() {
      return this.groups.filter(group => this.form.group_ids.includes(group.id))
    }
  },
  mounted() {
    if (this.user) {
      this.form = {
        ...this.user,
        role_ids: this.user.roles ? this.user.roles.map(r => r.id) : [],
        group_ids: this.user.groups ? this.user.groups.map(g => g.id) : []
      }
    }
  },
  methods: {
    // 角色操作方法
    addRole(role) {
      if (!this.form.role_ids.includes(role.id)) {
        this.form.role_ids.push(role.id)
      }
    },

    removeRole(role) {
      const index = this.form.role_ids.indexOf(role.id)
      if (index > -1) {
        this.form.role_ids.splice(index, 1)
      }
    },

    addAllRoles() {
      this.availableRoles.forEach(role => {
        if (!this.form.role_ids.includes(role.id)) {
          this.form.role_ids.push(role.id)
        }
      })
    },

    removeAllRoles() {
      this.form.role_ids = []
    },

    // 用户组操作方法
    addGroup(group) {
      if (!this.form.group_ids.includes(group.id)) {
        this.form.group_ids.push(group.id)
      }
    },

    removeGroup(group) {
      const index = this.form.group_ids.indexOf(group.id)
      if (index > -1) {
        this.form.group_ids.splice(index, 1)
      }
    },

    addAllGroups() {
      this.availableGroups.forEach(group => {
        if (!this.form.group_ids.includes(group.id)) {
          this.form.group_ids.push(group.id)
        }
      })
    },

    removeAllGroups() {
      this.form.group_ids = []
    },

    async handleSubmit() {
      if (this.saving) return

      this.saving = true
      try {
        const userData = { ...this.form }
        if (this.isEdit) {
          delete userData.password // 编辑时不传密码
        }

        this.$emit('save', userData)
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.modal {
  z-index: 1060;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
}

.input-group-text {
  background-color: #f8f9fa !important;
  border-color: #dee2e6;
}

.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check {
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
}

.form-check:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

.form-check-label {
  cursor: pointer;
  width: 100%;
}

.form-check-label small {
  display: block;
  margin-top: 0.25rem;
}

/* 传输面板样式 */
.transfer-panel {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: white;
  height: 280px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-body {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.panel-body::-webkit-scrollbar {
  width: 6px;
}

.panel-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.transfer-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.transfer-item:hover {
  border-color: #007bff;
  background: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.transfer-item.selected {
  background: #e7f3ff;
  border-color: #007bff;
}

.transfer-item.selected:hover {
  background: #d1ecf1;
  border-color: #0056b3;
}

.transfer-actions {
  display: flex;
  align-items: center;
}

.transfer-actions i {
  font-size: 1.1em;
  transition: all 0.2s ease;
}

.transfer-item:hover .transfer-actions i {
  transform: scale(1.1);
}

.form-check input[type="checkbox"]:checked + label {
  background-color: rgba(0, 123, 255, 0.1);
}

.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-content {
  border-radius: 15px;
  overflow: hidden;
}

.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.badge {
  font-size: 0.75em;
}

@media (max-width: 768px) {
  .modal-dialog {
    margin: 0.5rem;
  }

  .role-selection-area,
  .group-selection-area {
    max-height: 150px;
  }
}
</style>
