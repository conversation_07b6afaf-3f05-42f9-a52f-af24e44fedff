<template>
  <div class="home">
    <div class="jumbotron bg-light p-5 rounded mb-4">
      <h1 class="display-4">梆梆安全-运维信息登记平台</h1>
      <p class="lead">欢迎使用梆梆安全运维信息登记平台，您可以在这里填写和管理运维信息。</p>
    </div>

    <!-- 主要功能区域 -->
    <div class="row g-4">
      <!-- 填写运维信息 -->
      <div class="col-md-6">
        <div class="card main-feature-card">
          <div class="card-body text-center">
            <div class="main-feature-icon mb-3">
              <i class="bi bi-file-earmark-text"></i>
            </div>
            <h5 class="main-feature-title">填写运维信息</h5>
            <p class="main-feature-desc">创建新的运维信息表单，支持安全测评、安全监测、应用加固等多种类型。</p>
            <router-link to="/fill-sheet" class="btn btn-primary main-feature-btn">
              <i class="bi bi-plus-circle me-2"></i>开始填写
            </router-link>
          </div>
        </div>
      </div>

      <!-- 表单提交历史 -->
      <div class="col-md-6">
        <div class="card main-feature-card">
          <div class="card-body text-center">
            <div class="main-feature-icon mb-3">
              <i class="bi bi-clock-history"></i>
            </div>
            <h5 class="main-feature-title">表单提交历史</h5>
            <p class="main-feature-desc">查看和管理已提交的表单记录，支持编辑、下载和统计分析。</p>
            <router-link to="/history-data" class="btn btn-success main-feature-btn">
              <i class="bi bi-list-ul me-2"></i>查看历史
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统管理区域 -->
    <div class="mt-5">
      <h3 class="mb-4">
        <i class="bi bi-gear me-2"></i>系统管理
      </h3>

      <div class="row g-4">
        <!-- 性能监控 -->
        <div class="col-md-6">
          <div class="card main-feature-card">
            <div class="card-body text-center">
              <div class="main-feature-icon mb-3">
                <i class="bi bi-speedometer2"></i>
              </div>
              <h5 class="main-feature-title">性能监控</h5>
              <p class="main-feature-desc">实时监控系统性能指标，查看服务器状态、数据库连接、内存使用等关键信息。</p>
              <router-link to="/performance-monitor" class="btn btn-primary main-feature-btn">
                <i class="bi bi-speedometer2 me-2"></i>查看监控
              </router-link>
            </div>
          </div>
        </div>

        <!-- 表单模板管理 -->
        <div class="col-md-6">
          <div class="card main-feature-card">
            <div class="card-body text-center">
              <div class="main-feature-icon mb-3">
                <i class="bi bi-layout-text-window"></i>
              </div>
              <h5 class="main-feature-title">表单模板管理</h5>
              <p class="main-feature-desc">管理表单类型，查看组件和模板统计信息，是表单系统的核心管理功能。</p>
              <router-link to="/form-template-manager" class="btn btn-info main-feature-btn">
                <i class="bi bi-layout-text-window me-2"></i>进入管理
              </router-link>
            </div>
          </div>
        </div>
      </div>


    </div>

    <!-- 数据导入区域 -->
    <div class="mt-5">
      <h3 class="mb-4">
        <i class="bi bi-upload me-2"></i>数据导入
      </h3>
      <div class="row g-4">
        <!-- Excel文件导入 -->
        <div class="col-md-6">
          <div class="card main-feature-card">
            <div class="card-body text-center">
              <div class="main-feature-icon mb-3">
                <i class="bi bi-file-earmark-excel"></i>
              </div>
              <h5 class="main-feature-title">Excel文件导入</h5>
              <p class="main-feature-desc">批量导入Excel格式的运维信息数据，支持模板验证和数据预览。</p>
              <router-link to="/excel-import" class="btn btn-success main-feature-btn">
                <i class="bi bi-file-earmark-excel me-2"></i>导入Excel
              </router-link>
            </div>
          </div>
        </div>

        <!-- JSON数据导入 -->
        <div class="col-md-6">
          <div class="card main-feature-card">
            <div class="card-body text-center">
              <div class="main-feature-icon mb-3">
                <i class="bi bi-code-square"></i>
              </div>
              <h5 class="main-feature-title">JSON数据导入</h5>
              <p class="main-feature-desc">导入JSON格式的结构化数据，适用于系统间数据迁移和批量更新。</p>
              <router-link to="/json-import" class="btn btn-warning main-feature-btn">
                <i class="bi bi-code-square me-2"></i>导入JSON
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限管理区域 - 仅管理员可见 -->
    <div class="mt-5" v-if="isAdmin">
      <h3 class="mb-4">
        <i class="bi bi-people me-2"></i>权限管理
        <span class="badge bg-warning text-dark ms-2">管理员专用</span>
      </h3>
      <div class="row g-4">
        <!-- 用户管理 -->
        <div class="col-md-4">
          <div class="card admin-feature-card">
            <div class="card-body text-center">
              <div class="admin-feature-icon mb-3">
                <i class="bi bi-person"></i>
              </div>
              <h6 class="admin-feature-title">用户管理</h6>
              <p class="admin-feature-desc">管理系统用户账户，设置用户基本信息和状态。</p>
              <router-link to="/user-management" class="btn btn-primary admin-feature-btn">
                <i class="bi bi-person me-1"></i>管理用户
              </router-link>
            </div>
          </div>
        </div>

        <!-- 用户组管理 -->
        <div class="col-md-4">
          <div class="card admin-feature-card">
            <div class="card-body text-center">
              <div class="admin-feature-icon mb-3">
                <i class="bi bi-people-fill"></i>
              </div>
              <h6 class="admin-feature-title">用户组管理</h6>
              <p class="admin-feature-desc">创建和管理用户组，批量分配用户权限。</p>
              <router-link to="/group-management" class="btn btn-success admin-feature-btn">
                <i class="bi bi-people-fill me-1"></i>管理用户组
              </router-link>
            </div>
          </div>
        </div>

        <!-- 权限配置 -->
        <div class="col-md-4">
          <div class="card admin-feature-card">
            <div class="card-body text-center">
              <div class="admin-feature-icon mb-3">
                <i class="bi bi-shield-check"></i>
              </div>
              <h6 class="admin-feature-title">权限配置</h6>
              <p class="admin-feature-desc">配置系统权限规则，管理角色和权限分配。</p>
              <router-link to="/permission-config" class="btn btn-warning admin-feature-btn">
                <i class="bi bi-shield-check me-1"></i>配置权限
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速统计信息 -->
    <div class="mt-5">
      <div class="row g-4">
        <div class="col-md-12">
          <div class="card bg-light">
            <div class="card-body">
              <h6 class="card-title">
                <i class="bi bi-info-circle me-2"></i>系统说明
              </h6>
              <div class="row">
                <div class="col-md-4">
                  <h6 class="text-primary">填写运维信息</h6>
                  <p class="small text-muted">支持三种表单类型：安全测评、安全监测、应用加固。每种类型都有对应的组件配置和Excel模板。</p>
                </div>
                <div class="col-md-4">
                  <h6 class="text-success">历史记录管理</h6>
                  <p class="small text-muted">可以查看、编辑、删除历史提交记录，支持批量操作和数据统计分析。</p>
                </div>
                <div class="col-md-4">
                  <h6 class="text-info">系统配置</h6>
                  <p class="small text-muted">通过系统管理功能可以配置表单类型、上传Excel模板、管理系统组件。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

/**
 * 首页组件
 * 显示应用的主页面和功能入口
 */
export default {
  name: 'Home',
  computed: {
    ...mapGetters(['currentUser']),

    /**
     * 检查当前用户是否为管理员
     */
    isAdmin() {
      return this.currentUser && this.currentUser.is_admin === true
    }
  }
}
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
}

.card {
  transition: all 0.3s ease;
  border: none;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* 确保卡片内容对齐 */
.card-body.d-flex.flex-column {
  min-height: 200px;
}

.card-title {
  min-height: 1.5rem;
  margin-bottom: 1rem;
}

.card-text {
  min-height: 3rem;
  margin-bottom: 1rem;
}

/* 确保按钮对齐 */
.mt-auto {
  margin-top: auto !important;
}

.jumbotron {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
}

.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
}

.card-body i {
  transition: all 0.3s ease;
}

.card:hover .card-body i {
  transform: scale(1.1);
}

.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-info { color: #0dcaf0 !important; }
.text-warning { color: #ffc107 !important; }
.text-secondary { color: #6c757d !important; }

/* 权限管理区域特殊样式 */
.border-warning {
  border-color: #ffc107 !important;
  border-width: 2px !important;
}

.border-warning:hover {
  border-color: #ffb300 !important;
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2);
}

.badge {
  font-size: 0.75em;
  padding: 0.5em 0.75em;
}

/* 性能监控卡片特殊样式 */
.border-primary {
  border-color: #007bff !important;
  border-width: 2px !important;
}

.border-primary:hover {
  border-color: #0056b3 !important;
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
}

/* 表模板管理卡片特殊样式 */
.border-info {
  border-color: #0dcaf0 !important;
  border-width: 2px !important;
}

.border-info:hover {
  border-color: #0aa2c0 !important;
  box-shadow: 0 8px 25px rgba(13, 202, 240, 0.2);
}

/* 分组标题样式 */
h5 {
  font-weight: 600;
  margin-bottom: 1rem;
}

h5 i {
  margin-right: 0.5rem;
}

/* 系统管理卡片样式 - 匹配截图设计 */
.system-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: none;
  transition: all 0.3s ease;
  background: #fff;
}

.system-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.system-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.system-icon i {
  font-size: 20px;
  color: #6c757d;
}

.system-title {
  font-size: 15px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 6px;
}

.system-desc {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 0;
  line-height: 1.4;
}

.system-btn {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 6px;
  font-weight: 500;
  white-space: nowrap;
}

/* 主要功能卡片样式 */
.main-feature-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: none;
  transition: all 0.3s ease;
  background: #fff;
  height: 100%;
}

.main-feature-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

.main-feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.main-feature-icon i {
  font-size: 32px;
  color: #495057;
}

.main-feature-title {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 16px;
}

.main-feature-desc {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 24px;
  line-height: 1.6;
}

.main-feature-btn {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 500;
}

/* 管理员功能卡片样式 */
.admin-feature-card {
  border: 2px solid #ffc107;
  border-radius: 12px;
  box-shadow: none;
  transition: all 0.3s ease;
  background: #fff;
  height: 100%;
}

.admin-feature-card:hover {
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
  transform: translateY(-4px);
  border-color: #ffb300;
}

.admin-feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.admin-feature-icon i {
  font-size: 24px;
  color: #856404;
}

.admin-feature-title {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 12px;
}

.admin-feature-desc {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 20px;
  line-height: 1.5;
}

.admin-feature-btn {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home {
    padding: 0 15px;
  }

  .jumbotron {
    padding: 2rem !important;
  }

  .display-4 {
    font-size: 2rem;
  }

  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }
}
</style>
