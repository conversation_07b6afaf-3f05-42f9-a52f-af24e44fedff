<template>
  <div class="role-management">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="bi bi-shield-check me-2"></i>角色管理
              </h5>
              <button class="btn btn-primary" @click="showCreateModal">
                <i class="bi bi-plus-circle me-1"></i>新建角色
              </button>
            </div>
            <div class="card-body">
              <!-- 搜索和筛选 -->
              <div class="row mb-3">
                <div class="col-md-4">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="bi bi-search"></i>
                    </span>
                    <input 
                      type="text" 
                      class="form-control" 
                      placeholder="搜索角色名称或代码"
                      v-model="searchQuery"
                      @input="searchRoles"
                    >
                  </div>
                </div>
                <div class="col-md-3">
                  <select class="form-select" v-model="statusFilter" @change="filterRoles">
                    <option value="">全部状态</option>
                    <option value="active">启用</option>
                    <option value="inactive">禁用</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <select class="form-select" v-model="typeFilter" @change="filterRoles">
                    <option value="">全部类型</option>
                    <option value="system">系统角色</option>
                    <option value="custom">自定义角色</option>
                  </select>
                </div>
              </div>

              <!-- 角色列表 -->
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>角色名称</th>
                      <th>角色代码</th>
                      <th>描述</th>
                      <th>权限数量</th>
                      <th>用户数量</th>
                      <th>类型</th>
                      <th>状态</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="role in paginatedRoles" :key="role.id">
                      <td>
                        <div class="d-flex align-items-center">
                          <i class="bi bi-shield-check me-2 text-primary"></i>
                          {{ role.name }}
                        </div>
                      </td>
                      <td>
                        <code>{{ role.code }}</code>
                      </td>
                      <td>{{ role.description || '-' }}</td>
                      <td>
                        <span v-if="role.code === 'admin' && (!role.permissions || role.permissions.length === 0)"
                              class="badge bg-warning"
                              title="系统管理员拥有所有权限，无需单独配置">
                          全部权限
                        </span>
                        <span v-else class="badge bg-info">{{ role.permissions?.length || 0 }}</span>
                      </td>
                      <td>
                        <span class="badge bg-secondary">{{ role.user_count || 0 }}</span>
                      </td>
                      <td>
                        <span 
                          :class="role.is_system ? 'badge bg-warning' : 'badge bg-primary'"
                        >
                          {{ role.is_system ? '系统角色' : '自定义' }}
                        </span>
                      </td>
                      <td>
                        <span 
                          :class="role.is_active ? 'badge bg-success' : 'badge bg-danger'"
                        >
                          {{ role.is_active ? '启用' : '禁用' }}
                        </span>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button
                            class="btn btn-outline-info"
                            @click="viewPermissions(role)"
                            title="查看权限"
                          >
                            <i class="bi bi-eye"></i>
                          </button>

                          <div class="position-relative d-inline-block tooltip-container">
                            <button
                              class="btn btn-outline-primary"
                              @click="editRole(role)"
                              :disabled="role.is_system"
                              :title="role.is_system ? '系统角色不可编辑' : '编辑角色'"
                            >
                              <i class="bi bi-pencil"></i>
                            </button>
                            <div v-if="role.is_system" class="custom-tooltip">
                              <small>系统角色不可编辑</small>
                            </div>
                          </div>

                          <div class="position-relative d-inline-block tooltip-container">
                            <button
                              class="btn btn-outline-warning"
                              @click="toggleRoleStatus(role)"
                              :disabled="role.is_system"
                              :title="role.is_system ? '系统角色不可禁用' : (role.is_active ? '禁用角色' : '启用角色')"
                            >
                              <i :class="role.is_active ? 'bi bi-lock' : 'bi bi-unlock'"></i>
                            </button>
                            <div v-if="role.is_system" class="custom-tooltip">
                              <small>系统角色不可禁用</small>
                            </div>
                          </div>

                          <div class="position-relative d-inline-block tooltip-container">
                            <button
                              class="btn btn-outline-danger"
                              @click="deleteRole(role)"
                              :disabled="role.is_system || role.user_count > 0"
                              :title="getSimpleDeleteTitle(role)"
                            >
                              <i class="bi bi-trash"></i>
                            </button>
                            <div v-if="role.is_system || role.user_count > 0" class="custom-tooltip">
                              <small>{{ getSimpleDeleteTitle(role) }}</small>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- 分页 -->
              <nav v-if="totalPages > 1">
                <ul class="pagination justify-content-center">
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <button class="page-link" @click="changePage(currentPage - 1)">上一页</button>
                  </li>
                  <li 
                    v-for="page in visiblePages" 
                    :key="page" 
                    class="page-item" 
                    :class="{ active: page === currentPage }"
                  >
                    <button class="page-link" @click="changePage(page)">{{ page }}</button>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <button class="page-link" @click="changePage(currentPage + 1)">下一页</button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 角色编辑模态框 -->
    <RoleEditModal 
      v-if="showEditModal"
      :role="selectedRole"
      :permissions="permissions"
      @close="closeEditModal"
      @save="saveRole"
    />

    <!-- 权限查看模态框 -->
    <PermissionViewModal
      v-if="showPermissionModal"
      :role="selectedRole"
      @close="closePermissionModal"
    />


  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import RoleEditModal from '@/components/modals/RoleEditModal.vue'
import PermissionViewModal from '@/components/modals/PermissionViewModal.vue'

export default {
  name: 'RoleManagement',
  components: {
    RoleEditModal,
    PermissionViewModal
  },
  data() {
    return {
      roles: [],
      permissions: [],
      filteredRoles: [],
      searchQuery: '',
      statusFilter: '',
      typeFilter: '',
      currentPage: 1,
      pageSize: 10,
      totalPages: 1,
      showEditModal: false,
      showPermissionModal: false,
      selectedRole: null,
      loading: false
    }
  },
  computed: {
    ...mapState(['currentUser']),
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      return pages
    },
    paginatedRoles() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredRoles.slice(start, end)
    }
  },
  async mounted() {
    await this.loadData()
    this.initTooltips()
  },
  methods: {
    
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadRoles(),
          this.loadPermissions()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$store.dispatch('showToast', { message: '加载数据失败', type: 'error' })
      } finally {
        this.loading = false
      }
    },

    async loadRoles() {
      console.log('加载角色列表')
      try {
        const response = await fetch('/api/rbac/roles', {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.data && result.data.roles) {
            this.roles = result.data.roles
          } else if (result.data) {
            this.roles = result.data
          } else {
            this.roles = result
          }
          this.filterRoles()
          this.$nextTick(() => {
            this.initTooltips()
          })

        } else {
          console.error('加载角色列表失败:', response.status)
          this.$store.dispatch('showToast', { message: '加载角色列表失败', type: 'error' })
        }
      } catch (error) {
        console.error('加载角色列表异常:', error)
        this.$store.dispatch('showToast', { message: '加载角色列表失败', type: 'error' })
      }
    },

    async loadPermissions() {
      console.log('加载权限列表')
      try {
        const response = await fetch('/api/rbac/permissions', {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.data && result.data.permissions) {
            this.permissions = result.data.permissions
          } else if (result.data) {
            this.permissions = result.data
          } else {
            this.permissions = result
          }
          console.log('权限列表加载成功:', this.permissions.length, '个权限')
        } else {
          console.error('加载权限列表失败:', response.status)
          this.$store.dispatch('showToast', { message: '加载权限列表失败', type: 'error' })
        }
      } catch (error) {
        console.error('加载权限列表异常:', error)
        this.$store.dispatch('showToast', { message: '加载权限列表失败', type: 'error' })
      }
    },

    searchRoles() {
      this.filterRoles()
    },

    filterRoles() {
      let filtered = [...this.roles]
      
      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(role => 
          role.name.toLowerCase().includes(query) ||
          role.code.toLowerCase().includes(query) ||
          (role.description && role.description.toLowerCase().includes(query))
        )
      }
      
      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(role => 
          this.statusFilter === 'active' ? role.is_active : !role.is_active
        )
      }
      
      // 类型过滤
      if (this.typeFilter) {
        filtered = filtered.filter(role => 
          this.typeFilter === 'system' ? role.is_system : !role.is_system
        )
      }
      
      this.filteredRoles = filtered
      this.updatePagination()
    },

    updatePagination() {
      this.totalPages = Math.ceil(this.filteredRoles.length / this.pageSize)
      this.currentPage = Math.min(this.currentPage, this.totalPages || 1)
    },

    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
      }
    },

    showCreateModal() {
      this.selectedRole = null
      this.showEditModal = true
    },

    editRole(role) {
      this.selectedRole = { ...role }
      this.showEditModal = true
    },

    closeEditModal() {
      this.showEditModal = false
      this.selectedRole = null
    },

    viewPermissions(role) {
      this.selectedRole = role
      this.showPermissionModal = true
    },

    closePermissionModal() {
      this.showPermissionModal = false
      this.selectedRole = null
    },

    async saveRole(roleData) {
      try {
        const isEdit = !!roleData.id
        let response

        if (isEdit) {
          // 编辑角色
          response = await fetch(`/api/rbac/roles/${roleData.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.$store.state.token}`
            },
            body: JSON.stringify({
              name: roleData.name,
              description: roleData.description || '',
              is_active: roleData.is_active !== false,
              permission_ids: roleData.permission_ids || []
            })
          })
        } else {
          // 创建新角色
          response = await fetch('/api/rbac/roles', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.$store.state.token}`
            },
            body: JSON.stringify({
              code: roleData.code,
              name: roleData.name,
              description: roleData.description || '',
              is_active: roleData.is_active !== false,
              permission_ids: roleData.permission_ids || []
            })
          })
        }

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            this.$store.dispatch('showToast', {
              message: isEdit ? '角色编辑成功' : '角色创建成功',
              type: 'success'
            })
            this.closeEditModal()
            await this.loadRoles() // 重新加载角色列表
          } else {
            this.$store.dispatch('showToast', {
              message: result.message || '保存角色失败',
              type: 'error'
            })
          }
        } else {
          const errorResult = await response.json()
          this.$store.dispatch('showToast', {
            message: errorResult.message || '保存角色失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('保存角色失败:', error)
        this.$store.dispatch('showToast', { message: '保存角色失败', type: 'error' })
      }
    },

    async toggleRoleStatus(role) {
      try {
        const newStatus = !role.is_active
        const statusText = newStatus ? '启用' : '禁用'

        const response = await fetch(`/api/rbac/roles/${role.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.$store.state.token}`
          },
          body: JSON.stringify({
            is_active: newStatus
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            // 更新本地数据
            const roleIndex = this.roles.findIndex(r => r.id === role.id)
            if (roleIndex !== -1) {
              this.roles[roleIndex].is_active = newStatus
              this.filterRoles() // 重新过滤以更新显示
            }
            this.$store.dispatch('showToast', { message: `角色已${statusText}`, type: 'success' })
          } else {
            this.$store.dispatch('showToast', { message: result.message || '操作失败', type: 'error' })
          }
        } else {
          const errorResult = await response.json()
          this.$store.dispatch('showToast', { message: errorResult.message || '操作失败', type: 'error' })
        }
      } catch (error) {
        console.error('切换角色状态失败:', error)
        this.$store.dispatch('showToast', { message: '操作失败', type: 'error' })
      }
    },

    // 获取简单的删除按钮提示
    getSimpleDeleteTitle(role) {
      if (role.is_system && role.user_count > 0) {
        return `系统角色且有${role.user_count}个用户，不可删除`
      } else if (role.is_system) {
        return '系统角色不可删除'
      } else if (role.user_count > 0) {
        return `角色下有${role.user_count}个用户，不可删除`
      }
      return '删除角色'
    },

    async deleteRole(role) {
      if (!confirm(`确定要删除角色 "${role.name}" 吗？此操作不可恢复。`)) {
        return
      }

      try {
        const response = await fetch(`/api/rbac/roles/${role.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            this.$store.dispatch('showToast', { message: '角色删除成功', type: 'success' })
            await this.loadRoles() // 重新加载角色列表
          } else {
            this.$store.dispatch('showToast', {
              message: result.message || '删除角色失败',
              type: 'error'
            })
          }
        } else {
          const errorResult = await response.json()
          this.$store.dispatch('showToast', {
            message: errorResult.message || '删除角色失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('删除角色失败:', error)
        this.$store.dispatch('showToast', { message: '删除角色失败', type: 'error' })
      }
    },

    // 初始化工具提示
    initTooltips() {
      this.$nextTick(() => {
        const tooltipContainers = document.querySelectorAll('.tooltip-container')
        tooltipContainers.forEach(container => {
          const button = container.querySelector('button')
          const tooltip = container.querySelector('.custom-tooltip')

          if (button && tooltip) {
            button.addEventListener('mouseenter', (e) => {
              this.showTooltip(e.target, tooltip)
            })

            button.addEventListener('mouseleave', () => {
              this.hideTooltip(tooltip)
            })
          }
        })
      })
    },

    // 显示工具提示
    showTooltip(button, tooltip) {
      const rect = button.getBoundingClientRect()
      const tooltipRect = tooltip.getBoundingClientRect()

      // 计算位置
      let top = rect.bottom + 8
      let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2)

      // 确保不超出视窗
      if (left < 10) left = 10
      if (left + tooltipRect.width > window.innerWidth - 10) {
        left = window.innerWidth - tooltipRect.width - 10
      }
      if (top + tooltipRect.height > window.innerHeight - 10) {
        top = rect.top - tooltipRect.height - 8
      }

      tooltip.style.top = `${top}px`
      tooltip.style.left = `${left}px`
      tooltip.style.opacity = '1'
      tooltip.style.visibility = 'visible'
    },

    // 隐藏工具提示
    hideTooltip(tooltip) {
      tooltip.style.opacity = '0'
      tooltip.style.visibility = 'hidden'
    }
  }
}
</script>

<style scoped>
.role-management {
  padding: 20px 0;
}

.table th {
  border-top: none;
  font-weight: 600;
}

.badge {
  font-size: 0.75em;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
}

.pagination {
  margin-top: 20px;
}

code {
  background-color: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.custom-tooltip {
  position: fixed;
  top: auto;
  left: auto;
  transform: none;
  background-color: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  pointer-events: none;
  max-width: 200px;
}

.custom-tooltip::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid #333;
}

/* 移除默认的hover效果，改用JavaScript控制 */
/* .position-relative:hover .custom-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
} */

/* 工具提示容器样式 */
.tooltip-container {
  z-index: 1;
}

.tooltip-container:hover {
  z-index: 1071;
}

/* 确保工具提示容器有足够的层级 */
.position-relative {
  z-index: 1;
}

.position-relative:hover {
  z-index: 1071;
}

.custom-tooltip small {
  color: white !important;
}

/* 表格行悬停时确保工具提示可见 */
.table tbody tr:hover {
  position: relative;
  z-index: 1;
}

/* 按钮组样式优化 */
.btn-group-sm .btn {
  position: relative;
}

/* 确保表格容器不会裁剪工具提示 */
.table-responsive {
  overflow-x: auto;
  overflow-y: visible;
}

/* 表格单元格样式 */
.table td {
  position: relative;
  vertical-align: middle;
}

/* 操作列特殊处理 */
.table td:last-child {
  overflow: visible;
}

/* 在小屏幕上调整工具提示位置 */
@media (max-width: 768px) {
  .custom-tooltip {
    left: auto;
    right: 0;
    transform: none;
    margin-top: 5px;
  }

  .custom-tooltip::before {
    left: auto;
    right: 10px;
    transform: none;
  }
}
</style>
