<template>
  <div class="group-management">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="bi bi-people-fill me-2"></i>用户组管理
              </h5>
              <button class="btn btn-primary" @click="showCreateModal">
                <i class="bi bi-plus-circle me-1"></i>新建用户组
              </button>
            </div>
            <div class="card-body">
              <!-- 搜索和筛选 -->
              <div class="row mb-3">
                <div class="col-md-4">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="bi bi-search"></i>
                    </span>
                    <input 
                      type="text" 
                      class="form-control" 
                      placeholder="搜索用户组名称或代码"
                      v-model="searchQuery"
                      @input="searchGroups"
                    >
                  </div>
                </div>
                <div class="col-md-3">
                  <select class="form-select" v-model="statusFilter" @change="filterGroups">
                    <option value="">全部状态</option>
                    <option value="active">启用</option>
                    <option value="inactive">禁用</option>
                  </select>
                </div>
              </div>

              <!-- 用户组列表 -->
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>用户组名称</th>
                      <th>用户组代码</th>
                      <th>描述</th>
                      <th>成员数量</th>
                      <th>关联角色</th>
                      <th>状态</th>
                      <th>创建时间</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="group in filteredGroups" :key="group.id">
                      <td>
                        <div class="d-flex align-items-center">
                          <i class="bi bi-people-fill me-2 text-primary"></i>
                          {{ group.name }}
                        </div>
                      </td>
                      <td>
                        <code>{{ group.code }}</code>
                      </td>
                      <td>{{ group.description || '-' }}</td>
                      <td>
                        <span class="badge bg-secondary">{{ group.user_count || 0 }}</span>
                      </td>
                      <td>
                        <span 
                          v-for="role in group.roles" 
                          :key="role.id" 
                          class="badge bg-info me-1"
                        >
                          {{ role.name }}
                        </span>
                        <span v-if="!group.roles?.length" class="text-muted">-</span>
                      </td>
                      <td>
                        <span 
                          :class="group.is_active ? 'badge bg-success' : 'badge bg-danger'"
                        >
                          {{ group.is_active ? '启用' : '禁用' }}
                        </span>
                      </td>
                      <td>{{ formatDate(group.created_at) }}</td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button
                            class="btn btn-outline-info"
                            @click="manageMembers(group)"
                            title="管理成员"
                          >
                            <i class="bi bi-people"></i>
                          </button>
                          <button 
                            class="btn btn-outline-primary" 
                            @click="editGroup(group)"
                            title="编辑"
                          >
                            <i class="bi bi-pencil"></i>
                          </button>
                          <button 
                            class="btn btn-outline-warning" 
                            @click="toggleGroupStatus(group)"
                            :title="group.is_active ? '禁用' : '启用'"
                          >
                            <i :class="group.is_active ? 'bi bi-lock' : 'bi bi-unlock'"></i>
                          </button>
                          <button 
                            class="btn btn-outline-danger" 
                            @click="deleteGroup(group)"
                            title="删除"
                            :disabled="group.user_count > 0"
                          >
                            <i class="bi bi-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户组编辑模态框 -->
    <GroupEditModal
      v-if="showEditModal"
      :group="selectedGroup"
      :availableRoles="availableRoles"
      @close="closeEditModal"
      @save="saveGroup"
    />

    <!-- 用户组成员管理模态框 -->
    <GroupMembersModal
      v-if="showMembersModal"
      :group="selectedGroup"
      :allUsers="allUsers"
      @close="closeMembersModal"
      @save="saveMembers"
    />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import GroupEditModal from '@/components/modals/GroupEditModal.vue'
import GroupMembersModal from '@/components/modals/GroupMembersModal.vue'

export default {
  name: 'GroupManagement',
  components: {
    GroupEditModal,
    GroupMembersModal
  },
  data() {
    return {
      groups: [],
      filteredGroups: [],
      availableRoles: [],
      allUsers: [],
      searchQuery: '',
      statusFilter: '',
      loading: false,
      showEditModal: false,
      showMembersModal: false,
      selectedGroup: null
    }
  },
  computed: {
    ...mapState(['currentUser'])
  },
  async mounted() {
    await this.loadData()

    // 添加页面可见性检测，当页面重新可见时刷新数据
    this.handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('页面重新可见，刷新用户组数据') // 调试日志
        this.loadGroups()
      }
    }

    document.addEventListener('visibilitychange', this.handleVisibilityChange)
  },

  beforeUnmount() {
    // 清理事件监听器
    if (this.handleVisibilityChange) {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange)
    }
  },
  methods: {
    ...mapActions(['showToast']),
    
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadGroups(),
          this.loadRoles(),
          this.loadUsers()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        this.showToast('加载数据失败', 'error')
      } finally {
        this.loading = false
      }
    },

    async loadGroups() {
      try {
        // 请求包含用户信息的用户组数据
        const response = await fetch('/api/rbac/groups?include_users=true', {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          console.log('用户组列表API响应:', result) // 调试日志

          if (result.data && result.data.groups) {
            this.groups = result.data.groups
          } else if (result.data) {
            this.groups = result.data
          } else {
            this.groups = result
          }

          console.log('用户组数据:', this.groups) // 调试日志
          this.filterGroups()

        } else {
          console.error('加载用户组列表失败:', response.status)
          this.$store.dispatch('showToast', { message: '加载用户组列表失败', type: 'error' })
        }
      } catch (error) {
        console.error('加载用户组列表异常:', error)
        this.$store.dispatch('showToast', { message: '加载用户组列表失败', type: 'error' })
      }
    },

    async loadRoles() {
      try {
        const response = await fetch('/api/rbac/roles', {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.data && result.data.roles) {
            this.availableRoles = result.data.roles
          } else if (result.data) {
            this.availableRoles = result.data
          } else {
            this.availableRoles = result
          }
        } else {
          console.error('加载角色列表失败:', response.status)
          this.$store.dispatch('showToast', { message: '加载角色列表失败', type: 'error' })
        }
      } catch (error) {
        console.error('加载角色列表异常:', error)
        this.$store.dispatch('showToast', { message: '加载角色列表失败', type: 'error' })
      }
    },

    async loadUsers() {
      try {
        const response = await fetch('/api/rbac/users', {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.data && result.data.users) {
            this.allUsers = result.data.users
          } else if (result.data) {
            this.allUsers = result.data
          } else {
            this.allUsers = result
          }
        } else {
          console.error('加载用户列表失败:', response.status)
          this.$store.dispatch('showToast', { message: '加载用户列表失败', type: 'error' })
        }
      } catch (error) {
        console.error('加载用户列表异常:', error)
        this.$store.dispatch('showToast', { message: '加载用户列表失败', type: 'error' })
      }
    },

    searchGroups() {
      this.filterGroups()
    },

    filterGroups() {
      let filtered = [...this.groups]
      
      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(group => 
          group.name.toLowerCase().includes(query) ||
          group.code.toLowerCase().includes(query) ||
          (group.description && group.description.toLowerCase().includes(query))
        )
      }
      
      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(group => 
          this.statusFilter === 'active' ? group.is_active : !group.is_active
        )
      }
      
      this.filteredGroups = filtered
    },

    showCreateModal() {
      this.selectedGroup = null
      this.showEditModal = true
    },

    editGroup(group) {
      this.selectedGroup = { ...group }
      this.showEditModal = true
    },

    closeEditModal() {
      this.showEditModal = false
      this.selectedGroup = null
    },

    async saveGroup(groupData) {
      try {
        const isEdit = !!groupData.id
        let response

        if (isEdit) {
          // 编辑用户组
          response = await fetch(`/api/rbac/groups/${groupData.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.$store.state.token}`
            },
            body: JSON.stringify({
              name: groupData.name,
              description: groupData.description || '',
              is_active: groupData.is_active !== false,
              role_ids: groupData.roleIds || []
            })
          })
        } else {
          // 创建新用户组
          response = await fetch('/api/rbac/groups', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.$store.state.token}`
            },
            body: JSON.stringify({
              code: groupData.code,
              name: groupData.name,
              description: groupData.description || '',
              is_active: groupData.is_active !== false,
              role_ids: groupData.roleIds || []
            })
          })
        }

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            this.$store.dispatch('showToast', {
              message: isEdit ? '用户组编辑成功' : '用户组创建成功',
              type: 'success'
            })
            this.closeEditModal()
            await this.loadGroups() // 重新加载用户组列表
          } else {
            this.$store.dispatch('showToast', {
              message: result.message || '保存用户组失败',
              type: 'error'
            })
          }
        } else {
          const errorResult = await response.json()
          this.$store.dispatch('showToast', {
            message: errorResult.message || '保存用户组失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('保存用户组失败:', error)
        this.$store.dispatch('showToast', { message: '保存用户组失败', type: 'error' })
      }
    },

    manageMembers(group) {
      this.selectedGroup = { ...group }
      this.showMembersModal = true
    },

    closeMembersModal() {
      this.showMembersModal = false
      this.selectedGroup = null
    },

    async saveMembers(memberData) {
      try {
        const response = await fetch(`/api/rbac/groups/${memberData.groupId}/members`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.$store.state.token}`
          },
          body: JSON.stringify({
            user_ids: memberData.memberIds
          })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('用户组成员保存响应:', result) // 调试日志
          if (result.status === 'success') {
            this.$store.dispatch('showToast', { message: '用户组成员更新成功', type: 'success' })
            this.closeMembersModal()
            // 重新加载用户组列表以确保数据同步
            await this.loadGroups()
            console.log('用户组列表重新加载完成') // 调试日志
          } else {
            console.error('用户组成员保存失败:', result.message) // 调试日志
            this.$store.dispatch('showToast', { message: result.message || '保存用户组成员失败', type: 'error' })
          }
        } else {
          const errorResult = await response.json()
          console.error('HTTP错误:', response.status, errorResult) // 调试日志
          this.$store.dispatch('showToast', { message: errorResult.message || '保存用户组成员失败', type: 'error' })
        }
      } catch (error) {
        console.error('保存用户组成员失败:', error)
        this.$store.dispatch('showToast', { message: '保存用户组成员失败', type: 'error' })
      }
    },

    async toggleGroupStatus(group) {
      try {
        const newStatus = !group.is_active
        const statusText = newStatus ? '启用' : '禁用'

        const response = await fetch(`/api/rbac/groups/${group.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.$store.state.token}`
          },
          body: JSON.stringify({
            is_active: newStatus
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            // 更新本地数据
            const groupIndex = this.groups.findIndex(g => g.id === group.id)
            if (groupIndex !== -1) {
              this.groups[groupIndex].is_active = newStatus
              this.filterGroups() // 重新过滤以更新显示
            }
            this.$store.dispatch('showToast', { message: `用户组已${statusText}`, type: 'success' })
          } else {
            this.$store.dispatch('showToast', { message: result.message || '操作失败', type: 'error' })
          }
        } else {
          const errorResult = await response.json()
          this.$store.dispatch('showToast', { message: errorResult.message || '操作失败', type: 'error' })
        }
      } catch (error) {
        console.error('切换用户组状态失败:', error)
        this.$store.dispatch('showToast', { message: '操作失败', type: 'error' })
      }
    },

    async deleteGroup(group) {
      if (!confirm(`确定要删除用户组 "${group.name}" 吗？此操作不可恢复。`)) {
        return
      }

      try {
        const response = await fetch(`/api/rbac/groups/${group.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            this.$store.dispatch('showToast', { message: '用户组删除成功', type: 'success' })
            await this.loadGroups() // 重新加载用户组列表
          } else {
            this.$store.dispatch('showToast', {
              message: result.message || '删除用户组失败',
              type: 'error'
            })
          }
        } else {
          const errorResult = await response.json()
          this.$store.dispatch('showToast', {
            message: errorResult.message || '删除用户组失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('删除用户组失败:', error)
        this.$store.dispatch('showToast', { message: '删除用户组失败', type: 'error' })
      }
    },

    formatDate(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.group-management {
  padding: 20px 0;
}

.table th {
  border-top: none;
  font-weight: 600;
}

.badge {
  font-size: 0.75em;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
}

code {
  background-color: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}
</style>
