<template>
  <div class="dynamic-form-renderer">
    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载表单配置中...</span>
      </div>
      <p class="mt-2 text-muted">正在加载表单配置...</p>
    </div>

    <!-- 动态表单内容 -->
    <div v-else-if="formGroups.length > 0">
      <!-- 移除嵌套的form标签，使用div代替 -->
      <div>
        <!-- 表单分组 -->
        <div v-for="group in formGroups" :key="group.id" class="form-group-section mb-4">
          <!-- 分组标题 -->
          <div class="group-header mb-3">
            <h5 class="group-title d-flex align-items-center">
              <i class="bi bi-folder me-2"></i>
              {{ group.group_label }}
              <span class="badge bg-secondary ms-2">{{ group.fields.length }} 个字段</span>
            </h5>
            <p v-if="group.group_description" class="group-description text-muted mb-0">
              {{ group.group_description }}
            </p>
          </div>

          <!-- 字段列表 -->
          <div class="row">
            <div 
              v-for="field in group.fields" 
              :key="field.id" 
              :class="`col-md-${field.grid_columns || 12}`"
              class="mb-3"
            >
              <div class="field-container">
                <!-- 字段标签 -->
                <label :for="`field_${field.field_name}`" class="form-label">
                  {{ field.field_label }}
                  <span v-if="field.is_required" class="text-danger">*</span>
                  <i v-if="field.is_auto_fill" class="bi bi-magic ms-1 text-info" title="自动填充字段"></i>
                </label>

                <!-- 字段描述 -->
                <div v-if="field.field_description" class="field-description text-muted small mb-2">
                  {{ field.field_description }}
                </div>

                <!-- 动态字段渲染 -->
                <component 
                  :is="getFieldComponent(field.field_type)"
                  :id="`field_${field.field_name}`"
                  :field="field"
                  :value="formData[field.field_name]"
                  @update:value="updateFieldValue(field.field_name, $event)"
                  @field-change="handleFieldChange"
                />

                <!-- 验证错误提示 -->
                <div v-if="fieldErrors[field.field_name]" class="invalid-feedback d-block">
                  {{ fieldErrors[field.field_name] }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-actions mt-4 pt-3 border-top">
          <div class="row">
            <div class="col-md-6">
              <button type="button" class="btn btn-outline-secondary me-2" @click="resetForm">
                <i class="bi bi-arrow-clockwise me-1"></i>重置表单
              </button>
              <button type="button" class="btn btn-outline-info" @click="previewData">
                <i class="bi bi-eye me-1"></i>预览数据
              </button>
            </div>
            <div class="col-md-6 text-end">
              <button type="button" class="btn btn-outline-primary me-2" @click="saveDraft">
                <i class="bi bi-save me-1"></i>保存草稿
              </button>
              <button type="button" class="btn btn-primary" :disabled="submitting" @click="handleSubmit">
                <i class="bi bi-check-circle me-1"></i>
                <span v-if="submitting">提交中...</span>
                <span v-else>提交表单</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无配置状态 -->
    <div v-else class="text-center py-5">
      <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
      <h5 class="text-muted mt-3">暂无表单配置</h5>
      <p class="text-muted">该表单类型还没有配置字段，请先在表单字段配置中添加字段。</p>
      <router-link to="/form-field-config" class="btn btn-primary">
        <i class="bi bi-sliders me-1"></i>去配置字段
      </router-link>
    </div>

    <!-- 数据预览模态框 -->
    <div class="modal fade" id="dataPreviewModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">表单数据预览</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <pre class="bg-light p-3 rounded">{{ JSON.stringify(formData, null, 2) }}</pre>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

// 导入字段组件
import TextFieldComponent from './fields/TextFieldComponent.vue'
import TextareaFieldComponent from './fields/TextareaFieldComponent.vue'
import SelectFieldComponent from './fields/SelectFieldComponent.vue'
import CheckboxFieldComponent from './fields/CheckboxFieldComponent.vue'
import RadioFieldComponent from './fields/RadioFieldComponent.vue'
import DateFieldComponent from './fields/DateFieldComponent.vue'
import NumberFieldComponent from './fields/NumberFieldComponent.vue'
import FileFieldComponent from './fields/FileFieldComponent.vue'
import SwitchFieldComponent from './fields/SwitchFieldComponent.vue'

export default {
  name: 'DynamicFormRenderer',
  components: {
    TextFieldComponent,
    TextareaFieldComponent,
    SelectFieldComponent,
    CheckboxFieldComponent,
    RadioFieldComponent,
    DateFieldComponent,
    NumberFieldComponent,
    FileFieldComponent,
    SwitchFieldComponent
  },
  props: {
    formType: {
      type: String,
      required: true
    },
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['form-submit', 'form-change', 'show-toast'],
  data() {
    return {
      loading: false,
      submitting: false,
      formGroups: [],
      formData: {},
      fieldErrors: {}
    }
  },
  async mounted() {
    await this.loadFormConfig()
    this.initializeFormData()
  },
  watch: {
    formType: {
      handler: 'loadFormConfig',
      immediate: false
    },
    initialData: {
      handler: 'initializeFormData',
      deep: true
    }
  },
  methods: {
    async loadFormConfig() {
      if (!this.formType) return
      
      this.loading = true
      try {
        // 加载字段分组
        const groupsResponse = await axios.get(`/excel/form-field-groups/${this.formType}`)
        const configsResponse = await axios.get(`/excel/form-field-configs/${this.formType}`)
        
        if (groupsResponse.data.status === 'success' && configsResponse.data.status === 'success') {
          const groups = groupsResponse.data.data
          const configs = configsResponse.data.data
          
          // 组织数据结构
          this.formGroups = groups.map(group => {
            const groupFields = configs
              .filter(field => field.group_id === group.id)
              .sort((a, b) => (a.display_order || 0) - (b.display_order || 0))
            
            return {
              ...group,
              fields: groupFields
            }
          }).filter(group => group.fields.length > 0)
          .sort((a, b) => (a.display_order || 0) - (b.display_order || 0))
        }
      } catch (error) {
        console.error('加载表单配置失败:', error)
        this.$emit('show-toast', '加载表单配置失败', '错误', 'error')
      } finally {
        this.loading = false
      }
    },

    initializeFormData() {
      // 初始化表单数据
      const data = { ...this.initialData }
      
      // 为每个字段设置默认值
      this.formGroups.forEach(group => {
        group.fields.forEach(field => {
          if (!(field.field_name in data)) {
            data[field.field_name] = field.default_value || this.getDefaultValueByType(field.field_type)
          }
        })
      })
      
      this.formData = data
    },

    getDefaultValueByType(fieldType) {
      const defaults = {
        'text': '',
        'textarea': '',
        'number': 0,
        'email': '',
        'url': '',
        'password': '',
        'date': '',
        'datetime': '',
        'select': '',
        'radio': '',
        'checkbox': [],
        'switch': false,
        'file': null,
        'slider': 50,
        'color': '#000000'
      }
      return defaults[fieldType] || ''
    },

    getFieldComponent(fieldType) {
      const componentMap = {
        'text': 'TextFieldComponent',
        'email': 'TextFieldComponent',
        'url': 'TextFieldComponent',
        'password': 'TextFieldComponent',
        'textarea': 'TextareaFieldComponent',
        'select': 'SelectFieldComponent',
        'checkbox': 'CheckboxFieldComponent',
        'radio': 'RadioFieldComponent',
        'date': 'DateFieldComponent',
        'datetime': 'DateFieldComponent',
        'number': 'NumberFieldComponent',
        'slider': 'NumberFieldComponent',
        'file': 'FileFieldComponent',
        'switch': 'SwitchFieldComponent',
        'color': 'TextFieldComponent'
      }
      return componentMap[fieldType] || 'TextFieldComponent'
    },

    updateFieldValue(fieldName, value) {
      this.formData[fieldName] = value
      this.clearFieldError(fieldName)
      this.$emit('form-change', { fieldName, value, formData: this.formData })
    },

    handleFieldChange(event) {
      this.$emit('form-change', event)
    },

    clearFieldError(fieldName) {
      if (this.fieldErrors[fieldName]) {
        delete this.fieldErrors[fieldName]
      }
    },

    validateForm() {
      this.fieldErrors = {}
      let isValid = true

      this.formGroups.forEach(group => {
        group.fields.forEach(field => {
          if (field.is_required) {
            const value = this.formData[field.field_name]
            if (!value || (Array.isArray(value) && value.length === 0)) {
              this.fieldErrors[field.field_name] = `${field.field_label}是必填字段`
              isValid = false
            }
          }
        })
      })

      return isValid
    },

    handleSubmit() {
      if (!this.validateForm()) {
        this.$emit('show-toast', '请填写所有必填字段', '验证失败', 'warning')
        return
      }

      this.submitting = true
      this.$emit('form-submit', {
        formType: this.formType,
        formData: this.formData
      })
      
      // 模拟提交延迟
      setTimeout(() => {
        this.submitting = false
      }, 1000)
    },

    resetForm() {
      this.initializeFormData()
      this.fieldErrors = {}
      this.$emit('show-toast', '表单已重置', '提示', 'info')
    },

    saveDraft() {
      // 保存草稿功能
      localStorage.setItem(`form_draft_${this.formType}`, JSON.stringify(this.formData))
      this.$emit('show-toast', '草稿已保存', '成功', 'success')
    },

    previewData() {
      const modal = new window.bootstrap.Modal(document.getElementById('dataPreviewModal'))
      modal.show()
    }
  }
}
</script>

<style scoped>
.dynamic-form-renderer {
  max-width: 100%;
}

.form-group-section {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  background-color: #f8f9fa;
}

.group-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.group-title {
  color: #495057;
  margin-bottom: 0.5rem;
}

.group-description {
  font-size: 0.9rem;
}

.field-container {
  background-color: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.field-description {
  font-style: italic;
}

.form-actions {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
}

.badge {
  font-size: 0.75em;
}

.text-danger {
  color: #dc3545 !important;
}

.invalid-feedback {
  font-size: 0.875rem;
}

pre {
  font-size: 0.875rem;
  max-height: 400px;
  overflow-y: auto;
}
</style>
