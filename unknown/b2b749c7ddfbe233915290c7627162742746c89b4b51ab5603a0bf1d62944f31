<template>
  <div class="form-group mb-3">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <label class="form-label mb-0 fw-bold">部署组件</label>
      <div class="d-flex gap-2">
        <button type="button" class="btn btn-sm btn-outline-success" @click="refreshComponentData" :disabled="refreshing">
          <i class="bi" :class="refreshing ? 'bi-arrow-clockwise spin' : 'bi-arrow-clockwise'"></i>
          {{ refreshing ? '刷新中...' : '刷新组件' }}
        </button>

        <button type="button" class="btn btn-sm btn-outline-secondary" @click="toggleComponentGroups">
          <i class="bi" :class="serverInfo.组件折叠 ? 'bi-chevron-down' : 'bi-chevron-up'"></i>
          {{ serverInfo.组件折叠 ? '展开组件选择' : '折叠组件选择' }}
        </button>
      </div>
    </div>

    <!-- 已选组件摘要（折叠时显示） -->
    <div v-if="serverInfo.组件折叠" class="mb-3">
      <!-- 有选择组件时显示组件列表 -->
      <div v-if="serverInfo.部署应用 && serverInfo.部署应用.length > 0"
           class="d-flex flex-wrap gap-2 border rounded p-3 bg-light component-summary-clickable"
           @click="toggleComponentGroups"
           title="点击展开组件选择">
        <span v-for="componentName in serverInfo.部署应用" :key="componentName"
              class="badge" :class="getServiceBadgeClass(componentName)">
          {{ getComponentDisplayName(componentName) }}
        </span>
        <small class="text-primary ms-2 align-self-center fw-bold">
          <i class="bi bi-pencil-square me-1"></i>点击修改组件
        </small>
      </div>

      <!-- 没有选择组件时显示提示 -->
      <div v-else
           class="border rounded p-3 bg-light component-summary-clickable text-center"
           @click="toggleComponentGroups"
           title="点击选择部署组件">
        <i class="bi bi-plus-circle me-2 text-primary"></i>
        <span class="text-muted">暂未选择部署组件，</span>
        <span class="text-primary fw-bold">点击选择</span>
      </div>
    </div>

    <!-- 组件选择区域（展开时显示） -->
    <div v-if="!serverInfo.组件折叠" class="component-selection-area">
      <!-- 全选所有分组按钮 -->
      <div class="d-flex justify-content-between mb-3">
        <button type="button" class="btn btn-outline-info btn-sm" @click="showAliasManager = true">
          <i class="bi bi-tags me-1"></i>组件别名
        </button>
        <button type="button" class="btn btn-outline-primary" @click="toggleSelectAllGroupsComponents">
          {{ isAllGroupsComponentsSelected() ? '取消全选所有分组' : '全选所有分组' }}
        </button>
      </div>

      <!-- 组件分组容器 - 使用网格布局 -->
      <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-3">
        <!-- 安全测评组件 -->
        <template v-if="documentType === '安全测评'">
          <!-- 检查是否有组件数据 -->
          <template v-if="Object.keys(getGenericComponentGroups()).length > 0">
            <!-- 动态渲染所有组件分组 -->
            <div v-for="(components, groupKey) in getGenericComponentGroups()" :key="groupKey" class="col">
              <div class="card h-100">
                <div class="card-header text-white py-1 px-2" :class="getGroupHeaderClass(groupKey)">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="small">{{ getGroupDisplayName(groupKey) }}</span>
                    <button type="button" class="btn btn-sm btn-outline-light py-0 px-2" @click="toggleSelectAllComponents(groupKey)">
                      {{ isAllComponentsSelected(groupKey) ? '取消全选' : '全选' }}
                    </button>
                  </div>
                </div>
                <div class="card-body p-2">
                  <div class="component-list" style="max-height: 150px; overflow-y: auto;">
                    <div v-for="component in components" :key="component.name" class="form-check">
                      <input class="form-check-input" type="checkbox"
                            :id="'component' + index + '-' + component.name"
                            :value="component.name"
                            v-model="serverInfo.部署应用"
                            @change="handleComponentSelectionChange(component)">
                      <label class="form-check-label small" :for="'component' + index + '-' + component.name">
                        {{ getComponentDisplayName(component.name) }}
                        <span v-if="component.version" class="text-muted ms-1">v{{ component.version }}</span>
                        <span v-if="component.description" class="text-muted ms-1">- {{ component.description }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 使用端口卡片 - 所有表单类型都隐藏 -->
            <!-- 注释掉使用端口卡片，但保留端口配置功能 -->
            <!--
            <div class="col">
              <div class="card h-100">
                <div class="card-header bg-warning text-dark py-1 px-2">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="small">使用端口</span>
                    <button type="button" class="btn btn-sm btn-outline-dark py-0 px-2" @click="togglePortDisplay">
                      {{ showPorts ? '隐藏端口' : '显示端口' }}
                    </button>
                  </div>
                </div>
                <div class="card-body p-2" v-if="showPorts">
                  <div class="component-list" style="max-height: 150px; overflow-y: auto;">
                    <div v-for="component in getAllComponents()" :key="component.name" class="d-flex justify-content-between align-items-center mb-1">
                      <span class="small">{{ getComponentDisplayName(component.name) }}</span>
                      <span class="badge" :class="component.port === '无' ? 'bg-light text-dark' : 'bg-secondary'">{{ component.port }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            -->
          </template>

          <!-- 没有组件数据时的空状态提示 -->
          <template v-else>
            <div class="col-12">
              <div class="card">
                <div class="card-body text-center py-5">
                  <i class="bi bi-inbox display-4 text-muted mb-3"></i>
                  <h5 class="text-muted mb-2">暂无可用组件</h5>
                  <p class="text-muted mb-3">
                    当前表单类型 "{{ documentType }}" 还没有配置组件数据。
                  </p>
                  <div class="d-flex justify-content-center gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" @click="refreshComponentData" :disabled="refreshing">
                      <i class="bi" :class="refreshing ? 'bi-arrow-clockwise spin' : 'bi-arrow-clockwise'"></i>
                      {{ refreshing ? '刷新中...' : '刷新组件' }}
                    </button>
                    <a href="/component-manager" class="btn btn-outline-success btn-sm" target="_blank">
                      <i class="bi bi-gear me-1"></i>
                      组件管理
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </template>

        <!-- 安全监测组件 -->
        <template v-else-if="documentType === '安全监测'">
          <!-- 检查是否有组件数据 -->
          <template v-if="Object.keys(getSecurityComponentGroups()).length > 0">
            <!-- 动态渲染所有组件分组 -->
            <div v-for="(components, groupKey) in getSecurityComponentGroups()" :key="groupKey" class="col">
              <div class="card h-100">
                <div class="card-header text-white py-1 px-2" :class="getGroupHeaderClass(groupKey)">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="small">{{ getGroupDisplayName(groupKey) }}</span>
                    <button type="button" class="btn btn-sm btn-outline-light py-0 px-2" @click="toggleSelectAllComponents(groupKey)">
                      {{ isAllComponentsSelected(groupKey) ? '取消全选' : '全选' }}
                    </button>
                  </div>
                </div>
                <div class="card-body p-2">
                  <div class="component-list" style="max-height: 150px; overflow-y: auto;">
                    <div v-for="component in components" :key="component.name" class="form-check">
                      <input class="form-check-input" type="checkbox"
                            :id="'component' + index + '-' + component.name"
                            :value="component.name"
                            v-model="serverInfo.部署应用"
                            @change="handleComponentSelectionChange(component)">
                      <label class="form-check-label small" :for="'component' + index + '-' + component.name">
                        {{ getComponentDisplayName(component.name) }}
                        <span v-if="component.version" class="text-muted ms-1">v{{ component.version }}</span>
                        <span v-if="component.description" class="text-muted ms-1">- {{ component.description }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 使用端口卡片 - 所有表单类型都隐藏 -->
            <!-- 注释掉使用端口卡片，但保留端口配置功能 -->
            <!--
            <div class="col">
              <div class="card h-100">
                <div class="card-header bg-warning text-dark py-1 px-2">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="small">使用端口</span>
                    <button type="button" class="btn btn-sm btn-outline-dark py-0 px-2" @click="togglePortDisplay">
                      {{ showPorts ? '隐藏端口' : '显示端口' }}
                    </button>
                  </div>
                </div>
                <div class="card-body p-2" v-if="showPorts">
                  <div class="component-list" style="max-height: 150px; overflow-y: auto;">
                    <div v-for="component in getAllComponents()" :key="component.name" class="d-flex justify-content-between align-items-center mb-1">
                      <span class="small">{{ getComponentDisplayName(component.name) }}</span>
                      <span class="badge" :class="component.port === '无' ? 'bg-light text-dark' : 'bg-secondary'">{{ component.port }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            -->
          </template>

          <!-- 没有组件数据时的空状态提示 -->
          <template v-else>
            <div class="col-12">
              <div class="card">
                <div class="card-body text-center py-5">
                  <i class="bi bi-inbox display-4 text-muted mb-3"></i>
                  <h5 class="text-muted mb-2">暂无可用组件</h5>
                  <p class="text-muted mb-3">
                    当前表单类型 "{{ documentType }}" 还没有配置组件数据。
                  </p>
                  <div class="d-flex justify-content-center gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" @click="refreshComponentData" :disabled="refreshing">
                      <i class="bi" :class="refreshing ? 'bi-arrow-clockwise spin' : 'bi-arrow-clockwise'"></i>
                      {{ refreshing ? '刷新中...' : '刷新组件' }}
                    </button>
                    <a href="/component-manager" class="btn btn-outline-success btn-sm" target="_blank">
                      <i class="bi bi-gear me-1"></i>
                      组件管理
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </template>

        <!-- 应用加固组件 -->
        <template v-else-if="documentType === '应用加固'">
          <!-- 检查是否有组件数据 -->
          <template v-if="Object.keys(getGenericComponentGroups()).length > 0">
            <!-- 动态渲染所有组件分组 -->
            <div v-for="(components, groupKey) in getGenericComponentGroups()" :key="groupKey" class="col">
              <div class="card h-100">
                <div class="card-header text-white py-1 px-2" :class="getGroupHeaderClass(groupKey)">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="small">{{ getGroupDisplayName(groupKey) }}</span>
                    <button type="button" class="btn btn-sm btn-outline-light py-0 px-2" @click="toggleSelectAllComponents(groupKey)">
                      {{ isAllComponentsSelected(groupKey) ? '取消全选' : '全选' }}
                    </button>
                  </div>
                </div>
                <div class="card-body p-2">
                  <div class="component-list" style="max-height: 150px; overflow-y: auto;">
                    <div v-for="component in components" :key="component.name" class="form-check">
                      <input class="form-check-input" type="checkbox"
                            :id="'component' + index + '-' + component.name"
                            :value="component.name"
                            v-model="serverInfo.部署应用"
                            @change="handleComponentSelectionChange(component)">
                      <label class="form-check-label small" :for="'component' + index + '-' + component.name">
                        {{ getComponentDisplayName(component.name) }}
                        <span v-if="component.version" class="text-muted ms-1">v{{ component.version }}</span>
                        <span v-if="component.description" class="text-muted ms-1">- {{ component.description }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 没有组件数据时的空状态提示 -->
          <template v-else>
            <div class="col-12">
              <div class="card">
                <div class="card-body text-center py-5">
                  <i class="bi bi-inbox display-4 text-muted mb-3"></i>
                  <h5 class="text-muted mb-2">暂无可用组件</h5>
                  <p class="text-muted mb-3">
                    当前表单类型 "{{ documentType }}" 还没有配置组件数据。
                  </p>
                  <div class="d-flex justify-content-center gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" @click="refreshComponentData" :disabled="refreshing">
                      <i class="bi" :class="refreshing ? 'bi-arrow-clockwise spin' : 'bi-arrow-clockwise'"></i>
                      {{ refreshing ? '刷新中...' : '刷新组件' }}
                    </button>
                    <a href="/component-manager" class="btn btn-outline-success btn-sm" target="_blank">
                      <i class="bi bi-gear me-1"></i>
                      组件管理
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </template>

        <!-- 新表单类型的通用组件渲染 -->
        <template v-else>
          <!-- 检查是否有组件数据 -->
          <template v-if="Object.keys(getGenericComponentGroups()).length > 0">
            <!-- 动态渲染所有组件分组 -->
            <div v-for="(components, groupKey) in getGenericComponentGroups()" :key="groupKey" class="col">
              <div class="card h-100">
                <div class="card-header text-white py-1 px-2" :class="getGroupHeaderClass(groupKey)">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="small">{{ getGroupDisplayName(groupKey) }}</span>
                    <button type="button" class="btn btn-sm btn-outline-light py-0 px-2" @click="toggleSelectAllComponents(groupKey)">
                      {{ isAllComponentsSelected(groupKey) ? '取消全选' : '全选' }}
                    </button>
                  </div>
                </div>
                <div class="card-body p-2">
                  <div class="component-list" style="max-height: 150px; overflow-y: auto;">
                    <div v-for="component in components" :key="component.name" class="form-check">
                      <input class="form-check-input" type="checkbox"
                            :id="'component' + index + '-' + component.name"
                            :value="component.name"
                            v-model="serverInfo.部署应用"
                            @change="handleComponentSelectionChange(component)">
                      <label class="form-check-label small" :for="'component' + index + '-' + component.name">
                        {{ getComponentDisplayName(component.name) }}
                        <span v-if="component.version" class="text-muted ms-1">v{{ component.version }}</span>
                        <span v-if="component.description" class="text-muted ms-1">- {{ component.description }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 使用端口卡片 - 所有表单类型都隐藏 -->
            <!-- 注释掉使用端口卡片，但保留端口配置功能 -->
            <!--
            <div class="col">
              <div class="card h-100">
                <div class="card-header bg-warning text-dark py-1 px-2">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="small">使用端口</span>
                    <button type="button" class="btn btn-sm btn-outline-dark py-0 px-2" @click="togglePortDisplay">
                      {{ showPorts ? '隐藏端口' : '显示端口' }}
                    </button>
                  </div>
                </div>
                <div class="card-body p-2" v-if="showPorts">
                  <div class="component-list" style="max-height: 150px; overflow-y: auto;">
                    <div v-for="component in getAllComponents()" :key="component.name" class="d-flex justify-content-between align-items-center mb-1">
                      <span class="small">{{ getComponentDisplayName(component.name) }}</span>
                      <span class="badge" :class="component.port === '无' ? 'bg-light text-dark' : 'bg-secondary'">{{ component.port }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            -->
          </template>

          <!-- 没有组件数据时的空状态提示 -->
          <template v-else>
            <div class="col-12">
              <div class="card">
                <div class="card-body text-center py-5">
                  <i class="bi bi-inbox display-4 text-muted mb-3"></i>
                  <h5 class="text-muted mb-2">暂无可用组件</h5>
                  <p class="text-muted mb-3">
                    当前表单类型 "{{ documentType }}" 还没有配置组件数据。
                  </p>
                  <div class="d-flex justify-content-center gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" @click="refreshComponentData" :disabled="refreshing">
                      <i class="bi" :class="refreshing ? 'bi-arrow-clockwise spin' : 'bi-arrow-clockwise'"></i>
                      {{ refreshing ? '刷新中...' : '刷新组件' }}
                    </button>
                    <a href="/component-manager" class="btn btn-outline-success btn-sm" target="_blank">
                      <i class="bi bi-gear me-1"></i>
                      组件管理
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </template>
      </div>
    </div>

    <!-- 组件端口配置开关 -->
    <div v-if="serverInfo.部署应用 && serverInfo.部署应用.length > 0" class="mt-3">
      <div class="d-flex justify-content-between align-items-center mb-2">
        <h6 class="mb-0">组件端口配置</h6>
        <div class="d-flex align-items-center">
          <button type="button" class="btn btn-sm btn-outline-primary me-2" @click="togglePortEditArea">
            <i class="bi" :class="showPortEditArea ? 'bi-chevron-up' : 'bi-chevron-down'"></i>
            {{ showPortEditArea ? '收起' : '展开' }}
          </button>
          <div class="form-check form-switch" v-if="showPortEditArea">
            <input class="form-check-input" type="checkbox"
                  :id="'enablePortEdit' + index"
                  v-model="serverInfo.启用端口修改">
            <label class="form-check-label" :for="'enablePortEdit' + index">
              修改默认端口
            </label>
          </div>
        </div>
      </div>

      <!-- 端口修改界面（只在展开时显示，根据启用状态决定是否可编辑） -->
      <div v-if="showPortEditArea" class="port-edit-area">
        <div class="row">
          <div v-for="componentName in serverInfo.部署应用" :key="componentName" class="col-md-4 mb-2">
            <div class="input-group">
              <span class="input-group-text">{{ componentName }}</span>
              <input type="text" class="form-control port-input"
                    :placeholder="getComponentDefaultPort(componentName)"
                    v-model="serverInfo.组件端口[componentName]"
                    :readonly="!serverInfo.启用端口修改"
                    :class="{'bg-light': !serverInfo.启用端口修改}"
                    @focus="selectAllText">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 组件别名管理模态框 -->
    <ComponentAliasManager
      v-if="showAliasManager"
      :form-type="documentType"
      :component-groups="getCurrentFormTypeComponents()"
      @aliases-updated="handleAliasesUpdated"
      @show-toast="$emit('show-toast', $event)"
      @close="showAliasManager = false"
    />
  </div>
</template>

<script>
/**
 * 服务器组件选择器组件
 * 用于选择服务器上部署的组件
 */
import { getComponentDefaultPort } from '@/utils/componentUtils'
import ComponentAliasManager from './ComponentAliasManager.vue'

export default {
  name: 'ServerComponentSelector',
  components: {
    ComponentAliasManager
  },
  props: {
    // 服务器信息对象
    serverInfo: {
      type: Object,
      required: true
    },
    // 服务器索引
    index: {
      type: Number,
      required: true
    },
    // 文档类型
    documentType: {
      type: String,
      default: '安全测评'
    },
    // 组件分组
    componentGroups: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showPorts: false, // 控制是否显示端口信息
      showPortEditArea: false, // 控制是否显示端口编辑区域
      showAliasManager: false, // 是否显示别名管理器
      componentAliases: {}, // 组件别名
      refreshing: false // 是否正在刷新组件数据
    }
  },
  mounted() {
    // 组件挂载时，初始化所有已选组件的端口
    this.initializeComponentPorts()
    // 加载组件别名
    this.loadComponentAliases()
  },
  methods: {
    /**
     * 切换组件选择的折叠状态
     */
    toggleComponentGroups() {
      // 确保组件折叠属性存在，使用配置文件中的默认值
      if (typeof this.serverInfo.组件折叠 === 'undefined') {
        this.serverInfo.组件折叠 = false // 默认展开
      }
      // 切换折叠状态
      this.serverInfo.组件折叠 = !this.serverInfo.组件折叠
    },

    /**
     * 选中输入框中的所有文本
     * @param {Event} event - 焦点事件对象
     */
    selectAllText(event) {
      event.target.select()
    },

    /**
     * 获取表单类型对应的组件分组键
     * @param {String} documentType - 文档类型
     * @returns {String} - 组件分组键
     */
    getFormTypeKey(documentType) {
      const formTypeMapping = {
        '安全监测': 'security',
        '安全测评': 'testing',
        '应用加固': 'hardening'
      }

      // 对于新的表单类型，直接使用表单名称作为键
      return formTypeMapping[documentType] || documentType
    },

    /**
     * 获取组件分组
     * @param {String} formType - 表单类型（testing/security）
     * @param {String} groupKey - 组件分组键名
     * @returns {Array} - 组件数组
     */
    getComponentGroup(formType, groupKey) {
      // 调试信息
      console.log(`🔍 获取组件分组: formType=${formType}, groupKey=${groupKey}`)
      console.log('📦 当前componentGroups:', this.componentGroups)

      if (!this.componentGroups || !this.componentGroups[formType]) {
        console.warn(`⚠️ 组件分组不存在: ${formType}`)
        return []
      }

      const result = this.componentGroups[formType][groupKey] || []
      console.log(`📋 分组 ${groupKey} 的组件:`, result)
      return result
    },

    /**
     * 检查指定分组的所有组件是否都已选中
     * @param {String} groupKey - 组件分组的键名
     * @returns {Boolean} - 是否全部选中
     */
    isAllComponentsSelected(groupKey) {
      // 获取当前服务器的已选组件
      const selectedComponents = this.serverInfo.部署应用 || []

      // 使用新的方法获取表单类型键
      const formType = this.getFormTypeKey(this.documentType)

      // 获取指定分组的所有组件名称
      const groupComponents = this.getComponentGroup(formType, groupKey).map(component => component.name)

      // 检查分组中的所有组件是否都已选中
      return groupComponents.every(componentName => selectedComponents.includes(componentName))
    },

    /**
     * 切换指定分组的所有组件的选中状态
     * @param {String} groupKey - 组件分组的键名
     */
    toggleSelectAllComponents(groupKey) {
      // 获取当前服务器的已选组件
      const selectedComponents = this.serverInfo.部署应用 || []

      // 使用新的方法获取表单类型键
      const formType = this.getFormTypeKey(this.documentType)

      // 获取指定分组的所有组件名称
      const groupComponents = this.getComponentGroup(formType, groupKey).map(component => component.name)

      // 检查是否全部选中
      const isAllSelected = this.isAllComponentsSelected(groupKey)

      if (isAllSelected) {
        // 如果全部选中，则取消选中该分组的所有组件
        this.serverInfo.部署应用 = selectedComponents.filter(
          componentName => !groupComponents.includes(componentName)
        )

        // 更新组件端口
        for (const componentName of groupComponents) {
          this.updateComponentPort(componentName)
        }
      } else {
        // 如果未全部选中，则选中该分组的所有组件
        // 先创建一个新的数组，包含当前已选的组件
        const newSelectedComponents = [...selectedComponents]

        // 添加该分组中尚未选中的组件
        for (const componentName of groupComponents) {
          if (!newSelectedComponents.includes(componentName)) {
            newSelectedComponents.push(componentName)
          }
        }

        // 更新已选组件
        this.serverInfo.部署应用 = newSelectedComponents

        // 更新组件端口
        for (const componentName of groupComponents) {
          this.updateComponentPort(componentName)
        }
      }
    },

    /**
     * 检查是否所有分组的所有组件都已选中
     * @returns {Boolean} - 是否全部选中
     */
    isAllGroupsComponentsSelected() {
      // 获取当前服务器的已选组件
      const selectedComponents = this.serverInfo.部署应用 || []

      // 使用新的方法获取表单类型键
      const formType = this.getFormTypeKey(this.documentType)
      const componentSet = this.componentGroups[formType]

      // 获取所有组件名称
      let allComponents = []
      for (const groupKey in componentSet) {
        const groupComponents = componentSet[groupKey].map(component => component.name)
        allComponents = [...allComponents, ...groupComponents]
      }

      // 检查所有组件是否都已选中
      return allComponents.every(componentName => selectedComponents.includes(componentName))
    },

    /**
     * 切换所有分组的所有组件的选中状态
     */
    toggleSelectAllGroupsComponents() {
      // 获取当前服务器的已选组件
      // 不需要使用 selectedComponents 变量，直接使用 this.serverInfo.部署应用

      // 使用新的方法获取表单类型键
      const formType = this.getFormTypeKey(this.documentType)
      const componentSet = this.componentGroups[formType]

      // 获取所有组件名称
      let allComponents = []
      for (const groupKey in componentSet) {
        const groupComponents = componentSet[groupKey].map(component => component.name)
        allComponents = [...allComponents, ...groupComponents]
      }

      // 检查是否全部选中
      const isAllSelected = this.isAllGroupsComponentsSelected()

      if (isAllSelected) {
        // 如果全部选中，则取消选中所有组件
        this.serverInfo.部署应用 = []

        // 更新组件端口
        for (const componentName of allComponents) {
          this.updateComponentPort(componentName)
        }
      } else {
        // 如果未全部选中，则选中所有组件
        this.serverInfo.部署应用 = [...allComponents]

        // 更新组件端口
        for (const componentName of allComponents) {
          this.updateComponentPort(componentName)
        }
      }
    },

    /**
     * 获取组件的默认端口
     * @param {String} componentName - 组件名称
     * @returns {String} - 组件的默认端口
     */
    getComponentDefaultPort(componentName) {
      // 使用工具函数获取默认端口
      return getComponentDefaultPort(componentName, this.documentType)
    },

    /**
     * 更新组件端口
     * 当组件被选中时，自动设置默认端口
     * @param {String} componentName - 组件名称
     */
    updateComponentPort(componentName) {
      // 确保组件端口对象存在
      if (!this.serverInfo.组件端口) {
        this.serverInfo.组件端口 = {}
      }

      // 检查组件是否被选中
      const isSelected = this.serverInfo.部署应用.includes(componentName)

      if (isSelected) {
        // 如果组件被选中，则设置默认端口（无论是否已有值）
        const defaultPort = this.getComponentDefaultPort(componentName)
        if (defaultPort && defaultPort !== '无') {
          this.serverInfo.组件端口[componentName] = defaultPort
        }
        console.log(`设置组件 ${componentName} 的端口为 ${defaultPort}`)
      } else {
        // 如果组件被取消选中，则删除对应的端口
        if (this.serverInfo.组件端口[componentName]) {
          delete this.serverInfo.组件端口[componentName]
        }
      }
    },

    /**
     * 获取组件信息
     * 根据组件名称返回组件的详细信息
     * @param {String} componentName - 组件名称
     * @returns {Object|null} - 组件信息对象或null
     */
    getComponentInfo(componentName) {
      // 使用新的方法获取表单类型键
      const formType = this.getFormTypeKey(this.documentType)
      const componentSet = this.componentGroups[formType]

      // 在选定的组件集中查找组件
      for (const groupKey in componentSet) {
        const group = componentSet[groupKey]
        for (const component of group) {
          if (component.name === componentName) {
            return {
              ...component,
              category: groupKey
            }
          }
        }
      }
      return null
    },

    /**
     * 获取组件的徽章样式
     * 根据组件类别返回不同的Bootstrap徽章样式
     * @param {String} componentName - 组件名称
     * @returns {String} - 徽章样式类名
     */
    getServiceBadgeClass(componentName) {
      const component = this.getComponentInfo(componentName)

      if (!component) return 'bg-secondary'

      // 根据表单类型选择不同的颜色方案
      const formType = this.documentType
      let categoryColors = {}

      if (formType === '安全监测') {
        // 安全监测组件的颜色方案
        categoryColors = {
          'app': 'bg-primary',    // 应用组件为蓝色
          'server': 'bg-success', // 服务器组件为绿色
          'ops': 'bg-info'        // 运维组件为浅蓝色
        }
      } else if (formType === '安全测评') {
        // 应用测评组件的颜色方案
        categoryColors = {
          'base': 'bg-dark',       // 基础组件为深色
          'database': 'bg-success', // 数据库组件为绿色
          'tools': 'bg-info',      // 工具组件为浅蓝色
          'service': 'bg-primary', // 服务组件为蓝色
          'engine': 'bg-danger',   // 引擎组件为红色
          'frontend': 'bg-warning', // 前端组件为黄色
          'backend': 'bg-secondary' // 后端组件为灰色
        }
      } else if (formType === '应用加固') {
        // 应用加固组件的颜色方案
        categoryColors = {
          'base': 'bg-dark',       // 基础组件为深色
          'platform': 'bg-primary', // 平台组件为蓝色
          'engine': 'bg-danger',   // 加固引擎为红色
          'service': 'bg-success'  // 服务组件为绿色
        }
      } else {
        // 默认颜色方案
        categoryColors = {
          'app': 'bg-primary',
          'server': 'bg-success',
          'ops': 'bg-info'
        }
      }

      return categoryColors[component.category] || 'bg-secondary'
    },

    /**
     * 切换端口显示状态
     */
    togglePortDisplay() {
      this.showPorts = !this.showPorts
    },

    /**
     * 切换端口编辑区域的显示状态
     */
    togglePortEditArea() {
      this.showPortEditArea = !this.showPortEditArea
    },

    /**
     * 获取所有组件
     * @returns {Array} - 所有组件数组
     */
    getAllComponents() {
      // 使用新的方法获取表单类型键
      const formType = this.getFormTypeKey(this.documentType)

      if (!this.componentGroups || !this.componentGroups[formType]) {
        console.warn(`⚠️ 表单类型 ${formType} 的组件分组不存在`)
        return []
      }

      const componentSet = this.componentGroups[formType]

      // 获取所有组件
      let allComponents = []
      for (const groupKey in componentSet) {
        if (componentSet[groupKey] && Array.isArray(componentSet[groupKey])) {
          allComponents = [...allComponents, ...componentSet[groupKey]]
        }
      }

      // 按名称排序
      return allComponents.sort((a, b) => a.name.localeCompare(b.name))
    },

    /**
     * 初始化所有已选组件的端口
     * 在组件挂载时调用，确保所有已选组件都有默认端口
     */
    initializeComponentPorts() {
      // 确保部署应用和组件端口对象存在
      if (!this.serverInfo.部署应用) {
        this.serverInfo.部署应用 = []
      }

      if (!this.serverInfo.组件端口) {
        this.serverInfo.组件端口 = {}
      }

      // 为所有已选组件设置默认端口
      if (this.serverInfo.部署应用.length > 0) {
        for (const componentName of this.serverInfo.部署应用) {
          if (!this.serverInfo.组件端口[componentName]) {
            const defaultPort = this.getComponentDefaultPort(componentName)
            if (defaultPort && defaultPort !== '无') {
              this.serverInfo.组件端口[componentName] = defaultPort
              console.log(`初始化组件 ${componentName} 的端口为 ${defaultPort}`)
            }
          }
        }
      }
    },

    /**
     * 获取组件显示名称（优先使用别名）
     */
    getComponentDisplayName(componentName) {
      const alias = this.componentAliases[componentName]
      return alias && alias.trim() ? alias : componentName
    },

    /**
     * 获取当前表单类型的组件分组
     */
    getCurrentFormTypeComponents() {
      const formType = this.getFormTypeKey(this.documentType)
      return this.componentGroups[formType] || {}
    },

    /**
     * 加载组件别名
     */
    loadComponentAliases() {
      const storageKey = `component_aliases_${this.documentType}`
      const saved = localStorage.getItem(storageKey)
      if (saved) {
        try {
          this.componentAliases = JSON.parse(saved)
        } catch (e) {
          console.warn('加载组件别名失败:', e)
          this.componentAliases = {}
        }
      }
    },

    /**
     * 保存组件别名
     */
    saveComponentAliases() {
      const storageKey = `component_aliases_${this.documentType}`
      localStorage.setItem(storageKey, JSON.stringify(this.componentAliases))
    },

    /**
     * 更新组件别名
     */
    updateComponentAlias(componentId, alias) {
      this.componentAliases[componentId] = alias.trim()
      this.saveComponentAliases()
    },

    /**
     * 处理别名更新事件
     */
    handleAliasesUpdated(newAliases) {
      this.componentAliases = { ...newAliases }
      this.showAliasManager = false
      this.$emit('show-toast', '组件别名已更新', '成功', 'success')
    },

    /**
     * 刷新组件数据
     */
    async refreshComponentData() {
      if (this.refreshing) return

      this.refreshing = true
      try {
        console.log('🔄 开始刷新组件数据...')

        // 触发父组件重新加载组件数据
        this.$emit('refresh-components')

        // 显示成功提示
        this.$emit('show-toast', '组件数据已刷新', '成功', 'success')

        console.log('✅ 组件数据刷新完成')
      } catch (error) {
        console.error('❌ 刷新组件数据失败:', error)
        this.$emit('show-toast', '刷新组件数据失败: ' + error.message, '错误', 'error')
      } finally {
        // 延迟一点时间再隐藏加载状态，让用户看到反馈
        setTimeout(() => {
          this.refreshing = false
        }, 500)
      }
    },

    /**
     * 获取安全监测的组件分组（动态）
     */
    getSecurityComponentGroups() {
      if (!this.componentGroups || !this.componentGroups.security) {
        console.warn('⚠️ 安全监测组件分组不存在')
        return {}
      }

      // 检查组件分组是否为空
      const componentSet = this.componentGroups.security
      const hasComponents = Object.keys(componentSet).some(groupKey => {
        return componentSet[groupKey] && Array.isArray(componentSet[groupKey]) && componentSet[groupKey].length > 0
      })

      if (!hasComponents) {
        console.warn('⚠️ 安全监测组件分组为空')
        return {}
      }

      // 过滤掉使用端口分组，因为它有特殊处理
      const groups = { ...componentSet }
      delete groups.ports // 如果有的话

      console.log('📦 安全监测组件分组:', Object.keys(groups))
      return groups
    },

    /**
     * 获取通用表单类型的组件分组（动态）
     */
    getGenericComponentGroups() {
      const formType = this.getFormTypeKey(this.documentType)

      if (!this.componentGroups || !this.componentGroups[formType]) {
        console.warn(`⚠️ ${this.documentType} 组件分组不存在`)
        return {}
      }

      // 检查组件分组是否为空
      const componentSet = this.componentGroups[formType]
      const hasComponents = Object.keys(componentSet).some(groupKey => {
        return componentSet[groupKey] && Array.isArray(componentSet[groupKey]) && componentSet[groupKey].length > 0
      })

      if (!hasComponents) {
        console.warn(`⚠️ ${this.documentType} 组件分组为空`)
        return {}
      }

      // 过滤掉使用端口分组，因为它有特殊处理
      const groups = { ...componentSet }
      delete groups.ports // 如果有的话

      console.log(`📦 ${this.documentType} 组件分组:`, Object.keys(groups))
      return groups
    },

    /**
     * 获取分组的显示名称
     */
    getGroupDisplayName(groupKey) {
      const displayNames = {
        'app': '应用组件',
        'server': '服务组件',
        'ops': '运维组件',
        'anquan-database': '数据库',
        'database': '数据库组件',
        'base': '基础组件',
        'tools': '工具组件',
        'service': '服务组件',
        'engine': '引擎组件',
        'frontend': '前端组件',
        'backend': '后端组件',
        'platform': '平台组件'
      }

      return displayNames[groupKey] || groupKey
    },

    /**
     * 获取分组头部的样式类
     */
    getGroupHeaderClass(groupKey) {
      const headerClasses = {
        'app': 'bg-primary',
        'server': 'bg-success',
        'ops': 'bg-info',
        'anquan-database': 'bg-warning text-dark',
        'database': 'bg-success',
        'base': 'bg-dark',
        'tools': 'bg-info',
        'service': 'bg-primary',
        'engine': 'bg-danger',
        'frontend': 'bg-warning text-dark',
        'backend': 'bg-secondary',
        'platform': 'bg-primary'
      }

      return headerClasses[groupKey] || 'bg-secondary'
    },

    // 处理组件选择状态变化
    handleComponentSelectionChange(component) {
      // 找到完整的组件对象
      const selectedComponent = this.findComponentDetails(component.name);
      if (!selectedComponent) return;

      // 确保 serverInfo.selectedComponentDetails 存在
      if (!this.serverInfo.selectedComponentDetails) {
        this.serverInfo.selectedComponentDetails = {};
      }

      // 更新 selectedComponentDetails
      if (this.serverInfo.部署应用.includes(component.name)) {
        // 组件被选中，添加详细信息
        this.serverInfo.selectedComponentDetails[component.name] = selectedComponent;
        // 如果端口是空的，使用默认端口填充
        if (!this.serverInfo.组件端口[component.name]) {
           this.serverInfo.组件端口[component.name] = selectedComponent.defaultPort || '';
        }
      } else {
        // 组件被取消选中，移除详细信息
        if (this.serverInfo.selectedComponentDetails[component.name]) {
          delete this.serverInfo.selectedComponentDetails[component.name];
        }
        // 移除端口信息
        if (this.serverInfo.组件端口[component.name]) {
             delete this.serverInfo.组件端口[component.name];
        }
      }
    },

    // 根据组件名称查找完整的组件详细信息
    findComponentDetails(componentName) {
      // 只在当前表单类型中查找组件，避免重复
      const formType = this.getFormTypeKey(this.documentType)

      if (!this.componentGroups || !this.componentGroups[formType]) {
        console.warn(`⚠️ 表单类型 ${formType} 的组件分组不存在`)
        return null
      }

      const currentFormComponents = this.componentGroups[formType]
      for (const category in currentFormComponents) {
        if (currentFormComponents[category] && Array.isArray(currentFormComponents[category])) {
          const found = currentFormComponents[category].find(comp => comp.name === componentName);
          if (found) {
            return found;
          }
        }
      }
      return null;
    }
  }
}
</script>

<style scoped>
/* 组件选择区域样式 */
.component-selection-area {
  animation: fadeIn 0.3s ease;
}

/* 端口修改区域样式 */
.port-edit-area {
  animation: fadeIn 0.3s ease;
}

/* 端口输入框样式 */
.port-input {
  cursor: pointer;
  transition: all 0.2s ease;
}

.port-input:hover {
  background-color: #f8f9fa;
}

.port-input:focus {
  background-color: #fff;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 组件卡片样式 */
.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

.form-check-label {
  cursor: pointer;
}

.form-check-input {
  cursor: pointer;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 可点击的组件摘要样式 */
.component-summary-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.component-summary-clickable:hover {
  background-color: #e9ecef !important;
  border-color: #007bff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

/* 旋转动画 */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
