/**
 * 模态框调试工具
 * 用于诊断和修复Bootstrap模态框问题
 */

/**
 * 检查Bootstrap是否正确加载
 */
export function checkBootstrapStatus() {
  console.log('=== Bootstrap状态检查 ===')
  
  const checks = {
    windowBootstrap: !!window.bootstrap,
    modalClass: !!(window.bootstrap && window.bootstrap.Modal),
    jqueryBootstrap: !!(window.jQuery && window.jQuery.fn.modal),
    bootstrapCSS: checkBootstrapCSS()
  }
  
  console.log('Bootstrap检查结果:', checks)
  
  if (checks.windowBootstrap && checks.modalClass) {
    console.log('✅ Bootstrap Modal可用')
    return true
  } else {
    console.error('❌ Bootstrap Modal不可用')
    return false
  }
}

/**
 * 检查Bootstrap CSS是否加载
 */
function checkBootstrapCSS() {
  const stylesheets = Array.from(document.styleSheets)
  return stylesheets.some(sheet => {
    try {
      return sheet.href && sheet.href.includes('bootstrap')
    } catch (e) {
      return false
    }
  })
}

/**
 * 诊断模态框问题
 * @param {string} modalId - 模态框ID
 */
export function diagnoseModal(modalId) {
  console.log(`=== 模态框诊断: ${modalId} ===`)
  
  const modalElement = document.getElementById(modalId)
  if (!modalElement) {
    console.error(`❌ 模态框元素未找到: ${modalId}`)
    return false
  }
  
  console.log('✅ 模态框元素已找到:', modalElement)
  
  // 检查模态框的基本属性
  const diagnostics = {
    hasModalClass: modalElement.classList.contains('modal'),
    hasTabIndex: modalElement.hasAttribute('tabindex'),
    isVisible: modalElement.style.display !== 'none',
    hasBackdrop: !!document.querySelector('.modal-backdrop'),
    zIndex: window.getComputedStyle(modalElement).zIndex,
    position: window.getComputedStyle(modalElement).position
  }
  
  console.log('模态框诊断结果:', diagnostics)
  
  // 检查是否有其他模态框打开
  const openModals = document.querySelectorAll('.modal.show')
  if (openModals.length > 0) {
    console.warn('⚠️ 检测到其他打开的模态框:', openModals)
  }
  
  return diagnostics
}

/**
 * 强制清理模态框状态
 * @param {string} modalId - 模态框ID
 */
export function forceCleanModal(modalId) {
  console.log(`🧹 强制清理模态框: ${modalId}`)
  
  try {
    const modalElement = document.getElementById(modalId)
    if (modalElement) {
      // 移除show类
      modalElement.classList.remove('show')
      modalElement.style.display = 'none'
      modalElement.setAttribute('aria-hidden', 'true')
      modalElement.removeAttribute('aria-modal')
    }
    
    // 清理body类
    document.body.classList.remove('modal-open')
    
    // 移除所有backdrop
    const backdrops = document.querySelectorAll('.modal-backdrop')
    backdrops.forEach(backdrop => backdrop.remove())
    
    // 清理Bootstrap实例
    if (window.bootstrap && window.bootstrap.Modal) {
      const instance = window.bootstrap.Modal.getInstance(modalElement)
      if (instance) {
        instance.dispose()
      }
    }
    
    console.log('✅ 模态框状态已清理')
    return true
  } catch (error) {
    console.error('❌ 清理模态框时出错:', error)
    return false
  }
}

/**
 * 安全显示模态框
 * @param {string} modalId - 模态框ID
 * @param {Object} options - 模态框选项
 */
export function safeShowModal(modalId, options = {}) {
  console.log(`🔧 安全显示模态框: ${modalId}`)
  
  // 检查Bootstrap状态
  if (!checkBootstrapStatus()) {
    alert('Bootstrap未正确加载，请刷新页面重试')
    return false
  }
  
  // 诊断模态框
  const diagnostics = diagnoseModal(modalId)
  if (!diagnostics) {
    return false
  }
  
  // 清理可能存在的状态
  forceCleanModal(modalId)
  
  // 等待一帧后显示模态框
  return new Promise((resolve) => {
    requestAnimationFrame(() => {
      try {
        const modalElement = document.getElementById(modalId)
        
        // 默认选项
        const defaultOptions = {
          backdrop: true,
          keyboard: true,
          focus: true
        }
        
        const finalOptions = { ...defaultOptions, ...options }
        
        // 创建模态框实例
        const modal = new window.bootstrap.Modal(modalElement, finalOptions)
        
        // 添加事件监听器
        modalElement.addEventListener('shown.bs.modal', () => {
          console.log('✅ 模态框已显示')
          // 自动聚焦到第一个可聚焦元素
          const focusableElement = modalElement.querySelector('input, textarea, select, button')
          if (focusableElement) {
            focusableElement.focus()
          }
          resolve(true)
        }, { once: true })
        
        modalElement.addEventListener('hidden.bs.modal', () => {
          console.log('✅ 模态框已隐藏')
        }, { once: true })
        
        // 显示模态框
        modal.show()
        console.log('✅ 模态框显示命令已执行')
        
      } catch (error) {
        console.error('❌ 显示模态框时出错:', error)
        alert('显示模态框失败: ' + error.message)
        resolve(false)
      }
    })
  })
}

/**
 * 检查页面中所有模态框的状态
 */
export function checkAllModals() {
  console.log('=== 检查所有模态框状态 ===')
  
  const modals = document.querySelectorAll('.modal')
  modals.forEach((modal, index) => {
    console.log(`模态框 ${index + 1}:`, {
      id: modal.id,
      classes: Array.from(modal.classList),
      display: modal.style.display,
      zIndex: window.getComputedStyle(modal).zIndex,
      hasInstance: !!(window.bootstrap && window.bootstrap.Modal.getInstance(modal))
    })
  })
  
  const backdrops = document.querySelectorAll('.modal-backdrop')
  console.log(`发现 ${backdrops.length} 个backdrop元素`)
  
  const bodyClasses = Array.from(document.body.classList)
  console.log('Body类:', bodyClasses)
}

// 导出调试工具到全局（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  window.modalDebugger = {
    checkBootstrapStatus,
    diagnoseModal,
    forceCleanModal,
    safeShowModal,
    checkAllModals
  }
  console.log('🔧 模态框调试工具已加载到 window.modalDebugger')
}

export default {
  checkBootstrapStatus,
  diagnoseModal,
  forceCleanModal,
  safeShowModal,
  checkAllModals
}
