#!/bin/bash

# Export Excel 服务管理脚本
# 用于管理后端服务和nginx的启动、停止、重启等操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}[SERVICE]${NC} $1"
}

# 配置
PROJECT_DIR=$(pwd)
BACKEND_DIR="$PROJECT_DIR/backend"
PID_FILE="$PROJECT_DIR/backend.pid"
LOG_FILE="$PROJECT_DIR/logs/backend.log"
NGINX_CONF="/etc/nginx/conf.d/export_excel.conf"

# 显示帮助
show_help() {
    echo -e "${CYAN}Export Excel 服务管理脚本${NC}"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|nginx-start|nginx-stop|nginx-restart|nginx-status|full-start|full-stop|full-restart|full-status}"
    echo ""
    echo -e "${YELLOW}后端服务命令:${NC}"
    echo "  start           启动后端服务"
    echo "  stop            停止后端服务"
    echo "  restart         重启后端服务"
    echo "  status          查看后端服务状态"
    echo "  logs            查看后端服务日志"
    echo ""
    echo -e "${YELLOW}Nginx服务命令:${NC}"
    echo "  nginx-start     启动nginx服务"
    echo "  nginx-stop      停止nginx服务"
    echo "  nginx-restart   重启nginx服务"
    echo "  nginx-status    查看nginx服务状态"
    echo ""
    echo -e "${YELLOW}完整服务命令:${NC}"
    echo "  full-start      启动所有服务(后端+nginx)"
    echo "  full-stop       停止所有服务(后端+nginx)"
    echo "  full-restart    重启所有服务(后端+nginx)"
    echo "  full-status     查看所有服务状态"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  $0 start                # 启动后端服务"
    echo "  $0 nginx-restart        # 重启nginx"
    echo "  $0 full-status          # 查看所有服务状态"
    echo "  $0 logs                 # 查看后端日志"
}

# 检查服务状态
check_backend_status() {
    if [[ -f "$PID_FILE" ]]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            return 0  # 服务运行中
        else
            rm -f "$PID_FILE"  # 清理无效的 PID 文件
            return 1  # 服务未运行
        fi
    else
        return 1  # 服务未运行
    fi
}

# 检查nginx状态
check_nginx_status() {
    if systemctl is-active --quiet nginx 2>/dev/null; then
        return 0  # nginx运行中
    else
        return 1  # nginx未运行
    fi
}

# 启动后端服务
start_backend() {
    log_header "启动后端服务"

    # 检查服务是否已运行
    if check_backend_status; then
        log_warning "后端服务已在运行中 (PID: $(cat $PID_FILE))"
        return 0
    fi

    # 检查后端目录
    if [[ ! -d "$BACKEND_DIR" ]]; then
        log_error "后端目录不存在: $BACKEND_DIR"
        exit 1
    fi

    cd "$BACKEND_DIR"

    # 创建日志目录
    mkdir -p "$(dirname $LOG_FILE)"

    # 检查虚拟环境
    if [[ -d "venv" ]]; then
        log_info "激活虚拟环境..."
        source venv/bin/activate
    elif [[ -d "../venv" ]]; then
        log_info "激活项目虚拟环境..."
        source ../venv/bin/activate
    else
        log_warning "未发现虚拟环境，使用系统 Python"
    fi

    # 设置环境变量
    export FLASK_APP=run.py
    export FLASK_ENV=production

    # 检查端口是否被占用
    if lsof -Pi :5000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 5000 已被占用"
        log_info "尝试查找占用进程..."
        lsof -Pi :5000 -sTCP:LISTEN
        read -p "是否要停止占用进程? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            pkill -f "python.*run.py" || true
            sleep 2
        else
            log_error "无法启动服务，端口被占用"
            return 1
        fi
    fi

    # 启动服务
    log_info "启动 Flask 应用..."
    nohup python run.py > "$LOG_FILE" 2>&1 &
    PID=$!

    # 保存 PID
    echo $PID > "$PID_FILE"

    # 等待服务启动
    sleep 3

    # 检查服务是否启动成功
    if check_backend_status; then
        log_success "后端服务启动成功 (PID: $PID)"
        log_info "日志文件: $LOG_FILE"
        log_info "访问地址: http://localhost:5000"
    else
        log_error "后端服务启动失败"
        if [[ -f "$LOG_FILE" ]]; then
            log_error "错误日志:"
            tail -10 "$LOG_FILE"
        fi
        return 1
    fi

    cd - > /dev/null
}

# 停止后端服务
stop_backend() {
    log_header "停止后端服务"

    if check_backend_status; then
        PID=$(cat "$PID_FILE")
        log_info "停止服务 (PID: $PID)..."

        # 优雅停止
        kill $PID 2>/dev/null || true

        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p $PID > /dev/null 2>&1; then
                break
            fi
            sleep 1
        done

        # 强制停止
        if ps -p $PID > /dev/null 2>&1; then
            log_warning "强制停止服务..."
            kill -9 $PID 2>/dev/null || true
        fi

        rm -f "$PID_FILE"
        log_success "后端服务已停止"
    else
        log_warning "后端服务未运行"
    fi

    # 清理可能残留的进程
    pkill -f "python.*run.py" 2>/dev/null || true
}

# 重启后端服务
restart_backend() {
    log_header "重启后端服务"
    stop_backend
    sleep 2
    start_backend
}

# 查看后端服务状态
show_backend_status() {
    log_header "后端服务状态"

    if check_backend_status; then
        PID=$(cat "$PID_FILE")
        log_success "后端服务运行中 (PID: $PID)"

        # 显示进程信息
        echo ""
        echo "进程信息:"
        ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || echo "  无法获取进程信息"

        # 显示端口信息
        if command -v lsof &> /dev/null; then
            echo ""
            echo "端口信息:"
            lsof -Pi :5000 -sTCP:LISTEN 2>/dev/null || echo "  未监听端口 5000"
        fi

        # 显示最近日志
        if [[ -f "$LOG_FILE" ]]; then
            echo ""
            echo "最近日志 (最后10行):"
            tail -10 "$LOG_FILE" 2>/dev/null || echo "  无法读取日志文件"
        fi
    else
        log_warning "后端服务未运行"

        # 检查是否有残留进程
        if pgrep -f "python.*run.py" > /dev/null; then
            echo ""
            log_warning "发现可能的残留进程:"
            pgrep -f "python.*run.py" | xargs ps -p 2>/dev/null || true
        fi
    fi
}

# 查看后端日志
show_backend_logs() {
    log_header "后端服务日志"

    if [[ -f "$LOG_FILE" ]]; then
        log_info "显示服务日志 (按 Ctrl+C 退出):"
        echo ""
        tail -f "$LOG_FILE"
    else
        log_warning "日志文件不存在: $LOG_FILE"

        # 尝试查找其他可能的日志文件
        if [[ -d "logs" ]]; then
            echo ""
            log_info "logs 目录中的文件:"
            ls -la logs/ 2>/dev/null || echo "  目录为空"
        fi
    fi
}

# 启动nginx服务
start_nginx() {
    log_header "启动 Nginx 服务"

    if check_nginx_status; then
        log_warning "Nginx 已在运行中"
        return 0
    fi

    # 检查配置文件
    if [[ ! -f "$NGINX_CONF" ]]; then
        log_error "Nginx 配置文件不存在: $NGINX_CONF"
        log_info "请先运行部署脚本: ./deploy.sh"
        return 1
    fi

    # 测试配置
    log_info "测试 Nginx 配置..."
    if ! sudo nginx -t; then
        log_error "Nginx 配置测试失败"
        return 1
    fi

    # 启动服务
    log_info "启动 Nginx 服务..."
    if sudo systemctl start nginx; then
        log_success "Nginx 服务启动成功"
        log_info "访问地址: http://localhost:9999"
    else
        log_error "Nginx 服务启动失败"
        return 1
    fi
}

# 停止nginx服务
stop_nginx() {
    log_header "停止 Nginx 服务"

    if check_nginx_status; then
        log_info "停止 Nginx 服务..."
        if sudo systemctl stop nginx; then
            log_success "Nginx 服务已停止"
        else
            log_error "Nginx 服务停止失败"
            return 1
        fi
    else
        log_warning "Nginx 服务未运行"
    fi
}

# 重启nginx服务
restart_nginx() {
    log_header "重启 Nginx 服务"

    # 测试配置
    log_info "测试 Nginx 配置..."
    if ! sudo nginx -t; then
        log_error "Nginx 配置测试失败，取消重启"
        return 1
    fi

    log_info "重启 Nginx 服务..."
    if sudo systemctl restart nginx; then
        log_success "Nginx 服务重启成功"
        log_info "访问地址: http://localhost:9999"
    else
        log_error "Nginx 服务重启失败"
        return 1
    fi
}

# 查看nginx服务状态
show_nginx_status() {
    log_header "Nginx 服务状态"

    if check_nginx_status; then
        log_success "Nginx 服务运行中"

        # 显示详细状态
        echo ""
        echo "服务状态:"
        sudo systemctl status nginx --no-pager -l 2>/dev/null || echo "  无法获取详细状态"

        # 显示端口信息
        if command -v lsof &> /dev/null; then
            echo ""
            echo "端口信息:"
            lsof -Pi :9999 -sTCP:LISTEN 2>/dev/null || echo "  未监听端口 9999"
            lsof -Pi :80 -sTCP:LISTEN 2>/dev/null || echo "  未监听端口 80"
        fi

        # 显示配置文件
        if [[ -f "$NGINX_CONF" ]]; then
            echo ""
            echo "配置文件: $NGINX_CONF"
            echo "配置测试:"
            sudo nginx -t 2>&1 | head -5
        fi
    else
        log_warning "Nginx 服务未运行"

        # 显示服务状态
        echo ""
        echo "服务状态:"
        sudo systemctl status nginx --no-pager -l 2>/dev/null || echo "  无法获取服务状态"
    fi
}

# 完整服务管理
full_start() {
    log_header "启动所有服务"
    echo ""
    start_nginx
    echo ""
    start_backend
    echo ""
    log_success "所有服务启动完成"
    echo ""
    log_info "访问地址:"
    echo "  - 前端页面: http://localhost:9999"
    echo "  - 后端API: http://localhost:5000"
}

full_stop() {
    log_header "停止所有服务"
    echo ""
    stop_backend
    echo ""
    stop_nginx
    echo ""
    log_success "所有服务已停止"
}

full_restart() {
    log_header "重启所有服务"
    echo ""
    stop_backend
    echo ""
    restart_nginx
    echo ""
    start_backend
    echo ""
    log_success "所有服务重启完成"
    echo ""
    log_info "访问地址:"
    echo "  - 前端页面: http://localhost:9999"
    echo "  - 后端API: http://localhost:5000"
}

full_status() {
    log_header "所有服务状态"
    echo ""
    show_nginx_status
    echo ""
    echo "=================================================="
    echo ""
    show_backend_status
    echo ""

    # 显示整体状态摘要
    echo "=================================================="
    log_header "服务状态摘要"

    if check_nginx_status; then
        echo -e "  Nginx:  ${GREEN}运行中${NC}"
    else
        echo -e "  Nginx:  ${RED}已停止${NC}"
    fi

    if check_backend_status; then
        echo -e "  后端:   ${GREEN}运行中${NC} (PID: $(cat $PID_FILE 2>/dev/null || echo 'N/A'))"
    else
        echo -e "  后端:   ${RED}已停止${NC}"
    fi

    echo ""
    if check_nginx_status && check_backend_status; then
        log_success "所有服务正常运行"
        echo "  访问地址: http://localhost:9999"
    else
        log_warning "部分服务未运行"
        echo "  使用 '$0 full-start' 启动所有服务"
    fi
}

# 主逻辑
case "${1:-}" in
    start)
        start_backend
        ;;
    stop)
        stop_backend
        ;;
    restart)
        restart_backend
        ;;
    status)
        show_backend_status
        ;;
    logs)
        show_backend_logs
        ;;
    nginx-start)
        start_nginx
        ;;
    nginx-stop)
        stop_nginx
        ;;
    nginx-restart)
        restart_nginx
        ;;
    nginx-status)
        show_nginx_status
        ;;
    full-start)
        full_start
        ;;
    full-stop)
        full_stop
        ;;
    full-restart)
        full_restart
        ;;
    full-status)
        full_status
        ;;
    *)
        show_help
        exit 1
        ;;
esac