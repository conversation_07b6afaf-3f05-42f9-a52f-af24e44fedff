<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-people-fill me-2"></i>
            {{ isEdit ? '编辑用户组' : '新建用户组' }}
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handleSubmit">
            <!-- 基本信息 -->
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label">用户组名称 <span class="text-danger">*</span></label>
                <input 
                  type="text" 
                  class="form-control" 
                  v-model="formData.name"
                  :class="{ 'is-invalid': errors.name }"
                  placeholder="请输入用户组名称"
                  required
                >
                <div v-if="errors.name" class="invalid-feedback">{{ errors.name }}</div>
              </div>
              <div class="col-md-6">
                <label class="form-label">用户组代码 <span class="text-danger">*</span></label>
                <input 
                  type="text" 
                  class="form-control" 
                  v-model="formData.code"
                  :class="{ 'is-invalid': errors.code }"
                  placeholder="请输入用户组代码"
                  :disabled="isEdit"
                  required
                >
                <div v-if="errors.code" class="invalid-feedback">{{ errors.code }}</div>
                <small v-if="isEdit" class="text-muted">编辑时不能修改用户组代码</small>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label">描述</label>
              <textarea 
                class="form-control" 
                v-model="formData.description"
                rows="3"
                placeholder="请输入用户组描述"
              ></textarea>
            </div>

            <!-- 角色分配 -->
            <div class="mb-3">
              <label class="form-label fw-semibold">
                <i class="bi bi-person-gear me-1"></i>关联角色
              </label>
              <div class="role-transfer-container">
                <div class="row g-3">
                  <!-- 可选角色 -->
                  <div class="col-5">
                    <div class="transfer-panel">
                      <div class="panel-header">
                        <small class="text-muted fw-semibold">可选角色</small>
                        <span class="badge bg-secondary">{{ availableRolesList.length }}</span>
                      </div>
                      <div class="panel-body">
                        <div v-if="availableRolesList.length === 0" class="text-muted text-center py-3">
                          <i class="bi bi-info-circle"></i>
                          <div>暂无可选角色</div>
                        </div>
                        <div v-else>
                          <div
                            v-for="role in availableRolesList"
                            :key="role.id"
                            class="transfer-item"
                            @click="addRole(role)"
                          >
                            <div class="d-flex align-items-center">
                              <div class="flex-grow-1">
                                <div class="fw-semibold">{{ role.name }}</div>
                                <small class="text-muted">{{ role.description || '无描述' }}</small>
                              </div>
                              <div class="transfer-actions">
                                <span v-if="role.is_system" class="badge bg-warning text-dark me-2">系统</span>
                                <i class="bi bi-plus-circle text-primary"></i>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="col-2 d-flex flex-column justify-content-center align-items-center">
                    <button
                      type="button"
                      class="btn btn-outline-primary btn-sm mb-2"
                      @click="addAllRoles"
                      :disabled="availableRolesList.length === 0"
                      title="添加全部"
                    >
                      <i class="bi bi-chevron-double-right"></i>
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-secondary btn-sm"
                      @click="removeAllRoles"
                      :disabled="selectedRolesList.length === 0"
                      title="移除全部"
                    >
                      <i class="bi bi-chevron-double-left"></i>
                    </button>
                  </div>

                  <!-- 已选角色 -->
                  <div class="col-5">
                    <div class="transfer-panel">
                      <div class="panel-header">
                        <small class="text-muted fw-semibold">已选角色</small>
                        <span class="badge bg-primary">{{ selectedRolesList.length }}</span>
                      </div>
                      <div class="panel-body">
                        <div v-if="selectedRolesList.length === 0" class="text-muted text-center py-3">
                          <i class="bi bi-info-circle"></i>
                          <div>暂无已选角色</div>
                        </div>
                        <div v-else>
                          <div
                            v-for="role in selectedRolesList"
                            :key="role.id"
                            class="transfer-item selected"
                            @click="removeRole(role)"
                          >
                            <div class="d-flex align-items-center">
                              <div class="flex-grow-1">
                                <div class="fw-semibold">{{ role.name }}</div>
                                <small class="text-muted">{{ role.description || '无描述' }}</small>
                              </div>
                              <div class="transfer-actions">
                                <span v-if="role.is_system" class="badge bg-warning text-dark me-2">系统</span>
                                <i class="bi bi-dash-circle text-danger"></i>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 状态 -->
            <div class="mb-3">
              <div class="form-check form-switch">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="groupStatus"
                  v-model="formData.is_active"
                >
                <label class="form-check-label" for="groupStatus">
                  启用用户组
                </label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            取消
          </button>
          <button type="button" class="btn btn-primary" @click="handleSubmit" :disabled="loading">
            <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
            {{ isEdit ? '保存' : '创建' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GroupEditModal',
  props: {
    group: {
      type: Object,
      default: null
    },
    availableRoles: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'save'],
  data() {
    return {
      loading: false,
      errors: {},
      formData: {
        name: '',
        code: '',
        description: '',
        is_active: true,
        roleIds: []
      }
    }
  },
  computed: {
    isEdit() {
      return !!this.group
    },

    // 可选角色（未被选中的角色）
    availableRolesList() {
      return this.availableRoles.filter(role => !this.formData.roleIds.includes(role.id))
    },

    // 已选角色
    selectedRolesList() {
      return this.availableRoles.filter(role => this.formData.roleIds.includes(role.id))
    }
  },
  mounted() {
    this.initFormData()
  },
  methods: {
    initFormData() {
      if (this.group) {
        // 编辑模式
        this.formData = {
          name: this.group.name || '',
          code: this.group.code || '',
          description: this.group.description || '',
          is_active: this.group.is_active !== false,
          roleIds: this.group.roles ? this.group.roles.map(role => role.id) : []
        }
      } else {
        // 新建模式
        this.formData = {
          name: '',
          code: '',
          description: '',
          is_active: true,
          roleIds: []
        }
      }
      this.errors = {}
    },

    validateForm() {
      this.errors = {}
      
      if (!this.formData.name.trim()) {
        this.errors.name = '用户组名称不能为空'
      }
      
      if (!this.formData.code.trim()) {
        this.errors.code = '用户组代码不能为空'
      } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(this.formData.code)) {
        this.errors.code = '用户组代码只能包含字母、数字和下划线，且必须以字母开头'
      }
      
      return Object.keys(this.errors).length === 0
    },

    // 角色操作方法
    addRole(role) {
      if (!this.formData.roleIds.includes(role.id)) {
        this.formData.roleIds.push(role.id)
      }
    },

    removeRole(role) {
      const index = this.formData.roleIds.indexOf(role.id)
      if (index > -1) {
        this.formData.roleIds.splice(index, 1)
      }
    },

    addAllRoles() {
      this.availableRolesList.forEach(role => {
        if (!this.formData.roleIds.includes(role.id)) {
          this.formData.roleIds.push(role.id)
        }
      })
    },

    removeAllRoles() {
      this.formData.roleIds = []
    },

    async handleSubmit() {
      if (!this.validateForm()) {
        return
      }

      this.loading = true
      try {
        const submitData = {
          ...this.formData,
          id: this.group?.id
        }
        
        this.$emit('save', submitData)
      } catch (error) {
        console.error('提交表单失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.form-check-label {
  cursor: pointer;
}

.modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 传输面板样式 */
.transfer-panel {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: white;
  height: 250px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-body {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.panel-body::-webkit-scrollbar {
  width: 6px;
}

.panel-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.transfer-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.transfer-item:hover {
  border-color: #007bff;
  background: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.transfer-item.selected {
  background: #e7f3ff;
  border-color: #007bff;
}

.transfer-item.selected:hover {
  background: #d1ecf1;
  border-color: #0056b3;
}

.transfer-actions {
  display: flex;
  align-items: center;
}

.transfer-actions i {
  font-size: 1.1em;
  transition: all 0.2s ease;
}

.transfer-item:hover .transfer-actions i {
  transform: scale(1.1);
}
</style>
