/**
 * 表单数据本地存储工具
 *
 * 注意：配置相关的内容已迁移到 @/config/formDataConfig.js
 * 请从那里导入 getInitialFormData, getComponentGroups, createNewServerItem 等函数
 */

// 重新导出配置函数，保持向后兼容
// 注意：getComponentGroups 已废弃，请使用 getComponentGroupsFromDatabase
export {
  getInitialFormData,
  createNewServerItem,
  createNewCustomItem,
  createNewAppItem
} from '@/config/formDataConfig'

/**
 * 保存表单数据到本地存储
 * @param {Object} formData - 表单数据
 * @param {String} key - 存储键名，默认为 'tempFormData'
 */
export const saveFormDataToLocalStorage = (formData, key = 'tempFormData') => {
  try {
    // 创建一个副本，避免引用问题
    const formDataCopy = JSON.parse(JSON.stringify(formData))
    // 保存到本地存储
    localStorage.setItem(key, JSON.stringify(formDataCopy))
    return true
  } catch (error) {
    console.error('保存表单数据失败:', error)
    return false
  }
}

/**
 * 从本地存储加载表单数据
 * @param {String} key - 存储键名，默认为 'tempFormData'
 * @returns {Object|null} - 加载的表单数据，如果不存在则返回 null
 */
export const loadFormDataFromLocalStorage = (key = 'tempFormData') => {
  try {
    const savedData = localStorage.getItem(key)
    if (savedData) {
      return JSON.parse(savedData)
    }
    return null
  } catch (error) {
    console.error('加载表单数据失败:', error)
    return null
  }
}

/**
 * 清除本地存储中的表单数据
 * @param {String} key - 存储键名，默认为 'tempFormData'
 */
export const clearFormDataFromLocalStorage = (key = 'tempFormData') => {
  try {
    localStorage.removeItem(key)
    return true
  } catch (error) {
    console.error('清除表单数据失败:', error)
    return false
  }
}