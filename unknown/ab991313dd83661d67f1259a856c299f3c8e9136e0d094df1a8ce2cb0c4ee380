<template>
  <collapsible-card storage-key="basic-info-section" card-class="border-primary">
    <template #header>
      <i class="bi bi-info-circle me-2"></i>基本信息
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-primary">{{ companyName || '未填写公司名称' }}</span>
        <span class="badge bg-info">日期: {{ recordDate || '未填写' }}</span>
      </div>
    </template>

    <!-- 基本信息分组 - 动态渲染核心字段 -->
    <div v-if="visibleBasicFields.length > 0" class="basic-info-section mb-4">
      <div class="section-header mb-3">
        <h6 class="section-title">
          <i class="bi bi-info-circle me-2 text-primary"></i>
          基本信息
        </h6>
      </div>

      <!-- 动态基本字段 -->
      <template v-for="(field, index) in visibleBasicFields" :key="field.id">
        <!-- 行字段 -->
        <div class="row g-3 mb-3" v-if="field.type === 'row'">
          <div :class="getColumnClass(subfield)" v-for="(subfield, subIndex) in field.fields" :key="subfield.id">
            <div class="form-floating">
              <component
                :is="getFieldComponent(subfield.type)"
                :id="subfield.id"
                :label="subfield.label"
                :placeholder="subfield.placeholder"
                :required="subfield.required"
                :value="getFieldValue(subfield.id)"
                :options="subfield.options"
                :help-text="subfield.helpText"
                :readonly="subfield.readonly"
                :style="subfield.style"
                :icon="subfield.icon"
                @update:value="updateFieldValue(subfield.id, $event)"
                class="form-control enhanced-input"
              />
              <label :for="subfield.id">
                <i v-if="subfield.icon" :class="`${subfield.icon} me-1`"></i>
                {{ subfield.label }}
                <span v-if="subfield.required" class="text-danger">*</span>
              </label>
            </div>
            <!-- 帮助文本 -->
            <small v-if="subfield.helpText" class="text-muted mt-1">
              <i class="bi bi-info-circle me-1"></i>{{ subfield.helpText }}
            </small>
          </div>
        </div>
        <!-- 单个字段 -->
        <div class="row g-3 mb-3" v-else-if="field.type !== 'group'">
          <div :class="getColumnClass(field)">
            <div class="form-floating">
              <component
                :is="getFieldComponent(field.type)"
                :id="field.id"
                :label="field.label"
                :placeholder="field.placeholder"
                :required="field.required"
                :value="getFieldValue(field.id)"
                :options="field.options"
                :help-text="field.helpText"
                :readonly="field.readonly"
                :style="field.style"
                :icon="field.icon"
                @update:value="updateFieldValue(field.id, $event)"
                class="form-control enhanced-input"
              />
              <label :for="field.id">
                <i v-if="field.icon" :class="`${field.icon} me-1`"></i>
                {{ field.label }}
                <span v-if="field.required" class="text-danger">*</span>
              </label>
            </div>
            <!-- 帮助文本 -->
            <small v-if="field.helpText" class="text-muted mt-1">
              <i class="bi bi-info-circle me-1"></i>{{ field.helpText }}
            </small>
          </div>
        </div>
      </template>
    </div>

    <!-- 客户信息分组 - 显示动态配置的客户字段 -->
    <div v-if="visibleCustomerFields.length > 0" class="extended-info-section">
      <div class="section-header mb-3">
        <h6 class="section-title">
          <i class="bi bi-person-badge me-2 text-info"></i>
          客户信息
        </h6>
      </div>

      <!-- 动态客户字段 -->
      <template v-for="(field, index) in visibleCustomerFields" :key="field.id">
        <!-- 行字段 -->
        <div class="row g-3 mb-3" v-if="field.type === 'row'">
          <div :class="getColumnClass(subfield)" v-for="(subfield, subIndex) in field.fields" :key="subfield.id">
            <div class="form-floating">
              <component
                :is="getFieldComponent(subfield.type)"
                :id="subfield.id"
                :label="subfield.label"
                :placeholder="subfield.placeholder"
                :required="subfield.required"
                :value="getFieldValue(subfield.id)"
                :options="subfield.options"
                :help-text="subfield.helpText"
                :readonly="subfield.readonly"
                :style="subfield.style"
                :icon="subfield.icon"
                @update:value="updateFieldValue(subfield.id, $event)"
                class="form-control enhanced-input"
              />
              <label :for="subfield.id">
                <i v-if="subfield.icon" :class="`${subfield.icon} me-1`"></i>
                {{ subfield.label }}
                <span v-if="subfield.required" class="text-danger">*</span>
              </label>
            </div>
            <!-- 帮助文本 -->
            <small v-if="subfield.helpText" class="text-muted mt-1">
              <i class="bi bi-info-circle me-1"></i>{{ subfield.helpText }}
            </small>
          </div>
        </div>
        <!-- 单个字段 -->
        <div class="row g-3 mb-3" v-else-if="field.type !== 'group'">
          <div :class="getColumnClass(field)">
            <div class="form-floating">
              <component
                :is="getFieldComponent(field.type)"
                :id="field.id"
                :label="field.label"
                :placeholder="field.placeholder"
                :required="field.required"
                :value="getFieldValue(field.id)"
                :options="field.options"
                :help-text="field.helpText"
                :readonly="field.readonly"
                :style="field.style"
                :icon="field.icon"
                @update:value="updateFieldValue(field.id, $event)"
                class="form-control enhanced-input"
              />
              <label :for="field.id">
                <i v-if="field.icon" :class="`${field.icon} me-1`"></i>
                {{ field.label }}
                <span v-if="field.required" class="text-danger">*</span>
              </label>
            </div>
            <!-- 帮助文本 -->
            <small v-if="field.helpText" class="text-muted mt-1">
              <i class="bi bi-info-circle me-1"></i>{{ field.helpText }}
            </small>
          </div>
        </div>
      </template>
    </div>

    <!-- 部署包信息分组 - 显示动态配置的部署字段 -->
    <div v-if="visibleDeploymentFields.length > 0" class="extended-info-section">
      <div class="section-header mb-3">
        <h6 class="section-title">
          <i class="bi bi-gear me-2 text-success"></i>
          部署包信息
        </h6>
      </div>

      <!-- 动态部署字段 -->
      <template v-for="(field, index) in visibleDeploymentFields" :key="field.id">
        <!-- 行字段 -->
        <div class="row g-3 mb-3" v-if="field.type === 'row'">
          <div :class="getColumnClass(subfield)" v-for="(subfield, subIndex) in field.fields" :key="subfield.id">
            <div class="form-floating">
              <component
                :is="getFieldComponent(subfield.type)"
                :id="subfield.id"
                :label="subfield.label"
                :placeholder="subfield.placeholder"
                :required="subfield.required"
                :value="getFieldValue(subfield.id)"
                :options="subfield.options"
                :help-text="subfield.helpText"
                :readonly="subfield.readonly"
                :style="subfield.style"
                :icon="subfield.icon"
                @update:value="updateFieldValue(subfield.id, $event)"
                class="form-control enhanced-input"
              />
              <label :for="subfield.id">
                <i v-if="subfield.icon" :class="`${subfield.icon} me-1`"></i>
                {{ subfield.label }}
                <span v-if="subfield.required" class="text-danger">*</span>
              </label>
            </div>
            <!-- 帮助文本 -->
            <small v-if="subfield.helpText" class="text-muted mt-1">
              <i class="bi bi-info-circle me-1"></i>{{ subfield.helpText }}
            </small>
          </div>
        </div>
        <!-- 单个字段 -->
        <div class="row g-3 mb-3" v-else-if="field.type !== 'group'">
          <div :class="getColumnClass(field)">
            <div class="form-floating">
              <component
                :is="getFieldComponent(field.type)"
                :id="field.id"
                :label="field.label"
                :placeholder="field.placeholder"
                :required="field.required"
                :value="getFieldValue(field.id)"
                :options="field.options"
                :help-text="field.helpText"
                :readonly="field.readonly"
                :style="field.style"
                :icon="field.icon"
                @update:value="updateFieldValue(field.id, $event)"
                class="form-control enhanced-input"
              />
              <label :for="field.id">
                <i v-if="field.icon" :class="`${field.icon} me-1`"></i>
                {{ field.label }}
                <span v-if="field.required" class="text-danger">*</span>
              </label>
            </div>
            <!-- 帮助文本 -->
            <small v-if="field.helpText" class="text-muted mt-1">
              <i class="bi bi-info-circle me-1"></i>{{ field.helpText }}
            </small>
          </div>
        </div>
      </template>
    </div>
  </collapsible-card>
</template>

<script>
// 导入字段组件
import TextInput from './fields/TextInput.vue'
import TextArea from './fields/TextArea.vue'
import SelectInput from './fields/SelectInput.vue'
import DateInput from './fields/DateInput.vue'
import CollapsibleCard from './CollapsibleCard.vue'

export default {
  name: 'CommonBasicInfo',
  components: {
    TextInput,
    TextArea,
    SelectInput,
    DateInput,
    CollapsibleCard
  },
  props: {
    // 公司名称
    company: {
      type: String,
      default: ''
    },
    // 记录日期
    date: {
      type: String,
      default: ''
    },
    // 编辑人
    editor: {
      type: String,
      default: ''
    },
    // 表单类型
    formType: {
      type: String,
      default: '安全测评'
    },
    // 字段配置（保留兼容性）
    fieldConfig: {
      type: Array,
      default: () => []
    },
    // 基本信息字段配置
    basicFieldConfig: {
      type: Array,
      default: () => []
    },
    // 客户信息字段配置
    customerFieldConfig: {
      type: Array,
      default: () => []
    },
    // 部署包信息字段配置
    deploymentFieldConfig: {
      type: Array,
      default: () => []
    },
    // 字段值
    fieldValues: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      companyName: this.company || '',
      recordDate: this.date || '',
      editorName: this.editor || '',
      localFieldValues: { ...this.fieldValues }
    }
  },
  watch: {
    // 监听props变化，同步到本地数据
    company(newVal) {
      this.companyName = newVal || ''
    },
    date(newVal) {
      this.recordDate = newVal || ''
    },
    editor(newVal) {
      this.editorName = newVal || ''
    },
    fieldValues: {
      handler(newVal) {
        this.localFieldValues = { ...newVal }
      },
      deep: true
    }
  },
  computed: {
    // 根据表单类型和配置过滤出可见的基本字段
    visibleBasicFields() {
      return this.basicFieldConfig.filter(field => {
        // 如果字段有formTypes属性，检查当前表单类型是否包含在内
        if (field.formTypes && Array.isArray(field.formTypes)) {
          return field.formTypes.includes(this.formType)
        }
        // 如果没有formTypes属性，默认显示
        return true
      })
    },
    // 根据表单类型和配置过滤出可见的客户字段
    visibleCustomerFields() {
      return this.customerFieldConfig.filter(field => {
        // 如果字段有formTypes属性，检查当前表单类型是否包含在内
        if (field.formTypes && Array.isArray(field.formTypes)) {
          return field.formTypes.includes(this.formType)
        }
        // 如果没有formTypes属性，默认显示
        return true
      })
    },
    // 根据表单类型和配置过滤出可见的部署字段
    visibleDeploymentFields() {
      return this.deploymentFieldConfig.filter(field => {
        // 如果字段有formTypes属性，检查当前表单类型是否包含在内
        if (field.formTypes && Array.isArray(field.formTypes)) {
          return field.formTypes.includes(this.formType)
        }
        // 如果没有formTypes属性，默认显示
        return true
      })
    },
    // 保留原有的计算属性以兼容旧代码
    visibleFields() {
      return this.fieldConfig.filter(field => {
        // 如果字段有formTypes属性，检查当前表单类型是否包含在内
        if (field.formTypes && Array.isArray(field.formTypes)) {
          return field.formTypes.includes(this.formType)
        }
        // 如果没有formTypes属性，默认显示
        return true
      })
    }
  },
  methods: {
    // 更新方法
    updateCompanyName() {
      this.$emit('update:company', this.companyName)
    },
    updateRecordDate() {
      this.$emit('update:date', this.recordDate)
    },
    // 更新客户标识
    updateCustomerId() {
      this.localFieldValues.customerId = this.localFieldValues.customerId || ''
      this.$emit('update:field-values', { ...this.localFieldValues })
    },
    // 更新字段值
    updateFieldValue(fieldId, value) {
      // 检查字段是否为只读或自动填充
      const field = this.findFieldById(fieldId)
      if (field && (field.readonly || field.autoFill)) {
        console.warn(`字段 ${fieldId} 是只读字段，不允许修改`)
        return
      }

      // 特殊处理基本字段
      switch (fieldId) {
        case 'companyName':
          this.companyName = value
          this.$emit('update:company', value)
          break
        case 'recordDate':
          // 记录日期是自动填充的，不允许手动修改
          console.warn('记录日期是自动填充的，不允许手动修改')
          break
        case 'customerId':
          this.localFieldValues.customerId = value
          this.$emit('update:field-values', { ...this.localFieldValues })
          break
        case 'editorName':
          // 编辑人字段是自动填充的，不允许修改
          console.warn('编辑人字段是自动填充的，不允许修改')
          break
        default:
          this.localFieldValues[fieldId] = value
          this.$emit('update:field-values', { ...this.localFieldValues })
      }
    },

    // 查找字段配置
    findFieldById(fieldId) {
      // 在所有字段配置中查找指定ID的字段
      const allFields = [
        ...this.basicFieldConfig,
        ...this.customerFieldConfig,
        ...this.deploymentFieldConfig
      ]

      for (const fieldGroup of allFields) {
        if (fieldGroup.fields) {
          const field = fieldGroup.fields.find(f => f.id === fieldId)
          if (field) return field
        }
      }
      return null
    },
    // 获取字段组件
    getFieldComponent(type) {
      switch (type) {
        case 'textarea':
          return 'text-area'
        case 'select':
          return 'select-input'
        case 'date':
          return 'date-input'
        case 'text':
        default:
          return 'text-input'
      }
    },
    // 获取列类名
    getColumnClass(field) {
      return field.columnClass || 'col-md-6'
    },
    // 获取字段值
    getFieldValue(fieldId) {
      // 特殊处理基本字段
      switch (fieldId) {
        case 'companyName':
          return this.companyName
        case 'recordDate':
          return this.recordDate
        case 'customerId':
          return this.localFieldValues.customerId || ''
        case 'editorName':
          return this.editorName
        default:
          return this.localFieldValues[fieldId] || this.fieldValues[fieldId] || ''
      }
    },

    updateEditorName() {
      try {
        let editorName = ''

        // 方法1：从Vuex store获取当前用户信息
        const currentUser = this.$store.getters.currentUser
        if (currentUser) {
          editorName = currentUser.real_name || currentUser.username || currentUser.name
          console.log('从Vuex store获取编辑人:', editorName)
        }

        // 方法2：如果Vuex中没有，尝试从localStorage获取
        if (!editorName) {
          const userInfo = localStorage.getItem('user_info')
          if (userInfo) {
            try {
              const user = JSON.parse(userInfo)
              editorName = user.real_name || user.username || user.name
              console.log('从localStorage获取编辑人:', editorName)
            } catch (e) {
              console.error('解析localStorage用户信息失败:', e)
            }
          }
        }

        // 方法3：如果都没有，使用默认值
        if (!editorName) {
          editorName = 'admin' // 默认编辑人
          console.warn('使用默认编辑人:', editorName)
        }

        // 更新编辑人信息
        this.editorName = editorName
        this.$emit('update:editor', editorName)

        console.log('最终设置的编辑人:', editorName)
      } catch (error) {
        console.error('填充当前用户信息失败:', error)
        // 出错时使用默认值
        this.editorName = 'admin'
        this.$emit('update:editor', 'admin')
      }
    }
  },
  mounted() {
    // 自动填充系统字段
    this.autoFillSystemFields()
  },
  methods: {
    // 自动填充系统字段（记录日期和编辑人）
    autoFillSystemFields() {
      // 自动填充记录日期为当前日期
      if (!this.recordDate) {
        const today = new Date().toISOString().split('T')[0]
        this.recordDate = today
        this.$emit('update:date', today)
        console.log('自动填充记录日期:', today)
      }

      // 自动填充编辑人信息
      this.updateEditorName()
    },
    // 更新字段值
    updateFieldValue(fieldId, value) {
      // 检查字段是否为只读或自动填充
      const field = this.findFieldById(fieldId)
      if (field && (field.readonly || field.autoFill)) {
        console.warn(`字段 ${fieldId} 是只读字段，不允许修改`)
        return
      }

      // 特殊处理基本字段
      switch (fieldId) {
        case 'companyName':
          this.companyName = value
          this.$emit('update:company', value)
          break
        case 'recordDate':
          // 记录日期是自动填充的，不允许手动修改
          console.warn('记录日期是自动填充的，不允许手动修改')
          break
        case 'customerId':
          this.localFieldValues.customerId = value
          this.$emit('update:field-values', { ...this.localFieldValues })
          break
        case 'editorName':
          // 编辑人字段是自动填充的，不允许修改
          console.warn('编辑人字段是自动填充的，不允许修改')
          break
        default:
          this.localFieldValues[fieldId] = value
          this.$emit('update:field-values', { ...this.localFieldValues })
      }
    },

    // 查找字段配置
    findFieldById(fieldId) {
      // 在所有字段配置中查找指定ID的字段
      const allFields = [
        ...this.basicFieldConfig,
        ...this.customerFieldConfig,
        ...this.deploymentFieldConfig
      ]

      for (const fieldGroup of allFields) {
        if (fieldGroup.fields) {
          const field = fieldGroup.fields.find(f => f.id === fieldId)
          if (field) return field
        }
      }
      return null
    },
    // 获取字段组件
    getFieldComponent(type) {
      switch (type) {
        case 'textarea':
          return 'text-area'
        case 'select':
          return 'select-input'
        case 'date':
          return 'date-input'
        case 'text':
        default:
          return 'text-input'
      }
    },
    // 获取列类名
    getColumnClass(field) {
      return field.columnClass || 'col-md-6'
    },
    // 获取字段值
    getFieldValue(fieldId) {
      // 特殊处理基本字段
      switch (fieldId) {
        case 'companyName':
          return this.companyName
        case 'recordDate':
          return this.recordDate
        case 'customerId':
          return this.localFieldValues.customerId || ''
        case 'editorName':
          return this.editorName
        default:
          return this.localFieldValues[fieldId] || this.fieldValues[fieldId] || ''
      }
    },

    updateEditorName() {
      try {
        let editorName = ''

        // 方法1：从Vuex store获取当前用户信息
        const currentUser = this.$store.getters.currentUser
        if (currentUser) {
          editorName = currentUser.real_name || currentUser.username || currentUser.name
          console.log('从Vuex store获取编辑人:', editorName)
        }

        // 方法2：如果Vuex中没有，尝试从localStorage获取
        if (!editorName) {
          const userInfo = localStorage.getItem('user_info')
          if (userInfo) {
            try {
              const user = JSON.parse(userInfo)
              editorName = user.real_name || user.username || user.name
              console.log('从localStorage获取编辑人:', editorName)
            } catch (e) {
              console.error('解析localStorage用户信息失败:', e)
            }
          }
        }

        // 方法3：如果都没有，使用默认值
        if (!editorName) {
          editorName = 'admin' // 默认编辑人
          console.warn('使用默认编辑人:', editorName)
        }

        // 更新编辑人信息
        this.editorName = editorName
        this.$emit('update:editor', editorName)

        console.log('最终设置的编辑人:', editorName)
      } catch (error) {
        console.error('填充当前用户信息失败:', error)
        // 出错时使用默认值
        this.editorName = 'admin'
        this.$emit('update:editor', 'admin')
      }
    }
  }
}
</script>

<style scoped>
/* 基本信息分组样式 */
.basic-info-section,
.extended-info-section {
  position: relative;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.basic-info-section:hover,
.extended-info-section:hover {
  border-color: #dee2e6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-header {
  position: relative;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

.section-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.section-title i {
  font-size: 1.1rem;
}

/* 增强的输入框样式 */
.enhanced-input {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.enhanced-input:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
  background-color: #ffffff;
}

.enhanced-input:hover:not(:focus) {
  border-color: #ced4da;
}

/* 标签样式增强 */
.form-floating > label {
  color: #6c757d;
  font-weight: 500;
}

.form-floating > .enhanced-input:focus ~ label,
.form-floating > .enhanced-input:not(:placeholder-shown) ~ label {
  color: #0d6efd;
  font-weight: 600;
}

/* 原有的浮动标签样式 */
.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 聚焦状态的分组高亮 */
.basic-info-section:focus-within,
.extended-info-section:focus-within {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .basic-info-section,
  .extended-info-section {
    padding: 1rem;
  }

  .section-title {
    font-size: 0.9rem;
  }
}
</style>
