/**
 * 表单字段配置
 * 用于配置不同表单的字段
 */

// 客户信息字段配置（包含通用+专用客户相关字段）
export const customerInfoFields = [
  // 通用客户信息字段 - 所有表单都显示
  {
    id: 'row_customer_core_info',
    type: 'row',
    formTypes: ['安全测评', '安全监测', '应用加固'],
    fields: [
      {
        id: 'companyName',
        type: 'text',
        label: '公司名称',
        placeholder: '请输入客户公司名称',
        required: true,
        columnClass: 'col-md-6',
        icon: 'bi-building'
      },
      {
        id: 'customerId',
        type: 'text',
        label: '客户标识',
        placeholder: '客户标识',
        required: false,
        columnClass: 'col-md-6',
        icon: 'bi-person-badge'
      }
    ]
  },
  // 专用客户信息字段 - 安全监测表单专用
  {
    id: 'row_customer_specific_monitoring',
    type: 'row',
    formTypes: ['安全监测'],
    fields: [
      {
        id: 'dailyActive',
        type: 'text',
        label: '日活',
        placeholder: '请输入日活数据',
        required: false,
        columnClass: 'col-md-12'
      }
    ]
  }
]

// 部署包信息字段配置
export const deploymentInfoFields = [
  // 平台版本字段 - 应用加固表单专用
  {
    id: 'row_platform_version',
    type: 'row',
    formTypes: ['应用加固'],
    fields: [
      {
        id: 'platformVersion',
        type: 'text',
        label: '部署的平台版本',
        placeholder: '请输入部署的平台版本',
        required: false,
        columnClass: 'col-md-12'
      }
    ]
  },
  // 版本信息字段 - 安全测评表单专用
  {
    id: 'row_deployment_version',
    type: 'row',
    formTypes: ['安全测评'],
    fields: [
      {
        id: 'deploymentVersion',
        type: 'text',
        label: '部署包版本',
        placeholder: '请输入部署包版本',
        required: true,
        columnClass: 'col-md-12'
      }
    ]
  },
  // 版本信息字段 - 安全监测表单专用
  {
    id: 'row_version_info',
    type: 'row',
    formTypes: ['安全监测'],
    fields: [
      {
        id: 'frontendVersion',
        type: 'text',
        label: '前端版本',
        placeholder: '例如：V5.1.2sp2',
        required: true,
        columnClass: 'col-md-4'
      },
      {
        id: 'backendVersion',
        type: 'text',
        label: '后端版本',
        placeholder: '例如：V5.1.2sp2',
        required: true,
        columnClass: 'col-md-4'
      },
      {
        id: 'standardOrCustom',
        type: 'select',
        label: '标准/定制',
        placeholder: '标准/定制',
        required: false,
        columnClass: 'col-md-4',
        options: [
          { value: '标准版', label: '标准版' },
          { value: '定制版', label: '定制版' }
        ]
      }
    ]
  }
]

// 基本信息字段配置（只保留非客户相关的系统字段）
export const basicInfoFields = [
  {
    id: 'row_system_info',
    type: 'row',
    formTypes: ['安全测评', '安全监测', '应用加固'], // 所有表单类型都显示
    fields: [
      {
        id: 'recordDate',
        type: 'date',
        label: '记录日期',
        placeholder: '',
        required: true,
        columnClass: 'col-md-6',
        icon: 'bi-calendar-event',
        hidden: true, // 隐藏字段，自动填充当前日期
        autoFill: true,
        readonly: true // 自动填充的字段不可编辑
      },
      {
        id: 'editorName',
        type: 'text',
        label: '编辑人',
        placeholder: '编辑人',
        required: false,
        columnClass: 'col-md-6',
        icon: 'bi-person-check',
        hidden: true, // 隐藏字段，自动填充当前用户
        autoFill: true,
        readonly: true, // 自动填充的字段不可编辑
        helpText: '自动填充当前登录用户，不可修改',
        style: 'background-color: #f8f9fa; cursor: not-allowed;'
      }
    ]
  }
]

// 可选：如果需要显示系统字段但设为只读，可以使用这个配置
export const visibleSystemFields = [
  {
    id: 'row_visible_system_info',
    type: 'row',
    formTypes: ['安全测评', '安全监测', '应用加固'],
    fields: [
      {
        id: 'recordDate',
        type: 'date',
        label: '记录日期',
        placeholder: '',
        required: true,
        columnClass: 'col-md-6',
        icon: 'bi-calendar-event',
        autoFill: true,
        readonly: true, // 只读，不可编辑
        style: 'background-color: #f8f9fa; cursor: not-allowed;',
        helpText: '自动填充当前日期，不可修改'
      },
      {
        id: 'editorName',
        type: 'text',
        label: '编辑人',
        placeholder: '编辑人',
        required: false,
        columnClass: 'col-md-6',
        icon: 'bi-person-check',
        autoFill: true,
        readonly: true, // 只读，不可编辑
        helpText: '自动填充当前登录用户，不可修改',
        style: 'background-color: #f8f9fa; cursor: not-allowed;'
      }
    ]
  }
]

// 服务器信息字段配置
export const serverInfoFields = [
  // IP地址字段 - 所有表单通用
  {
    id: 'serverIp',
    type: 'text',
    label: '服务器IP地址',
    placeholder: '服务器IP地址',
    required: false,
    formTypes: ['安全测评', '安全监测', '应用加固']
  },
  // 操作系统字段 - 所有表单通用
  {
    id: 'serverOs',
    type: 'text',
    label: '服务器操作系统',
    placeholder: '服务器操作系统',
    required: false,
    helpText: '例如：Kylin Linux Advanced Server',
    formTypes: ['安全测评', '安全监测', '应用加固']
  },
  // 配置字段 - 所有表单通用
  {
    id: 'serverConfig',
    type: 'text',
    label: '服务器配置',
    placeholder: '服务器配置',
    required: false,
    helpText: '例如：8核 16G /data 500G',
    formTypes: ['安全测评', '安全监测', '应用加固']
  },
  // SSH端口字段 - 所有表单通用
  {
    id: 'sshPort',
    type: 'text',
    label: 'SSH端口',
    placeholder: 'SSH端口',
    required: false,
    helpText: '例如：22',
    formTypes: ['安全测评', '安全监测', '应用加固']
  },
  // Root密码字段 - 所有表单通用
  {
    id: 'rootPassword',
    type: 'password',
    label: 'Root密码',
    placeholder: 'Root密码',
    required: false,
    helpText: '服务器root用户密码',
    formTypes: ['安全测评', '安全监测', '应用加固']
  },
  // 运维用户字段 - 所有表单通用（使用动态数组结构）
  {
    id: 'opsUsers',
    type: 'array',
    label: '运维用户',
    placeholder: '运维用户信息',
    required: false,
    helpText: '运维用户账号和密码信息，支持添加多个用户',
    formTypes: ['安全测评', '安全监测', '应用加固'],
    arrayItemFields: [
      {
        id: 'username',
        type: 'text',
        label: '用户名',
        placeholder: '运维用户名',
        required: false
      },
      {
        id: 'password',
        type: 'password',
        label: '密码',
        placeholder: '运维用户密码',
        required: false
      }
    ]
  },

]

/**
 * 根据表单类型获取客户信息字段配置
 * @param {String} formType - 表单类型
 * @returns {Array} - 字段配置数组
 */
export const getCustomerInfoFieldsByFormType = (formType) => {
  return customerInfoFields.filter(field => {
    // 如果字段有formTypes属性，检查当前表单类型是否包含在内
    if (field.formTypes && Array.isArray(field.formTypes)) {
      return field.formTypes.includes(formType)
    }
    // 如果没有formTypes属性，默认显示
    return true
  })
}

/**
 * 根据表单类型获取部署包信息字段配置
 * @param {String} formType - 表单类型
 * @returns {Array} - 字段配置数组
 */
export const getDeploymentInfoFieldsByFormType = (formType) => {
  return deploymentInfoFields.filter(field => {
    // 如果字段有formTypes属性，检查当前表单类型是否包含在内
    if (field.formTypes && Array.isArray(field.formTypes)) {
      return field.formTypes.includes(formType)
    }
    // 如果没有formTypes属性，默认显示
    return true
  })
}

/**
 * 根据表单类型获取基本信息字段配置
 * @param {String} formType - 表单类型
 * @returns {Array} - 字段配置数组
 */
export const getBasicInfoFieldsByFormType = (formType) => {
  return basicInfoFields.filter(field => {
    // 如果字段有formTypes属性，检查当前表单类型是否包含在内
    if (field.formTypes && Array.isArray(field.formTypes)) {
      return field.formTypes.includes(formType)
    }
    // 如果没有formTypes属性，默认显示
    return true
  })
}

/**
 * 根据表单类型获取服务器信息字段配置
 * @param {String} formType - 表单类型
 * @returns {Array} - 字段配置数组
 */
export const getServerInfoFieldsByFormType = (formType) => {
  return serverInfoFields.filter(field => {
    // 如果字段有formTypes属性，检查当前表单类型是否包含在内
    if (field.formTypes && Array.isArray(field.formTypes)) {
      return field.formTypes.includes(formType)
    }
    // 如果没有formTypes属性，默认显示
    return true
  })
}
