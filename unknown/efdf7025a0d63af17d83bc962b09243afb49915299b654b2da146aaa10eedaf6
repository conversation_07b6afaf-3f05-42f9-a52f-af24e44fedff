<template>
  <base-form
    v-model="formData"
    form-type="安全监测"
    :component-groups="componentGroups"
    @save-form="$emit('save-form')"
    @load-form="$emit('load-form')"
    @refresh-components="$emit('refresh-components')"
    class="security-monitoring-form"
  >
    <!-- 安全监测特有的部分 -->
    <template #custom-sections>
      <!-- 网络配置部分 -->
      <div id="network-config-section">
        <network-config-section
          v-model:sdk-external="formData.SDK外网流量入口"
          v-model:sdk-nginx="formData.SDK流量转发到Nginx入口"
          :server-list="formData.服务器信息"
        />
      </div>

      <!-- 运维定制内容部分 -->
      <div id="custom-content-section">
        <custom-content-section
          v-model="formData.运维定制内容"
        />
      </div>

      <!-- 客户APP部分 -->
      <div id="client-app-section">
        <client-app-section
          v-model="formData.客户APP"
        />
      </div>
    </template>
  </base-form>
</template>

<script>
import BaseForm from '../common/BaseForm.vue'
import NetworkConfigSection from './NetworkConfigSection.vue'
import CustomContentSection from './CustomContentSection.vue'
import ClientAppSection from './ClientAppSection.vue'

/**
 * 安全监测表单组件
 * 使用BaseForm提供通用功能，只需定义特有的网络配置、运维定制内容和客户APP部分
 */
export default {
  name: 'SecurityMonitoringForm',
  components: {
    BaseForm,
    NetworkConfigSection,
    CustomContentSection,
    ClientAppSection
  },
  props: {
    modelValue: { type: Object, required: true },
    componentGroups: { type: Object, required: true }
  },
  emits: ['update:modelValue', 'save-form', 'load-form', 'refresh-components'],
  computed: {
    formData: {
      get() { return this.modelValue },
      set(value) { this.$emit('update:modelValue', value) }
    }
  },
  mounted() {
    // 为安全监测表单的服务器添加默认的bangcle用户
    this.addBangcleUserToServers()
  },
  methods: {
    /**
     * 为安全监测表单的所有服务器添加bangcle用户
     */
    addBangcleUserToServers() {
      if (this.formData.服务器信息 && Array.isArray(this.formData.服务器信息)) {
        this.formData.服务器信息.forEach(server => {
          if (server.运维用户 && Array.isArray(server.运维用户)) {
            // 检查是否已经有bangcle用户
            const hasBangcle = server.运维用户.some(user => user.用户名 === 'bangcle')
            if (!hasBangcle) {
              // 添加bangcle用户
              server.运维用户.push({
                用户名: 'bangcle',
                密码: 'beap123',
                showPassword: false,
                isDefault: true
              })
            }
          }
        })
      }
    }
  }
}
</script>

<style scoped>
/* 安全监测表单特有样式 */
.security-monitoring-form {
  /* 基础样式由BaseForm提供 */
}

/* 网络配置、运维定制内容、客户APP部分特殊样式 */
#network-config-section .card,
#custom-content-section .card,
#client-app-section .card {
  border-left-color: #007bff; /* 蓝色边框，表示安全监测 */
}
</style>
