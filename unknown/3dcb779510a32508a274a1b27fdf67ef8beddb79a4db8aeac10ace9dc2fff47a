<template>
  <div class="help-page">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="mb-1">
          <i class="bi bi-question-circle me-2 text-primary"></i>
          帮助文档
        </h2>
        <p class="text-muted mb-0">运维文档管理平台使用指南</p>
      </div>
      <div>
        <button class="btn btn-outline-primary" @click="printPage">
          <i class="bi bi-printer me-1"></i>
          打印文档
        </button>
      </div>
    </div>

    <!-- 导航目录 -->
    <div class="row">
      <div class="col-lg-3">
        <div class="card sticky-top" style="top: 20px;">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="bi bi-list me-2"></i>
              目录导航
            </h6>
          </div>
          <div class="card-body p-0">
            <nav class="nav nav-pills flex-column">
              <a class="nav-link" href="#overview" @click="scrollTo('overview')">
                <i class="bi bi-house me-2"></i>平台概述
              </a>
              <a class="nav-link" href="#getting-started" @click="scrollTo('getting-started')">
                <i class="bi bi-play-circle me-2"></i>快速开始
              </a>
              <a class="nav-link" href="#form-management" @click="scrollTo('form-management')">
                <i class="bi bi-file-earmark-text me-2"></i>表单管理
              </a>
              <a class="nav-link" href="#component-management" @click="scrollTo('component-management')">
                <i class="bi bi-puzzle me-2"></i>组件管理
              </a>
              <a class="nav-link" href="#template-management" @click="scrollTo('template-management')">
                <i class="bi bi-file-earmark-excel me-2"></i>模板管理
              </a>
              <a class="nav-link" href="#user-management" @click="scrollTo('user-management')">
                <i class="bi bi-people me-2"></i>用户管理
              </a>
              <a class="nav-link" href="#system-management" @click="scrollTo('system-management')">
                <i class="bi bi-gear me-2"></i>系统管理
              </a>
              <a class="nav-link" href="#api-docs" @click="scrollTo('api-docs')">
                <i class="bi bi-code-square me-2"></i>API文档
              </a>
              <a class="nav-link" href="#troubleshooting" @click="scrollTo('troubleshooting')">
                <i class="bi bi-tools me-2"></i>故障排除
              </a>
              <a class="nav-link" href="#faq" @click="scrollTo('faq')">
                <i class="bi bi-question-circle me-2"></i>常见问题
              </a>
            </nav>
          </div>
        </div>
      </div>

      <div class="col-lg-9">
        <div class="help-content">
          <!-- 平台概述 -->
          <section id="overview" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-house me-2 text-primary"></i>
                  平台概述
                </h3>
              </div>
              <div class="card-body">
                <p class="lead">运维文档管理平台是一个专业的运维文档生成和管理系统，帮助运维团队高效管理服务器配置、应用部署和系统监控信息。</p>

                <h5 class="mt-4">🎯 主要功能</h5>
                <div class="row">
                  <div class="col-md-6">
                    <ul class="list-unstyled">
                      <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        <strong>表单管理</strong> - 创建和管理运维文档表单
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        <strong>组件管理</strong> - 管理服务器组件和配置
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        <strong>模板管理</strong> - 自定义Excel导出模板
                      </li>
                    </ul>
                  </div>
                  <div class="col-md-6">
                    <ul class="list-unstyled">
                      <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        <strong>用户权限</strong> - 基于角色的权限管理
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        <strong>数据导入</strong> - 支持Excel和JSON数据导入
                      </li>
                      <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        <strong>性能监控</strong> - 实时系统性能监控
                      </li>
                    </ul>
                  </div>
                </div>

                <h5 class="mt-4">🏗️ 系统架构</h5>
                <div class="alert alert-info">
                  <div class="row">
                    <div class="col-md-4 text-center">
                      <i class="bi bi-display fs-1 text-primary"></i>
                      <h6 class="mt-2">前端界面</h6>
                      <small>Vue.js + Bootstrap</small>
                    </div>
                    <div class="col-md-4 text-center">
                      <i class="bi bi-server fs-1 text-success"></i>
                      <h6 class="mt-2">后端服务</h6>
                      <small>Python Flask</small>
                    </div>
                    <div class="col-md-4 text-center">
                      <i class="bi bi-database fs-1 text-warning"></i>
                      <h6 class="mt-2">数据存储</h6>
                      <small>MySQL + Redis</small>
                    </div>
                  </div>
                </div>

                <h5 class="mt-4">🚀 部署架构</h5>
                <div class="row">
                  <div class="col-md-12">
                    <div class="alert alert-primary">
                      <h6 class="alert-heading">
                        <i class="bi-diagram-3 me-2"></i>部署架构图生成机制
                      </h6>
                      <p class="mb-2">系统提供自动化的部署架构图生成功能：</p>
                      <ul class="mb-2">
                        <li><strong>Excel自动生成</strong> - 导出Excel时会根据服务器和组件信息自动生成部署架构图</li>
                        <li><strong>组件分类渲染</strong> - 不同类型的组件会以不同颜色和形状显示在架构图中</li>
                        <li><strong>服务器信息关联</strong> - 架构图会显示服务器IP、组件部署关系等关键信息</li>
                      </ul>
                      <small class="text-muted">
                        部署架构图会在Excel文档中自动生成，包含组件分类、名称、端口和IP信息。
                      </small>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="card border-primary">
                      <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                          <i class="bi bi-cloud me-2"></i>生产环境
                        </h6>
                      </div>
                      <div class="card-body">
                        <ul class="list-unstyled mb-0">
                          <li><strong>Web服务器:</strong> Nginx (反向代理)</li>
                          <li><strong>应用服务:</strong> Flask (端口5000)</li>
                          <li><strong>数据库:</strong> MySQL 8.0.42</li>
                          <li><strong>缓存:</strong> Redis (DB 0)</li>
                          <li><strong>服务器:</strong> ************</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="card border-success">
                      <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                          <i class="bi bi-laptop me-2"></i>开发环境
                        </h6>
                      </div>
                      <div class="card-body">
                        <ul class="list-unstyled mb-0">
                          <li><strong>前端开发:</strong> Vue Dev Server (8080)</li>
                          <li><strong>后端开发:</strong> Flask Dev (5000)</li>
                          <li><strong>开发数据库:</strong> export_excel_dev</li>
                          <li><strong>开发缓存:</strong> Redis (DB 1)</li>
                          <li><strong>包管理:</strong> uv (Python)</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <h5 class="mt-4">📊 技术栈详情</h5>
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>层级</th>
                        <th>技术</th>
                        <th>版本/配置</th>
                        <th>说明</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td><span class="badge bg-primary">前端</span></td>
                        <td>Vue.js + Bootstrap</td>
                        <td>Vue 3 + Bootstrap 5</td>
                        <td>响应式用户界面</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-success">后端</span></td>
                        <td>Python Flask</td>
                        <td>Flask + SQLAlchemy</td>
                        <td>RESTful API服务</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-warning">数据库</span></td>
                        <td>MySQL</td>
                        <td>8.0.42</td>
                        <td>主数据存储</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-info">缓存</span></td>
                        <td>Redis</td>
                        <td>6.x</td>
                        <td>会话和数据缓存</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-secondary">代理</span></td>
                        <td>Nginx</td>
                        <td>反向代理</td>
                        <td>负载均衡和静态文件</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </section>

          <!-- 快速开始 -->
          <section id="getting-started" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-play-circle me-2 text-primary"></i>
                  快速开始
                </h3>
              </div>
              <div class="card-body">
                <h5>🚀 第一次使用</h5>
                <div class="alert alert-primary">
                  <h6 class="alert-heading">默认管理员账户</h6>
                  <p class="mb-1"><strong>用户名:</strong> admin</p>
                  <p class="mb-0"><strong>密码:</strong> admin123</p>
                  <hr>
                  <small class="text-muted">首次登录后请及时修改密码</small>
                </div>

                <h5 class="mt-4">📝 创建第一个表单</h5>
                <ol class="list-group list-group-numbered">
                  <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="ms-2 me-auto">
                      <div class="fw-bold">选择表单类型</div>
                      在首页选择"应用加固"或"安全监控"表单类型
                    </div>
                    <span class="badge bg-primary rounded-pill">1</span>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="ms-2 me-auto">
                      <div class="fw-bold">填写基本信息</div>
                      输入客户公司名称、项目名称等基础信息
                    </div>
                    <span class="badge bg-primary rounded-pill">2</span>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="ms-2 me-auto">
                      <div class="fw-bold">配置服务器信息</div>
                      添加服务器IP、组件配置等技术信息
                    </div>
                    <span class="badge bg-primary rounded-pill">3</span>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div class="ms-2 me-auto">
                      <div class="fw-bold">生成文档</div>
                      点击"生成表单"按钮导出Excel运维文档
                    </div>
                    <span class="badge bg-primary rounded-pill">4</span>
                  </li>
                </ol>

                <h5 class="mt-4">🎯 使用技巧</h5>
                <div class="row">
                  <div class="col-md-6">
                    <div class="card border-success">
                      <div class="card-body">
                        <h6 class="card-title text-success">
                          <i class="bi bi-lightbulb me-2"></i>提示
                        </h6>
                        <ul class="card-text small mb-0">
                          <li>使用表单快照功能保存草稿</li>
                          <li>组件信息可以重复使用</li>
                          <li>支持批量导入Excel数据</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="card border-warning">
                      <div class="card-body">
                        <h6 class="card-title text-warning">
                          <i class="bi bi-exclamation-triangle me-2"></i>注意
                        </h6>
                        <ul class="card-text small mb-0">
                          <li>密码字段默认隐藏，可点击查看</li>
                          <li>必填字段标有红色星号</li>
                          <li>表单提交前会检查重复</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- 表单管理 -->
          <section id="form-management" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-file-earmark-text me-2 text-primary"></i>
                  表单管理
                </h3>
              </div>
              <div class="card-body">
                <h5>📋 表单类型</h5>
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>表单类型</th>
                        <th>用途</th>
                        <th>主要字段</th>
                        <th>生成文档</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td><span class="badge bg-primary">应用加固</span></td>
                        <td>移动应用安全加固项目</td>
                        <td>客户信息、平台版本、服务器配置</td>
                        <td>应用加固运维文档</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-success">安全监测</span></td>
                        <td>安全监测平台部署</td>
                        <td>前后端版本、访问地址、组件配置</td>
                        <td>安全监测运维文档</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-warning">安全测评</span></td>
                        <td>移动应用安全测评项目</td>
                        <td>部署包版本、管理员信息、服务器配置</td>
                        <td>安全测评运维文档</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <h5 class="mt-4">🔧 表单操作</h5>
                <div class="row">
                  <div class="col-md-4">
                    <div class="card border-primary">
                      <div class="card-body text-center">
                        <i class="bi bi-plus-circle fs-1 text-primary"></i>
                        <h6 class="mt-2">新建表单</h6>
                        <p class="small text-muted">创建新的运维文档表单</p>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="card border-warning">
                      <div class="card-body text-center">
                        <i class="bi bi-pencil-square fs-1 text-warning"></i>
                        <h6 class="mt-2">编辑表单</h6>
                        <p class="small text-muted">修改已有表单内容</p>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="card border-success">
                      <div class="card-body text-center">
                        <i class="bi bi-download fs-1 text-success"></i>
                        <h6 class="mt-2">导出文档</h6>
                        <p class="small text-muted">生成Excel运维文档</p>
                      </div>
                    </div>
                  </div>
                </div>

                <h5 class="mt-4">💾 表单快照</h5>
                <div class="alert alert-info">
                  <h6 class="alert-heading">
                    <i class="bi bi-camera me-2"></i>快照功能
                  </h6>
                  <p>表单快照可以保存当前填写状态，方便下次继续编辑：</p>
                  <ul class="mb-0">
                    <li><strong>自动保存:</strong> 系统会定期自动保存表单状态</li>
                    <li><strong>手动保存:</strong> 点击"保存草稿"按钮手动保存</li>
                    <li><strong>加载快照:</strong> 在表单页面可以选择加载之前的快照</li>
                    <li><strong>快照管理:</strong> 可以删除不需要的快照释放空间</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          <!-- 组件管理 -->
          <section id="component-management" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-puzzle me-2 text-primary"></i>
                  组件管理
                </h3>
              </div>
              <div class="card-body">
                <h5>🧩 组件管理功能</h5>
                <p>组件管理功能允许您集中管理服务器上部署的软件或服务，如Web服务器、数据库、监控工具等。</p>

                <h5 class="mt-4">📝 如何使用组件管理</h5>
                <div class="alert alert-info">
                  <h6 class="alert-heading">
                    <i class="bi bi-lightbulb me-2"></i>使用步骤
                  </h6>
                  <ol class="mb-0">
                    <li><strong>添加组件</strong> - 在组件管理页面添加常用的软件组件</li>
                    <li><strong>设置信息</strong> - 配置组件名称、默认端口、分类等信息</li>
                    <li><strong>表单选择</strong> - 在填写表单时，从组件列表中选择需要的组件</li>
                    <li><strong>自动填充</strong> - 系统会自动填充组件的端口和配置信息</li>
                    <li><strong>生成文档</strong> - 在Excel文档中自动生成部署架构图</li>
                  </ol>
                </div>

                <h5 class="mt-4">🎯 组件管理功能</h5>
                <ul>
                  <li><strong>统一管理</strong> - 集中管理所有常用组件信息</li>
                  <li><strong>快速复用</strong> - 在表单中快速选择和复用组件</li>
                  <li><strong>自动配置</strong> - 自动填充端口号和默认配置</li>
                  <li><strong>架构图生成</strong> - 自动生成部署架构图表</li>
                </ul>

                <h5 class="mt-4">📊 组件分类与服务器信息渲染</h5>
                <div class="alert alert-primary">
                  <h6 class="alert-heading">
                    <i class="bi bi-info-circle me-2"></i>组件维护与表单渲染机制
                  </h6>
                  <p class="mb-2">组件管理中维护的组件信息会自动渲染到填写表单的服务器信息部分：</p>
                  <ul class="mb-0">
                    <li><strong>组件选择</strong> - 在表单中选择服务器组件时，系统会从组件库中加载对应信息</li>
                    <li><strong>信息渲染</strong> - 组件的端口、版本、配置等信息会自动填充到表单字段中</li>
                    <li><strong>数据同步</strong> - 修改组件信息后，新建表单会自动使用最新的组件配置</li>
                    <li><strong>统一管理</strong> - 避免重复输入，确保组件信息的一致性和准确性</li>
                  </ul>
                </div>

                <div class="row">
                  <div class="col-md-3">
                    <div class="card border-primary">
                      <div class="card-body text-center">
                        <i class="bi bi-server text-primary fs-1"></i>
                        <h6 class="mt-2">Web服务</h6>
                        <small class="text-muted">Nginx, Apache, Tomcat</small>
                        <div class="mt-2">
                          <span class="badge bg-primary">自动填充端口</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card border-success">
                      <div class="card-body text-center">
                        <i class="bi bi-database text-success fs-1"></i>
                        <h6 class="mt-2">数据库</h6>
                        <small class="text-muted">MySQL, Redis, MongoDB</small>
                        <div class="mt-2">
                          <span class="badge bg-success">版本信息</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card border-warning">
                      <div class="card-body text-center">
                        <i class="bi bi-shield text-warning fs-1"></i>
                        <h6 class="mt-2">安全组件</h6>
                        <small class="text-muted">防火墙, WAF, IDS</small>
                        <div class="mt-2">
                          <span class="badge bg-warning">配置参数</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card border-info">
                      <div class="card-body text-center">
                        <i class="bi bi-eye text-info fs-1"></i>
                        <h6 class="mt-2">监控工具</h6>
                        <small class="text-muted">Zabbix, Prometheus</small>
                        <div class="mt-2">
                          <span class="badge bg-info">监控端口</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="alert alert-success mt-3">
                  <h6 class="alert-heading">
                    <i class="bi bi-magic me-2"></i>自动渲染机制
                  </h6>
                  <p class="mb-2">当您在表单中选择服务器组件时，系统会自动：</p>
                  <ul class="mb-0">
                    <li><strong>填充端口信息</strong> - 根据组件类型自动填入默认端口号</li>
                    <li><strong>显示版本信息</strong> - 自动显示组件的版本和配置信息</li>
                    <li><strong>生成架构图</strong> - 在Excel文档中自动生成部署架构图</li>
                    <li><strong>统计组件数量</strong> - 自动统计每种组件的部署数量</li>
                  </ul>
                </div>

                <h5 class="mt-4">⚙️ 组件操作</h5>
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>操作</th>
                        <th>说明</th>
                        <th>权限要求</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td><i class="bi bi-plus-circle text-success me-2"></i>新建组件</td>
                        <td>添加新的组件类型和配置</td>
                        <td>component.create</td>
                      </tr>
                      <tr>
                        <td><i class="bi bi-pencil text-warning me-2"></i>编辑组件</td>
                        <td>修改组件名称、端口、描述等信息</td>
                        <td>component.edit</td>
                      </tr>
                      <tr>
                        <td><i class="bi bi-toggle-on text-primary me-2"></i>启用/禁用</td>
                        <td>控制组件是否在表单中可选</td>
                        <td>component.edit</td>
                      </tr>
                      <tr>
                        <td><i class="bi bi-trash text-danger me-2"></i>删除组件</td>
                        <td>永久删除组件（谨慎操作）</td>
                        <td>component.delete</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </section>

          <!-- 模板管理 -->
          <section id="template-management" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-file-earmark-excel me-2 text-primary"></i>
                  模板管理
                </h3>
              </div>
              <div class="card-body">
                <h5>📄 模板概念</h5>
                <p>Excel模板定义了运维文档的格式和样式，支持自定义模板来满足不同项目需求。</p>

                <h5 class="mt-4">🔧 模板功能</h5>
                <div class="row">
                  <div class="col-md-6">
                    <div class="card border-info">
                      <div class="card-body">
                        <h6 class="card-title">
                          <i class="bi bi-upload me-2"></i>模板上传
                        </h6>
                        <p class="card-text small">支持上传自定义Excel模板文件，系统会自动解析模板结构。</p>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="card border-success">
                      <div class="card-body">
                        <h6 class="card-title">
                          <i class="bi bi-arrow-repeat me-2"></i>版本管理
                        </h6>
                        <p class="card-text small">支持多个模板版本，可以动态切换使用不同版本的模板。</p>
                      </div>
                    </div>
                  </div>
                </div>

                <h5 class="mt-4">🎨 模板占位符</h5>
                <div class="alert alert-warning">
                  <h6 class="alert-heading">
                    <i class="bi bi-code me-2"></i>占位符语法说明
                  </h6>
                  <p>模板使用双花括号占位符处理动态内容：</p>

                  <div class="row">
                    <div class="col-md-6">
                      <strong>基本字段占位符：</strong><br>
                      <code v-text="'{{公司名称}}'"></code><br>
                      <code v-text="'{{记录日期}}'"></code><br>
                      <code v-text="'{{部署包版本}}'"></code><br>
                      <code v-text="'{{管理员账号}}'"></code>
                    </div>
                    <div class="col-md-6">
                      <strong>组件占位符：</strong><br>
                      <code v-text="'{{docker.port}}'"></code><br>
                      <code v-text="'{{nginx.version}}'"></code><br>
                      <code v-text="'{{mysql.count}}'"></code><br>
                      <code v-text="'{{组件名.字段}}'"></code>
                    </div>
                  </div>

                  <div class="row mt-3">
                    <div class="col-md-6">
                      <strong>动态列表占位符：</strong><br>
                      <code v-text="'{{服务器信息 | range}}'"></code><br>
                      <code v-text="'{{维护记录 | range}}'"></code>
                    </div>
                    <div class="col-md-6">
                      <strong>复合占位符：</strong><br>
                      <code v-text="'{{运维信息}}'"></code><br>
                      <code v-text="'{{客户APP}}'"></code>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- 用户管理 -->
          <section id="user-management" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-people me-2 text-primary"></i>
                  用户管理
                </h3>
              </div>
              <div class="card-body">
                <h5>👥 权限体系</h5>
                <p>系统采用基于角色的权限管理（RBAC），支持用户、角色、权限的灵活配置。</p>

                <h5 class="mt-4">🎭 默认角色</h5>
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>角色</th>
                        <th>权限范围</th>
                        <th>适用人员</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td><span class="badge bg-danger">管理员</span></td>
                        <td>所有权限</td>
                        <td>系统管理员</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-primary">运维人员</span></td>
                        <td>表单、组件、缓存管理</td>
                        <td>运维工程师</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-success">表单管理员</span></td>
                        <td>表单和模板管理</td>
                        <td>文档管理员</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-secondary">查看者</span></td>
                        <td>只读权限</td>
                        <td>普通用户</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <h5 class="mt-4">🔐 权限模块</h5>
                <div class="row">
                  <div class="col-md-4">
                    <ul class="list-group">
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        用户管理
                        <span class="badge bg-primary rounded-pill">user.*</span>
                      </li>
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        角色管理
                        <span class="badge bg-primary rounded-pill">role.*</span>
                      </li>
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        权限管理
                        <span class="badge bg-primary rounded-pill">permission.*</span>
                      </li>
                    </ul>
                  </div>
                  <div class="col-md-4">
                    <ul class="list-group">
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        表单管理
                        <span class="badge bg-success rounded-pill">form.*</span>
                      </li>
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        组件管理
                        <span class="badge bg-success rounded-pill">component.*</span>
                      </li>
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        模板管理
                        <span class="badge bg-success rounded-pill">template.*</span>
                      </li>
                    </ul>
                  </div>
                  <div class="col-md-4">
                    <ul class="list-group">
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        系统管理
                        <span class="badge bg-warning rounded-pill">system.*</span>
                      </li>
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        缓存管理
                        <span class="badge bg-warning rounded-pill">cache.*</span>
                      </li>
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        历史记录
                        <span class="badge bg-warning rounded-pill">history.*</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- 系统管理 -->
          <section id="system-management" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-gear me-2 text-primary"></i>
                  系统管理
                </h3>
              </div>
              <div class="card-body">
                <h5>📊 性能监控</h5>
                <p>实时监控系统性能，包括CPU、内存、磁盘使用率，以及API响应时间和慢查询。</p>

                <div class="row">
                  <div class="col-md-3">
                    <div class="card border-info">
                      <div class="card-body text-center">
                        <i class="bi bi-cpu text-info fs-1"></i>
                        <h6 class="mt-2">CPU监控</h6>
                        <small class="text-muted">实时CPU使用率</small>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card border-warning">
                      <div class="card-body text-center">
                        <i class="bi bi-memory text-warning fs-1"></i>
                        <h6 class="mt-2">内存监控</h6>
                        <small class="text-muted">内存使用情况</small>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card border-success">
                      <div class="card-body text-center">
                        <i class="bi bi-hdd text-success fs-1"></i>
                        <h6 class="mt-2">磁盘监控</h6>
                        <small class="text-muted">磁盘空间使用</small>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card border-primary">
                      <div class="card-body text-center">
                        <i class="bi bi-graph-up text-primary fs-1"></i>
                        <h6 class="mt-2">性能统计</h6>
                        <small class="text-muted">API响应时间</small>
                      </div>
                    </div>
                  </div>
                </div>

                <h5 class="mt-4">🗂️ 文件管理</h5>
                <div class="alert alert-info">
                  <h6 class="alert-heading">
                    <i class="bi bi-folder me-2"></i>历史文件管理
                  </h6>
                  <ul class="mb-0">
                    <li><strong>批量操作:</strong> 支持批量选择和删除历史文件</li>
                    <li><strong>分页显示:</strong> 支持10、20、50条数据的分页显示</li>
                    <li><strong>文件备份:</strong> 重要文件自动备份到专用目录</li>
                    <li><strong>编辑历史:</strong> 记录文件编辑操作和修改内容</li>
                  </ul>
                </div>

                <h5 class="mt-4">⚡ 缓存管理</h5>
                <p>系统使用多级缓存提升性能，包括用户权限缓存、表单数据缓存等。</p>
                <div class="row">
                  <div class="col-md-6">
                    <div class="card border-primary">
                      <div class="card-body">
                        <h6 class="card-title">
                          <i class="bi bi-lightning-charge me-2"></i>缓存类型
                        </h6>
                        <ul class="card-text small mb-0">
                          <li>用户权限缓存</li>
                          <li>表单数据缓存</li>
                          <li>组件信息缓存</li>
                          <li>系统配置缓存</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="card border-success">
                      <div class="card-body">
                        <h6 class="card-title">
                          <i class="bi bi-tools me-2"></i>缓存操作
                        </h6>
                        <ul class="card-text small mb-0">
                          <li>查看缓存状态</li>
                          <li>清理过期缓存</li>
                          <li>缓存命中率统计</li>
                          <li>缓存性能监控</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- API文档 -->
          <section id="api-docs" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-code-square me-2 text-primary"></i>
                  API文档
                </h3>
              </div>
              <div class="card-body">
                <h5>🔌 API接口</h5>
                <p>系统提供完整的RESTful API接口，支持第三方系统集成和自动化操作。</p>

                <h5 class="mt-4">📚 接口分类</h5>
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>模块</th>
                        <th>接口数量</th>
                        <th>主要功能</th>
                        <th>权限要求</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td><span class="badge bg-primary">认证管理</span></td>
                        <td>7个</td>
                        <td>登录、注册、token验证</td>
                        <td>无需权限</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-success">表单管理</span></td>
                        <td>15+个</td>
                        <td>表单CRUD、导入导出</td>
                        <td>form.*</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-warning">组件管理</span></td>
                        <td>5个</td>
                        <td>组件CRUD操作</td>
                        <td>component.*</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-info">权限管理</span></td>
                        <td>12+个</td>
                        <td>用户、角色、权限管理</td>
                        <td>user.*, role.*</td>
                      </tr>
                      <tr>
                        <td><span class="badge bg-secondary">性能监控</span></td>
                        <td>12个</td>
                        <td>系统监控、统计报告</td>
                        <td>system.view</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <h5 class="mt-4">🔑 认证方式</h5>
                <div class="alert alert-warning">
                  <h6 class="alert-heading">JWT Token认证</h6>
                  <p>所有API接口使用JWT Token进行认证：</p>
                  <ol>
                    <li>通过 <code>/auth/login</code> 接口获取access_token</li>
                    <li>在请求头中添加: <code>Authorization: Bearer {token}</code></li>
                    <li>Token有效期为24小时，过期需重新登录</li>
                  </ol>
                </div>

                <h5 class="mt-4">🧪 在线调试</h5>
                <p>访问 <strong>API文档</strong> 页面可以：</p>
                <ul>
                  <li>查看完整的接口文档</li>
                  <li>在线测试API接口</li>
                  <li>查看请求和响应示例</li>
                  <li>根据权限显示可用接口</li>
                </ul>
              </div>
            </div>
          </section>

          <!-- 故障排除 -->
          <section id="troubleshooting" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-tools me-2 text-primary"></i>
                  故障排除
                </h3>
              </div>
              <div class="card-body">
                <h5>🚨 常见问题解决</h5>

                <div class="accordion" id="troubleshootingAccordion">
                  <div class="accordion-item">
                    <h2 class="accordion-header" id="headingOne">
                      <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                        <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                        登录失败或权限不足
                      </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#troubleshootingAccordion">
                      <div class="accordion-body">
                        <strong>可能原因：</strong>
                        <ul>
                          <li>用户名或密码错误</li>
                          <li>用户账户被禁用</li>
                          <li>Token过期或无效</li>
                          <li>用户权限不足</li>
                        </ul>
                        <strong>解决方案：</strong>
                        <ol>
                          <li>检查用户名和密码是否正确</li>
                          <li>联系管理员检查账户状态</li>
                          <li>清除浏览器缓存重新登录</li>
                          <li>联系管理员分配相应权限</li>
                        </ol>
                      </div>
                    </div>
                  </div>

                  <div class="accordion-item">
                    <h2 class="accordion-header" id="headingTwo">
                      <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                        <i class="bi bi-file-x text-danger me-2"></i>
                        表单提交失败
                      </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                      <div class="accordion-body">
                        <strong>可能原因：</strong>
                        <ul>
                          <li>必填字段未填写</li>
                          <li>数据格式不正确</li>
                          <li>网络连接问题</li>
                          <li>服务器错误</li>
                        </ul>
                        <strong>解决方案：</strong>
                        <ol>
                          <li>检查所有必填字段（红色星号标记）</li>
                          <li>验证邮箱、电话等格式是否正确</li>
                          <li>检查网络连接状态</li>
                          <li>稍后重试或检查系统状态</li>
                        </ol>
                      </div>
                    </div>
                  </div>

                  <div class="accordion-item">
                    <h2 class="accordion-header" id="headingThree">
                      <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                        <i class="bi bi-download text-info me-2"></i>
                        Excel导出异常
                      </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                      <div class="accordion-body">
                        <strong>可能原因：</strong>
                        <ul>
                          <li>模板文件损坏</li>
                          <li>数据包含特殊字符</li>
                          <li>服务器磁盘空间不足</li>
                          <li>Excel模板版本不兼容</li>
                        </ul>
                        <strong>解决方案：</strong>
                        <ol>
                          <li>重新上传Excel模板</li>
                          <li>检查数据中的特殊字符</li>
                          <li>联系管理员检查服务器状态</li>
                          <li>使用默认模板重新生成</li>
                        </ol>
                      </div>
                    </div>
                  </div>

                  <div class="accordion-item">
                    <h2 class="accordion-header" id="headingFour">
                      <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour">
                        <i class="bi bi-speedometer2 text-success me-2"></i>
                        系统性能问题
                      </button>
                    </h2>
                    <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                      <div class="accordion-body">
                        <strong>性能优化建议：</strong>
                        <ul>
                          <li><strong>清理缓存：</strong> 定期清理系统缓存释放内存</li>
                          <li><strong>数据清理：</strong> 删除不需要的历史记录和文件</li>
                          <li><strong>浏览器优化：</strong> 清除浏览器缓存和Cookie</li>
                          <li><strong>网络检查：</strong> 确保网络连接稳定</li>
                        </ul>
                        <strong>监控指标：</strong>
                        <ul>
                          <li>CPU使用率应低于80%</li>
                          <li>内存使用率应低于85%</li>
                          <li>磁盘使用率应低于90%</li>
                          <li>API响应时间应低于2秒</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>


              </div>
            </div>
          </section>

          <!-- 常见问题 -->
          <section id="faq" class="mb-5">
            <div class="card">
              <div class="card-header">
                <h3 class="mb-0">
                  <i class="bi bi-question-circle me-2 text-primary"></i>
                  常见问题
                </h3>
              </div>
              <div class="card-body">
                <h5>❓ 使用问题</h5>

                <div class="row">
                  <div class="col-md-6">
                    <div class="card border-primary mb-3">
                      <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                          <i class="bi bi-question-circle me-2"></i>
                          如何修改密码？
                        </h6>
                      </div>
                      <div class="card-body">
                        <p class="card-text small">
                          点击右上角用户头像 → 个人资料 → 修改密码，输入当前密码和新密码即可。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="card border-success mb-3">
                      <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                          <i class="bi bi-question-circle me-2"></i>
                          如何批量导入数据？
                        </h6>
                      </div>
                      <div class="card-body">
                        <p class="card-text small">
                          在表单页面点击"导入数据"按钮，支持Excel和JSON格式的批量数据导入。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="card border-warning mb-3">
                      <div class="card-header bg-warning text-white">
                        <h6 class="mb-0">
                          <i class="bi bi-question-circle me-2"></i>
                          表单数据能否恢复？
                        </h6>
                      </div>
                      <div class="card-body">
                        <p class="card-text small">
                          系统会自动备份重要数据，删除的表单可以在一定时间内通过管理员恢复。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="card border-info mb-3">
                      <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                          <i class="bi bi-question-circle me-2"></i>
                          如何自定义模板？
                        </h6>
                      </div>
                      <div class="card-body">
                        <p class="card-text small">
                          在模板管理页面上传自定义Excel模板，使用Jinja2语法定义动态内容。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="card border-secondary mb-3">
                      <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                          <i class="bi bi-question-circle me-2"></i>
                          支持哪些浏览器？
                        </h6>
                      </div>
                      <div class="card-body">
                        <p class="card-text small">
                          推荐使用Chrome、Firefox、Edge等现代浏览器，IE浏览器可能存在兼容性问题。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="card border-dark mb-3">
                      <div class="card-header bg-dark text-white">
                        <h6 class="mb-0">
                          <i class="bi bi-question-circle me-2"></i>
                          数据安全如何保障？
                        </h6>
                      </div>
                      <div class="card-body">
                        <p class="card-text small">
                          系统采用JWT认证、权限控制、数据加密等多重安全措施保护用户数据。
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <h5 class="mt-4">📈 更新日志</h5>
                <div class="timeline">
                  <div class="alert alert-light border-start border-primary border-4">
                    <h6 class="alert-heading">
                      <i class="bi bi-calendar-event me-2"></i>
                      v2.1.0 (2024-12-06)
                    </h6>
                    <ul class="mb-0">
                      <li>新增性能监控功能</li>
                      <li>优化权限管理体系</li>
                      <li>增强API文档和在线调试</li>
                      <li>改进用户界面和体验</li>
                    </ul>
                  </div>

                  <div class="alert alert-light border-start border-success border-4">
                    <h6 class="alert-heading">
                      <i class="bi bi-calendar-event me-2"></i>
                      v2.0.0 (2024-11-15)
                    </h6>
                    <ul class="mb-0">
                      <li>重构权限管理系统</li>
                      <li>新增表单快照功能</li>
                      <li>优化Excel模板引擎</li>
                      <li>增强数据导入导出</li>
                    </ul>
                  </div>

                  <div class="alert alert-light border-start border-warning border-4">
                    <h6 class="alert-heading">
                      <i class="bi bi-calendar-event me-2"></i>
                      v1.5.0 (2024-10-20)
                    </h6>
                    <ul class="mb-0">
                      <li>新增组件管理功能</li>
                      <li>支持多模板版本管理</li>
                      <li>优化表单验证机制</li>
                      <li>改进系统性能</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Help',
  methods: {
    scrollTo(elementId) {
      const element = document.getElementById(elementId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    },
    
    printPage() {
      window.print()
    }
  }
}
</script>

<style scoped>
.help-page {
  padding: 20px;
}

.sticky-top {
  z-index: 1020;
}

.nav-link {
  color: #6c757d;
  border: none;
  border-radius: 0;
  border-left: 3px solid transparent;
  padding: 0.75rem 1rem;
}

.nav-link:hover {
  background-color: #f8f9fa;
  border-left-color: #007bff;
  color: #007bff;
}

.nav-link.active {
  background-color: #e3f2fd;
  border-left-color: #007bff;
  color: #007bff;
}

.help-content section {
  scroll-margin-top: 100px;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

@media print {
  .sticky-top {
    position: static !important;
  }
  
  .col-lg-3 {
    display: none;
  }
  
  .col-lg-9 {
    width: 100%;
    max-width: 100%;
  }
}
</style>
