/**
 * 响应式窗口全局样式
 * 适用于所有模态框、弹窗、对话框等组件
 */

/* ===== 基础响应式变量 ===== */
:root {
  /* 断点定义 */
  --mobile-max: 768px;
  --tablet-max: 1024px;
  --desktop-min: 1025px;
  --large-screen-min: 1400px;
  
  /* 模态框尺寸比例 */
  --modal-sm-width: 400px;
  --modal-md-width: 600px;
  --modal-lg-width: 900px;
  --modal-xl-width: 1200px;
  --modal-xxl-width: 1400px;
  
  /* 响应式间距 */
  --modal-padding-mobile: 1rem;
  --modal-padding-tablet: 1.5rem;
  --modal-padding-desktop: 2rem;
  
  /* 动画时长 */
  --modal-transition: 0.3s ease;
}

/* ===== 通用响应式模态框基类 ===== */
.responsive-window {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  animation: fadeIn var(--modal-transition);
}

.responsive-window-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn var(--modal-transition);
  max-width: 95vw;
  max-height: 95vh;
}

/* ===== 动画效果 ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0; 
    transform: scale(0.9) translateY(-20px); 
  }
  to { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
  }
}

/* ===== 尺寸变体 ===== */
.responsive-window-sm .responsive-window-container {
  width: min(90vw, var(--modal-sm-width));
  height: min(80vh, 500px);
}

.responsive-window-md .responsive-window-container {
  width: min(90vw, var(--modal-md-width));
  height: min(85vh, 600px);
}

.responsive-window-lg .responsive-window-container {
  width: min(95vw, var(--modal-lg-width));
  height: min(90vh, 700px);
}

.responsive-window-xl .responsive-window-container {
  width: min(98vw, var(--modal-xl-width));
  height: min(95vh, 800px);
}

.responsive-window-xxl .responsive-window-container {
  width: min(98vw, var(--modal-xxl-width));
  height: min(95vh, 900px);
}

/* ===== 移动端适配 (≤768px) ===== */
@media (max-width: 768px) {
  .responsive-window-container {
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0 !important;
    margin: 0 !important;
  }
  
  .responsive-window {
    align-items: stretch;
    justify-content: stretch;
  }
  
  /* 移动端内容区域 */
  .responsive-window-header {
    padding: var(--modal-padding-mobile) !important;
    font-size: 1rem !important;
  }
  
  .responsive-window-body {
    padding: var(--modal-padding-mobile) !important;
  }
  
  .responsive-window-footer {
    padding: var(--modal-padding-mobile) !important;
  }
}

/* ===== 平板端适配 (769px - 1024px) ===== */
@media (min-width: 769px) and (max-width: 1024px) {
  .responsive-window-sm .responsive-window-container {
    width: 80vw;
    height: 70vh;
  }
  
  .responsive-window-md .responsive-window-container {
    width: 85vw;
    height: 75vh;
  }
  
  .responsive-window-lg .responsive-window-container,
  .responsive-window-xl .responsive-window-container,
  .responsive-window-xxl .responsive-window-container {
    width: 90vw;
    height: 80vh;
  }
  
  .responsive-window-header,
  .responsive-window-body,
  .responsive-window-footer {
    padding: var(--modal-padding-tablet);
  }
}

/* ===== 桌面端适配 (≥1025px) ===== */
@media (min-width: 1025px) {
  .responsive-window-header,
  .responsive-window-body,
  .responsive-window-footer {
    padding: var(--modal-padding-desktop);
  }
}

/* ===== 大屏幕优化 (≥1400px) ===== */
@media (min-width: 1400px) {
  .responsive-window-container {
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4);
  }
}

/* ===== 通用组件样式 ===== */
.responsive-window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #dee2e6;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  flex-shrink: 0;
}

.responsive-window-title {
  margin: 0;
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  font-weight: 600;
  color: #2c3e50;
}

.responsive-window-close {
  background: none;
  border: none;
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  cursor: pointer;
  padding: 0.5rem;
  color: #6c757d;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  min-height: 2rem;
}

.responsive-window-close:hover {
  color: #343a40;
  background-color: #e9ecef;
  transform: scale(1.1);
}

.responsive-window-body {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  background: white;
}

.responsive-window-footer {
  border-top: 1px solid #dee2e6;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  flex-shrink: 0;
}

/* ===== 滚动条美化 ===== */
.responsive-window-body::-webkit-scrollbar {
  width: 8px;
}

.responsive-window-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.responsive-window-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.responsive-window-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ===== 特殊状态 ===== */
.responsive-window-fullscreen .responsive-window-container {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0 !important;
  max-width: none !important;
  max-height: none !important;
}

.responsive-window-loading {
  pointer-events: none;
  opacity: 0.7;
}

.responsive-window-loading .responsive-window-container {
  transform: scale(0.98);
}

/* ===== 无障碍支持 ===== */
@media (prefers-reduced-motion: reduce) {
  .responsive-window,
  .responsive-window-container {
    animation: none;
  }
  
  .responsive-window-close {
    transition: none;
  }
}

/* ===== 高对比度模式 ===== */
@media (prefers-contrast: high) {
  .responsive-window {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .responsive-window-container {
    border: 2px solid #000;
  }
  
  .responsive-window-header,
  .responsive-window-footer {
    border-color: #000;
  }
}

/* ===== 打印样式 ===== */
@media print {
  .responsive-window {
    position: static;
    background: none;
  }
  
  .responsive-window-container {
    width: 100% !important;
    height: auto !important;
    box-shadow: none;
    border: 1px solid #000;
  }
}
