<template>
  <div class="user-profile">
    <div class="container-fluid">
      <div class="row justify-content-center">
        <div class="col-lg-8">
          <!-- 页面标题 -->
          <div class="d-flex align-items-center mb-4">
            <button class="btn btn-outline-secondary me-3" @click="$router.go(-1)">
              <i class="bi bi-arrow-left"></i>
            </button>
            <h2 class="mb-0">
              <i class="bi bi-person-gear me-2 text-primary"></i>
              个人设置
            </h2>
          </div>

          <!-- 个人信息卡片 -->
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-gradient-primary text-white">
              <h5 class="card-title mb-0">
                <i class="bi bi-person-circle me-2"></i>
                个人信息
              </h5>
            </div>
            <div class="card-body p-4">
              <form @submit.prevent="updateProfile">
                <div class="row g-3">
                  <!-- 用户名（只读） -->
                  <div class="col-md-6">
                    <label class="form-label fw-semibold">
                      <i class="bi bi-person me-1"></i>用户名
                    </label>
                    <input 
                      type="text" 
                      class="form-control" 
                      :value="userInfo.username"
                      readonly
                      style="background-color: #f8f9fa;"
                    >
                    <small class="text-muted">用户名不可修改</small>
                  </div>

                  <!-- 邮箱 -->
                  <div class="col-md-6">
                    <label class="form-label fw-semibold">
                      <i class="bi bi-envelope me-1"></i>邮箱地址 <span class="text-danger">*</span>
                    </label>
                    <input 
                      type="email" 
                      class="form-control" 
                      v-model="profileForm.email"
                      required
                    >
                  </div>

                  <!-- 真实姓名 -->
                  <div class="col-md-6">
                    <label class="form-label fw-semibold">
                      <i class="bi bi-person-badge me-1"></i>真实姓名
                    </label>
                    <input 
                      type="text" 
                      class="form-control" 
                      v-model="profileForm.real_name"
                      placeholder="请输入真实姓名"
                    >
                  </div>

                  <!-- 手机号 -->
                  <div class="col-md-6">
                    <label class="form-label fw-semibold">
                      <i class="bi bi-phone me-1"></i>手机号码
                    </label>
                    <input 
                      type="tel" 
                      class="form-control" 
                      v-model="profileForm.phone"
                      placeholder="请输入手机号码"
                    >
                  </div>

                  <!-- 部门 -->
                  <div class="col-md-6">
                    <label class="form-label fw-semibold">
                      <i class="bi bi-building me-1"></i>所属部门
                    </label>
                    <input 
                      type="text" 
                      class="form-control" 
                      v-model="profileForm.department"
                      placeholder="请输入所属部门"
                    >
                  </div>

                  <!-- 职位 -->
                  <div class="col-md-6">
                    <label class="form-label fw-semibold">
                      <i class="bi bi-briefcase me-1"></i>职位
                    </label>
                    <input 
                      type="text" 
                      class="form-control" 
                      v-model="profileForm.position"
                      placeholder="请输入职位"
                    >
                  </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                  <button type="submit" class="btn btn-primary px-4" :disabled="updating">
                    <span v-if="updating" class="spinner-border spinner-border-sm me-2"></span>
                    <i v-else class="bi bi-check-circle me-1"></i>
                    {{ updating ? '保存中...' : '保存更改' }}
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- 密码修改卡片 -->
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-gradient-warning text-dark">
              <h5 class="card-title mb-0">
                <i class="bi bi-shield-lock me-2"></i>
                修改密码
              </h5>
            </div>
            <div class="card-body p-4">
              <form @submit.prevent="changePassword">
                <div class="row g-3">
                  <!-- 当前密码 -->
                  <div class="col-12">
                    <label class="form-label fw-semibold">
                      <i class="bi bi-key me-1"></i>当前密码 <span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                      <input 
                        :type="showOldPassword ? 'text' : 'password'" 
                        class="form-control" 
                        v-model="passwordForm.oldPassword"
                        placeholder="请输入当前密码"
                        required
                      >
                      <button 
                        type="button" 
                        class="btn btn-outline-secondary"
                        @click="showOldPassword = !showOldPassword"
                      >
                        <i :class="showOldPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                      </button>
                    </div>
                  </div>

                  <!-- 新密码 -->
                  <div class="col-md-6">
                    <label class="form-label fw-semibold">
                      <i class="bi bi-shield-plus me-1"></i>新密码 <span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                      <input 
                        :type="showNewPassword ? 'text' : 'password'" 
                        class="form-control" 
                        v-model="passwordForm.newPassword"
                        placeholder="请输入新密码"
                        required
                        minlength="6"
                      >
                      <button 
                        type="button" 
                        class="btn btn-outline-secondary"
                        @click="showNewPassword = !showNewPassword"
                      >
                        <i :class="showNewPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                      </button>
                    </div>
                    <small class="text-muted">密码长度至少6位</small>
                  </div>

                  <!-- 确认新密码 -->
                  <div class="col-md-6">
                    <label class="form-label fw-semibold">
                      <i class="bi bi-shield-check me-1"></i>确认新密码 <span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                      <input 
                        :type="showConfirmPassword ? 'text' : 'password'" 
                        class="form-control" 
                        v-model="passwordForm.confirmPassword"
                        placeholder="请再次输入新密码"
                        required
                        :class="{ 'is-invalid': passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword }"
                      >
                      <button 
                        type="button" 
                        class="btn btn-outline-secondary"
                        @click="showConfirmPassword = !showConfirmPassword"
                      >
                        <i :class="showConfirmPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                      </button>
                    </div>
                    <div v-if="passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword" 
                         class="invalid-feedback">
                      两次输入的密码不一致
                    </div>
                  </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                  <button 
                    type="submit" 
                    class="btn btn-warning px-4" 
                    :disabled="changingPassword || passwordForm.newPassword !== passwordForm.confirmPassword"
                  >
                    <span v-if="changingPassword" class="spinner-border spinner-border-sm me-2"></span>
                    <i v-else class="bi bi-shield-check me-1"></i>
                    {{ changingPassword ? '修改中...' : '修改密码' }}
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- 账户信息卡片 -->
          <div class="card shadow-sm">
            <div class="card-header bg-gradient-info text-white">
              <h5 class="card-title mb-0">
                <i class="bi bi-info-circle me-2"></i>
                账户信息
              </h5>
            </div>
            <div class="card-body p-4">
              <div class="row g-3">
                <div class="col-md-6">
                  <div class="info-item">
                    <label class="text-muted small">账户状态</label>
                    <div>
                      <span :class="userInfo.is_active ? 'badge bg-success' : 'badge bg-danger'">
                        {{ userInfo.is_active ? '已激活' : '已禁用' }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-item">
                    <label class="text-muted small">账户类型</label>
                    <div>
                      <span :class="userInfo.is_admin ? 'badge bg-warning text-dark' : 'badge bg-primary'">
                        {{ userInfo.is_admin ? '管理员' : '普通用户' }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-item">
                    <label class="text-muted small">注册时间</label>
                    <div>{{ formatDateTime(userInfo.created_at) }}</div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-item">
                    <label class="text-muted small">最后登录</label>
                    <div>{{ userInfo.last_login ? formatDateTime(userInfo.last_login) : '从未登录' }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Toast通知 -->
    <toast-notification ref="toast" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ToastNotification from '@/components/common/ToastNotification.vue'

export default {
  name: 'UserProfile',
  components: {
    ToastNotification
  },
  data() {
    return {
      userInfo: {},
      profileForm: {
        email: '',
        real_name: '',
        phone: '',
        department: '',
        position: ''
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      updating: false,
      changingPassword: false,
      showOldPassword: false,
      showNewPassword: false,
      showConfirmPassword: false
    }
  },
  computed: {
    ...mapGetters(['currentUser'])
  },
  async mounted() {
    await this.loadUserProfile()
  },
  methods: {
    async loadUserProfile() {
      try {
        const API_BASE_URL = process.env.NODE_ENV === 'production'
          ? (process.env.VUE_APP_API_BASE_URL || '/api')
          : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

        const response = await fetch(`${API_BASE_URL}/auth/profile`, {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            this.userInfo = result.data.user
            // 填充表单
            this.profileForm = {
              email: this.userInfo.email || '',
              real_name: this.userInfo.real_name || '',
              phone: this.userInfo.phone || '',
              department: this.userInfo.department || '',
              position: this.userInfo.position || ''
            }
          }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        this.showToast('加载用户信息失败', '错误', 'error')
      }
    },

    async updateProfile() {
      this.updating = true
      try {
        const API_BASE_URL = process.env.NODE_ENV === 'production'
          ? (process.env.VUE_APP_API_BASE_URL || '/api')
          : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

        const response = await fetch(`${API_BASE_URL}/auth/profile`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.$store.state.token}`
          },
          body: JSON.stringify(this.profileForm)
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            this.userInfo = result.data.user
            // 更新store中的用户信息
            this.$store.commit('SET_USER', this.userInfo)
            this.showToast('个人信息更新成功', '成功', 'success')
          } else {
            this.showToast(result.message || '更新失败', '错误', 'error')
          }
        } else {
          const errorResult = await response.json()
          this.showToast(errorResult.message || '更新失败', '错误', 'error')
        }
      } catch (error) {
        console.error('更新个人信息失败:', error)
        this.showToast('更新失败', '错误', 'error')
      } finally {
        this.updating = false
      }
    },

    async changePassword() {
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        this.showToast('两次输入的密码不一致', '错误', 'error')
        return
      }

      this.changingPassword = true
      try {
        const API_BASE_URL = process.env.NODE_ENV === 'production'
          ? (process.env.VUE_APP_API_BASE_URL || '/api')
          : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

        const response = await fetch(`${API_BASE_URL}/auth/change-password`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.$store.state.token}`
          },
          body: JSON.stringify({
            old_password: this.passwordForm.oldPassword,
            new_password: this.passwordForm.newPassword
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            this.showToast('密码修改成功', '成功', 'success')
            // 清空密码表单
            this.passwordForm = {
              oldPassword: '',
              newPassword: '',
              confirmPassword: ''
            }
          } else {
            this.showToast(result.message || '密码修改失败', '错误', 'error')
          }
        } else {
          const errorResult = await response.json()
          this.showToast(errorResult.message || '密码修改失败', '错误', 'error')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        this.showToast('密码修改失败', '错误', 'error')
      } finally {
        this.changingPassword = false
      }
    },

    formatDateTime(dateString) {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleString('zh-CN')
    },

    showToast(message, title = '提示', type = 'info') {
      this.$refs.toast.showToast(message, title, type)
    }
  }
}
</script>

<style scoped>
.bg-gradient-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.bg-gradient-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.card {
  transition: all 0.3s ease;
  border: none;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.info-item {
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  border-left: 4px solid #007bff;
}

.info-item label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .container-fluid {
    padding: 1rem;
  }
}
</style>
