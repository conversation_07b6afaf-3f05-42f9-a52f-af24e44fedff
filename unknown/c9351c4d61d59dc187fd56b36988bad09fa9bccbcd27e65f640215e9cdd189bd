<template>
  <div id="app">
    <!-- 导航栏 - 只在已登录时显示 -->
    <nav v-if="isAuthenticated" class="navbar navbar-expand-lg navbar-dark modern-navbar">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-shield-check me-2"></i>
          梆梆安全-运维信息登记平台
        </router-link>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item" v-if="hasPermission('form.create')">
              <router-link class="nav-link" to="/fill-sheet" active-class="active">
                <i class="bi bi-file-earmark-plus me-1"></i>
                填写运维信息
              </router-link>
            </li>
            <li class="nav-item" v-if="hasPermission('form.view')">
              <router-link class="nav-link" to="/history-data" active-class="active">
                <i class="bi bi-clock-history me-1"></i>
                表单提交历史
              </router-link>
            </li>
            <li class="nav-item dropdown" v-if="hasPermission('template.view') || hasPermission('component.view')">
              <a class="nav-link dropdown-toggle" href="#" id="importDropdown" role="button" data-bs-toggle="dropdown">
                <i class="bi bi-upload me-1"></i>
                数据导入
              </a>
              <ul class="dropdown-menu">
                <li v-if="hasPermission('template.view')">
                  <router-link class="dropdown-item" to="/excel-import">
                    <i class="bi bi-file-earmark-excel me-2"></i>Excel文件导入
                  </router-link>
                </li>
                <li v-if="hasPermission('template.view')">
                  <router-link class="dropdown-item" to="/json-import">
                    <i class="bi bi-code-square me-2"></i>JSON数据导入
                  </router-link>
                </li>
              </ul>
            </li>
            <li class="nav-item dropdown" v-if="hasPermission('template.view') || hasPermission('system.view')">
              <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                <i class="bi bi-gear me-1"></i>
                系统管理
              </a>
              <ul class="dropdown-menu">


                <!-- 表单模板管理 -->
                <li v-if="hasPermission('template.view')">
                  <router-link class="dropdown-item" to="/form-template-manager">
                    <i class="bi bi-layout-text-window me-2"></i>表单模板管理
                  </router-link>
                </li>


              </ul>
            </li>
            <!-- 权限管理菜单 - 仅管理员可见 -->
            <li class="nav-item dropdown" v-if="isAdmin">
              <a class="nav-link dropdown-toggle" href="#" id="rbacDropdown" role="button" data-bs-toggle="dropdown">
                <i class="bi bi-people me-1"></i>
                权限管理
              </a>
              <ul class="dropdown-menu">
                <li>
                  <router-link class="dropdown-item" to="/user-management">
                    <i class="bi bi-person me-2"></i>用户管理
                  </router-link>
                </li>
                <li>
                  <router-link class="dropdown-item" to="/group-management">
                    <i class="bi bi-people-fill me-2"></i>用户组管理
                  </router-link>
                </li>
                <li>
                  <router-link class="dropdown-item" to="/role-management">
                    <i class="bi bi-person-badge me-2"></i>角色管理
                  </router-link>
                </li>
                <li>
                  <router-link class="dropdown-item" to="/permission-management">
                    <i class="bi bi-key me-2"></i>权限点管理
                  </router-link>
                </li>
              </ul>
            </li>
          </ul>

          <!-- 帮助菜单 -->
          <ul class="navbar-nav">
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="helpDropdown" role="button" data-bs-toggle="dropdown">
                <i class="bi bi-question-circle me-1"></i>
                帮助
              </a>
              <ul class="dropdown-menu">
                <li>
                  <router-link class="dropdown-item" to="/api-docs">
                    <i class="bi bi-book me-2"></i>API文档
                  </router-link>
                </li>
                <li>
                  <router-link class="dropdown-item" to="/help">
                    <i class="bi bi-question-circle me-2"></i>使用指南
                  </router-link>
                </li>
                <li>
                  <router-link class="dropdown-item" to="/about">
                    <i class="bi bi-shield-check me-2"></i>关于平台
                  </router-link>
                </li>
              </ul>
            </li>

            <!-- 用户信息和登出 -->
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                <i class="bi bi-person-circle me-1"></i>
                {{ currentUser?.real_name || currentUser?.username }}
                <span v-if="isAdmin" class="badge bg-warning text-dark ms-1">管理员</span>
              </a>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <h6 class="dropdown-header">
                    <i class="bi bi-person me-2"></i>
                    {{ currentUser?.real_name || currentUser?.username }}
                  </h6>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item" href="#" @click="showProfile">
                    <i class="bi bi-person-gear me-2"></i>个人设置
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="#" @click="changePassword">
                    <i class="bi bi-key me-2"></i>修改密码
                  </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item text-danger" href="#" @click="handleLogout">
                    <i class="bi bi-box-arrow-right me-2"></i>退出登录
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <router-view/>
    </div>

    <!-- 修改密码模态框 -->
    <change-password-modal
      v-if="showChangePasswordModal"
      @close="showChangePasswordModal = false"
      @success="handlePasswordChangeSuccess"
      @error="handlePasswordChangeError"
    />
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import ChangePasswordModal from '@/components/modals/ChangePasswordModal.vue'

export default {
  name: 'App',
  components: {
    ChangePasswordModal
  },
  data() {
    return {
      showChangePasswordModal: false
    }
  },
  computed: {
    ...mapGetters(['isAuthenticated', 'currentUser', 'hasPermission', 'isAdmin'])
  },
  async created() {
    // 应用启动时初始化认证状态
    if (!this.isAuthenticated) {
      try {
        await this.initAuth()
      } catch (error) {
        // 认证失败，跳转到登录页面
        if (this.$route.path !== '/login' && this.$route.path !== '/register') {
          this.$router.push('/login')
        }
      }
    }
  },
  methods: {
    ...mapActions(['logout', 'initAuth']),

    async handleLogout() {
      try {
        await this.logout()
        this.$router.push('/login')
        this.$toast?.success('已安全退出', '再见！')
      } catch (error) {
        console.error('Logout error:', error)
        // 即使出错也跳转到登录页面
        this.$router.push('/login')
      }
    },

    showProfile() {
      // 跳转到个人设置页面
      this.$router.push('/profile')
    },

    changePassword() {
      // 显示修改密码模态框
      this.showChangePasswordModal = true
    },

    handlePasswordChangeSuccess(message) {
      // 密码修改成功的处理
      this.$toast?.success(message || '密码修改成功', '成功')
      this.showChangePasswordModal = false
    },

    handlePasswordChangeError(message) {
      // 密码修改失败的处理
      this.$toast?.error(message || '密码修改失败', '错误')
    },

    showUserGuide() {
      // 显示使用指南
      alert('使用指南功能开发中...')
    }
  }
}
</script>

<style>
#app {
  font-family: 'Microsoft YaHei', Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

/* ==================== 优化的导航栏样式 ==================== */

/* 主导航栏容器 - 简化渐变 */
.modern-navbar {
  background: #1e40af;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 1030;
  padding: 0.75rem 0;
}

/* 品牌logo样式 - 简化效果 */
.modern-navbar .navbar-brand {
  font-weight: 600;
  font-size: 1.25rem;
  color: #ffffff !important;
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  transition: opacity 0.15s ease;
}

.modern-navbar .navbar-brand i {
  color: #60a5fa;
  margin-right: 0.75rem;
  font-size: 1.5rem;
}

.modern-navbar .navbar-brand:hover {
  opacity: 0.9;
}

/* 导航链接基础样式 - 简化过渡 */
.modern-navbar .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.75rem 0.85rem !important;
  border-radius: 6px;
  margin: 0 0.15rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: background-color 0.15s ease, color 0.15s ease;
  white-space: nowrap;
}

/* 导航链接图标 */
.modern-navbar .nav-link i {
  margin-right: 0.5rem;
  font-size: 1rem;
  width: 16px;
  text-align: center;
}

/* 导航链接悬停状态 - 简化效果 */
.modern-navbar .nav-link:hover {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.1);
}

/* 活跃状态 - 简化样式 */
.modern-navbar .nav-link.active {
  color: #ffffff !important;
  background: #2563eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 下拉菜单触发器 */
.modern-navbar .dropdown-toggle {
  color: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s ease;
}

.modern-navbar .dropdown-toggle:hover {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.15);
}

.modern-navbar .dropdown-toggle::after {
  margin-left: 0.5rem;
  border-top-color: rgba(255, 255, 255, 0.8);
}

/* 徽章样式 */
.modern-navbar .badge {
  font-size: 0.7rem;
  padding: 0.25em 0.5em;
  border-radius: 12px;
  margin-left: 0.5rem;
  font-weight: 600;
}

.modern-navbar .badge.bg-warning {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
  color: #212529 !important;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

/* 简化的下拉菜单样式 */
.modern-navbar .dropdown-menu {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  min-width: 220px;
}

.modern-navbar .dropdown-item {
  color: #495057;
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  display: flex;
  align-items: center;
  transition: background-color 0.15s ease, color 0.15s ease;
  font-size: 0.875rem;
  white-space: nowrap;
}

.modern-navbar .dropdown-item i {
  width: 18px;
  text-align: center;
  margin-right: 0.75rem;
  font-size: 1rem;
  color: #6c757d;
  transition: color 0.15s ease;
}

.modern-navbar .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #1e40af;
}

.modern-navbar .dropdown-item:hover i {
  color: #1e40af;
}

/* 修正下拉菜单内dropdown-toggle的颜色问题 */
.modern-navbar .dropdown-menu .dropdown-toggle {
  color: #495057 !important;  /* 与其他dropdown-item保持一致 */
}

.modern-navbar .dropdown-menu .dropdown-toggle:hover {
  color: #007bff !important;  /* 悬停时的蓝色 */
  background-color: #f8f9fa;
}

.badge {
  font-size: 0.7em;
}

/* 分割线样式优化 */
.modern-navbar .dropdown-divider {
  margin: 0.75rem 0;
  border-top: 1px solid #e9ecef;
  opacity: 0.6;
}

/* 下拉菜单头部样式 */
.modern-navbar .dropdown-header {
  color: #6c757d;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.5rem 1.5rem 0.25rem;
  margin-bottom: 0.5rem;
}

/* 简化的子菜单样式 */
.dropdown-submenu {
  position: relative;
}

.dropdown-submenu .dropdown-toggle {
  position: relative;
  cursor: pointer;
  padding-right: 2.5rem !important;
}

.submenu-arrow {
  position: absolute;
  right: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: #6c757d;
  transition: transform 0.15s ease, color 0.15s ease;
}

.dropdown-submenu:hover .submenu-arrow {
  transform: translateY(-50%) rotate(90deg);
  color: #1e40af;
}

.dropdown-submenu-menu {
  position: absolute;
  top: -0.5rem;
  left: 100%;
  min-width: 240px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateX(-8px);
  transition: opacity 0.15s ease, visibility 0.15s ease, transform 0.15s ease;
  z-index: 1050;
  list-style: none;
  padding: 0.5rem 0;
  margin: 0;
}

.dropdown-submenu:hover .dropdown-submenu-menu {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.submenu-item {
  padding: 0.625rem 1.5rem !important;
  font-size: 0.875rem;
  color: #495057 !important;
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: background-color 0.15s ease, color 0.15s ease;
  border: none;
  background-color: transparent !important;
  white-space: nowrap;
}

.submenu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #1e40af;
  transform: scaleY(0);
  transition: transform 0.15s ease;
}

.submenu-item:hover::before {
  transform: scaleY(1);
}

.submenu-item i {
  width: 16px;
  text-align: center;
  margin-right: 0.75rem;
  font-size: 0.875rem;
  color: #6c757d;
  transition: color 0.15s ease;
}

.submenu-item:hover {
  background-color: #f8f9fa !important;
  color: #1e40af !important;
}

.submenu-item:hover i {
  color: #1e40af !important;
}

/* 特殊菜单项样式 */
.modern-navbar .dropdown-item.text-danger {
  color: #dc3545 !important;
}

.modern-navbar .dropdown-item.text-danger:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545 !important;
}

.modern-navbar .dropdown-item.text-danger i {
  color: #dc3545 !important;
}

/* 用户下拉菜单特殊样式 */
.modern-navbar .dropdown-menu-end {
  min-width: 220px;
}

/* 确保导航栏不换行 */
.modern-navbar .navbar-nav {
  flex-wrap: nowrap;
}

.modern-navbar .navbar-collapse {
  flex-grow: 1;
}

/* 防止菜单项文字换行 */
.modern-navbar .nav-item {
  flex-shrink: 0;
}

/* 品牌名称在小屏幕上的优化 */
.modern-navbar .navbar-brand {
  flex-shrink: 0;
  max-width: none;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .modern-navbar {
    padding: 0.5rem 0;
  }

  .modern-navbar .navbar-brand {
    font-size: 1.1rem;
  }

  .modern-navbar .nav-link {
    padding: 0.5rem 0.6rem !important;
    margin: 0.1rem;
    font-size: 0.85rem;
    white-space: nowrap;
  }

  .navbar-nav .dropdown-menu {
    border: 1px solid #dee2e6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    min-width: 180px;
  }

  .modern-navbar .dropdown-item {
    padding: 0.6rem 1.25rem;
    font-size: 0.9rem;
  }

  /* 移动端子菜单样式调整 */
  .dropdown-submenu-menu {
    position: static !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
    box-shadow: none;
    border: none;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin: 0.5rem 0 0 1.5rem;
    border-left: 3px solid #007bff;
    border-radius: 0 8px 8px 0;
    backdrop-filter: none;
  }

  .dropdown-submenu .dropdown-toggle {
    padding-right: 1.5rem !important;
  }

  .submenu-arrow {
    display: none;
  }

  .submenu-item {
    padding: 0.5rem 1.25rem !important;
    font-size: 0.85rem;
  }

  .submenu-item::before {
    display: none;
  }

  .submenu-item:hover {
    transform: none;
  }
}
</style>
