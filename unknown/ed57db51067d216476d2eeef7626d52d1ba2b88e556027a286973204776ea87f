<template>
  <collapsible-card card-class="border-warning" storage-key="maintenance-record-section">
    <template #header>
      <i class="bi bi-tools me-2"></i>维护记录
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-warning text-dark">{{ maintenanceTime ? '维护时间: ' + maintenanceTime : '未填写维护时间' }}</span>
        <span class="badge bg-warning text-dark">{{ maintenanceStaff ? '维护人员: ' + maintenanceStaff : '未填写维护人员' }}</span>
        <span class="badge bg-warning text-dark">{{ maintenanceType ? '维护类型: ' + maintenanceType : '未填写维护类型' }}</span>
      </div>
    </template>

    <!-- 基本信息分组 -->
    <div class="maintenance-section mb-4">
      <div class="section-header mb-3">
        <h6 class="section-title">
          <i class="bi bi-calendar-event me-2 text-primary"></i>
          基本信息
        </h6>
      </div>
      <div class="row g-3">
        <div class="col-md-6">
          <div class="form-floating">
            <input
              type="datetime-local"
              class="form-control enhanced-input"
              id="maintenanceTime"
              v-model="maintenanceTime"
              @input="updateMaintenanceTime"
            >
            <label for="maintenanceTime">
              <i class="bi bi-clock me-1"></i>维护时间
            </label>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating">
            <input
              type="text"
              class="form-control enhanced-input"
              id="maintenanceStaff"
              v-model="maintenanceStaff"
              placeholder="请输入维护人员姓名"
              @input="updateMaintenanceStaff"
            >
            <label for="maintenanceStaff">
              <i class="bi bi-person me-1"></i>维护人员
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 维护类型分组 -->
    <div class="maintenance-section mb-4">
      <div class="section-header mb-3">
        <h6 class="section-title">
          <i class="bi bi-gear me-2 text-success"></i>
          维护类型
        </h6>
      </div>
      <div class="row g-3">
        <div class="col-md-6">
          <div class="form-floating">
            <select
              class="form-select enhanced-input"
              id="maintenanceType"
              v-model="maintenanceType"
              @change="updateMaintenanceType"
            >
              <option value="">-- 请选择维护类型 --</option>
              <option value="平台部署">🚀 平台部署</option>
              <option value="平台升级">⬆️ 平台升级</option>
              <option value="配置变更">⚙️ 配置变更</option>
              <option value="常规维护">🔧 常规维护</option>
              <option value="漏洞修复">🛡️ 漏洞修复</option>
              <option value="其他">📝 其他</option>
            </select>
            <label for="maintenanceType">
              <i class="bi bi-list-ul me-1"></i>维护类型
            </label>
          </div>
          <!-- 自定义维护类型输入框 -->
          <div v-if="maintenanceType === '其他'" class="mt-3">
            <div class="form-floating">
              <input
                type="text"
                class="form-control enhanced-input custom-type-input"
                id="customMaintenanceType"
                v-model="customMaintenanceType"
                placeholder="请详细描述自定义维护类型"
                @input="updateCustomMaintenanceType"
              >
              <label for="customMaintenanceType">
                <i class="bi bi-pencil me-1"></i>自定义维护类型
              </label>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating">
            <input
              type="text"
              class="form-control enhanced-input"
              id="onesTaskLink"
              v-model="onesTaskLink"
              placeholder="请输入ONES任务链接或任务编号"
              @input="updateOnesTaskLink"
            >
            <label for="onesTaskLink">
              <i class="bi bi-link-45deg me-1"></i>相关ONES任务链接
            </label>
          </div>
          <div class="form-text mt-1">
            <i class="bi bi-info-circle me-1"></i>
            可以输入完整链接地址或任务编号
          </div>
        </div>
      </div>
    </div>

    <!-- 维护内容分组 -->
    <div class="maintenance-section">
      <div class="section-header mb-3">
        <h6 class="section-title">
          <i class="bi bi-file-text me-2 text-info"></i>
          维护内容
        </h6>
      </div>
      <div class="form-floating">
        <textarea
          class="form-control enhanced-textarea"
          id="maintenanceContent"
          v-model="maintenanceContent"
          style="height: 140px; resize: vertical;"
          placeholder="请详细描述本次维护的具体内容、操作步骤、影响范围等..."
          @input="updateMaintenanceContent"
        ></textarea>
        <label for="maintenanceContent">
          <i class="bi bi-journal-text me-1"></i>维护详细内容
        </label>
      </div>
      <div class="form-text mt-2">
        <i class="bi bi-lightbulb me-1"></i>
        建议包含：操作步骤、影响范围、注意事项、回滚方案等信息
      </div>
    </div>
  </collapsible-card>
</template>

<script>
/**
 * 维护记录组件
 * 用于记录维护相关信息
 */
import CollapsibleCard from './CollapsibleCard.vue'

export default {
  name: 'MaintenanceRecordSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 维护时间
    time: {
      type: String,
      default: ''
    },
    // 维护人员
    staff: {
      type: String,
      default: ''
    },
    // 维护类型
    type: {
      type: String,
      default: ''
    },
    // 相关ONES任务链接
    onesLink: {
      type: String,
      default: ''
    },
    // 维护内容
    content: {
      type: String,
      default: ''
    }
  },
  data() {
    // 预定义的维护类型选项
    const predefinedTypes = ['平台部署', '平台升级', '配置变更', '常规维护', '漏洞修复']

    // 处理初始维护类型值
    let initialMaintenanceType = this.type
    let initialCustomMaintenanceType = ''

    if (this.type && !predefinedTypes.includes(this.type) && this.type !== '其他' && this.type !== '') {
      // 如果传入的是自定义类型，设置为"其他"并填入自定义输入框
      initialMaintenanceType = '其他'
      initialCustomMaintenanceType = this.type
    }

    return {
      maintenanceTime: this.time || new Date().toISOString().slice(0, 16), // 默认为当前时间
      maintenanceStaff: this.staff,
      maintenanceType: initialMaintenanceType,
      customMaintenanceType: initialCustomMaintenanceType, // 自定义维护类型
      onesTaskLink: this.onesLink,
      maintenanceContent: this.content
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    time(newVal) {
      this.maintenanceTime = newVal
    },
    staff(newVal) {
      this.maintenanceStaff = newVal
    },
    type(newVal) {
      // 预定义的维护类型选项
      const predefinedTypes = ['平台部署', '平台升级', '配置变更', '常规维护', '漏洞修复']

      if (predefinedTypes.includes(newVal) || newVal === '' || newVal === '其他') {
        // 如果是预定义类型、空值或"其他"，直接设置
        this.maintenanceType = newVal
        if (newVal !== '其他') {
          this.customMaintenanceType = ''
        }
      } else if (newVal) {
        // 如果是自定义类型，设置为"其他"并填入自定义输入框
        this.maintenanceType = '其他'
        this.customMaintenanceType = newVal
      }
    },
    onesLink(newVal) {
      this.onesTaskLink = newVal
    },
    content(newVal) {
      this.maintenanceContent = newVal
    }
  },
  methods: {
    /**
     * 更新维护时间
     * 向父组件发送update:time事件
     */
    updateMaintenanceTime() {
      this.$emit('update:time', this.maintenanceTime)
    },

    /**
     * 更新维护人员
     * 向父组件发送update:staff事件
     */
    updateMaintenanceStaff() {
      this.$emit('update:staff', this.maintenanceStaff)
    },

    /**
     * 更新维护类型
     * 向父组件发送update:type事件
     */
    updateMaintenanceType() {
      if (this.maintenanceType !== '其他') {
        // 如果选择的不是"其他"，清空自定义维护类型并直接发送选择的值
        this.customMaintenanceType = ''
        this.$emit('update:type', this.maintenanceType)
      } else {
        // 如果选择的是"其他"，发送自定义维护类型的值（可能为空）
        this.$emit('update:type', this.customMaintenanceType)
      }
    },

    /**
     * 更新自定义维护类型
     * 当选择"其他"时，发送自定义维护类型值
     */
    updateCustomMaintenanceType() {
      if (this.maintenanceType === '其他') {
        this.$emit('update:type', this.customMaintenanceType)
      }
    },

    /**
     * 更新相关ONES任务链接
     * 向父组件发送update:onesLink事件
     */
    updateOnesTaskLink() {
      this.$emit('update:onesLink', this.onesTaskLink)
    },

    /**
     * 更新维护内容
     * 向父组件发送update:content事件
     */
    updateMaintenanceContent() {
      this.$emit('update:content', this.maintenanceContent)
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

/* 维护记录分组样式 */
.maintenance-section {
  position: relative;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.maintenance-section:hover {
  border-color: #dee2e6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-header {
  position: relative;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

.section-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.section-title i {
  font-size: 1.1rem;
}

/* 增强的输入框样式 */
.enhanced-input {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.enhanced-input:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
  background-color: #ffffff;
}

.enhanced-input:hover:not(:focus) {
  border-color: #ced4da;
}

/* 增强的文本域样式 */
.enhanced-textarea {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #ffffff;
  font-family: inherit;
  line-height: 1.5;
}

.enhanced-textarea:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
  background-color: #ffffff;
}

.enhanced-textarea:hover:not(:focus) {
  border-color: #ced4da;
}

/* 自定义维护类型输入框特殊样式 */
.custom-type-input {
  border-color: #ffc107;
  background-color: #fffbf0;
}

.custom-type-input:focus {
  border-color: #ffb300;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.15);
  background-color: #fffbf0;
}

/* 标签样式增强 */
.form-floating > label {
  color: #6c757d;
  font-weight: 500;
}

.form-floating > .enhanced-input:focus ~ label,
.form-floating > .enhanced-input:not(:placeholder-shown) ~ label,
.form-floating > .enhanced-textarea:focus ~ label,
.form-floating > .enhanced-textarea:not(:placeholder-shown) ~ label {
  color: #0d6efd;
  font-weight: 600;
}

/* 表单提示文字样式 */
.form-text {
  font-size: 0.875rem;
  color: #6c757d;
  display: flex;
  align-items: center;
}

.form-text i {
  color: #0d6efd;
}

/* 选择框选项样式 */
.form-select option {
  padding: 0.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .maintenance-section {
    padding: 1rem;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .enhanced-textarea {
    height: 120px !important;
  }
}

/* 动画效果 */
.custom-type-input {
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 聚焦状态的分组高亮 */
.maintenance-section:focus-within {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.1);
}
</style>
