<template>
  <div class="generic-form">
    <!-- 基本信息部分 -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="bi bi-info-circle me-2"></i>
          基本信息
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="form-floating mb-3">
              <input 
                type="text" 
                class="form-control" 
                id="companyName"
                v-model="formData.公司名称"
                placeholder="公司名称"
                required
              >
              <label for="companyName">公司名称 *</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating mb-3">
              <input 
                type="text" 
                class="form-control" 
                id="customerIdentifier"
                v-model="formData.客户标识"
                placeholder="客户标识"
              >
              <label for="customerIdentifier">客户标识</label>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <div class="form-floating mb-3">
              <input 
                type="date" 
                class="form-control" 
                id="recordDate"
                v-model="formData.记录日期"
                required
              >
              <label for="recordDate">记录日期 *</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating mb-3">
              <input 
                type="text" 
                class="form-control" 
                id="version"
                v-model="formData.版本信息"
                placeholder="版本信息"
              >
              <label for="version">版本信息</label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 访问信息部分 -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="bi bi-key me-2"></i>
          访问信息
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="form-floating mb-3">
              <input 
                type="text" 
                class="form-control" 
                id="adminIP"
                v-model="formData.管理员页面IP"
                placeholder="管理员页面IP"
              >
              <label for="adminIP">管理员页面IP</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating mb-3">
              <input 
                type="text" 
                class="form-control" 
                id="userIP"
                v-model="formData.用户页面IP"
                placeholder="用户页面IP"
              >
              <label for="userIP">用户页面IP</label>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <div class="form-floating mb-3">
              <input 
                type="text" 
                class="form-control" 
                id="adminAccount"
                v-model="formData.管理员账号"
                placeholder="管理员账号"
              >
              <label for="adminAccount">管理员账号</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating mb-3">
              <input 
                type="password" 
                class="form-control" 
                id="adminPassword"
                v-model="formData.管理员密码"
                placeholder="管理员密码"
              >
              <label for="adminPassword">管理员密码</label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务器信息部分 -->
    <server-info-section
      v-model="formData.服务器信息"
      :component-groups="componentGroups"
      :document-type="formData.文档后缀"
    />

    <!-- 维护记录部分 -->
    <maintenance-records-section
      v-model="formData.维护记录"
    />
  </div>
</template>

<script>
import ServerInfoSection from '@/components/forms/common/ServerInfoSection.vue'
import MaintenanceRecordsSection from '@/components/forms/common/MultiMaintenanceRecordSection.vue'

export default {
  name: 'GenericForm',
  components: {
    ServerInfoSection,
    MaintenanceRecordsSection
  },
  props: {
    modelValue: {
      type: Object,
      required: true
    },
    componentGroups: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'save-form', 'load-form', 'refresh-components'],
  data() {
    return {
      // 移除了availableFormTypes，因为FormHeader已经在父组件中处理
    }
  },
  computed: {
    formData: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  mounted() {
    // 确保表单数据有默认结构
    this.ensureFormStructure()
  },
  methods: {
    /**
     * 确保表单数据有正确的结构
     */
    ensureFormStructure() {
      const defaultData = {
        公司名称: '',
        客户标识: '',
        记录日期: this.formatDate(new Date()),
        版本信息: '',
        管理员页面IP: '',
        用户页面IP: '',
        管理员账号: 'admin',
        管理员密码: '',
        服务器信息: [],
        维护记录: []
      }

      // 在Vue 3中，直接赋值即可实现响应式更新
      Object.keys(defaultData).forEach(key => {
        if (!(key in this.formData)) {
          this.formData[key] = defaultData[key]
        }
      })
    },



    /**
     * 格式化日期
     */
    formatDate(date) {
      return date.toISOString().split('T')[0]
    }
  }
}
</script>

<style scoped>
.generic-form {
  max-width: 1200px;
  margin: 0 auto;
}

.card {
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.card-title {
  color: #495057;
  font-weight: 600;
}

.form-floating {
  position: relative;
}

.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
