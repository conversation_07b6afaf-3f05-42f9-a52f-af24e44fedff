<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-eye me-2"></i>
            查看角色权限 - {{ role?.name }}
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <div v-if="role">
            <!-- 角色基本信息 -->
            <div class="row mb-4">
              <div class="col-md-6">
                <h6 class="text-muted mb-3">角色信息</h6>
                <table class="table table-sm">
                  <tbody>
                    <tr>
                      <td class="fw-bold">角色名称:</td>
                      <td>{{ role.name }}</td>
                    </tr>
                    <tr>
                      <td class="fw-bold">角色代码:</td>
                      <td><code>{{ role.code }}</code></td>
                    </tr>
                    <tr>
                      <td class="fw-bold">描述:</td>
                      <td>{{ role.description || '-' }}</td>
                    </tr>
                    <tr>
                      <td class="fw-bold">类型:</td>
                      <td>
                        <span :class="role.is_system ? 'badge bg-warning' : 'badge bg-primary'">
                          {{ role.is_system ? '系统角色' : '自定义角色' }}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td class="fw-bold">状态:</td>
                      <td>
                        <span :class="role.is_active ? 'badge bg-success' : 'badge bg-danger'">
                          {{ role.is_active ? '启用' : '禁用' }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="col-md-6">
                <h6 class="text-muted mb-3">统计信息</h6>
                <table class="table table-sm">
                  <tbody>
                    <tr>
                      <td class="fw-bold">权限数量:</td>
                      <td>
                        <span class="badge bg-info">{{ role.permissions?.length || 0 }}</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="fw-bold">用户数量:</td>
                      <td>
                        <span class="badge bg-secondary">{{ role.user_count || 0 }}</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="fw-bold">创建时间:</td>
                      <td>{{ formatDate(role.created_at) }}</td>
                    </tr>
                    <tr>
                      <td class="fw-bold">更新时间:</td>
                      <td>{{ formatDate(role.updated_at) }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 权限列表 -->
            <div>
              <h6 class="text-muted mb-3">
                权限列表
                <span class="badge bg-info ms-2">{{ role.permissions?.length || 0 }}</span>
              </h6>

              <!-- 系统管理员特殊提示 -->
              <div v-if="role.code === 'admin' && (!role.permissions || role.permissions.length === 0)" class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                <strong>系统管理员角色说明：</strong>
                <p class="mb-2 mt-2">系统管理员角色采用特殊权限机制，不依赖具体权限配置：</p>
                <ul class="mb-2">
                  <li>拥有系统所有权限，无需单独配置</li>
                  <li>权限通过用户的 <code>is_admin</code> 字段控制</li>
                  <li>确保系统管理功能始终可用</li>
                  <li>避免因权限配置错误导致系统无法管理</li>
                </ul>
                <div class="text-muted small">
                  💡 这是企业级RBAC系统的标准设计，确保系统安全性和可维护性。
                </div>
              </div>

              <div v-else-if="role.permissions && role.permissions.length > 0">
                <!-- 按模块分组显示权限 -->
                <div v-for="(permissions, module) in groupedPermissions" :key="module" class="mb-3">
                  <h6 class="text-primary mb-2">
                    <i class="bi bi-folder me-1"></i>
                    {{ module }} 模块
                    <span class="badge bg-primary ms-2">{{ permissions.length }}</span>
                  </h6>
                  <div class="row">
                    <div v-for="permission in permissions" :key="permission.id" class="col-md-6 mb-2">
                      <div class="card card-body py-2 px-3">
                        <div class="d-flex justify-content-between align-items-center">
                          <div>
                            <div class="fw-bold">{{ permission.name }}</div>
                            <small class="text-muted">
                              <code>{{ permission.code }}</code>
                            </small>
                            <div v-if="permission.description" class="small text-muted mt-1">
                              {{ permission.description }}
                            </div>
                          </div>
                          <div>
                            <span :class="permission.is_active ? 'badge bg-success' : 'badge bg-danger'">
                              {{ permission.is_active ? '启用' : '禁用' }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div v-else class="text-center py-4">
                <i class="bi bi-inbox display-4 text-muted"></i>
                <p class="text-muted mt-2">该角色暂无权限</p>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PermissionViewModal',
  props: {
    role: {
      type: Object,
      default: null
    }
  },
  emits: ['close'],
  computed: {
    groupedPermissions() {
      if (!this.role?.permissions) return {}
      
      const grouped = {}
      this.role.permissions.forEach(permission => {
        const module = permission.module || '其他'
        if (!grouped[module]) {
          grouped[module] = []
        }
        grouped[module].push(permission)
      })
      
      // 按模块名排序
      const sortedGrouped = {}
      Object.keys(grouped).sort().forEach(key => {
        sortedGrouped[key] = grouped[key].sort((a, b) => a.name.localeCompare(b.name))
      })
      
      return sortedGrouped
    }
  },
  methods: {
    formatDate(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.modal {
  z-index: 1060;
}

.card {
  border: 1px solid #dee2e6;
  transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.badge {
  font-size: 0.75em;
}

code {
  background-color: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.table td {
  border-top: none;
  padding: 0.5rem 0.75rem;
}

.table .fw-bold {
  width: 30%;
}
</style>
