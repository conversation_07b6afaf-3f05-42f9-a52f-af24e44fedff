<template>
  <div class="permission-management">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-key me-2"></i>权限管理
              </h5>
              <small class="text-muted">权限点由系统定义，不支持手动创建或删除</small>
            </div>
            <div class="card-body">
              <!-- 搜索和筛选 -->
              <div class="row mb-3">
                <div class="col-md-4">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="bi bi-search"></i>
                    </span>
                    <input 
                      type="text" 
                      class="form-control" 
                      placeholder="搜索权限名称或代码"
                      v-model="searchQuery"
                      @input="searchPermissions"
                    >
                  </div>
                </div>
                <div class="col-md-3">
                  <select class="form-select" v-model="moduleFilter" @change="filterPermissions">
                    <option value="">全部模块</option>
                    <option v-for="module in modules" :key="module" :value="module">
                      {{ module }}
                    </option>
                  </select>
                </div>
                <div class="col-md-3">
                  <select class="form-select" v-model="statusFilter" @change="filterPermissions">
                    <option value="">全部状态</option>
                    <option value="active">启用</option>
                    <option value="inactive">禁用</option>
                  </select>
                </div>
              </div>

              <!-- 权限列表 -->
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>权限名称</th>
                      <th>权限代码</th>
                      <th>所属模块</th>
                      <th>描述</th>
                      <th>关联角色</th>
                      <th>状态</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="permission in filteredPermissions" :key="permission.id">
                      <td>
                        <div class="d-flex align-items-center">
                          <i class="bi bi-key me-2 text-primary"></i>
                          {{ permission.name }}
                        </div>
                      </td>
                      <td>
                        <code>{{ permission.code }}</code>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ permission.module }}</span>
                      </td>
                      <td>{{ permission.description || '-' }}</td>
                      <td>
                        <span 
                          v-for="role in permission.roles" 
                          :key="role.id" 
                          class="badge bg-secondary me-1"
                        >
                          {{ role.name }}
                        </span>
                        <span v-if="!permission.roles?.length" class="text-muted">-</span>
                      </td>
                      <td>
                        <span 
                          :class="permission.is_active ? 'badge bg-success' : 'badge bg-danger'"
                        >
                          {{ permission.is_active ? '启用' : '禁用' }}
                        </span>
                      </td>
                      <td>
                        <div class="d-flex align-items-center">
                          <button
                            class="btn btn-outline-warning btn-sm"
                            @click="togglePermissionStatus(permission)"
                            :title="permission.is_active ? '禁用权限' : '启用权限'"
                          >
                            <i :class="permission.is_active ? 'bi bi-lock' : 'bi bi-unlock'"></i>
                          </button>
                          <span class="text-muted small ms-2">系统权限</span>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'PermissionManagement',
  data() {
    return {
      permissions: [],
      filteredPermissions: [],
      modules: [],
      searchQuery: '',
      moduleFilter: '',
      statusFilter: '',
      loading: false
    }
  },
  computed: {
    ...mapState(['currentUser'])
  },
  async mounted() {
    await this.loadData()
  },
  methods: {
    ...mapActions(['showToast']),
    
    async loadData() {
      this.loading = true
      try {
        await this.loadPermissions()
      } catch (error) {
        console.error('加载数据失败:', error)
        this.showToast('加载数据失败', 'error')
      } finally {
        this.loading = false
      }
    },

    async loadPermissions() {
      try {
        const response = await fetch('/api/rbac/permissions', {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.data && result.data.permissions) {
            this.permissions = result.data.permissions
          } else if (result.data) {
            this.permissions = result.data
          } else {
            this.permissions = result
          }
          this.filterPermissions()
          this.modules = [...new Set(this.permissions.map(p => p.module))]

        } else {
          console.error('加载权限列表失败:', response.status)
          this.$store.dispatch('showToast', { message: '加载权限列表失败', type: 'error' })
        }
      } catch (error) {
        console.error('加载权限列表异常:', error)
        this.$store.dispatch('showToast', { message: '加载权限列表失败', type: 'error' })
      }
    },

    searchPermissions() {
      this.filterPermissions()
    },

    filterPermissions() {
      let filtered = [...this.permissions]
      
      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(permission => 
          permission.name.toLowerCase().includes(query) ||
          permission.code.toLowerCase().includes(query) ||
          (permission.description && permission.description.toLowerCase().includes(query))
        )
      }
      
      // 模块过滤
      if (this.moduleFilter) {
        filtered = filtered.filter(permission => permission.module === this.moduleFilter)
      }
      
      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(permission => 
          this.statusFilter === 'active' ? permission.is_active : !permission.is_active
        )
      }
      
      this.filteredPermissions = filtered
    },



    async togglePermissionStatus(permission) {
      try {
        const newStatus = !permission.is_active
        const statusText = newStatus ? '启用' : '禁用'

        const response = await fetch(`/api/rbac/permissions/${permission.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.$store.state.token}`
          },
          body: JSON.stringify({
            is_active: newStatus
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            // 更新本地数据
            const permissionIndex = this.permissions.findIndex(p => p.id === permission.id)
            if (permissionIndex !== -1) {
              this.permissions[permissionIndex].is_active = newStatus
              this.filterPermissions() // 重新过滤以更新显示
            }
            this.$store.dispatch('showToast', { message: `权限已${statusText}`, type: 'success' })
          } else {
            this.$store.dispatch('showToast', { message: result.message || '操作失败', type: 'error' })
          }
        } else {
          const errorResult = await response.json()
          this.$store.dispatch('showToast', { message: errorResult.message || '操作失败', type: 'error' })
        }
      } catch (error) {
        console.error('切换权限状态失败:', error)
        this.$store.dispatch('showToast', { message: '操作失败', type: 'error' })
      }
    }
  }
}
</script>

<style scoped>
.permission-management {
  padding: 20px 0;
}

.table th {
  border-top: none;
  font-weight: 600;
}

.badge {
  font-size: 0.75em;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
}

code {
  background-color: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}
</style>
