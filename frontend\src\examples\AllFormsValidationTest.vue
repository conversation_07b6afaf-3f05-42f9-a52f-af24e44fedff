<template>
  <div class="all-forms-validation-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-check2-all me-2"></i>
                全表单类型验证测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 操作面板 -->
              <div class="row mb-4">
                <div class="col-md-8">
                  <button class="btn btn-primary me-2" @click="testAllForms">
                    测试所有表单
                  </button>
                  <button class="btn btn-success me-2" @click="fixAllMappings">
                    修复所有映射
                  </button>
                  <button class="btn btn-info me-2" @click="exportReports">
                    导出报告
                  </button>
                  <button class="btn btn-warning" @click="clearAll">
                    清空数据
                  </button>
                </div>
                <div class="col-md-4">
                  <div class="alert alert-info mb-0 py-2">
                    <strong>总体状态：</strong>
                    <span :class="overallStatus.class">
                      {{ overallStatus.text }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 表单类型选择 -->
              <div class="row mb-4">
                <div class="col-12">
                  <h6>选择要测试的表单类型</h6>
                  <div class="form-check-group">
                    <div v-for="formType in formTypes" :key="formType" class="form-check form-check-inline">
                      <input 
                        class="form-check-input" 
                        type="checkbox" 
                        :id="`form-${formType}`"
                        v-model="selectedFormTypes"
                        :value="formType"
                      >
                      <label class="form-check-label" :for="`form-${formType}`">
                        {{ formType }}
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 测试结果 -->
              <div v-if="testResults.length > 0" class="row">
                <div class="col-12">
                  <h6>测试结果</h6>
                  <div class="accordion" id="testResultsAccordion">
                    <div 
                      v-for="(result, index) in testResults" 
                      :key="result.formType" 
                      class="accordion-item"
                    >
                      <h2 class="accordion-header" :id="`heading${index}`">
                        <button 
                          class="accordion-button" 
                          :class="{ collapsed: index !== 0 }"
                          type="button" 
                          data-bs-toggle="collapse" 
                          :data-bs-target="`#collapse${index}`"
                          :aria-expanded="index === 0"
                          :aria-controls="`collapse${index}`"
                        >
                          <div class="d-flex justify-content-between align-items-center w-100 me-3">
                            <span>
                              <i :class="result.validation.isValid ? 'bi bi-check-circle text-success' : 'bi bi-x-circle text-danger'"></i>
                              {{ result.formType }}
                            </span>
                            <div class="d-flex gap-2">
                              <span class="badge bg-primary">
                                完成率: {{ result.validation.completionRate }}%
                              </span>
                              <span v-if="result.validation.missingFields.length > 0" class="badge bg-danger">
                                缺少: {{ result.validation.missingFields.length }}
                              </span>
                              <span v-if="result.validation.warnings.length > 0" class="badge bg-warning">
                                警告: {{ result.validation.warnings.length }}
                              </span>
                            </div>
                          </div>
                        </button>
                      </h2>
                      <div 
                        :id="`collapse${index}`" 
                        class="accordion-collapse collapse"
                        :class="{ show: index === 0 }"
                        :aria-labelledby="`heading${index}`"
                        data-bs-parent="#testResultsAccordion"
                      >
                        <div class="accordion-body">
                          <div class="row">
                            <div class="col-md-6">
                              <h6>验证详情</h6>
                              <div class="mb-3">
                                <strong>必填字段 ({{ result.validation.totalRequiredFields }}):</strong>
                                <ul class="list-unstyled mt-2">
                                  <li v-for="field in result.validation.fieldDetails" :key="field.fieldName" class="mb-1">
                                    <i :class="field.isFilled ? 'bi bi-check-circle text-success' : 'bi bi-x-circle text-danger'"></i>
                                    <strong>{{ field.fieldName }}:</strong>
                                    <span class="ms-2">{{ field.value || '(未填写)' }}</span>
                                  </li>
                                </ul>
                              </div>
                              
                              <div v-if="result.validation.warnings.length > 0" class="mb-3">
                                <strong class="text-warning">警告:</strong>
                                <ul class="list-unstyled mt-2">
                                  <li v-for="warning in result.validation.warnings" :key="warning" class="text-warning">
                                    <i class="bi bi-exclamation-triangle"></i> {{ warning }}
                                  </li>
                                </ul>
                              </div>
                            </div>
                            
                            <div class="col-md-6">
                              <h6>后端验证模拟</h6>
                              <div class="alert" :class="result.backendValidation.success ? 'alert-success' : 'alert-danger'">
                                <strong>{{ result.backendValidation.success ? '✅ 通过' : '❌ 失败' }}:</strong>
                                {{ result.backendValidation.message }}
                              </div>
                              
                              <div v-if="!result.backendValidation.success" class="mb-3">
                                <strong>后端要求的字段:</strong>
                                <ul class="list-unstyled mt-2">
                                  <li v-for="field in result.backendValidation.requiredFields" :key="field">
                                    <i :class="result.formData[field] ? 'bi bi-check-circle text-success' : 'bi bi-x-circle text-danger'"></i>
                                    {{ field }}: {{ result.formData[field] || '(空)' }}
                                  </li>
                                </ul>
                              </div>
                              
                              <div class="mt-3">
                                <button 
                                  class="btn btn-sm btn-outline-primary me-2" 
                                  @click="fixFormMapping(result.formType)"
                                >
                                  修复映射
                                </button>
                                <button 
                                  class="btn btn-sm btn-outline-info" 
                                  @click="exportFormReport(result)"
                                >
                                  导出报告
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 修复建议 -->
              <div v-if="fixSuggestions.length > 0" class="row mt-4">
                <div class="col-12">
                  <div class="alert alert-warning">
                    <h6 class="alert-heading">修复建议</h6>
                    <ul class="mb-0">
                      <li v-for="suggestion in fixSuggestions" :key="suggestion">
                        {{ suggestion }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getInitialFormData } from '@/config/formDataConfig'
import { 
  validateFormData, 
  fixFormDataMapping, 
  generateValidationReport, 
  simulateBackendValidation,
  exportValidationReport,
  logValidationReport
} from '@/utils/formValidationFix'

export default {
  name: 'AllFormsValidationTest',
  data() {
    return {
      formTypes: ['安全测评', '安全监测', '应用加固'],
      selectedFormTypes: ['安全测评', '安全监测', '应用加固'],
      testResults: [],
      fixSuggestions: []
    }
  },
  computed: {
    overallStatus() {
      if (this.testResults.length === 0) {
        return { text: '未测试', class: 'text-muted' }
      }
      
      const validCount = this.testResults.filter(r => r.validation.isValid).length
      const totalCount = this.testResults.length
      
      if (validCount === totalCount) {
        return { text: '全部通过', class: 'text-success' }
      } else if (validCount === 0) {
        return { text: '全部失败', class: 'text-danger' }
      } else {
        return { text: `部分通过 (${validCount}/${totalCount})`, class: 'text-warning' }
      }
    }
  },
  methods: {
    /**
     * 测试所有选中的表单类型
     */
    async testAllForms() {
      this.testResults = []
      this.fixSuggestions = []
      
      console.group('🧪 开始测试所有表单类型')
      
      for (const formType of this.selectedFormTypes) {
        console.log(`\n📋 测试表单类型: ${formType}`)
        
        // 获取初始数据并填充测试值
        const formData = this.createTestData(formType)
        
        // 前端验证
        const validation = generateValidationReport(formData, formType)
        
        // 后端验证模拟
        const backendValidation = simulateBackendValidation(formData)
        
        // 记录结果
        const result = {
          formType,
          formData,
          validation,
          backendValidation,
          timestamp: new Date().toISOString()
        }
        
        this.testResults.push(result)
        
        // 打印详细报告
        logValidationReport(validation)
        console.log('后端验证结果:', backendValidation)
        
        // 生成修复建议
        if (!validation.isValid || !backendValidation.success) {
          this.generateFixSuggestions(result)
        }
      }
      
      console.groupEnd()
      
      // 显示总体结果
      this.showOverallResults()
    },

    /**
     * 创建测试数据
     */
    createTestData(formType) {
      const formData = getInitialFormData(formType)
      
      // 填充基本测试数据
      formData.公司名称 = `${formType}测试公司`
      formData.记录日期 = new Date().toISOString().split('T')[0]
      
      // 根据表单类型填充特定字段
      if (formType === '安全测评') {
        formData.部署包版本 = 'V5.1.2sp2'
        formData.管理员页面IP = '*************:8080'
        formData.用户页面IP = '*************:8081'
        formData.客户标识 = 'TEST001'
      } else if (formType === '安全监测') {
        formData.前端版本 = 'V5.1.2sp2'
        formData.后端版本 = 'V5.1.2sp2'
        formData.日活 = '10000'
        formData.标准或定制 = '标准版'
      } else if (formType === '应用加固') {
        formData.客户 = '测试客户公司'
        formData.部署的平台版本 = 'V5.1.2sp2'
      }
      
      return formData
    },

    /**
     * 修复所有表单的映射
     */
    fixAllMappings() {
      console.group('🔧 修复所有表单映射')
      
      this.testResults.forEach(result => {
        const fixedData = fixFormDataMapping(result.formData)
        result.formData = fixedData
        
        // 重新验证
        result.validation = generateValidationReport(fixedData, result.formType)
        result.backendValidation = simulateBackendValidation(fixedData)
        
        console.log(`✅ ${result.formType} 映射已修复`)
      })
      
      console.groupEnd()
      this.showOverallResults()
    },

    /**
     * 修复单个表单映射
     */
    fixFormMapping(formType) {
      const result = this.testResults.find(r => r.formType === formType)
      if (result) {
        const fixedData = fixFormDataMapping(result.formData)
        result.formData = fixedData
        result.validation = generateValidationReport(fixedData, formType)
        result.backendValidation = simulateBackendValidation(fixedData)
        
        console.log(`✅ ${formType} 映射已修复`)
      }
    },

    /**
     * 生成修复建议
     */
    generateFixSuggestions(result) {
      const { formType, validation, backendValidation } = result
      
      if (validation.missingFields.length > 0) {
        this.fixSuggestions.push(
          `${formType}: 需要填写字段 ${validation.missingFields.join(', ')}`
        )
      }
      
      if (!backendValidation.success) {
        this.fixSuggestions.push(
          `${formType}: 后端验证失败，检查字段映射是否正确`
        )
      }
      
      if (validation.warnings.length > 0) {
        this.fixSuggestions.push(
          `${formType}: 存在映射警告，建议检查字段一致性`
        )
      }
    },

    /**
     * 显示总体结果
     */
    showOverallResults() {
      const validCount = this.testResults.filter(r => r.validation.isValid && r.backendValidation.success).length
      const totalCount = this.testResults.length
      
      console.log(`\n📊 总体测试结果: ${validCount}/${totalCount} 通过`)
      
      if (validCount === totalCount) {
        console.log('🎉 所有表单验证通过！')
      } else {
        console.warn('⚠️ 部分表单验证失败，请查看详细报告')
      }
    },

    /**
     * 导出单个表单报告
     */
    exportFormReport(result) {
      const reportData = {
        ...result,
        exportTime: new Date().toISOString()
      }
      
      const dataStr = exportValidationReport(reportData)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${result.formType}_验证报告_${new Date().toISOString().split('T')[0]}.json`
      link.click()
      URL.revokeObjectURL(url)
    },

    /**
     * 导出所有报告
     */
    exportReports() {
      const allReports = {
        testTime: new Date().toISOString(),
        overallStatus: this.overallStatus,
        results: this.testResults,
        suggestions: this.fixSuggestions
      }
      
      const dataStr = JSON.stringify(allReports, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `全表单验证报告_${new Date().toISOString().split('T')[0]}.json`
      link.click()
      URL.revokeObjectURL(url)
    },

    /**
     * 清空所有数据
     */
    clearAll() {
      this.testResults = []
      this.fixSuggestions = []
      console.log('🧹 已清空所有测试数据')
    }
  },
  mounted() {
    console.log('🚀 全表单验证测试工具已加载')
  }
}
</script>

<style scoped>
.all-forms-validation-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
}

.form-check-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.accordion-button:not(.collapsed) {
  background-color: #f8f9fa;
}

.text-success { color: #198754 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-muted { color: #6c757d !important; }

.badge {
  font-size: 0.75rem;
}
</style>
