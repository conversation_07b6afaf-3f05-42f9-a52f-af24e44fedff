<template>
  <div class="server-info-wrapper" :data-server-index="index">
    <div class="server-info-card">
      <!-- 服务器标题和基本信息（始终显示） -->
      <div class="server-header">
        <div class="server-title-section">
          <div class="server-number">{{ index + 1 }}</div>
          <h5 class="server-title">
            <i class="bi bi-hdd-rack me-2"></i>服务器 #{{ index + 1 }}
          </h5>
          <!-- 折叠状态下显示用途和IP -->
          <div v-if="serverInfo.折叠" class="server-summary">
            <span class="server-badge server-badge-purpose">{{ serverInfo.用途类型 === '自定义' ? serverInfo.用途 : serverInfo.用途类型 }}</span>
            <span class="server-badge server-badge-ip">内网: {{ serverInfo.IP地址 || '未设置' }}</span>
            <span v-if="serverInfo.外网IP地址" class="server-badge server-badge-external">外网: {{ serverInfo.外网IP地址 }}</span>
          </div>
        </div>
        <div class="server-actions">
          <!-- 折叠/展开按钮 -->
          <button type="button" class="btn btn-sm btn-server-action" @click="toggleCollapse">
            <i class="bi" :class="serverInfo.折叠 ? 'bi-chevron-down' : 'bi-chevron-up'"></i>
            {{ serverInfo.折叠 ? '展开' : '折叠' }}
          </button>
          <!-- 删除按钮 -->
          <button type="button" class="btn btn-sm btn-server-danger" @click="$emit('remove')">
            <i class="bi bi-trash me-1"></i>删除
          </button>
        </div>
      </div>

      <!-- 可折叠的详细信息 -->
      <div v-if="!serverInfo.折叠" class="server-content">
        <!-- 基本配置部分 -->
        <div class="content-section mb-4">
          <div class="content-header">
            <h6 class="content-title">
              <i class="bi bi-gear me-2"></i>基本配置
            </h6>
          </div>
          <div class="content-body">
            <!-- 服务器用途 - 单选框 -->
            <div class="form-group mb-3">
              <label class="form-label">服务器用途 <span class="text-danger">*</span></label>
              <div class="d-flex flex-wrap gap-3 mb-2">
                <div class="form-check">
                  <input class="form-check-input" type="radio" :name="'serverPurposeRadio' + index" :id="'dmz' + index" value="DMZ" v-model="serverInfo.用途类型">
                  <label class="form-check-label" :for="'dmz' + index">DMZ</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" :name="'serverPurposeRadio' + index" :id="'internal' + index" value="内网" v-model="serverInfo.用途类型">
                  <label class="form-check-label" :for="'internal' + index">内网</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" :name="'serverPurposeRadio' + index" :id="'other' + index" value="其他" v-model="serverInfo.用途类型">
                  <label class="form-check-label" :for="'other' + index">其他</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" :name="'serverPurposeRadio' + index" :id="'custom' + index" value="自定义" v-model="serverInfo.用途类型">
                  <label class="form-check-label" :for="'custom' + index">自定义</label>
                </div>
              </div>
              <div v-if="serverInfo.用途类型 === '自定义'" class="mt-2">
                <input type="text" class="form-control enhanced-input" :id="'serverPurpose' + index" v-model="serverInfo.用途" placeholder="请输入自定义用途">
              </div>
            </div>

            <!-- IP地址 -->
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="form-group">
                  <label :for="'serverIP' + index" class="form-label">内网IP地址 <span class="text-danger">*</span></label>
                  <input type="text" class="form-control enhanced-input" :id="'serverIP' + index" v-model="serverInfo.IP地址" placeholder="例如：*************">
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label :for="'serverExternalIP' + index" class="form-label">外网IP地址</label>
                  <input type="text" class="form-control enhanced-input" :id="'serverExternalIP' + index" v-model="serverInfo.外网IP地址" placeholder="例如：************">
                  <small class="form-text text-muted">选填，如果服务器有外网IP，请填写</small>
                </div>
              </div>
            </div>

            <div class="row mb-3">
              <!-- 系统发行版 - 直接输入 + 快捷选择 -->
              <div class="col-md-6">
                <div class="form-group mb-2">
                  <label :for="'osVersion' + index" class="form-label">系统发行版</label>
                  <input
                    type="text"
                    class="form-control enhanced-input"
                    :id="'osVersion' + index"
                    v-model="serverInfo.系统发行版"
                    placeholder="请输入系统发行版"
                    :list="'os-options-' + index"
                  >
                  <datalist :id="'os-options-' + index">
                    <option value="CentOS 7.9"></option>
                    <option value="CentOS 8.4"></option>
                    <option value="CentOS Stream 8"></option>
                    <option value="CentOS Stream 9"></option>
                    <option value="Ubuntu 18.04 LTS"></option>
                    <option value="Ubuntu 20.04 LTS"></option>
                    <option value="Ubuntu 22.04 LTS"></option>
                    <option value="Ubuntu 24.04 LTS"></option>
                    <option value="Debian 10"></option>
                    <option value="Debian 11"></option>
                    <option value="Debian 12"></option>
                    <option value="Red Hat Enterprise Linux 7"></option>
                    <option value="Red Hat Enterprise Linux 8"></option>
                    <option value="Red Hat Enterprise Linux 9"></option>
                    <option value="Rocky Linux 8"></option>
                    <option value="Rocky Linux 9"></option>
                    <option value="AlmaLinux 8"></option>
                    <option value="AlmaLinux 9"></option>
                    <option value="openSUSE Leap 15"></option>
                    <option value="SUSE Linux Enterprise Server 15"></option>
                  </datalist>
                  <div class="quick-select-buttons mt-1">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.系统发行版 = 'CentOS 7.9'">CentOS 7.9</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.系统发行版 = 'Ubuntu 20.04 LTS'">Ubuntu 20.04</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.系统发行版 = 'Ubuntu 22.04 LTS'">Ubuntu 22.04</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" @click="serverInfo.系统发行版 = 'Debian 11'">Debian 11</button>
                  </div>
                </div>
              </div>

              <!-- 系统架构 - 新增字段 -->
              <div class="col-md-6">
                <div class="form-group mb-2">
                  <label :for="'architecture' + index" class="form-label">系统架构</label>
                  <select class="form-select enhanced-input" :id="'architecture' + index" v-model="serverInfo.系统架构" @change="onArchitectureChange">
                    <option value="">请选择架构</option>
                    <option value="x86_64">x86_64</option>
                    <option value="aarch64">aarch64</option>
                    <option value="arm64">arm64</option>
                    <option value="i386">i386</option>
                    <option value="i686">i686</option>
                    <option value="armv7l">armv7l</option>
                    <option value="s390x">s390x</option>
                    <option value="ppc64le">ppc64le</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="row mb-3">
              <!-- CPU - 直接输入 + 快捷选择 -->
              <div class="col-md-3">
                <div class="form-group mb-2">
                  <label :for="'cpu' + index" class="form-label">CPU</label>
                  <input
                    type="text"
                    class="form-control enhanced-input"
                    :id="'cpu' + index"
                    v-model="serverInfo.CPU"
                    placeholder="请输入CPU信息"
                    :list="'cpu-options-' + index"
                  >
                  <datalist :id="'cpu-options-' + index">
                    <option value="1C"></option>
                    <option value="2C"></option>
                    <option value="4C"></option>
                    <option value="6C"></option>
                    <option value="8C"></option>
                    <option value="12C"></option>
                    <option value="16C"></option>
                    <option value="20C"></option>
                    <option value="24C"></option>
                    <option value="32C"></option>
                    <option value="48C"></option>
                    <option value="64C"></option>
                  </datalist>
                  <div class="quick-select-buttons mt-1">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.CPU = '2C'">2C</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.CPU = '4C'">4C</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.CPU = '8C'">8C</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" @click="serverInfo.CPU = '16C'">16C</button>
                  </div>
                </div>
              </div>

              <!-- 内存 - 直接输入 + 快捷选择 -->
              <div class="col-md-3">
                <div class="form-group mb-2">
                  <label :for="'memory' + index" class="form-label">内存</label>
                  <input
                    type="text"
                    class="form-control enhanced-input"
                    :id="'memory' + index"
                    v-model="serverInfo.内存"
                    placeholder="请输入内存大小"
                    :list="'memory-options-' + index"
                  >
                  <datalist :id="'memory-options-' + index">
                    <option value="2GB"></option>
                    <option value="4GB"></option>
                    <option value="6GB"></option>
                    <option value="8GB"></option>
                    <option value="12GB"></option>
                    <option value="16GB"></option>
                    <option value="24GB"></option>
                    <option value="32GB"></option>
                    <option value="48GB"></option>
                    <option value="64GB"></option>
                    <option value="96GB"></option>
                    <option value="128GB"></option>
                    <option value="192GB"></option>
                    <option value="256GB"></option>
                    <option value="384GB"></option>
                    <option value="512GB"></option>
                  </datalist>
                  <div class="quick-select-buttons mt-1">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.内存 = '4GB'">4GB</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.内存 = '8GB'">8GB</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.内存 = '16GB'">16GB</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" @click="serverInfo.内存 = '32GB'">32GB</button>
                  </div>
                </div>
              </div>

              <!-- 磁盘 - 直接输入 + 快捷选择 -->
              <div class="col-md-3">
                <div class="form-group mb-2">
                  <label :for="'disk' + index" class="form-label">磁盘</label>
                  <input
                    type="text"
                    class="form-control enhanced-input"
                    :id="'disk' + index"
                    v-model="serverInfo.磁盘"
                    placeholder="请输入磁盘信息"
                    :list="'disk-options-' + index"
                  >
                  <datalist :id="'disk-options-' + index">
                    <option value="50GB"></option>
                    <option value="100GB"></option>
                    <option value="200GB"></option>
                    <option value="300GB"></option>
                    <option value="500GB"></option>
                    <option value="1TB"></option>
                    <option value="1.5TB"></option>
                    <option value="2TB"></option>
                    <option value="3TB"></option>
                    <option value="4TB"></option>
                    <option value="5TB"></option>
                    <option value="8TB"></option>
                    <option value="10TB"></option>
                    <option value="12TB"></option>
                    <option value="16TB"></option>
                    <option value="20TB"></option>
                  </datalist>
                  <div class="quick-select-buttons mt-1">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.磁盘 = '200GB'">200GB</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.磁盘 = '500GB'">500GB</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" @click="serverInfo.磁盘 = '1TB'">1TB</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" @click="serverInfo.磁盘 = '2TB'">2TB</button>
                  </div>
                </div>
              </div>

              <!-- SSH端口 - 移到基本配置 -->
              <div class="col-md-3">
                <div class="form-group mb-2">
                  <label :for="'sshPort' + index" class="form-label">SSH端口</label>
                  <input
                    type="text"
                    class="form-control enhanced-input"
                    :id="'sshPort' + index"
                    v-model="serverInfo.SSH端口"
                    placeholder="SSH端口"
                  >
                  <small class="form-text text-muted">默认：22</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 运维信息部分 -->
        <div class="content-section mb-4">
          <div class="content-header">
            <h6 class="content-title">
              <i class="bi bi-tools me-2"></i>运维信息
            </h6>
          </div>
          <div class="content-body">

            <!-- 运维用户列表 -->
            <div v-if="!serverInfo.运维用户 || serverInfo.运维用户.length === 0" class="text-center py-3 text-muted">
              <i class="bi bi-info-circle me-2"></i>暂无运维用户
            </div>

            <transition-group name="user-list" tag="div">
              <div
                v-for="(user, userIndex) in serverInfo.运维用户"
                :key="'user-' + userIndex"
                class="row mb-3 ops-user-item"
                :class="{ 'ops-user-default': user.isDefault }"
              >
                <!-- 用户类型标识 -->
                <div class="col-12 mb-2" v-if="user.isDefault">
                  <div class="user-type-badge">
                    <i class="bi bi-shield-check me-1"></i>
                    <span v-if="user.用户名 === 'root'">系统管理员</span>
                    <span v-else-if="user.用户名 === 'bangcle'">安全监测用户</span>
                    <span v-else>默认用户</span>
                  </div>
                </div>

                <!-- 运维用户名 -->
                <div class="col-md-5">
                  <div class="form-floating mb-3">
                    <input
                      type="text"
                      class="form-control enhanced-input"
                      :id="'opsUser' + index + '_' + userIndex"
                      v-model="user.用户名"
                      placeholder="运维用户名"
                      :readonly="user.isDefault && user.用户名 === 'root'"
                    >
                    <label :for="'opsUser' + index + '_' + userIndex">运维用户名</label>
                  </div>
                </div>

                <!-- 运维用户密码 -->
                <div class="col-md-5">
                  <div class="password-field-wrapper">
                    <div class="form-floating mb-3">
                      <input
                        :type="user.showPassword ? 'text' : 'password'"
                        class="form-control enhanced-input"
                        :id="'opsPassword' + index + '_' + userIndex"
                        v-model="user.密码"
                        placeholder="运维用户密码"
                      >
                      <label :for="'opsPassword' + index + '_' + userIndex">运维用户密码</label>
                    </div>
                    <button
                      type="button"
                      class="btn btn-password-toggle"
                      @click="togglePasswordVisibility(user)"
                    >
                      <i :class="user.showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                    </button>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="col-md-2 d-flex align-items-center">
                  <button
                    type="button"
                    class="btn btn-sm btn-delete-user w-100"
                    :class="user.isDefault ? 'btn-outline-secondary' : 'btn-outline-danger'"
                    @click="removeOpsUser(userIndex)"
                    :disabled="user.isDefault || serverInfo.运维用户.length === 1"
                    :title="user.isDefault ? '默认用户不可删除' : '删除用户'"
                  >
                    <i class="bi bi-trash me-1"></i>
                    <span v-if="user.isDefault">锁定</span>
                    <span v-else>删除</span>
                  </button>
                </div>
              </div>
            </transition-group>

            <!-- 添加运维用户按钮 -->
            <div class="row mb-3">
              <div class="col-12 text-center">
                <button
                  type="button"
                  class="btn btn-add-user"
                  @click="addOpsUser"
                >
                  <i class="bi bi-plus-circle me-2"></i>
                  <span>添加运维用户</span>
                  <div class="btn-ripple"></div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 部署组件选择器 -->
        <server-component-selector
          :server-info="serverInfo"
          :index="index"
          :document-type="documentType"
          :component-groups="componentGroups"
          @refresh-components="$emit('refresh-components')"
          @show-toast="$emit('show-toast', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ServerComponentSelector from './ServerComponentSelector.vue'

/**
 * 服务器信息项组件
 * 用于展示和编辑单个服务器的信息
 */
export default {
  name: 'ServerInfoItem',
  components: {
    ServerComponentSelector
  },
  props: {
    // 服务器信息对象
    serverInfo: {
      type: Object,
      required: true
    },
    // 服务器索引
    index: {
      type: Number,
      required: true
    },
    // 文档类型
    documentType: {
      type: String,
      default: '安全测评'
    },
    // 组件分组
    componentGroups: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 组件数据
    }
  },
  mounted() {
    // 确保运维用户数组中的每个用户都有showPassword属性
    if (this.serverInfo.运维用户 && Array.isArray(this.serverInfo.运维用户)) {
      this.serverInfo.运维用户.forEach(user => {
        if (typeof user.showPassword === 'undefined') {
          user.showPassword = false
        }
      })
    }
  },
  methods: {
    /**
     * 切换服务器信息的折叠状态
     */
    toggleCollapse() {
      // 确保折叠属性存在
      if (typeof this.serverInfo.折叠 === 'undefined') {
        this.serverInfo.折叠 = false
      }
      // 切换折叠状态
      this.serverInfo.折叠 = !this.serverInfo.折叠
    },

    /**
     * 处理系统架构变化
     * 根据架构类型更新kibana认证信息
     */
    onArchitectureChange() {
      // 只有在安全监测表单中才处理kibana认证信息
      if (this.documentType === '安全监测') {
        this.$emit('architecture-changed', {
          serverIndex: this.index,
          architecture: this.serverInfo.系统架构
        })
      }
    },



    /**
     * 切换密码显示状态
     * @param {Object} user - 用户对象
     */
    togglePasswordVisibility(user) {
      user.showPassword = !user.showPassword
    },

    /**
     * 添加运维用户
     * 向运维用户列表添加新的用户项
     */
    addOpsUser() {
      // 确保运维用户数组存在
      if (!this.serverInfo.运维用户) {
        this.serverInfo.运维用户 = []
      }

      // 添加新的运维用户
      this.serverInfo.运维用户.push({
        用户名: '',
        密码: '',
        showPassword: false,
        isDefault: false
      })
    },

    /**
     * 删除运维用户
     * 从运维用户列表中删除指定索引的用户
     * @param {Number} userIndex - 要删除的用户索引
     */
    removeOpsUser(userIndex) {
      if (this.serverInfo.运维用户 && this.serverInfo.运维用户.length > 1) {
        this.serverInfo.运维用户.splice(userIndex, 1)
      }
    }
  }
}
</script>

<style scoped>
/* 服务器信息卡片样式 */
.server-info-wrapper {
  margin-bottom: 1.5rem;
}

.server-info-card {
  background: linear-gradient(135deg, #f8fffe 0%, #ffffff 100%);
  border: 2px solid #28a745;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1);
  transition: all 0.3s ease;
}

.server-info-card:hover {
  border-color: #20c997;
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.15);
  transform: translateY(-2px);
}

/* 服务器头部样式 */
.server-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.server-title-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.server-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 700;
  margin-right: 1rem;
  flex-shrink: 0;
}

.server-title {
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  margin-right: 1rem;
}

.server-title i {
  font-size: 1.2rem;
}

.server-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.server-badge {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.server-badge-purpose {
  background: rgba(255, 255, 255, 0.2);
}

.server-badge-ip {
  background: rgba(255, 255, 255, 0.15);
}

.server-badge-external {
  background: rgba(255, 193, 7, 0.3);
  border-color: rgba(255, 193, 7, 0.5);
}

/* 服务器操作按钮 */
.server-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-server-action {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  transition: all 0.3s ease;
}

.btn-server-action:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transform: translateY(-1px);
}

.btn-server-danger {
  background: rgba(220, 53, 69, 0.8);
  border: 1px solid rgba(220, 53, 69, 0.9);
  color: white;
  transition: all 0.3s ease;
}

.btn-server-danger:hover {
  background: rgba(220, 53, 69, 1);
  border-color: rgba(220, 53, 69, 1);
  color: white;
  transform: translateY(-1px);
}

/* 服务器内容区域 */
.server-content {
  padding: 1.5rem;
  animation: slideInDown 0.3s ease;
}

/* 内容分组样式 */
.content-section {
  background: rgba(248, 249, 250, 0.5);
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.content-section:hover {
  border-color: #28a745;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.08);
}

.content-header {
  border-bottom: 2px solid #28a745;
  padding-bottom: 0.75rem;
  margin-bottom: 1rem;
}

.content-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.content-title i {
  font-size: 1.1rem;
  color: #28a745;
}

.content-body {
  /* 内容主体样式 */
}

/* 快捷选择按钮样式 */
.quick-select-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.quick-select-buttons .btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

/* 增强的输入框样式 */
.enhanced-input,
.enhanced-textarea {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.enhanced-input:focus,
.enhanced-textarea:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.15);
  background-color: #ffffff;
}

.enhanced-input:hover:not(:focus),
.enhanced-textarea:hover:not(:focus) {
  border-color: #ced4da;
}

/* 密码字段包装器 */
.password-field-wrapper {
  position: relative;
}

.btn-password-toggle {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  z-index: 10;
}

.btn-password-toggle:hover {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

/* 运维用户项样式 */
.ops-user-item {
  background: rgba(248, 249, 250, 0.3);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  border: 1px solid #e9ecef;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.ops-user-item:hover {
  border-color: #28a745;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
  transform: translateY(-2px);
}

/* 默认用户特殊样式 */
.ops-user-default {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(32, 201, 151, 0.05) 100%);
  border-color: rgba(40, 167, 69, 0.3);
}

.ops-user-default:hover {
  border-color: #28a745;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

/* 用户类型标识 */
.user-type-badge {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

/* 添加用户按钮样式 */
.btn-add-user {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
  position: relative;
  overflow: hidden;
}

.btn-add-user:hover {
  background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
}

.btn-add-user:active {
  transform: translateY(0);
}

/* 删除按钮样式 */
.btn-delete-user {
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-delete-user:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.btn-delete-user:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 列表动画 */
.user-list-enter-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-list-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-list-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.user-list-leave-to {
  opacity: 0;
  transform: translateX(20px) scale(0.95);
}

.user-list-move {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 标签样式增强 */
.form-floating > label {
  color: #6c757d;
  font-weight: 500;
}

.form-floating > .enhanced-input:focus ~ label,
.form-floating > .enhanced-input:not(:placeholder-shown) ~ label {
  color: #28a745;
}

/* 动画效果 */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 聚焦状态的分组高亮 */
.content-section:focus-within {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .server-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .server-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .server-summary {
    margin-left: 0;
  }

  .server-actions {
    align-self: flex-end;
  }

  .server-content {
    padding: 1rem;
  }

  .content-section {
    padding: 1rem;
  }

  .content-header {
    padding-bottom: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .btn-password-toggle {
    right: 6px;
  }
}

/* 小屏幕优化 */
@media (max-width: 576px) {
  .server-number {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
    margin-right: 0.75rem;
  }

  .server-title {
    font-size: 1rem;
  }

  .server-badge {
    font-size: 0.75rem;
    padding: 0.2rem 0.6rem;
  }

  .content-title {
    font-size: 0.9rem;
  }
}
</style>
