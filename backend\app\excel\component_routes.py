"""
组件管理相关的API路由
"""

from flask import request, jsonify, current_app
from app.excel import bp
from app import db
from app.models.models import ComponentCategory, ComponentConfig, FormType
from app.utils.cache_utils import ComponentCacheManager
import traceback
from datetime import datetime


@bp.route('/component-categories', methods=['GET'])
def get_component_categories():
    """
    获取组件分类列表
    支持按表单类型筛选
    """
    try:
        form_type = request.args.get('form_type', '')
        current_app.logger.info(f"🔍 组件分类API请求: form_type={form_type}")
        
        query = ComponentCategory.query.filter_by(is_active=True)
        
        # 如果指定了表单类型，筛选适用的分类
        if form_type:
            # 查询form_types字段包含指定表单类型的分类
            all_categories = query.all()
            categories = []
            for category in all_categories:
                if form_type in category.get_form_types():
                    categories.append(category)
        else:
            categories = query.order_by(ComponentCategory.order).all()
        
        # 转换为字典格式
        categories_data = [category.to_dict() for category in categories]
        
        return jsonify({
            'status': 'success',
            'data': categories_data
        })
    except Exception as e:
        current_app.logger.error(f"获取组件分类失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取组件分类失败: {str(e)}'
        }), 500


@bp.route('/component-categories', methods=['POST'])
def create_component_category():
    """
    创建新的组件分类
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400
        
        # 验证必填字段
        required_fields = ['key', 'display_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'status': 'error',
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查分类键名是否已存在
        existing_category = ComponentCategory.query.filter_by(key=data['key']).first()
        if existing_category:
            return jsonify({
                'status': 'error',
                'message': f'分类键名 {data["key"]} 已存在'
            }), 400
        
        # 创建新分类
        new_category = ComponentCategory(
            key=data['key'],
            display_name=data['display_name'],
            icon=data.get('icon', ''),
            color=data.get('color', ''),
            order=data.get('order', 0),
            is_active=data.get('is_active', True)
        )
        
        # 设置适用的表单类型
        if 'form_types' in data:
            new_category.set_form_types(data['form_types'])
        elif 'form_type' in data:
            # 🔧 支持单个表单类型字段
            new_category.set_form_types([data['form_type']])
        
        db.session.add(new_category)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '组件分类创建成功',
            'data': new_category.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建组件分类失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'创建组件分类失败: {str(e)}'
        }), 500


@bp.route('/component-categories/<int:category_id>', methods=['PUT'])
def update_component_category(category_id):
    """
    更新组件分类
    """
    try:
        category = ComponentCategory.query.get_or_404(category_id)

        # 检查是否为系统保护的"未分组"分类
        if category.key.startswith('uncategorized'):
            return jsonify({
                'status': 'error',
                'message': '系统"未分组"分类不允许修改'
            }), 403

        data = request.get_json()

        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400

        # 更新字段
        if 'display_name' in data:
            category.display_name = data['display_name']
        if 'icon' in data:
            category.icon = data['icon']
        if 'color' in data:
            category.color = data['color']
        if 'order' in data:
            category.order = data['order']
        if 'is_active' in data:
            category.is_active = data['is_active']
        if 'form_types' in data:
            category.set_form_types(data['form_types'])
        elif 'form_type' in data:
            # 🔧 支持单个表单类型字段
            category.set_form_types([data['form_type']])
        
        category.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '组件分类更新成功',
            'data': category.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新组件分类失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'更新组件分类失败: {str(e)}'
        }), 500


@bp.route('/component-categories/<int:category_id>', methods=['DELETE'])
def delete_component_category(category_id):
    """
    删除组件分类，将该分类下的组件移动到"未分组"分类
    """
    try:
        category = ComponentCategory.query.get_or_404(category_id)
        category_name = category.display_name

        # 检查是否为系统保护的"未分组"分类，不能删除
        if category.key.startswith('uncategorized'):
            return jsonify({
                'status': 'error',
                'message': '系统"未分组"分类不能删除'
            }), 403

        # 查找该分类下的所有组件
        components = ComponentConfig.query.filter_by(category_key=category.key).all()

        # 按表单类型将组件移动到对应的未分组分类（单一表单类型设计）
        form_type_uncategorized_mapping = {
            '安全测评': 'uncategorized-testing',
            '安全监测': 'uncategorized-security',
            '应用加固': 'uncategorized-hardening'
        }

        moved_count = 0
        for component in components:
            # 根据组件的表单类型选择对应的未分组分类
            target_uncategorized_key = form_type_uncategorized_mapping.get(component.form_type)

            if target_uncategorized_key:
                # 确保目标未分组分类存在
                target_category = ComponentCategory.query.filter_by(key=target_uncategorized_key).first()
                if target_category:
                    component.category_key = target_uncategorized_key
                    component.updated_at = datetime.utcnow()
                    moved_count += 1
                    current_app.logger.info(f"组件 {component.display_name} ({component.form_type}) 已移动到 {target_category.display_name}")
                else:
                    current_app.logger.error(f"目标未分组分类 {target_uncategorized_key} 不存在")
            else:
                current_app.logger.warning(f"组件 {component.display_name} 的表单类型 {component.form_type} 没有对应的未分组分类")

        # 软删除分类：设置为不活跃
        category.is_active = False
        category.updated_at = datetime.utcnow()

        db.session.commit()

        message = f'组件分类"{category_name}"删除成功'
        if moved_count > 0:
            message += f'，已将 {moved_count} 个组件移动到"未分组"分类'

        return jsonify({
            'status': 'success',
            'message': message,
            'moved_components_count': moved_count
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除组件分类失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'删除组件分类失败: {str(e)}'
        }), 500


@bp.route('/components', methods=['GET'])
def get_components():
    """
    获取组件列表
    支持按表单类型和分类筛选
    """
    try:
        form_type = request.args.get('form_type', '')
        category_key = request.args.get('category_key', '')

        query = ComponentConfig.query.filter_by(is_active=True)

        # 按表单类型筛选
        if form_type:
            query = query.filter_by(form_type=form_type)

        # 按分类筛选
        if category_key:
            query = query.filter_by(category_key=category_key)

        # 排序
        components = query.order_by(ComponentConfig.category_key, ComponentConfig.order).all()

        # 转换为字典格式，包含分类信息
        components_data = [component.to_dict() for component in components]

        return jsonify({
            'status': 'success',
            'data': components_data
        })
    except Exception as e:
        current_app.logger.error(f"获取组件列表失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取组件列表失败: {str(e)}'
        }), 500



@bp.route('/components/by-category', methods=['GET'])
def get_components_by_category():
    """
    按分类获取组件列表（带缓存）
    返回按分类组织的组件数据
    """
    try:
        form_type = request.args.get('form_type', '')
        current_app.logger.info(f"🔍 按分类获取组件API请求: form_type={form_type}")

        if not form_type:
            return jsonify({
                'status': 'error',
                'message': '请指定表单类型'
            }), 400

        # 尝试从缓存获取组件数据
        cached_data = ComponentCacheManager.get_components(form_type)
        if cached_data is not None:
            current_app.logger.debug(f"组件配置缓存命中: form_type={form_type}")
            return jsonify({
                'status': 'success',
                'data': cached_data
            })

        # 缓存未命中，从数据库查询
        current_app.logger.debug(f"组件配置缓存未命中，从数据库查询: form_type={form_type}")

        # 获取指定表单类型的所有分类（排除"未分组"分类）
        categories = ComponentCategory.query.filter_by(is_active=True).all()
        current_app.logger.info(f"📋 数据库中的所有分类数量: {len(categories)}")

        applicable_categories = []
        for cat in categories:
            # 🔧 修改逻辑：检查"未分组"分类是否有组件，如果有则包含
            if cat.key.startswith('uncategorized'):
                # 检查该未分组分类是否有组件
                uncategorized_components_count = ComponentConfig.query.filter_by(
                    form_type=form_type,
                    category_key=cat.key
                ).count()

                if uncategorized_components_count > 0:
                    current_app.logger.info(f"✅ 包含有组件的未分组分类: {cat.key} ({uncategorized_components_count} 个组件)")
                    applicable_categories.append(cat)
                else:
                    current_app.logger.info(f"🚫 跳过空的未分组分类: {cat.key}")
                continue

            # 使用新的单一表单类型字段
            cat_form_type = cat.get_form_type()
            current_app.logger.info(f"🔍 分类 {cat.key}: form_type={cat_form_type}")
            if cat_form_type == form_type:
                applicable_categories.append(cat)
                current_app.logger.info(f"✅ 分类 {cat.key} 适用于 {form_type}")

        current_app.logger.info(f"📋 适用的分类数量: {len(applicable_categories)}")

        # 按分类组织组件数据（显示所有组件，包括停用的）
        result = {}
        for category in applicable_categories:
            components = ComponentConfig.query.filter_by(
                form_type=form_type,
                category_key=category.key
                # 移除 is_active=True 过滤，显示所有组件
            ).order_by(ComponentConfig.order).all()

            current_app.logger.info(f"🔍 分类 {category.key} 的组件数量: {len(components)}")
            for comp in components:
                current_app.logger.info(f"  - {comp.name} ({comp.display_name})")

            result[category.key] = {
                'category_info': category.to_dict(),
                'components': [comp.to_dict() for comp in components]
            }

        # 缓存查询结果
        ComponentCacheManager.cache_components(form_type, result)

        current_app.logger.info(f"🎯 最终结果: {len(result)} 个分类")
        return jsonify({
            'status': 'success',
            'data': result
        })
    except Exception as e:
        current_app.logger.error(f"按分类获取组件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'按分类获取组件失败: {str(e)}'
        }), 500


@bp.route('/components', methods=['POST'])
def create_component():
    """
    创建新组件
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400
        
        # 验证必填字段（表单类型将从分类自动确定）
        required_fields = ['name', 'display_name', 'category_key']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'status': 'error',
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 先验证分类并确定表单类型，然后检查组件是否已存在
        category = ComponentCategory.query.filter_by(key=data['category_key']).first()
        if not category:
            return jsonify({
                'status': 'error',
                'message': f'分类 {data["category_key"]} 不存在'
            }), 400

        # 从分类自动确定表单类型
        auto_form_type = category.get_form_type()
        if not auto_form_type:
            return jsonify({
                'status': 'error',
                'message': f'分类 {category.display_name} 没有设置表单类型，无法创建组件'
            }), 400

        current_app.logger.info(f"从分类 {category.display_name} 自动确定表单类型: {auto_form_type}")

        # 检查组件是否已存在（使用自动确定的表单类型）
        existing_component = ComponentConfig.query.filter_by(
            name=data['name'],
            form_type=auto_form_type
        ).first()
        if existing_component:
            return jsonify({
                'status': 'error',
                'message': f'组件 {data["name"]} 在表单类型 {auto_form_type} 中已存在'
            }), 400
        

        
        # 创建新组件（使用自动确定的表单类型）
        new_component = ComponentConfig(
            name=data['name'],
            display_name=data['display_name'],
            category_key=data['category_key'],
            form_type=auto_form_type,  # 使用从分类自动确定的表单类型
            version=data.get('version', ''),
            default_port=data.get('default_port', ''),
            description=data.get('description', ''),
            protocol=data.get('protocol', 'http'),
            alias=data.get('alias', ''),
            order=data.get('order', 0),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(new_component)
        db.session.commit()

        # 清除相关缓存
        ComponentCacheManager.clear_component_cache(auto_form_type)

        return jsonify({
            'status': 'success',
            'message': '组件创建成功',
            'data': new_component.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建组件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'创建组件失败: {str(e)}'
        }), 500


@bp.route('/components/<int:component_id>', methods=['PUT'])
def update_component(component_id):
    """
    更新组件
    """
    try:
        component = ComponentConfig.query.get_or_404(component_id)
        data = request.get_json()
        
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400
        
        # 更新字段
        if 'display_name' in data:
            component.display_name = data['display_name']
        if 'category_key' in data:
            # 当分类改变时，自动更新表单类型
            new_category = ComponentCategory.query.filter_by(key=data['category_key']).first()
            if not new_category:
                return jsonify({
                    'status': 'error',
                    'message': f'分类 {data["category_key"]} 不存在'
                }), 400

            auto_form_type = new_category.get_form_type()
            if not auto_form_type:
                return jsonify({
                    'status': 'error',
                    'message': f'分类 {new_category.display_name} 没有设置表单类型，无法更新组件'
                }), 400

            component.category_key = data['category_key']
            component.form_type = auto_form_type  # 自动更新表单类型
            current_app.logger.info(f"组件 {component.display_name} 分类更新为 {new_category.display_name}，表单类型自动更新为 {auto_form_type}")

        if 'version' in data:
            component.version = data['version']
        if 'default_port' in data:
            component.default_port = data['default_port']
        if 'description' in data:
            component.description = data['description']
        if 'protocol' in data:
            component.protocol = data['protocol']
        if 'alias' in data:
            component.alias = data['alias']
        if 'order' in data:
            component.order = data['order']
        if 'is_active' in data:
            component.is_active = data['is_active']
        
        component.updated_at = datetime.utcnow()
        db.session.commit()

        # 清除相关缓存
        ComponentCacheManager.clear_component_cache(component.form_type)

        return jsonify({
            'status': 'success',
            'message': '组件更新成功',
            'data': component.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新组件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'更新组件失败: {str(e)}'
        }), 500


@bp.route('/components/<int:component_id>', methods=['DELETE'])
def delete_component(component_id):
    """
    删除组件（真正删除，从数据库中移除）
    """
    try:
        component = ComponentConfig.query.get_or_404(component_id)
        component_name = component.display_name  # 保存名称用于日志

        # 保存表单类型用于清除缓存
        form_type = component.form_type

        # 真正删除：从数据库中移除
        db.session.delete(component)
        db.session.commit()

        # 清除相关缓存
        ComponentCacheManager.clear_component_cache(form_type)

        current_app.logger.info(f"组件 {component_name} (ID: {component_id}) 已被删除")

        return jsonify({
            'status': 'success',
            'message': f'组件 {component_name} 删除成功'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除组件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'删除组件失败: {str(e)}'
        }), 500


@bp.route('/components/<int:component_id>/active', methods=['PUT'])
def toggle_component_active_status(component_id):
    """
    切换组件的启用状态
    """
    try:
        component = ComponentConfig.query.get_or_404(component_id)
        data = request.get_json()
        
        if data is None or 'is_active' not in data:
             return jsonify({
                'status': 'error',
                'message': '请求数据无效，请提供 is_active 字段'
            }), 400
        
        is_active = data['is_active']

        if not isinstance(is_active, bool):
             return jsonify({
                'status': 'error',
                'message': 'is_active 字段必须是布尔值 (true/false)'
            }), 400

        component.is_active = is_active
        component.updated_at = datetime.utcnow()
        db.session.commit()

        # 清除相关缓存
        ComponentCacheManager.clear_component_cache(component.form_type)

        return jsonify({
            'status': 'success',
            'message': '组件启用状态更新成功',
            'data': component.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"切换组件启用状态失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'切换组件启用状态失败: {str(e)}'
        }), 500


# ========== 表单类型管理API ==========
# 注意：get_form_types 和 create_form_type 已移动到 routes.py 中，避免重复定义

@bp.route('/form-types-legacy', methods=['POST'])
def create_form_type_legacy():
    """
    创建新的表单类型
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400

        # 验证必填字段
        if not data.get('name'):
            return jsonify({
                'status': 'error',
                'message': '表单类型名称不能为空'
            }), 400

        # 检查表单类型是否已存在
        existing_form_type = FormType.query.filter_by(name=data['name']).first()
        if existing_form_type:
            return jsonify({
                'status': 'error',
                'message': f'表单类型 {data["name"]} 已存在'
            }), 400

        # 创建新表单类型
        new_form_type = FormType(
            name=data['name'],
            display_name=data.get('display_name', data['name']),
            description=data.get('description', ''),
            is_default=False,  # 新创建的表单类型不是默认类型
            is_active=True,
            order=data.get('order', 999)  # 新类型默认排在最后
        )

        db.session.add(new_form_type)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '表单类型创建成功',
            'data': new_form_type.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建表单类型失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'创建表单类型失败: {str(e)}'
        }), 500


@bp.route('/form-types/<int:form_type_id>', methods=['DELETE'])
def delete_form_type(form_type_id):
    """
    删除表单类型（只能删除非默认类型）
    """
    try:
        form_type = FormType.query.get_or_404(form_type_id)

        # 检查是否为默认表单类型
        if form_type.is_default:
            return jsonify({
                'status': 'error',
                'message': '不能删除默认表单类型'
            }), 400

        # 检查是否有组件正在使用这个表单类型
        components_count = ComponentConfig.query.filter_by(form_type=form_type.name).count()
        if components_count > 0:
            return jsonify({
                'status': 'error',
                'message': f'无法删除表单类型 {form_type.name}，还有 {components_count} 个组件正在使用'
            }), 400

        # 检查是否有分类正在使用这个表单类型
        categories = ComponentCategory.query.filter_by(is_active=True).all()
        using_categories = []
        for category in categories:
            if form_type.name in category.get_form_types():
                using_categories.append(category.display_name)

        if using_categories:
            return jsonify({
                'status': 'error',
                'message': f'无法删除表单类型 {form_type.name}，以下分类正在使用: {", ".join(using_categories)}'
            }), 400

        form_type_name = form_type.name

        # 级联删除相关记录
        current_app.logger.info(f"开始级联删除表单类型 {form_type_name} 的相关记录")

        # 删除FormFieldGroup表中的相关记录
        from app.models.form_field_config import FormFieldGroup, FormFieldConfig

        groups_to_delete = FormFieldGroup.query.filter_by(form_type=form_type_name).all()
        if groups_to_delete:
            current_app.logger.info(f"删除 {len(groups_to_delete)} 个FormFieldGroup记录")
            for group in groups_to_delete:
                current_app.logger.debug(f"删除分组: {group.group_label} (ID: {group.id})")
                db.session.delete(group)

        # 删除FormFieldConfig表中的相关记录
        configs_to_delete = FormFieldConfig.query.filter_by(form_type=form_type_name).all()
        if configs_to_delete:
            current_app.logger.info(f"删除 {len(configs_to_delete)} 个FormFieldConfig记录")
            for config in configs_to_delete:
                current_app.logger.debug(f"删除字段配置: {config.field_label} (ID: {config.id})")
                db.session.delete(config)

        # 删除FormType记录
        current_app.logger.info(f"删除FormType记录: {form_type_name}")
        db.session.delete(form_type)

        # 提交所有删除操作
        db.session.commit()
        current_app.logger.info(f"表单类型 {form_type_name} 及其相关记录删除完成")

        # 强制清除表单类型列表缓存
        try:
            from app.utils.cache_utils import FormTypeCacheManager

            # 使用专门的缓存管理器清除
            success = FormTypeCacheManager.clear_form_types_cache()

            if success:
                current_app.logger.info("✅ 表单类型列表缓存已成功清除")
            else:
                current_app.logger.warning("❌ 缓存清除可能失败")

            # 验证清除结果
            remaining_cache = FormTypeCacheManager.get_form_types_list()
            if remaining_cache is None:
                current_app.logger.info("✅ 缓存清除验证成功")
            else:
                current_app.logger.warning(f"❌ 缓存仍有数据: {len(remaining_cache) if isinstance(remaining_cache, list) else 'Unknown'}")

        except Exception as cache_error:
            current_app.logger.error(f"清除缓存时发生错误: {str(cache_error)}")
            # 缓存清除失败不应该影响删除操作的成功

        return jsonify({
            'status': 'success',
            'message': f'表单类型 {form_type_name} 删除成功'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除表单类型失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'删除表单类型失败: {str(e)}'
        }), 500


@bp.route('/form-types/usage', methods=['GET'])
def get_form_types_usage():
    """
    获取表单类型的使用情况统计
    """
    try:
        form_types = FormType.query.filter_by(is_active=True).all()
        usage_data = []

        for form_type in form_types:
            # 统计使用该表单类型的组件数量
            components_count = ComponentConfig.query.filter_by(form_type=form_type.name).count()

            # 统计使用该表单类型的分类数量
            categories = ComponentCategory.query.filter_by(is_active=True).all()
            categories_count = sum(1 for cat in categories if form_type.name in cat.get_form_types())

            usage_data.append({
                'form_type': form_type.to_dict(),
                'components_count': components_count,
                'categories_count': categories_count
            })

        return jsonify({
            'status': 'success',
            'data': usage_data
        })
    except Exception as e:
        current_app.logger.error(f"获取表单类型使用情况失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取表单类型使用情况失败: {str(e)}'
        }), 500


@bp.route('/components/statistics', methods=['GET'])
def get_components_statistics():
    """
    获取组件统计信息，按表单类型分组
    """
    try:
        # 获取所有活动的表单类型
        form_types = FormType.query.filter_by(is_active=True).all()

        statistics = {}
        for form_type in form_types:
            # 统计每个表单类型的组件数量
            component_count = ComponentConfig.query.filter_by(
                form_type=form_type.name,
                is_active=True
            ).count()

            statistics[form_type.name] = component_count

        return jsonify({
            'status': 'success',
            'data': statistics
        })
    except Exception as e:
        current_app.logger.error(f"获取组件统计信息失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取组件统计信息失败: {str(e)}'
        }), 500
