<template>
  <div class="container-fluid mt-4">
    <div class="row">
      <div class="col-12">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb" class="mb-3">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <router-link to="/form-template-manager" class="text-decoration-none">
                <i class="bi bi-house me-1"></i>表单模板管理
              </router-link>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              <i class="bi bi-sliders me-1"></i>字段配置
            </li>
          </ol>
        </nav>

        <!-- 页面标题和返回按钮 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h2 class="mb-0">
            <i class="bi bi-sliders me-2"></i>
            表单字段配置
          </h2>
          <div>
            <button class="btn btn-outline-secondary me-2" @click="goBack">
              <i class="bi bi-arrow-left me-1"></i>返回管理页面
            </button>
            <span class="badge bg-success">功能完整</span>
          </div>
        </div>

        <!-- 来源提示 -->
        <div v-if="fromUrl" class="alert alert-info mb-3">
          <i class="bi bi-info-circle me-2"></i>
          您正在配置表单类型：<strong>{{ selectedFormType }}</strong>
          <button class="btn btn-sm btn-outline-primary ms-2" @click="goBack">
            <i class="bi bi-arrow-left me-1"></i>返回管理页面
          </button>
        </div>

        <!-- 表单类型选择 -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="bi bi-list-ul me-2"></i>
              选择表单类型
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-4">
                <select v-model="selectedFormType" class="form-select" @change="loadFormConfig">
                  <option value="">请选择表单类型</option>
                  <option
                    v-for="formType in formTypes"
                    :key="formType.name"
                    :value="formType.name"
                    :class="{ 'text-muted': formType.is_built_in }"
                  >
                    {{ formType.name }}
                    <span v-if="formType.is_built_in" class="text-muted">(内置-只读)</span>
                    <span v-else class="text-success">(自定义)</span>
                  </option>
                </select>
              </div>
              <div class="col-md-8">
                <button class="btn btn-outline-primary me-2" @click="showCreateFormTypeModal">
                  <i class="bi bi-plus me-1"></i>新建表单类型
                </button>
                <button
                  class="btn me-2"
                  :class="canInitBasicFields ? 'btn-warning' : 'btn-outline-secondary'"
                  @click="initBasicFields"
                  :disabled="!canInitBasicFields"
                  :title="getInitBasicFieldsTooltip"
                >
                  <i class="bi bi-magic me-1"></i>初始化基础字段
                  <i v-if="!canInitBasicFields" class="bi bi-lock-fill ms-1 text-muted"></i>
                </button>
                <button
                  class="btn me-2"
                  :class="isBuiltInFormType ? 'btn-outline-secondary' : 'btn-primary'"
                  @click="showCreateGroupModal"
                  :disabled="!selectedFormType || isBuiltInFormType"
                  :title="isBuiltInFormType ? '内置表单类型不能添加新分组' : ''"
                >
                  <i class="bi bi-plus-circle me-1"></i>新建字段分组
                  <i v-if="isBuiltInFormType" class="bi bi-lock-fill ms-1 text-muted"></i>
                </button>
                <button
                  class="btn"
                  :class="isBuiltInFormType ? 'btn-outline-secondary' : 'btn-success'"
                  @click="showCreateFieldModal"
                  :disabled="!selectedFormType || isBuiltInFormType"
                  :title="isBuiltInFormType ? '内置表单类型不能添加新字段' : ''"
                >
                  <i class="bi bi-plus-square me-1"></i>新建字段
                  <i v-if="isBuiltInFormType" class="bi bi-lock-fill ms-1 text-muted"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 字段配置预览 -->
        <div v-if="selectedFormType" class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
              <i class="bi bi-eye me-2"></i>
              {{ selectedFormType }} - 字段配置预览
            </h5>
            <div>
              <button class="btn btn-outline-primary btn-sm me-2" @click="previewForm">
                <i class="bi bi-eye me-1"></i>预览表单
              </button>
              <button
                class="btn btn-outline-success btn-sm"
                @click="exportConfig"
                :title="isBuiltInFormType ? '导出内置表单配置（仅供参考）' : '导出自定义表单配置'"
              >
                <i class="bi bi-download me-1"></i>导出配置
                <i v-if="isBuiltInFormType" class="bi bi-info-circle ms-1" title="内置表单配置"></i>
              </button>
            </div>
          </div>
          <div class="card-body">
            <!-- 内置表单类型提示 -->
            <div v-if="isBuiltInFormType" class="alert alert-info mb-3">
              <i class="bi bi-info-circle me-2"></i>
              <strong>内置表单类型：</strong>
              {{ selectedFormType }} 是系统内置的表单类型，其字段配置为只读状态。您可以查看配置结构作为参考，但不能进行修改。
              如需自定义配置，请创建新的表单类型。
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
              <p class="mt-2 text-muted">正在加载字段配置...</p>
            </div>

            <!-- 字段分组列表 -->
            <div v-else-if="fieldGroups.length > 0">
              <div v-for="group in fieldGroups" :key="group.id" class="field-group mb-4">
                <div
                  class="group-header d-flex justify-content-between align-items-center p-3 rounded"
                  :class="isBuiltInFormType ? 'bg-light text-muted' : 'bg-light'"
                >
                  <div>
                    <h6 class="mb-1" :class="isBuiltInFormType ? 'text-muted' : ''">
                      <i class="bi bi-folder me-2"></i>
                      {{ group.group_label }}
                      <span class="badge ms-2" :class="isBuiltInFormType ? 'bg-secondary' : 'bg-secondary'">
                        {{ group.field_count }} 个字段
                      </span>
                      <span v-if="isDefaultGroup(group)" class="badge bg-info ms-1" title="默认分组">
                        默认
                      </span>
                      <i v-if="isBuiltInFormType" class="bi bi-lock-fill ms-2 text-muted" title="内置分组，不可编辑"></i>
                    </h6>
                    <small class="text-muted">{{ group.group_description }}</small>
                  </div>
                  <div>
                    <button
                      class="btn btn-sm me-2"
                      :class="isBuiltInFormType ? 'btn-outline-secondary' : 'btn-outline-primary'"
                      @click="editGroup(group)"
                      :disabled="isBuiltInFormType"
                      :title="isBuiltInFormType ? '内置分组不能编辑' : '编辑分组'"
                    >
                      <i class="bi bi-pencil"></i>
                    </button>
                    <button
                      class="btn btn-sm"
                      :class="isBuiltInFormType ? 'btn-outline-secondary' : 'btn-outline-danger'"
                      @click="deleteGroup(group)"
                      :disabled="isBuiltInFormType"
                      :title="isBuiltInFormType ? '内置分组不能删除' : '删除分组'"
                    >
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </div>

                <!-- 字段列表 -->
                <div class="fields-list mt-2">
                  <div v-for="field in getFieldsByGroup(group.id)" :key="field.id"
                       class="field-item d-flex justify-content-between align-items-center p-2 border rounded mb-2"
                       :class="isBuiltInFormType ? 'bg-light' : 'bg-white'"
                  >
                    <div class="field-info">
                      <div class="d-flex align-items-center">
                        <i :class="[getFieldTypeIcon(field.field_type), 'me-2', isBuiltInFormType ? 'text-muted' : '']"></i>
                        <strong :class="isBuiltInFormType ? 'text-muted' : ''">{{ field.field_label }}</strong>
                        <span class="badge ms-2" :class="isBuiltInFormType ? 'bg-secondary' : 'bg-info'">{{ field.field_type }}</span>
                        <span v-if="field.is_required" class="badge bg-danger ms-1">必填</span>
                        <span v-if="field.is_readonly" class="badge bg-secondary ms-1">只读</span>
                        <span v-if="isCommonField(field.field_name)" class="badge bg-success ms-1" title="公共字段">
                          <i class="bi bi-magic"></i> 公共
                        </span>
                        <i v-if="isBuiltInFormType" class="bi bi-lock-fill ms-2 text-muted" title="内置字段，不可编辑"></i>
                      </div>
                      <small class="text-muted d-block mt-1">
                        字段名: {{ field.field_name }}
                        <span v-if="field.field_description"> | {{ field.field_description }}</span>
                      </small>
                    </div>
                    <div class="field-actions">
                      <button
                        class="btn btn-sm me-1"
                        :class="isBuiltInFormType ? 'btn-outline-secondary' : 'btn-outline-primary'"
                        @click="editField(field)"
                        :disabled="isBuiltInFormType"
                        :title="isBuiltInFormType ? '内置字段不能编辑' : '编辑字段'"
                      >
                        <i class="bi bi-pencil"></i>
                      </button>
                      <button
                        class="btn btn-sm"
                        :class="isBuiltInFormType ? 'btn-outline-secondary' : 'btn-outline-danger'"
                        @click="deleteField(field)"
                        :disabled="isBuiltInFormType"
                        :title="isBuiltInFormType ? '内置字段不能删除' : '删除字段'"
                      >
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="text-center py-5">
              <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
              <h5 class="text-muted mt-3">暂无字段配置</h5>

              <!-- 内置表单类型的空状态 -->
              <div v-if="isBuiltInFormType">
                <p class="text-muted">内置表单类型的字段配置由系统管理，暂无可显示的配置</p>
                <div class="alert alert-info mt-3">
                  <i class="bi bi-info-circle me-2"></i>
                  内置表单类型的字段配置存储在代码中，如需查看具体配置请联系系统管理员
                </div>
              </div>

              <!-- 自定义表单类型的空状态 -->
              <div v-else>
                <p class="text-muted mb-4">该表单类型还没有字段配置</p>

                <div class="d-flex justify-content-center gap-3">
                  <button
                    class="btn btn-warning"
                    @click="initBasicFields"
                    :disabled="!canInitBasicFields"
                  >
                    <i class="bi bi-magic me-1"></i>快速初始化基础字段
                  </button>
                  <button class="btn btn-primary" @click="showCreateGroupModal">
                    <i class="bi bi-plus-circle me-1"></i>手动创建分组
                  </button>
                </div>

                <div class="alert alert-light mt-4">
                  <h6><i class="bi bi-lightbulb me-2"></i>建议</h6>
                  <p class="mb-2">
                    <strong>快速初始化：</strong>自动创建5个分组和12个公共字段，包含完整的字段库供您选择和修改
                  </p>
                  <p class="mb-0">
                    <strong>手动创建：</strong>从零开始自定义字段分组和字段配置
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能说明 -->
        <div v-else class="card">
          <div class="card-body text-center py-5">
            <i class="bi bi-info-circle text-primary" style="font-size: 3rem;"></i>
            <h4 class="mt-3">表单字段配置功能</h4>
            <p class="text-muted mb-4">
              此功能允许您可视化配置不同表单类型的字段结构，包括字段类型、验证规则、显示顺序等。
              <br>
              <strong>💡 提示：</strong>您可以在"表单模板管理"页面使用一键初始化功能快速创建包含完整字段配置的新表单类型。
            </p>
            
            <div class="row mt-4">
              <div class="col-md-3">
                <div class="feature-item">
                  <i class="bi bi-gear-fill text-primary" style="font-size: 2rem;"></i>
                  <h6 class="mt-2">字段配置</h6>
                  <p class="small text-muted">灵活配置字段类型、验证规则和显示属性</p>
                </div>
              </div>
              <div class="col-md-3">
                <div class="feature-item">
                  <i class="bi bi-sliders text-primary" style="font-size: 2rem;"></i>
                  <h6 class="mt-2">可视化配置</h6>
                  <p class="small text-muted">直观的字段编辑器，拖拽式配置</p>
                </div>
              </div>
              <div class="col-md-3">
                <div class="feature-item">
                  <i class="bi bi-eye text-success" style="font-size: 2rem;"></i>
                  <h6 class="mt-2">实时预览</h6>
                  <p class="small text-muted">实时预览表单效果，所见即所得</p>
                </div>
              </div>
              <div class="col-md-3">
                <div class="feature-item">
                  <i class="bi bi-magic text-info" style="font-size: 2rem;"></i>
                  <h6 class="mt-2">公共字段</h6>
                  <p class="small text-muted">复用标准字段，提高配置效率</p>
                </div>
              </div>
            </div>

            <div class="alert alert-success mt-4">
              <i class="bi bi-check-circle me-2"></i>
              <strong>功能状态：</strong>
              ✅ 公共字段复用 ✅ 可视化配置 ✅ 实时预览 ✅ 配置导入导出 ✅ 字段验证规则
            </div>

            <div class="alert alert-info mt-3">
              <i class="bi bi-lightbulb me-2"></i>
              <strong>快速开始：</strong>
              前往"表单模板管理"页面，使用"一键初始化表单"功能快速创建包含完整字段配置的新表单类型，然后回到此页面进行详细配置。
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Toast通知 -->
    <toast-notification ref="toast" />

    <!-- 字段分组编辑模态框 -->
    <field-group-modal
      ref="fieldGroupModal"
      :form-type="selectedFormType"
      @saved="onGroupSaved"
      @show-toast="showToast"
    />

    <!-- 字段配置编辑模态框 -->
    <field-config-modal
      ref="fieldConfigModal"
      :form-type="selectedFormType"
      :field-types="fieldTypes"
      :field-groups="fieldGroups"
      @saved="onFieldSaved"
      @show-toast="showToast"
    />

    <!-- 表单预览模态框 -->
    <form-preview-modal
      ref="formPreviewModal"
      :form-type="selectedFormType"
      :field-groups="fieldGroups"
      :field-configs="fieldConfigs"
      @show-toast="showToast"
    />

    <!-- 配置导入导出模态框 -->
    <config-import-export-modal
      ref="configImportExportModal"
      :form-type="selectedFormType"
      :field-groups="fieldGroups"
      :field-configs="fieldConfigs"
      @show-toast="showToast"
      @config-imported="onConfigImported"
    />
  </div>
</template>

<script>
import axios from 'axios'
import ToastNotification from '@/components/common/ToastNotification.vue'
import FieldGroupModal from '@/components/modals/FieldGroupModal.vue'
import FieldConfigModal from '@/components/modals/FieldConfigModal.vue'
import FormPreviewModal from '@/components/modals/FormPreviewModal.vue'
import ConfigImportExportModal from '@/components/modals/ConfigImportExportModal.vue'
import { isCommonField as checkIsCommonField } from '@/utils/commonFieldManager.js'

export default {
  name: 'FormFieldConfig',
  components: {
    ToastNotification,
    FieldGroupModal,
    FieldConfigModal,
    FormPreviewModal,
    ConfigImportExportModal
  },
  data() {
    return {
      selectedFormType: '',
      formTypes: [],
      fieldGroups: [],
      fieldConfigs: [],
      fieldTypes: [],
      loading: false,
      fromUrl: false // 标记是否从URL参数来的
    }
  },
  computed: {
    isBuiltInFormType() {
      if (!this.selectedFormType) return false
      const formType = this.formTypes.find(ft => ft.name === this.selectedFormType)
      return formType ? formType.is_built_in : false
    },
    selectedFormTypeInfo() {
      return this.formTypes.find(ft => ft.name === this.selectedFormType) || null
    },
    canInitBasicFields() {
      // 只有选择了表单类型、不是内置类型、且没有任何配置时才能初始化
      return this.selectedFormType &&
             !this.isBuiltInFormType &&
             this.fieldGroups.length === 0 &&
             this.fieldConfigs.length === 0
    },
    getInitBasicFieldsTooltip() {
      if (!this.selectedFormType) {
        return '请先选择表单类型'
      }
      if (this.isBuiltInFormType) {
        return '内置表单类型不能初始化基础字段'
      }
      if (this.fieldGroups.length > 0 || this.fieldConfigs.length > 0) {
        return '该表单类型已有配置，不能重复初始化'
      }
      return '为该表单类型初始化基础字段配置（基本信息、客户信息、访问信息、服务器信息、维护记录）'
    }
  },
  async mounted() {
    // 页面加载时先清理遮罩
    this.forceCleanAllBackdrops()
    await this.loadFormTypes()
    await this.loadFieldTypes()

    // 处理URL参数，自动选择表单类型
    this.handleUrlParams()
  },
  methods: {
    async loadFormTypes() {
      try {
        const response = await axios.get('/excel/form-types')
        if (response.data.status === 'success') {
          this.formTypes = response.data.data
        }
      } catch (error) {
        console.error('加载表单类型失败:', error)
        this.showToast('加载表单类型失败', '错误', 'error')
      }
    },

    async createFormType() {
      const formTypeName = prompt('请输入新表单类型名称:')
      if (!formTypeName || !formTypeName.trim()) {
        return
      }

      try {
        const response = await axios.post('/excel/form-types', {
          name: formTypeName.trim()
        })

        if (response.data.status === 'success') {
          const data = response.data.data
          const defaultGroups = data.default_groups || []
          const message = `表单类型创建成功！已自动添加默认分组：${defaultGroups.join('、')}`
          this.showToast(message, '成功', 'success')
          await this.loadFormTypes()
          this.selectedFormType = formTypeName.trim()
          await this.loadFormConfig()
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        console.error('创建表单类型失败:', error)
        this.showToast(`创建失败: ${error.response && error.response.data && error.response.data.message || error.message}`, '错误', 'error')
      }
    },

    async loadFieldTypes() {
      try {
        const response = await axios.get('/excel/field-types')
        if (response.data.status === 'success') {
          this.fieldTypes = response.data.data
        }
      } catch (error) {
        console.error('加载字段类型失败:', error)
      }
    },

    async loadFormConfig() {
      if (!this.selectedFormType) return
      
      this.loading = true
      try {
        // 加载字段分组
        const groupsResponse = await axios.get(`/excel/form-field-groups/${this.selectedFormType}`)
        if (groupsResponse.data.status === 'success') {
          this.fieldGroups = groupsResponse.data.data
        }

        // 加载字段配置
        const configsResponse = await axios.get(`/excel/form-field-configs/${this.selectedFormType}`)
        if (configsResponse.data.status === 'success') {
          this.fieldConfigs = configsResponse.data.data
        }
      } catch (error) {
        console.error('加载表单配置失败:', error)
        this.showToast('加载表单配置失败', '错误', 'error')
      } finally {
        this.loading = false
      }
    },

    getFieldsByGroup(groupId) {
      return this.fieldConfigs.filter(field => field.group_id === groupId)
    },

    getFieldTypeIcon(fieldType) {
      const fieldTypeDef = this.fieldTypes.find(type => type.type_name === fieldType)
      return fieldTypeDef ? fieldTypeDef.icon_class : 'bi-question-circle'
    },

    isDefaultGroup(group) {
      // 判断是否为默认分组
      const defaultGroupNames = ['basic_info', 'access_info', 'maintenance_records', 'server_info']
      return defaultGroupNames.includes(group.group_name)
    },

    isCommonField(fieldName) {
      // 检查是否为公共字段
      return checkIsCommonField(fieldName)
    },

    forceCleanAllBackdrops() {
      console.log('🧹 强制清理所有模态框遮罩')

      try {
        // 1. 移除所有Bootstrap遮罩
        const backdrops = document.querySelectorAll('.modal-backdrop')
        console.log(`发现 ${backdrops.length} 个遮罩层`)
        backdrops.forEach((backdrop, index) => {
          console.log(`移除遮罩层 ${index + 1}`)
          backdrop.remove()
        })

        // 2. 移除所有模态框的显示状态
        const modals = document.querySelectorAll('.modal.show')
        modals.forEach(modal => {
          if (modal.id !== 'fieldGroupModal' &&
              modal.id !== 'fieldConfigModal' &&
              modal.id !== 'formPreviewModal' &&
              modal.id !== 'configImportExportModal') {
            modal.classList.remove('show')
            modal.style.display = 'none'
            modal.setAttribute('aria-hidden', 'true')
            modal.removeAttribute('aria-modal')
          }
        })

        // 3. 清理Bootstrap实例
        const allModals = document.querySelectorAll('.modal')
        allModals.forEach(modal => {
          if (window.bootstrap && window.bootstrap.Modal) {
            const instance = window.bootstrap.Modal.getInstance(modal)
            if (instance && modal.id !== 'fieldGroupModal' &&
                modal.id !== 'fieldConfigModal' &&
                modal.id !== 'formPreviewModal' &&
                modal.id !== 'configImportExportModal') {
              instance.dispose()
            }
          }
        })

        // 4. 重置body状态
        document.body.classList.remove('modal-open')
        document.body.style.overflow = ''
        document.body.style.paddingRight = ''
        document.body.style.marginRight = ''

        // 5. 强制重置页面滚动
        window.scrollTo(0, window.scrollY)

        console.log('✅ 遮罩清理完成')
      } catch (error) {
        console.error('清理遮罩时出错:', error)
      }
    },

    showCreateFormTypeModal() {
      this.createFormType()
    },

    handleUrlParams() {
      // 处理URL参数，自动选择表单类型
      const urlParams = new URLSearchParams(window.location.search)
      const formType = urlParams.get('formType')

      if (formType) {
        console.log('从URL参数获取表单类型:', formType)
        this.selectedFormType = formType
        this.fromUrl = true
        // 延迟加载配置，确保表单类型列表已加载
        setTimeout(() => {
          this.loadFormConfig()
        }, 500)
      }
    },

    goBack() {
      // 返回到表单模板管理页面
      if (window.history.length > 1) {
        this.$router.go(-1)
      } else {
        this.$router.push('/form-template-manager')
      }
    },



    async initBasicFields() {
      if (!this.canInitBasicFields) {
        return
      }

      // 确认对话框
      const confirmed = confirm(`确定要为表单类型"${this.selectedFormType}"初始化完整字段配置吗？\n\n将会创建以下分组和字段：\n• 基本信息（公司名称、记录日期）\n• 客户信息（客户）\n• 访问信息（平台地址、部署的平台版本）\n• 服务器信息（服务器IP、操作系统、配置、SSH端口）\n• 维护记录（维护日期、维护内容、维护人员）\n\n共计10个字段，您可以根据需要删除或修改不需要的字段。`)

      if (!confirmed) {
        return
      }

      try {
        this.loading = true
        const response = await axios.post(`/excel/form-field-configs/init-basic-fields/${this.selectedFormType}`)

        if (response.data.status === 'success') {
          const data = response.data.data
          this.showToast(
            `基础字段配置初始化成功！创建了 ${data.groups_created} 个分组和 ${data.fields_created} 个字段`,
            '成功',
            'success'
          )

          // 重新加载配置
          await this.loadFormConfig()
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        console.error('初始化基础字段失败:', error)
        this.showToast(
          `初始化失败: ${error.response?.data?.message || error.message}`,
          '错误',
          'error'
        )
      } finally {
        this.loading = false
      }
    },

    showCreateGroupModal() {
      // 检查是否为内置表单类型
      const builtInFormTypes = ['安全测评', '安全监测', '应用加固']
      if (builtInFormTypes.includes(this.selectedFormType)) {
        this.showToast('内置表单类型不能添加新分组', '提示', 'warning')
        return
      }

      // 强制清理所有遮罩
      this.forceCleanAllBackdrops()
      this.$refs.fieldGroupModal.show()
    },

    showCreateFieldModal() {
      // 检查是否为内置表单类型
      const builtInFormTypes = ['安全测评', '安全监测', '应用加固']
      if (builtInFormTypes.includes(this.selectedFormType)) {
        this.showToast('内置表单类型不能添加新字段', '提示', 'warning')
        return
      }

      // 强制清理所有遮罩
      this.forceCleanAllBackdrops()
      this.$refs.fieldConfigModal.show()
    },

    editGroup(group) {
      // 检查是否为内置表单类型
      const builtInFormTypes = ['安全测评', '安全监测', '应用加固']
      if (builtInFormTypes.includes(group.form_type)) {
        this.showToast('内置表单类型的分组不能编辑', '提示', 'warning')
        return
      }

      this.$refs.fieldGroupModal.show(group)
    },

    async deleteGroup(group) {
      // 检查是否为内置表单类型
      const builtInFormTypes = ['安全测评', '安全监测', '应用加固']
      if (builtInFormTypes.includes(group.form_type)) {
        this.showToast('内置表单类型的分组不能删除', '提示', 'warning')
        return
      }

      if (!confirm(`确定要删除分组"${group.group_label}"吗？`)) {
        return
      }

      try {
        const response = await axios.delete(`/excel/form-field-groups/${group.id}`)
        if (response.data.status === 'success') {
          this.showToast('分组删除成功', '成功', 'success')
          await this.loadFormConfig()
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        console.error('删除分组失败:', error)
        this.showToast(`删除失败: ${error.response && error.response.data && error.response.data.message || error.message}`, '错误', 'error')
      }
    },

    editField(field) {
      // 检查是否为内置表单类型
      const builtInFormTypes = ['安全测评', '安全监测', '应用加固']
      if (builtInFormTypes.includes(field.form_type)) {
        this.showToast('内置表单类型的字段不能编辑', '提示', 'warning')
        return
      }

      this.$refs.fieldConfigModal.show(field)
    },

    async deleteField(field) {
      // 检查是否为内置表单类型
      const builtInFormTypes = ['安全测评', '安全监测', '应用加固']
      if (builtInFormTypes.includes(field.form_type)) {
        this.showToast('内置表单类型的字段不能删除', '提示', 'warning')
        return
      }

      if (!confirm(`确定要删除字段"${field.field_label}"吗？`)) {
        return
      }

      try {
        const response = await axios.delete(`/excel/form-field-configs/${field.id}`)
        if (response.data.status === 'success') {
          this.showToast('字段删除成功', '成功', 'success')
          await this.loadFormConfig()
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        console.error('删除字段失败:', error)
        this.showToast(`删除失败: ${error.response && error.response.data && error.response.data.message || error.message}`, '错误', 'error')
      }
    },

    async onGroupSaved() {
      await this.loadFormConfig()
    },

    async onFieldSaved() {
      await this.loadFormConfig()
    },

    previewForm() {
      if (!this.selectedFormType) {
        this.showToast('请先选择表单类型', '提示', 'warning')
        return
      }

      // 强制清理所有遮罩
      this.forceCleanAllBackdrops()
      this.$refs.formPreviewModal.show()
    },

    exportConfig() {
      if (!this.selectedFormType) {
        this.showToast('请先选择表单类型', '提示', 'warning')
        return
      }

      this.$refs.configImportExportModal.show()
    },

    onConfigImported() {
      this.loadFormConfig()
    },

    showToast(message, title = '提示', type = 'info') {
      if (this.$refs.toast) {
        this.$refs.toast.showToast(message, title, type)
      }
    }
  }
}
</script>

<style scoped>
.field-group {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.group-header {
  border-bottom: 1px solid #e9ecef;
}

.field-item {
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.field-item:hover {
  background-color: #e9ecef;
  transform: translateX(5px);
}

.field-item.bg-light {
  background-color: #f8f9fa !important;
  opacity: 0.8;
}

.field-item.bg-light:hover {
  background-color: #f1f3f4 !important;
  transform: none;
}

.feature-item {
  text-align: center;
  padding: 1rem;
}

.badge {
  font-size: 0.75em;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* 禁用状态的按钮样式 */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-outline-secondary:disabled {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
}

/* 内置表单类型的特殊样式 */
.text-muted {
  color: #6c757d !important;
}

.bg-light.text-muted {
  background-color: #f8f9fa !important;
}

/* 锁定图标的样式 */
.bi-lock-fill {
  font-size: 0.8em;
  opacity: 0.7;
}

/* 选择框中内置选项的样式 */
option.text-muted {
  color: #6c757d;
  font-style: italic;
}

/* 提示信息样式 */
.alert-info {
  border-left: 4px solid #0dcaf0;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

/* 全局模态框修复样式 */
:deep(.modal) {
  z-index: 1060 !important;
}

:deep(.modal-backdrop) {
  z-index: 1055 !important;
}

:deep(.modal.show) {
  display: block !important;
}

:deep(.modal-dialog) {
  pointer-events: auto;
}

:deep(.modal-content) {
  pointer-events: auto;
}
</style>
