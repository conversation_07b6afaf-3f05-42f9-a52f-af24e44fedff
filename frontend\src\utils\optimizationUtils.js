/**
 * 前端性能优化工具函数
 * 提供各种性能优化相关的工具方法
 */

/**
 * 防抖函数 - 优化版本
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @param {boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数 - 优化版本
 * @param {Function} func 要节流的函数
 * @param {number} limit 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit = 100) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深度比较对象是否相等（用于避免不必要的重渲染）
 * @param {any} obj1 对象1
 * @param {any} obj2 对象2
 * @returns {boolean} 是否相等
 */
export function deepEqual(obj1, obj2) {
  if (obj1 === obj2) return true
  
  if (obj1 == null || obj2 == null) return false
  
  if (typeof obj1 !== typeof obj2) return false
  
  if (typeof obj1 !== 'object') return obj1 === obj2
  
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)
  
  if (keys1.length !== keys2.length) return false
  
  for (let key of keys1) {
    if (!keys2.includes(key)) return false
    if (!deepEqual(obj1[key], obj2[key])) return false
  }
  
  return true
}

/**
 * 浅比较对象（性能更好的比较方式）
 * @param {Object} obj1 对象1
 * @param {Object} obj2 对象2
 * @returns {boolean} 是否相等
 */
export function shallowEqual(obj1, obj2) {
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)
  
  if (keys1.length !== keys2.length) return false
  
  for (let key of keys1) {
    if (obj1[key] !== obj2[key]) return false
  }
  
  return true
}

/**
 * 创建缓存函数（记忆化）
 * @param {Function} fn 要缓存的函数
 * @param {Function} keyGenerator 键生成函数
 * @returns {Function} 缓存后的函数
 */
export function memoize(fn, keyGenerator = (...args) => JSON.stringify(args)) {
  const cache = new Map()
  
  return function memoized(...args) {
    const key = keyGenerator(...args)
    
    if (cache.has(key)) {
      return cache.get(key)
    }
    
    const result = fn.apply(this, args)
    cache.set(key, result)
    
    // 限制缓存大小，避免内存泄漏
    if (cache.size > 100) {
      const firstKey = cache.keys().next().value
      cache.delete(firstKey)
    }
    
    return result
  }
}

/**
 * 批量DOM更新（减少重排重绘）
 * @param {Function} updateFn 更新函数
 */
export function batchDOMUpdate(updateFn) {
  requestAnimationFrame(() => {
    updateFn()
  })
}

/**
 * 虚拟滚动计算可见项目
 * @param {number} scrollTop 滚动位置
 * @param {number} itemHeight 项目高度
 * @param {number} containerHeight 容器高度
 * @param {number} totalItems 总项目数
 * @param {number} buffer 缓冲区大小
 * @returns {Object} 可见项目信息
 */
export function calculateVisibleItems(scrollTop, itemHeight, containerHeight, totalItems, buffer = 5) {
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer)
  const endIndex = Math.min(
    totalItems - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + buffer
  )
  
  return {
    startIndex,
    endIndex,
    visibleItems: endIndex - startIndex + 1,
    offsetY: startIndex * itemHeight
  }
}

/**
 * 图片懒加载观察器
 * @param {HTMLElement} target 目标元素
 * @param {Function} callback 回调函数
 * @param {Object} options 选项
 * @returns {IntersectionObserver} 观察器实例
 */
export function createLazyLoadObserver(callback, options = {}) {
  const defaultOptions = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1
  }
  
  const observerOptions = { ...defaultOptions, ...options }
  
  return new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        callback(entry.target)
      }
    })
  }, observerOptions)
}

/**
 * 检测设备性能等级
 * @returns {string} 性能等级：'high', 'medium', 'low'
 */
export function detectDevicePerformance() {
  const memory = navigator.deviceMemory || 4
  const cores = navigator.hardwareConcurrency || 4
  const connection = navigator.connection
  
  let score = 0
  
  // 内存评分
  if (memory >= 8) score += 3
  else if (memory >= 4) score += 2
  else score += 1
  
  // CPU核心评分
  if (cores >= 8) score += 3
  else if (cores >= 4) score += 2
  else score += 1
  
  // 网络连接评分
  if (connection) {
    if (connection.effectiveType === '4g') score += 2
    else if (connection.effectiveType === '3g') score += 1
  } else {
    score += 1 // 默认中等
  }
  
  if (score >= 7) return 'high'
  if (score >= 4) return 'medium'
  return 'low'
}

/**
 * 根据设备性能调整配置
 * @param {string} performance 性能等级
 * @returns {Object} 配置对象
 */
export function getPerformanceConfig(performance = detectDevicePerformance()) {
  const configs = {
    high: {
      animationDuration: 300,
      debounceDelay: 200,
      virtualScrollBuffer: 10,
      enableTransitions: true,
      enableShadows: true
    },
    medium: {
      animationDuration: 200,
      debounceDelay: 300,
      virtualScrollBuffer: 5,
      enableTransitions: true,
      enableShadows: false
    },
    low: {
      animationDuration: 100,
      debounceDelay: 500,
      virtualScrollBuffer: 3,
      enableTransitions: false,
      enableShadows: false
    }
  }
  
  return configs[performance] || configs.medium
}

/**
 * 优化的事件监听器管理
 */
export class EventManager {
  constructor() {
    this.listeners = new Map()
  }
  
  add(element, event, handler, options = {}) {
    const key = `${element}_${event}`
    const optimizedHandler = options.throttle 
      ? throttle(handler, options.throttle)
      : options.debounce 
        ? debounce(handler, options.debounce)
        : handler
    
    element.addEventListener(event, optimizedHandler, options)
    this.listeners.set(key, { element, event, handler: optimizedHandler, options })
  }
  
  remove(element, event) {
    const key = `${element}_${event}`
    const listener = this.listeners.get(key)
    
    if (listener) {
      listener.element.removeEventListener(listener.event, listener.handler, listener.options)
      this.listeners.delete(key)
    }
  }
  
  removeAll() {
    this.listeners.forEach(listener => {
      listener.element.removeEventListener(listener.event, listener.handler, listener.options)
    })
    this.listeners.clear()
  }
}

/**
 * 资源预加载管理
 */
export class ResourcePreloader {
  constructor() {
    this.cache = new Map()
  }
  
  preloadImage(src) {
    if (this.cache.has(src)) {
      return this.cache.get(src)
    }
    
    const promise = new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve(img)
      img.onerror = reject
      img.src = src
    })
    
    this.cache.set(src, promise)
    return promise
  }
  
  preloadScript(src) {
    if (this.cache.has(src)) {
      return this.cache.get(src)
    }
    
    const promise = new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.onload = resolve
      script.onerror = reject
      script.src = src
      document.head.appendChild(script)
    })
    
    this.cache.set(src, promise)
    return promise
  }
}
