# 🔌 API 接口文档

## 📋 概述

本文档描述了梆梆安全-运维信息登记平台的后端API接口。所有API接口都基于RESTful设计原则。

## 🔐 认证方式

### JWT Token认证
```http
Authorization: Bearer <your_jwt_token>
```

### 获取Token
```http
POST /auth/login
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}
```

## 📊 响应格式

### 成功响应
```json
{
    "success": true,
    "data": {},
    "message": "操作成功"
}
```

### 错误响应
```json
{
    "success": false,
    "error": "错误信息",
    "code": "ERROR_CODE"
}
```

## 🔗 API接口列表

### 🔐 认证相关

#### 用户登录
```http
POST /auth/login
```
**请求参数:**
```json
{
    "username": "string",
    "password": "string"
}
```

#### 用户注册
```http
POST /auth/register
```
**请求参数:**
```json
{
    "username": "string",
    "password": "string",
    "email": "string"
}
```

#### 刷新Token
```http
POST /auth/refresh
```

#### 用户登出
```http
POST /auth/logout
```

### 👥 用户管理

#### 获取用户列表
```http
GET /users
```
**查询参数:**
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 10)
- `search`: 搜索关键词

#### 获取用户详情
```http
GET /users/{user_id}
```

#### 创建用户
```http
POST /users
```
**请求参数:**
```json
{
    "username": "string",
    "email": "string",
    "password": "string",
    "role_ids": [1, 2],
    "group_ids": [1, 2]
}
```

#### 更新用户
```http
PUT /users/{user_id}
```

#### 删除用户
```http
DELETE /users/{user_id}
```

### 🎭 角色管理

#### 获取角色列表
```http
GET /roles
```

#### 创建角色
```http
POST /roles
```
**请求参数:**
```json
{
    "name": "string",
    "description": "string",
    "permission_ids": [1, 2, 3]
}
```

#### 更新角色
```http
PUT /roles/{role_id}
```

#### 删除角色
```http
DELETE /roles/{role_id}
```

### 🏢 用户组管理

#### 获取用户组列表
```http
GET /groups
```

#### 创建用户组
```http
POST /groups
```

#### 更新用户组
```http
PUT /groups/{group_id}
```

#### 删除用户组
```http
DELETE /groups/{group_id}
```

### 📝 表单管理

#### 提交表单
```http
POST /excel/submit
```
**请求参数:**
```json
{
    "form_type": "安全测评|安全监测|应用加固",
    "form_data": {},
    "components": []
}
```

#### 获取表单历史
```http
GET /excel/history
```
**查询参数:**
- `page`: 页码
- `per_page`: 每页数量
- `search`: 搜索关键词
- `form_type`: 表单类型

#### 下载Excel文件
```http
GET /excel/download/{file_id}
```

#### 编辑表单
```http
PUT /excel/edit/{file_id}
```

#### 删除表单记录
```http
DELETE /excel/delete/{file_id}
```

### 🧩 组件管理

#### 获取组件分类
```http
GET /excel/component-categories
```
**查询参数:**
- `form_type`: 表单类型

#### 创建组件分类
```http
POST /excel/component-categories
```
**请求参数:**
```json
{
    "key": "string",
    "display_name": "string",
    "icon": "string",
    "color": "string",
    "form_types": ["安全测评"],
    "order": 1
}
```

#### 获取组件列表
```http
GET /excel/components
```
**查询参数:**
- `form_type`: 表单类型
- `category_key`: 分类键名

#### 创建组件
```http
POST /excel/components
```
**请求参数:**
```json
{
    "name": "string",
    "category_key": "string",
    "default_port": "string",
    "description": "string",
    "form_types": ["安全测评"]
}
```

#### 更新组件
```http
PUT /excel/components/{component_id}
```

#### 删除组件
```http
DELETE /excel/components/{component_id}
```

### 📊 系统管理

#### 获取系统统计
```http
GET /system/stats
```

#### 清理Excel文件
```http
POST /system/cleanup-excel
```

#### 系统诊断
```http
GET /system/diagnose
```

## 📈 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 🔍 错误代码

| 错误代码 | 说明 |
|----------|------|
| AUTH_FAILED | 认证失败 |
| PERMISSION_DENIED | 权限不足 |
| VALIDATION_ERROR | 参数验证失败 |
| RESOURCE_NOT_FOUND | 资源不存在 |
| DUPLICATE_ENTRY | 重复条目 |
| SERVER_ERROR | 服务器错误 |

## 📝 使用示例

### JavaScript (Axios)
```javascript
// 登录
const response = await axios.post('/auth/login', {
    username: 'admin',
    password: 'password'
});

// 设置Token
axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.data.token}`;

// 获取用户列表
const users = await axios.get('/users');
```

### Python (Requests)
```python
import requests

# 登录
response = requests.post('/auth/login', json={
    'username': 'admin',
    'password': 'password'
})

token = response.json()['data']['token']

# 设置Headers
headers = {'Authorization': f'Bearer {token}'}

# 获取用户列表
users = requests.get('/users', headers=headers)
```

### cURL
```bash
# 登录
curl -X POST /auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# 获取用户列表
curl -X GET /users \
  -H "Authorization: Bearer <your_token>"
```

## 📚 相关文档

- [数据库配置](database-setup.md)
- [配置说明](configuration.md)
- [故障排除](troubleshooting.md)
