import axios from 'axios'

// 根据环境设置基础URL
const baseURL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')  // 生产环境使用相对路径，通过nginx代理
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')  // 开发环境直连后端

axios.defaults.baseURL = baseURL

console.log('API baseURL:', baseURL, 'Environment:', process.env.NODE_ENV)

/**
 * Excel相关API服务
 * 提供与后端API交互的方法
 */
export default {
  /**
   * 获取表单类型列表
   * @returns {Promise} 返回表单类型列表
   */
  getFormTypes() {
    return axios.get('/excel/form-types')
  },

  /**
   * 下载Excel文件
   * @param {Number} fileId - 文件ID
   * @returns {Promise} 返回下载链接
   */
  downloadFile(fileId) {
    return axios.get(`/excel/download/${fileId}`, { responseType: 'blob' })
  },

  /**
   * 填写并生成Excel表格
   * @param {Object} data - 表单数据
   * @returns {Promise} 返回生成结果
   */
  fillSheet(data) {
    return axios.post('/excel/fill_sheet', data)
  },

  /**
   * 获取所有可用的模板文件
   * @returns {Promise} 返回模板列表
   */
  getTemplates() {
    return axios.get('/excel/templates')
  },

  /**
   * 根据表单类型获取可用模板
   * @param {String} formType - 表单类型
   * @returns {Promise} 返回指定表单类型的模板列表
   */
  getTemplatesByFormType(formType) {
    // 确保中文正确编码
    const encodedFormType = encodeURIComponent(formType)
    return axios.get(`/excel/templates/by-form-type/${encodedFormType}`)
  },

  /**
   * 下载模板文件
   * @param {String} filename - 模板文件名
   * @returns {Promise} 返回文件内容
   */
  downloadTemplate(filename) {
    return axios.get(`/excel/templates/download/${filename}`, { responseType: 'blob' })
  },

  /**
   * 上传模板文件
   * @param {FormData} formData - 包含文件和类型的表单数据
   * @returns {Promise} 返回上传结果
   */
  uploadTemplate(formData) {
    return axios.post('/excel/templates/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 激活模板版本
   * @param {Number} id - 要激活的模板ID
   * @param {String} formType - 可选，指定要关联的表单类型
   * @returns {Promise} 返回激活结果
   */
  activateTemplate(id, formType) {
    return axios.post('/excel/templates/activate', { id, formType })
  },

  /**
   * 设置模板别名
   * @param {String} filename - 模板文件名
   * @param {String} alias - 模板别名
   * @returns {Promise} 返回设置结果
   */
  setTemplateAlias(filename, alias) {
    return axios.post('/excel/templates/set_alias', { filename, alias })
  },

  /**
   * 删除模板
   * @param {Number} id - 要删除的模板ID
   * @returns {Promise} 返回删除结果
   */
  deleteTemplate(id) {
    return axios.post('/excel/templates/delete', { id })
  },

  // ==================== Excel导入相关API ====================

  /**
   * 预览Excel导入数据
   * @param {FormData} formData - 包含文件和表单类型的FormData
   * @returns {Promise} 返回预览数据
   */
  previewExcelImport(formData) {
    return axios.post('/excel/import_excel/preview', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 确认Excel导入
   * @param {Object} data - 导入确认数据
   * @returns {Promise} 返回导入结果
   */
  confirmExcelImport(data) {
    return axios.post('/excel/import_excel/confirm', data)
  },

  /**
   * 获取Excel导入模板信息
   * @returns {Promise} 返回模板信息
   */
  getImportTemplates() {
    return axios.get('/excel/import_excel/templates')
  }
}
