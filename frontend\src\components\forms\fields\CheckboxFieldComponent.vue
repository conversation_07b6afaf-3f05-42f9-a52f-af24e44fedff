<template>
  <div class="checkbox-field-component">
    <div 
      v-for="(label, optionValue) in options" 
      :key="optionValue"
      class="form-check"
    >
      <input 
        :id="`${id}_${optionValue}`"
        type="checkbox" 
        :value="optionValue"
        :checked="isChecked(optionValue)"
        :disabled="field.is_readonly"
        class="form-check-input"
        @change="handleChange"
      >
      <label :for="`${id}_${optionValue}`" class="form-check-label">
        {{ label }}
      </label>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CheckboxFieldComponent',
  props: {
    id: String,
    field: {
      type: Object,
      required: true
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:value', 'field-change'],
  computed: {
    options() {
      return this.field.field_options || {}
    }
  },
  methods: {
    isChecked(optionValue) {
      return this.value.includes(optionValue)
    },
    
    handleChange(event) {
      const optionValue = event.target.value
      let newValue = [...this.value]
      
      if (event.target.checked) {
        if (!newValue.includes(optionValue)) {
          newValue.push(optionValue)
        }
      } else {
        newValue = newValue.filter(v => v !== optionValue)
      }
      
      this.$emit('update:value', newValue)
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: newValue,
        event: 'change'
      })
    }
  }
}
</script>

<style scoped>
.form-check {
  margin-bottom: 0.5rem;
}

.form-check:last-child {
  margin-bottom: 0;
}
</style>
