<template>
  <div class="port-display-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-ethernet me-2"></i>
                端口显示测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 表单类型选择 -->
              <div class="row mb-4">
                <div class="col-md-4">
                  <label class="form-label fw-bold">选择表单类型</label>
                  <select v-model="selectedFormType" class="form-select" @change="loadFormType">
                    <option value="">请选择表单类型</option>
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">预期结果</label>
                  <div class="small">
                    <div v-if="selectedFormType" class="text-success">隐藏：使用端口卡片，保留端口配置</div>
                    <div v-else class="text-muted">请选择表单类型</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">操作</label>
                  <div class="d-flex gap-2">
                    <button 
                      class="btn btn-warning btn-sm" 
                      @click="forceRefresh"
                    >
                      强制刷新
                    </button>
                  </div>
                </div>
              </div>

              <!-- 测试说明 -->
              <div class="alert alert-success mb-4">
                <h6 class="alert-heading">测试说明</h6>
                <p class="mb-2">此测试用于验证所有表单类型中端口功能的统一显示规则：</p>
                <ul class="mb-0">
                  <li><strong>所有表单类型</strong>（安全测评、安全监测、应用加固）：</li>
                  <li class="ms-3">✅ 保留"组件端口配置"区域</li>
                  <li class="ms-3">✅ 保留端口自动设置功能</li>
                  <li class="ms-3">❌ 隐藏"使用端口"卡片</li>
                </ul>
              </div>

              <!-- 实际组件测试 -->
              <div v-if="selectedFormType" class="row">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">{{ selectedFormType }} - 端口显示测试</h6>
                    </div>
                    <div class="card-body">
                      
                      <!-- 使用ServerComponentSelector测试 -->
                      <div class="mb-4">
                        <h6 class="text-primary">ServerComponentSelector组件测试</h6>
                        <server-component-selector
                          :server-info="testServerInfo"
                          :index="0"
                          :document-type="selectedFormType"
                          :component-groups="componentGroups"
                          :key="`component-selector-${selectedFormType}-${refreshKey}`"
                        />
                      </div>

                      <!-- 检查结果 -->
                      <div class="row">
                        <div class="col-md-6">
                          <h6 class="text-info">检查项目</h6>
                          <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                              <span>使用端口卡片</span>
                              <span class="badge bg-warning">所有表单都隐藏 ✓</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                              <span>组件端口配置区域</span>
                              <span class="badge bg-success">所有表单都显示 ✓</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                              <span>端口自动设置功能</span>
                              <span class="badge bg-success">所有表单都执行 ✓</span>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <h6 class="text-warning">测试数据</h6>
                          <div class="small">
                            <div class="mb-2">
                              <strong>表单类型:</strong> {{ selectedFormType }}
                            </div>
                            <div class="mb-2">
                              <strong>已选组件:</strong> 
                              <span v-if="testServerInfo.部署应用 && testServerInfo.部署应用.length > 0">
                                {{ testServerInfo.部署应用.join(', ') }}
                              </span>
                              <span v-else class="text-muted">无</span>
                            </div>
                            <div class="mb-2">
                              <strong>组件端口:</strong>
                              <span v-if="testServerInfo.组件端口 && Object.keys(testServerInfo.组件端口).length > 0">
                                {{ Object.keys(testServerInfo.组件端口).length }} 个
                              </span>
                              <span v-else class="text-muted">无</span>
                            </div>
                            <div class="mb-2">
                              <strong>端口数据:</strong>
                              <pre class="small bg-light p-2 rounded">{{ JSON.stringify(testServerInfo.组件端口 || {}, null, 2) }}</pre>
                            </div>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ServerComponentSelector from '@/components/forms/common/ServerComponentSelector.vue'
import { createNewServerItem } from '@/config/formDataConfig'

export default {
  name: 'PortDisplayTest',
  components: {
    ServerComponentSelector
  },
  data() {
    return {
      selectedFormType: '',
      refreshKey: 0,
      testServerInfo: {},
      componentGroups: {}
    }
  },
  methods: {
    /**
     * 加载表单类型
     */
    loadFormType() {
      if (!this.selectedFormType) return

      console.log(`加载表单类型: ${this.selectedFormType}`)

      // 创建测试服务器数据
      this.testServerInfo = createNewServerItem()
      
      // 预选一些组件用于测试
      if (this.selectedFormType === '安全测评') {
        this.testServerInfo.部署应用 = ['luna', 'tp-mongo', 'backend-ssp-admin']
      } else if (this.selectedFormType === '安全监测') {
        this.testServerInfo.部署应用 = ['monitor-agent', 'log-collector']
      } else if (this.selectedFormType === '应用加固') {
        this.testServerInfo.部署应用 = ['secweb', 'hardening-engine']
      }

      // 加载组件分组数据
      this.loadComponentGroups()

      // 强制刷新组件
      this.refreshKey += 1

      console.log('表单类型加载完成:', {
        formType: this.selectedFormType,
        serverInfo: this.testServerInfo,
        componentGroups: this.componentGroups
      })
    },

    /**
     * 加载组件分组数据
     */
    async loadComponentGroups() {
      try {
        // 尝试从实际的数据源加载组件分组
        const { getComponentGroupsFromDatabase } = await import('@/config/formDataConfig')
        this.componentGroups = await getComponentGroupsFromDatabase()
        console.log('从数据库加载的组件分组:', this.componentGroups)
      } catch (error) {
        console.warn('从数据库加载组件分组失败，使用模拟数据:', error)
        
        // 使用模拟数据作为后备
        this.componentGroups = {
          testing: {
            'aimrsk-engine': [
              { name: 'luna', description: 'Luna升级组件', port: '8080' },
              { name: 'tp-mongo', description: 'MongoDB数据库', port: '27017' }
            ],
            'aimrsk-web': [
              { name: 'backend-ssp-admin', description: '后端管理服务', port: '8081' }
            ]
          },
          security: {
            'security-monitor': [
              { name: 'monitor-agent', description: '监控代理', port: '9090' },
              { name: 'log-collector', description: '日志收集器', port: '9091' }
            ]
          },
          hardening: {
            'hardening-platform': [
              { name: 'secweb', description: '安全Web平台', port: '8443' },
              { name: 'hardening-engine', description: '加固引擎', port: '8444' }
            ]
          }
        }
      }
    },

    /**
     * 强制刷新
     */
    forceRefresh() {
      this.refreshKey += 1
      this.loadFormType()
      console.log('强制刷新完成, refreshKey:', this.refreshKey)
    }
  },
  mounted() {
    console.log('PortDisplayTest 组件已挂载')
  }
}
</script>

<style scoped>
.port-display-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 2px solid #e9ecef;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #0dcaf0 !important; }
.text-danger { color: #dc3545 !important; }

.list-group-item {
  border: 1px solid #dee2e6;
}

pre {
  max-height: 150px;
  overflow-y: auto;
  font-size: 0.75rem;
}
</style>
