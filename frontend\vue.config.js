module.exports = {
  // 开发服务器配置（仅在开发环境使用）
  devServer: {
    proxy: {
      '/api': {
        target: process.env.VUE_APP_API_URL || 'http://127.0.0.1:5000',
        changeOrigin: true,
        secure: false,
        logLevel: 'debug',
        pathRewrite: {
          '^/api': ''
        }
      },
      '/excel/': {
        target: process.env.VUE_APP_API_URL || 'http://127.0.0.1:5000',
        changeOrigin: true,
        secure: false,
        logLevel: 'debug'
      }
    }
  },

  // 生产环境构建配置
  publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
  // 禁用ESLint
  lintOnSave: false,

  // 配置 Vue 特性标志
  chainWebpack: config => {
    config.plugin('define').tap(definitions => {
      Object.assign(definitions[0], {
        __VUE_OPTIONS_API__: 'true',
        __VUE_PROD_DEVTOOLS__: 'false',
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false'
      })
      return definitions
    })
  }
}
