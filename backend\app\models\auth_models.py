from datetime import datetime, timezone, timedelta
from app import db
from flask_bcrypt import Bcrypt
from flask_jwt_extended import create_access_token
import json

bcrypt = Bcrypt()

# 定义北京时区
BEIJING_TZ = timezone(timedelta(hours=8))

def get_beijing_time():
    """获取北京时间"""
    return datetime.now(BEIJING_TZ)

# 导入缓存管理器（延迟导入避免循环依赖）
def get_user_cache_manager():
    from app.utils.cache_utils import UserCacheManager
    return UserCacheManager

# 用户-角色关联表
user_roles = db.Table('user_roles',
    db.Column('user_id', db.Integer, db.<PERSON>('user.id'), primary_key=True),
    db.Column('role_id', db.Integer, db.<PERSON>ey('role.id'), primary_key=True)
)

# 角色-权限关联表
role_permissions = db.Table('role_permissions',
    db.<PERSON>umn('role_id', db.<PERSON>te<PERSON>, db.<PERSON>('role.id'), primary_key=True),
    db.<PERSON>n('permission_id', db.Integer, db.<PERSON>('permission.id'), primary_key=True)
)

# 用户-用户组关联表
user_groups = db.Table('user_groups',
    db.Column('user_id', db.Integer, db.ForeignKey('user.id'), primary_key=True),
    db.Column('group_id', db.Integer, db.ForeignKey('user_group.id'), primary_key=True)
)

class User(db.Model):
    """用户模型"""
    __tablename__ = 'user'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    real_name = db.Column(db.String(100), nullable=True)  # 真实姓名
    phone = db.Column(db.String(20), nullable=True)  # 电话号码
    department = db.Column(db.String(100), nullable=True)  # 部门
    position = db.Column(db.String(100), nullable=True)  # 职位
    
    # 状态字段
    is_active = db.Column(db.Boolean, default=True)  # 是否激活
    is_admin = db.Column(db.Boolean, default=False)  # 是否为管理员
    last_login = db.Column(db.DateTime, nullable=True)  # 最后登录时间
    login_count = db.Column(db.Integer, default=0)  # 登录次数
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)
    
    # 关联关系
    roles = db.relationship('Role', secondary=user_roles, lazy='select',
                           backref=db.backref('users', lazy='select'))
    groups = db.relationship('UserGroup', secondary=user_groups, lazy='select',
                            backref=db.backref('users', lazy='select'))
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
    
    def check_password(self, password):
        """验证密码"""
        return bcrypt.check_password_hash(self.password_hash, password)
    
    def generate_token(self):
        """生成JWT token"""
        return create_access_token(identity=str(self.id))
    
    def has_permission(self, permission_code):
        """检查用户是否有指定权限（带缓存）"""
        # 管理员拥有所有权限
        if self.is_admin:
            return True

        # 尝试从缓存获取用户权限
        cache_manager = get_user_cache_manager()
        cached_permissions = cache_manager.get_user_permissions(self.id)

        if cached_permissions is not None:
            # 缓存命中，直接检查权限
            return permission_code in cached_permissions

        # 缓存未命中，从数据库查询并缓存结果
        all_permissions = self.get_all_permissions()
        cache_manager.cache_user_permissions(self.id, all_permissions)

        return permission_code in all_permissions
    
    def has_role(self, role_code):
        """检查用户是否有指定角色"""
        return any(role.code == role_code for role in self.roles)
    
    def get_all_permissions(self):
        """获取用户的所有权限"""
        permissions = set()
        
        # 如果是管理员，返回所有权限
        if self.is_admin:
            return [p.code for p in Permission.query.filter_by(is_active=True).all()]
        
        # 从角色获取权限
        for role in self.roles:
            if role.is_active:
                permissions.update([p.code for p in role.permissions if p.is_active])
        
        # 从用户组获取权限
        for group in self.groups:
            if group.is_active:
                for role in group.roles:
                    if role.is_active:
                        permissions.update([p.code for p in role.permissions if p.is_active])
        
        return list(permissions)
    
    def to_dict(self, include_permissions=False):
        """转换为字典（带缓存）"""
        # 尝试从缓存获取用户信息
        cache_manager = get_user_cache_manager()
        cached_info = cache_manager.get_user_info(self.id)

        if cached_info is not None and not include_permissions:
            # 缓存命中且不需要权限信息，直接返回
            return cached_info

        # 构建用户信息
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'phone': self.phone,
            'department': self.department,
            'position': self.position,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'login_count': self.login_count,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'roles': [role.to_dict() for role in self.roles],
            'groups': [group.to_dict() for group in self.groups]
        }

        if include_permissions:
            data['permissions'] = self.get_all_permissions()
        else:
            # 缓存基本用户信息（不包含权限）
            cache_manager.cache_user_info(self.id, data)

        return data


class Role(db.Model):
    """角色模型"""
    __tablename__ = 'role'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False, index=True)  # 角色代码
    name = db.Column(db.String(100), nullable=False)  # 角色名称
    description = db.Column(db.Text, nullable=True)  # 角色描述
    is_active = db.Column(db.Boolean, default=True)  # 是否激活
    is_system = db.Column(db.Boolean, default=False)  # 是否为系统角色（不可删除）
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)
    
    # 关联关系
    permissions = db.relationship('Permission', secondary=role_permissions, lazy='select',
                                 backref=db.backref('roles', lazy='select'))
    
    def has_permission(self, permission_code):
        """检查角色是否有指定权限"""
        if not self.is_active:
            return False
        return any(p.code == permission_code and p.is_active for p in self.permissions)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'is_system': self.is_system,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'permissions': [p.to_dict() for p in self.permissions],
            'user_count': len(self.users)
        }


class Permission(db.Model):
    """权限模型"""
    __tablename__ = 'permission'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(100), unique=True, nullable=False, index=True)  # 权限代码
    name = db.Column(db.String(100), nullable=False)  # 权限名称
    description = db.Column(db.Text, nullable=True)  # 权限描述
    module = db.Column(db.String(50), nullable=False)  # 所属模块
    is_active = db.Column(db.Boolean, default=True)  # 是否激活
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'module': self.module,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


# 用户组-角色关联表（需要在UserGroup类外定义）
group_roles = db.Table('group_roles',
    db.Column('group_id', db.Integer, db.ForeignKey('user_group.id'), primary_key=True),
    db.Column('role_id', db.Integer, db.ForeignKey('role.id'), primary_key=True)
)


class UserGroup(db.Model):
    """用户组模型"""
    __tablename__ = 'user_group'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False, index=True)  # 用户组代码
    name = db.Column(db.String(100), nullable=False)  # 用户组名称
    description = db.Column(db.Text, nullable=True)  # 用户组描述
    is_active = db.Column(db.Boolean, default=True)  # 是否激活

    # 时间戳
    created_at = db.Column(db.DateTime, default=get_beijing_time)
    updated_at = db.Column(db.DateTime, default=get_beijing_time, onupdate=get_beijing_time)

    # 关联关系
    roles = db.relationship('Role', secondary=group_roles, lazy='select',
                           backref=db.backref('groups', lazy='select'))
    # 注意：users关系通过User模型的groups关系的backref自动创建

    def to_dict(self, include_users=False):
        """转换为字典"""
        result = {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'roles': [role.to_dict() for role in self.roles],
            'user_count': len(self.users)
        }

        # 可选择性包含用户列表（避免循环引用和性能问题）
        if include_users:
            result['users'] = [
                {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'real_name': user.real_name,
                    'department': user.department,
                    'position': user.position,
                    'is_active': user.is_active
                } for user in self.users
            ]

        return result
