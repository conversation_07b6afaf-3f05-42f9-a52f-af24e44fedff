/**
 * 数据映射工具
 * 解决表单名称与客户公司信息之间的映射不完整问题
 * 提供统一的数据同步和映射机制
 */

/**
 * 表单字段映射配置
 * 定义不同表单类型中相同含义字段的映射关系
 */
export const FIELD_MAPPING_CONFIG = {
  // 公司名称相关字段映射
  companyName: {
    primary: '公司名称',
    aliases: ['客户', '客户公司名称', 'company_name', 'companyName'],
    description: '客户公司名称的主要字段'
  },
  
  // 客户标识相关字段映射
  customerId: {
    primary: '客户标识',
    aliases: ['客户ID', 'customer_id', 'customerId'],
    description: '客户唯一标识符'
  },
  
  // 版本信息相关字段映射
  version: {
    primary: '部署包版本',
    aliases: ['版本信息', '前端版本', '后端版本', '部署的平台版本', 'platformVersion'],
    description: '系统版本信息'
  },
  
  // 记录日期相关字段映射
  recordDate: {
    primary: '记录日期',
    aliases: ['创建日期', 'record_date', 'recordDate', 'created_at'],
    description: '表单记录日期'
  },
  
  // 编辑人相关字段映射
  editor: {
    primary: '编辑人',
    aliases: ['创建人', '操作人', 'editor', 'created_by'],
    description: '表单编辑人员'
  }
}

/**
 * 表单类型特定的字段映射
 */
export const FORM_TYPE_SPECIFIC_MAPPING = {
  '应用加固': {
    // 应用加固表单特有的映射关系
    companyFields: ['公司名称', '客户'],
    versionFields: ['部署的平台版本'],
    accessFields: ['平台访问地址', '升级平台地址'],
    userConfigFields: ['管理员信息', '升级用户配置']
  },
  
  '安全监测': {
    // 安全监测表单特有的映射关系
    companyFields: ['公司名称'],
    versionFields: ['前端版本', '后端版本'],
    accessFields: ['业务功能页面地址'],
    userConfigFields: ['超级管理员账号', '超级管理员密码', '客户管理员账号', '客户管理员密码']
  },
  
  '安全测评': {
    // 安全测评表单特有的映射关系
    companyFields: ['公司名称'],
    versionFields: ['部署包版本'],
    accessFields: ['管理员页面IP', '用户页面IP', '升级页面IP'],
    userConfigFields: ['超级管理员账号', '超级管理员密码', '管理员账号', '管理员密码']
  }
}

/**
 * 数据映射处理器类
 */
export class DataMappingProcessor {
  constructor(formType = '安全测评') {
    this.formType = formType
    this.mappingConfig = FORM_TYPE_SPECIFIC_MAPPING[formType] || {}
  }

  /**
   * 统一数据映射处理
   * @param {Object} formData - 表单数据
   * @returns {Object} - 映射后的表单数据
   */
  processDataMapping(formData) {
    if (!formData || typeof formData !== 'object') {
      console.warn('DataMappingProcessor: 无效的表单数据')
      return formData
    }

    const processedData = { ...formData }

    // 1. 处理公司名称映射
    this.processCompanyNameMapping(processedData)

    // 2. 处理版本信息映射
    this.processVersionMapping(processedData)

    // 3. 处理字段标准化
    this.processFieldNormalization(processedData)

    // 4. 处理表单类型特定映射
    this.processFormTypeSpecificMapping(processedData)

    return processedData
  }

  /**
   * 处理公司名称字段映射
   * 确保公司名称在不同字段间保持同步
   */
  processCompanyNameMapping(formData) {
    const companyFields = this.mappingConfig.companyFields || ['公司名称']
    
    // 找到第一个有值的公司名称字段
    let primaryCompanyName = ''
    for (const field of companyFields) {
      if (formData[field] && formData[field].trim()) {
        primaryCompanyName = formData[field].trim()
        break
      }
    }

    // 如果没有找到有效的公司名称，使用默认值
    if (!primaryCompanyName) {
      primaryCompanyName = '客户公司名称'
    }

    // 同步所有公司名称相关字段
    companyFields.forEach(field => {
      if (formData.hasOwnProperty(field)) {
        formData[field] = primaryCompanyName
      }
    })

    console.log(`公司名称映射完成: ${primaryCompanyName}`)
  }

  /**
   * 处理版本信息字段映射
   * 统一不同版本字段的命名
   */
  processVersionMapping(formData) {
    const versionFields = this.mappingConfig.versionFields || ['部署包版本']
    
    // 查找版本信息
    let versionValue = ''
    
    // 优先从配置的版本字段中查找
    for (const field of versionFields) {
      if (formData[field] && formData[field].trim()) {
        versionValue = formData[field].trim()
        break
      }
    }

    // 如果没有找到，从通用版本字段中查找
    if (!versionValue) {
      const genericVersionFields = ['版本信息', 'version', 'platformVersion']
      for (const field of genericVersionFields) {
        if (formData[field] && formData[field].trim()) {
          versionValue = formData[field].trim()
          break
        }
      }
    }

    // 如果找到版本信息，同步到主要版本字段
    if (versionValue && versionFields.length > 0) {
      const primaryVersionField = versionFields[0]
      formData[primaryVersionField] = versionValue
      
      // 清理其他版本字段，避免冗余
      const allVersionFields = [...versionFields, '版本信息', 'version', 'platformVersion']
      allVersionFields.forEach(field => {
        if (field !== primaryVersionField && formData.hasOwnProperty(field)) {
          // 保留原值，但确保一致性
          if (formData[field] && formData[field] !== versionValue) {
            console.warn(`版本字段不一致: ${field}=${formData[field]}, 主字段=${versionValue}`)
          }
        }
      })
    }

    console.log(`版本信息映射完成: ${versionValue || '未设置'}`)
  }

  /**
   * 处理字段标准化
   * 将字段名称标准化为统一格式
   */
  processFieldNormalization(formData) {
    const normalizations = [
      // 标准化记录日期格式
      {
        check: (data) => data['记录日期'] && typeof data['记录日期'] === 'string',
        process: (data) => {
          const dateStr = data['记录日期']
          // 确保日期格式为 YYYY-MM-DD
          if (dateStr.includes('/')) {
            const parts = dateStr.split('/')
            if (parts.length === 3) {
              data['记录日期'] = `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`
            }
          }
        }
      },
      
      // 标准化编辑人信息
      {
        check: (data) => !data['编辑人'] || !data['编辑人'].trim(),
        process: (data) => {
          // 如果编辑人为空，尝试从其他来源获取
          data['编辑人'] = this.getCurrentUser()
        }
      }
    ]

    normalizations.forEach(norm => {
      if (norm.check(formData)) {
        norm.process(formData)
      }
    })
  }

  /**
   * 处理表单类型特定映射
   * 根据表单类型进行特殊处理
   */
  processFormTypeSpecificMapping(formData) {
    switch (this.formType) {
      case '应用加固':
        this.processAppHardeningMapping(formData)
        break
      case '安全监测':
        this.processSecurityMonitoringMapping(formData)
        break
      case '安全测评':
        this.processSecurityTestingMapping(formData)
        break
      default:
        this.processGenericMapping(formData)
        break
    }
  }

  /**
   * 处理应用加固表单特定映射
   */
  processAppHardeningMapping(formData) {
    // 确保升级用户配置的默认值
    if (!formData['升级用户配置'] || typeof formData['升级用户配置'] !== 'object') {
      formData['升级用户配置'] = {
        username: 'upgrader',
        password: 'upgrader@abc#2020'
      }
    }

    // 确保管理员信息格式正确
    if (!formData['管理员信息'] || !formData['管理员信息'].trim()) {
      formData['管理员信息'] = this.generateDefaultAdminInfo()
    }
  }

  /**
   * 处理安全监测表单特定映射
   */
  processSecurityMonitoringMapping(formData) {
    // 确保标准或定制字段有默认值
    if (!formData['标准或定制']) {
      formData['标准或定制'] = '标准版'
    }
  }

  /**
   * 处理安全测评表单特定映射
   */
  processSecurityTestingMapping(formData) {
    // 确保升级用户信息
    if (!formData['升级用户账号']) {
      formData['升级用户账号'] = 'upgrader'
    }
    if (!formData['升级用户密码']) {
      formData['升级用户密码'] = 'upgrader@abc#2020'
    }
  }

  /**
   * 处理通用表单映射
   */
  processGenericMapping(formData) {
    // 通用表单的版本信息映射
    if (formData['版本信息'] && !formData['部署包版本']) {
      formData['部署包版本'] = formData['版本信息']
    }
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    try {
      // 尝试从localStorage获取用户信息
      const userInfo = localStorage.getItem('user_info')
      if (userInfo) {
        const user = JSON.parse(userInfo)
        return user.real_name || user.username || user.name || 'admin'
      }
    } catch (error) {
      console.error('获取当前用户信息失败:', error)
    }
    return 'admin'
  }

  /**
   * 生成默认管理员信息
   */
  generateDefaultAdminInfo() {
    return `平台用户账号： 密码：\n管理员账号：admin 密码：admin@secneo\n超级管理员账号：sadmin 密码：admin@secneo#2017`
  }
}

/**
 * 数据映射验证器
 */
export class DataMappingValidator {
  /**
   * 验证数据映射的完整性
   * @param {Object} formData - 表单数据
   * @param {String} formType - 表单类型
   * @returns {Object} - 验证结果
   */
  static validateMapping(formData, formType) {
    const errors = []
    const warnings = []

    // 验证必要字段是否存在
    const requiredFields = this.getRequiredFields(formType)
    requiredFields.forEach(field => {
      if (!formData[field] || !formData[field].toString().trim()) {
        errors.push(`缺少必填字段: ${field}`)
      }
    })

    // 验证公司名称一致性
    const companyFields = FORM_TYPE_SPECIFIC_MAPPING[formType]?.companyFields || ['公司名称']
    const companyValues = companyFields.map(field => formData[field]).filter(Boolean)
    const uniqueCompanyValues = [...new Set(companyValues)]
    
    if (uniqueCompanyValues.length > 1) {
      warnings.push(`公司名称字段不一致: ${uniqueCompanyValues.join(', ')}`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 获取表单类型的必填字段
   */
  static getRequiredFields(formType) {
    const commonRequired = ['公司名称', '记录日期']
    
    switch (formType) {
      case '应用加固':
        return [...commonRequired, '客户']
      case '安全监测':
        return [...commonRequired, '前端版本', '后端版本']
      case '安全测评':
        return [...commonRequired, '部署包版本', '管理员页面IP', '用户页面IP']
      default:
        return [...commonRequired, '版本信息']
    }
  }
}

/**
 * 便捷的数据映射函数
 * @param {Object} formData - 表单数据
 * @param {String} formType - 表单类型
 * @returns {Object} - 映射后的数据
 */
export function processFormDataMapping(formData, formType = '安全测评') {
  const processor = new DataMappingProcessor(formType)
  return processor.processDataMapping(formData)
}

/**
 * 便捷的数据验证函数
 * @param {Object} formData - 表单数据
 * @param {String} formType - 表单类型
 * @returns {Object} - 验证结果
 */
export function validateFormDataMapping(formData, formType = '安全测评') {
  return DataMappingValidator.validateMapping(formData, formType)
}
