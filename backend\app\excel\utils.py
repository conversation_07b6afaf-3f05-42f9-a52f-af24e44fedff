import os
import threading
import time
from flask import current_app
from copy import copy
import re
from app.models.models import ComponentConfig, ComponentCategory

# 文件操作锁，确保并发安全
_file_lock = threading.Lock()

# 模板缓存，避免重复加载
_template_cache = {}
_template_cache_lock = threading.Lock()


def load_template_safe(template_path):
    """
    安全加载模板文件，避免缓存导致的状态污染
    每次都重新加载模板，确保数据一致性

    Args:
        template_path: 模板文件路径

    Returns:
        openpyxl.Workbook: 新的模板工作簿对象
    """
    try:
        from openpyxl import load_workbook
        current_app.logger.debug(f"加载模板文件: {template_path}")
        return load_workbook(template_path)
    except Exception as e:
        current_app.logger.error(f"加载模板失败: {template_path}, 错误: {str(e)}")
        return None


def clear_template_cache():
    """清空模板缓存"""
    with _template_cache_lock:
        _template_cache.clear()
        current_app.logger.info("模板缓存已清空")


def get_template_cache_stats():
    """获取模板缓存统计信息"""
    with _template_cache_lock:
        return {
            'cached_templates': len(_template_cache),
            'cache_keys': list(_template_cache.keys()),
            'cache_size_mb': sum(
                len(str(wb)) for wb, _ in _template_cache.values()
            ) / (1024 * 1024)  # 估算内存使用
        }


def record_excel_generation_performance(form_type, duration, file_size, cache_hit=False):
    """记录Excel生成性能指标"""
    from app.utils.performance_monitor import performance_monitor

    # 记录到性能监控系统
    performance_monitor.record_request(
        f'excel_generation_{form_type}',
        duration,
        200
    )

    # 记录Excel生成特定指标
    if not hasattr(performance_monitor, 'excel_stats'):
        performance_monitor.excel_stats = {
            'total_generated': 0,
            'total_duration': 0,
            'total_file_size': 0,
            'by_form_type': {}
        }

    # 更新统计
    stats = performance_monitor.excel_stats
    stats['total_generated'] += 1
    stats['total_duration'] += duration
    stats['total_file_size'] += file_size

    # 按表单类型统计
    if form_type not in stats['by_form_type']:
        stats['by_form_type'][form_type] = {
            'count': 0,
            'total_duration': 0,
            'avg_duration': 0,
            'total_file_size': 0,
            'avg_file_size': 0
        }

    form_stats = stats['by_form_type'][form_type]
    form_stats['count'] += 1
    form_stats['total_duration'] += duration
    form_stats['avg_duration'] = form_stats['total_duration'] / form_stats['count']
    form_stats['total_file_size'] += file_size
    form_stats['avg_file_size'] = form_stats['total_file_size'] / form_stats['count']

    current_app.logger.info(
        f"Excel生成性能: {form_type}, 耗时: {duration:.2f}s, "
        f"文件大小: {file_size/1024:.1f}KB, 缓存命中: {cache_hit}"
    )


def generate_unique_filename(base_filename, directory):
    """
    生成唯一的文件名，避免并发冲突

    Args:
        base_filename: 基础文件名（不含扩展名）
        directory: 目标目录

    Returns:
        str: 唯一的文件名（含扩展名）
    """
    with _file_lock:
        # 确保目录存在
        os.makedirs(directory, exist_ok=True)

        # 添加.xlsx扩展名
        filename = f"{base_filename}.xlsx"
        filepath = os.path.join(directory, filename)

        # 如果文件不存在，直接返回
        if not os.path.exists(filepath):
            return filename

        # 如果文件存在，添加时间戳确保唯一性
        from datetime import datetime
        import uuid
        timestamp = datetime.now().strftime('%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        filename = f"{base_filename}_{timestamp}_{unique_id}.xlsx"

        return filename


def save_file_safe(workbook, filepath):
    """
    安全保存Excel文件，确保目录存在
    简化版本，避免复杂的原子性操作可能导致的问题

    Args:
        workbook: openpyxl工作簿对象
        filepath: 目标文件路径
    """
    try:
        # 确保目标目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # 直接保存文件
        workbook.save(filepath)
        current_app.logger.info(f"文件保存成功: {filepath}")

    except Exception as e:
        current_app.logger.error(f"文件保存失败: {filepath}, 错误: {str(e)}")
        raise e


def get_component_aliases(data):
    """
    从表单数据中提取组件别名映射

    Args:
        data: 表单数据字典

    Returns:
        dict: 组件名称到别名的映射字典
    """
    component_aliases = {}

    # 从表单数据中获取组件别名信息
    # 前端会将别名信息包含在表单数据中
    if 'component_aliases' in data:
        component_aliases = data['component_aliases']
        current_app.logger.info(f"从表单数据中获取到组件别名: {component_aliases}")

    # 也可以从localStorage类型的数据中获取（如果前端传递了）
    if 'form_type' in data:
        form_type = data['form_type']
        alias_key = f'component_aliases_{form_type}'
        if alias_key in data:
            component_aliases.update(data[alias_key])
            current_app.logger.info(f"从 {alias_key} 中获取到组件别名: {data[alias_key]}")

    return component_aliases


def apply_component_aliases(text, component_aliases):
    """
    在文本中应用组件别名替换

    Args:
        text: 要处理的文本
        component_aliases: 组件别名映射字典

    Returns:
        str: 应用别名后的文本
    """
    if not text or not component_aliases:
        return text

    # 替换文本中的组件名称为别名
    result = text
    for component_name, alias in component_aliases.items():
        if alias and alias.strip():
            # 使用单词边界确保精确匹配
            import re
            pattern = r'\b' + re.escape(component_name) + r'\b'
            result = re.sub(pattern, alias.strip(), result)

    return result


def normalize_form_data(data):
    """
    标准化表单数据，处理不同表单类型之间的字段名称差异
    确保通用表单的字段能正确映射到现有模板
    """
    # 创建数据副本，避免修改原始数据
    normalized_data = data.copy()

    # 获取表单类型
    form_type = data.get('文档后缀', '安全测评')

    # 处理版本信息字段的映射
    if form_type not in ['安全测评', '安全监测', '应用加固']:
        # 对于通用表单类型（如API平台），将版本信息映射为部署包版本
        if '版本信息' in data and '部署包版本' not in data:
            normalized_data['部署包版本'] = data['版本信息']
            # 使用日志记录而不是print，避免编码问题
            from flask import current_app
            if current_app:
                current_app.logger.info(f"字段映射: 版本信息 -> 部署包版本 = {data['版本信息']}")

    return normalized_data
















def create_default_template(template_type='应用加固'):
    """
    创建默认的Excel模板文件

    Args:
        template_type: 模板类型，可选值：应用加固、安全检测、安全加固、安全测评、渗透测试
    """
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

    # 根据模板类型生成不同的文件名
    template_filename = f'梆梆安全-运维信息登记模板-{template_type}.xlsx'
    template_path = os.path.join(current_app.config['EXCEL_TEMPLATES_FOLDER'], template_filename)

    # 如果模板文件已存在，则直接返回
    if os.path.exists(template_path):
        return template_path

    # 创建工作簿和工作表
    wb = Workbook()
    sheet = wb.active
    sheet.title = "基本信息"

    # 创建服务器列表sheet页
    server_sheet = wb.create_sheet(title='服务器列表')

    # 设置服务器列表sheet页的表头
    headers = ["用途", "IP地址", "部署的组件", "系统发行版", "内存", "CPU", "磁盘"]
    for col, header in enumerate(headers, 1):
        cell = server_sheet.cell(row=1, column=col)
        cell.value = header
        cell.font = Font(name='微软雅黑', size=11, bold=True)
        cell.fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")  # 浅蓝色
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

    # 设置列宽
    server_sheet.column_dimensions['A'].width = 20  # 用途
    server_sheet.column_dimensions['B'].width = 20  # IP地址
    server_sheet.column_dimensions['C'].width = 40  # 部署的组件
    server_sheet.column_dimensions['D'].width = 20  # 系统发行版
    server_sheet.column_dimensions['E'].width = 15  # 内存
    server_sheet.column_dimensions['F'].width = 15  # CPU
    server_sheet.column_dimensions['G'].width = 15  # 磁盘

    # 设置表头行高
    server_sheet.row_dimensions[1].height = 25

    # 设置列宽
    sheet.column_dimensions['A'].width = 20
    sheet.column_dimensions['B'].width = 60

    # 定义边框样式
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # 定义标题字体和对齐方式
    title_font = Font(name='微软雅黑', size=16, bold=True, color="FFFFFF")  # 白色字体
    title_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)



    # 定义普通单元格字体和对齐方式
    normal_font = Font(name='微软雅黑', size=11)
    header_font = Font(name='微软雅黑', size=11, bold=True)
    cell_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

    # 设置标题
    sheet.merge_cells('A1:B1')
    title_cell = sheet['A1']
    # 动态插入客户公司名称到第一行
    customer_name = "{{客户公司名称}}"
    title_cell.value = f"{customer_name}-运维文档（修改时间：{{修改时间}}）"
    title_cell.font = title_font
    title_cell.alignment = title_alignment
    title_cell.border = thin_border
    title_cell.fill = PatternFill(start_color="DCE6F1", end_color="DCE6F1", fill_type="solid")

    # 根据模板类型设置不同的表头
    if template_type == '安全测评':
        headers = [
            "基本信息",
            "部署包版本",
            "服务器信息",
            "管理员页面",
            "用户页面",
            "升级页面",
            "对外服务端口",
            "运维定制内容",
            "产品功能",
            "授权功能",
            "授权期限",
            "维护记录"
        ]
    elif template_type == '安全监测':
        # 安全监测使用专门的内容
        headers = [
            "基本信息",
            "日活",
            "记录日期",
            "标准或定制",
            "前端版本",
            "后端版本",
            "SDK外网流量入口",
            "SDK流量转发到Nginx入口",
            "业务功能页面访问",
            "init",
            "kibana数据页面访问",
            "运维定制内容",
            "客户APP",
            "维护记录"
        ]
    else:
        # 默认表头 - 使用安全测评的内容
        headers = [
            "基本信息",
            "部署包版本",
            "服务器信息",
            "管理员页面",
            "用户页面",
            "升级页面",
            "对外服务端口",
            "运维定制内容",
            "产品功能",
            "授权功能",
            "授权期限",
            "维护记录"
        ]

    # 添加表头和占位符
    for i, header in enumerate(headers, start=2):  # 从第2行开始，去掉空行
        if header in ["基本信息", "运维定制内容"]:
            sheet.merge_cells(f'A{i}:B{i}')
            cell = sheet[f'A{i}']
            cell.fill = PatternFill(start_color="90CAF9", end_color="90CAF9", fill_type="solid")  # 中蓝色
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.font = Font(name='微软雅黑', size=12, bold=True)  # 稍大的字体
            cell.border = thin_border
            # 设置行高
            sheet.row_dimensions[i].height = 30  # 增加行高
        else:
            cell = sheet[f'A{i}']
            cell_value = sheet[f'B{i}']

            # 设置行高，根据内容多少自适应调整
            if header in ["管理员页面", "服务器信息"]:
                sheet.row_dimensions[i].height = 80  # 多行内容需要更高的行高
            elif header in ["记录日期", "记录人", "日活", "标准或定制", "前端版本", "后端版本"]:
                sheet.row_dimensions[i].height = 25  # 单行内容的标准行高
            else:
                # 其他内容的行高根据内容长度自适应
                sheet.row_dimensions[i].height = 30  # 默认行高

            # 设置单元格样式
            cell.border = thin_border
            cell.font = header_font
            cell.alignment = Alignment(horizontal='left', vertical='center')
            cell.fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")  # 浅蓝色

            cell_value.border = thin_border
            cell_value.font = normal_font
            cell_value.alignment = cell_alignment

            # 根据模板类型设置不同的占位符
            if template_type == '安全监测':
                if header == "日活":
                    cell_value.value = "{{日活}}"
                elif header == "维护人员":
                    cell_value.value = "{{维护人员}}"
                elif header == "记录日期":
                    cell_value.value = "{{记录日期}}"
                elif header == "标准或定制":
                    cell_value.value = "{{标准或定制}}"
                elif header == "前端版本":
                    cell_value.value = "{{前端版本}}"
                elif header == "后端版本":
                    cell_value.value = "{{后端版本}}"
                elif header == "SDK外网流量入口":
                    cell_value.value = "{{SDK外网流量入口}}"
                elif header == "SDK流量转发到Nginx入口":
                    cell_value.value = "{{SDK流量转发到Nginx入口}}"
                elif header == "业务功能页面访问":
                    cell_value.value = "地址：{{业务功能页面地址}}\n超级管理员账号：{{超级管理员账号}} 密码：{{超级管理员密码}}\n客户管理员：{{客户管理员账号}} 密码：{{客户管理员密码}}"
                    # 计算行高：每行文本约20高度，加上一些额外空间
                    lines = cell_value.value.count('\n') + 1
                    sheet.row_dimensions[i].height = max(25, lines * 20 + 10)
                elif header == "init":
                    cell_value.value = "地址：{{init地址}}\n密码：{{init用户名}} / {{init密码}}"
                    # 计算行高：每行文本约20高度，加上一些额外空间
                    lines = cell_value.value.count('\n') + 1
                    sheet.row_dimensions[i].height = max(25, lines * 20 + 10)
                elif header == "kibana数据页面访问":
                    cell_value.value = "地址：{{kibana地址}}\n密码：{{kibana认证信息}}"
                    # 计算行高：每行文本约20高度，加上一些额外空间
                    lines = cell_value.value.count('\n') + 1
                    sheet.row_dimensions[i].height = max(25, lines * 20 + 10)
                elif header == "客户APP":
                    cell_value.value = "{{客户APP}}"
                elif header == "维护记录":
                    cell_value.value = "{{维护记录}}"
            else:
                # 安全测评和其他模板的占位符
                if header == "部署包版本":
                    cell_value.value = "{{部署包版本}}"
                elif header == "服务器信息":
                    # 使用更清晰的格式，确保每个字段单独一行，包含运维字段（不包含端口）
                    cell_value.value = "系统发行版：{{系统发行版}}\n内存：{{内存}}\nCPU：{{CPU}}\n磁盘：{{磁盘}}\n{{运维信息}}"
                    # 设置更高的行高以适应多行内容，支持自动拉伸
                    sheet.row_dimensions[i].height = 120
                elif header == "管理员页面":
                    # 使用更清晰的格式，确保账号密码对齐
                    cell_value.value = "{{管理员页面IP}}\n超级管理员账号：{{超级管理员账号}} 密码：{{超级管理员密码}}\n管理员账号：{{管理员账号}} 密码：{{管理员密码}}\n用户账号：{{用户账号}} 密码：{{用户密码}}"
                    # 设置更高的行高以适应多行内容
                    sheet.row_dimensions[i].height = 90
                elif header == "用户页面":
                    cell_value.value = "{{用户页面IP}}"
                elif header == "升级页面":
                    cell_value.value = "{{升级页面IP}}"
                elif header == "对外服务端口":
                    cell_value.value = "{{对外服务端口}}"
                elif header == "产品功能":
                    cell_value.value = "{{产品功能}}"
                elif header == "授权功能":
                    cell_value.value = "{{授权功能}}"
                elif header == "授权期限":
                    cell_value.value = "{{授权开始日期}} - {{授权结束日期}}"
                elif header == "维护记录":
                    cell_value.value = "{{维护记录}}"

        cell.value = header

    # 保存模板
    os.makedirs(os.path.dirname(template_path), exist_ok=True)
    wb.save(template_path)
    current_app.logger.info(f"已创建默认模板: {template_path}")

    return template_path

def generate_bangbang_excel(data, template_path=None):
    """
    支持 {{xxx | range}} 动态插入行渲染和 #DEPLOY_APPS_HEAD# 特殊占位符。
    新增支持 {{组件名.字段}} 占位符（如 {{docker.port}}、{{tp-minio.version}}），自动渲染组件信息。
    支持组件别名：在Excel中显示用户设置的组件别名而不是技术名称。
    集成性能监控，记录生成时间和缓存命中率。
    """
    from datetime import datetime
    import os
    from flask import current_app
    import re

    # 开始性能计时
    start_time = time.time()

    if template_path is None:
        template_path = os.path.join(current_app.config['EXCEL_TEMPLATES_FOLDER'], '安全测评-运维信息登记模板.xlsx')

    # 处理字段名称映射，确保通用表单的字段能正确映射到模板
    data = normalize_form_data(data)

    # 获取组件别名映射
    component_aliases = get_component_aliases(data)

    # 使用安全的模板加载方式
    wb = load_template_safe(template_path)
    if wb is None:
        current_app.logger.error(f"加载模板失败: {template_path}")
        return None

    # 构建组件信息字典（组件名 -> 字段dict），包含从数据库获取的信息
    form_type = data.get('文档后缀', '安全测评')
    db_components = ComponentConfig.query.filter_by(form_type=form_type, is_active=True).all()
    component_map = {}
    for comp in db_components:
        # 获取关联的分类信息
        category = ComponentCategory.query.filter_by(key=comp.category_key).first()
        category_info = {
            'display_name': category.display_name if category else '',
            'color': category.color if category else '',
            'icon': category.icon if category else '',
            'order': category.order if category else 0
        }

        component_map[comp.name] = {
            'id': comp.id,
            'name': comp.name,
            'display_name': comp.display_name,
            'version': comp.version or '',
            'port': comp.default_port or '',
            'default_port': comp.default_port or '',
            'description': comp.description or '',
            'protocol': comp.protocol or '',
            'alias': comp.alias or '',
            'category_key': comp.category_key or '',
            'form_type': comp.form_type or '',
            'is_active': comp.is_active,
            'created_at': comp.created_at.strftime('%Y-%m-%d %H:%M:%S') if comp.created_at else '',
            'updated_at': comp.updated_at.strftime('%Y-%m-%d %H:%M:%S') if comp.updated_at else '',
            # 添加扁平化的分类信息
            'category_display_name': category_info['display_name'],
            'category_color': category_info['color'],
            'category_icon': category_info['icon'],
            'category_order': category_info['order'],
            # 'category_info': category_info, # 暂不添加嵌套对象，前端占位符不易直接访问
        }

    # 统计组件部署数量，并添加到 component_map 中
    component_counts = {}
    server_info_list = data.get('服务器信息', [])
    if isinstance(server_info_list, list):
        for item in server_info_list:
            if '部署应用' in item and isinstance(item['部署应用'], list):
                for app in item['部署应用']:
                    if isinstance(app, str):
                         # 使用数据库中的组件名称作为key进行统计
                        db_comp = ComponentConfig.query.filter_by(name=app, form_type=form_type).first()
                        comp_name = db_comp.name if db_comp else app # 如果数据库中不存在，使用原始名称
                        component_counts[comp_name] = component_counts.get(comp_name, 0) + 1

    # 将统计数量添加到 component_map 中
    for comp_name, count in component_counts.items():
        if comp_name in component_map:
            component_map[comp_name]['count'] = count
        else:
             # 如果有未在数据库中配置的组件，也添加到map中（仅含数量）
             component_map[comp_name] = {'count': count, 'name': comp_name}

    # 正则匹配 {{组件名.字段}} 和 {{组件名.count}}
    # 支持组件名中的连字符、下划线等字符
    comp_field_pattern = re.compile(r'{{\s*([\w\-]+)\.([\w\-]+)\s*}}')

    for sheet in wb.worksheets:
        # 先处理动态range
        process_dynamic_range(sheet, data, component_aliases)
        # 再处理普通占位符（全表）
        rows_to_delete = []  # 记录需要删除的行号

        for row in sheet.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str) and '| range' not in cell.value and '| split:' not in cell.value:
                    original_value = cell.value
                    new_value = original_value
                    has_empty_placeholder = False

                    # 1. 处理 {{组件名.字段}} 和 {{组件名.count}} 占位符
                    def comp_field_replacer(match):
                        comp_name, field = match.group(1), match.group(2)
                        return str(component_map.get(comp_name, {}).get(field, ''))
                    new_value = comp_field_pattern.sub(comp_field_replacer, new_value)

                    # 2. 处理原有 {{key}} 占位符
                    for key, value in data.items():
                        placeholder = f"{{{{{key}}}}}"
                        if placeholder in new_value:
                            if not value or (isinstance(value, str) and value.strip() == ''):
                                has_empty_placeholder = True
                            else:
                                if isinstance(value, list):
                                    # 对列表中的组件名称应用别名
                                    if key == '部署应用' or '组件' in key:
                                        value = ', '.join(component_aliases.get(str(x), str(x)) for x in value)
                                    else:
                                        value = ', '.join(str(x) for x in value)
                                else:
                                    # 对字符串值应用别名（如果是组件名称）
                                    value_str = str(value)
                                    value = apply_component_aliases(value_str, component_aliases)
                                new_value = new_value.replace(placeholder, value)

                    cell.value = new_value

                    stripped_original = original_value.strip()
                    stripped_new = cell.value.strip()
                    is_single_placeholder = stripped_original.startswith("{{") and stripped_original.endswith("}}")

                    # 如果是单个占位符，并且为空或未被替换
                    if has_empty_placeholder and is_single_placeholder:
                        if re.search(r"{{.*?}}", cell.value):  # 替换后仍含占位符
                            if cell.row not in rows_to_delete:
                                rows_to_delete.append(cell.row)
                                current_app.logger.debug(f"标记删除空占位符行 (替换后仍有占位符) {cell.row}: {original_value}")
                        elif stripped_original == stripped_new:  # 替换前后一致，说明未真正替换
                            if cell.row not in rows_to_delete:
                                rows_to_delete.append(cell.row)
                                current_app.logger.debug(f"标记删除空占位符行 (无替换发生但有空占位符) {cell.row}: {original_value}")

        # 从后往前删除行，避免行号变化影响
        for row_num in sorted(rows_to_delete, reverse=True):
            sheet.delete_rows(row_num, 1)
            # print(f"已删除空占位符行 {row_num}") # 暂时注释掉打印，避免过多输出

    # 添加部署图sheet
    add_deployment_diagram_sheet(wb, data)

    # 处理文件名 - 添加并发安全机制
    company_name = data.get('公司名称', '梆梆安全')
    record_date = data.get('记录日期', datetime.now().strftime('%Y%m%d'))
    if isinstance(record_date, str):
        if '-' in record_date:
            record_date = record_date.replace('-', '')[:8]
        elif '/' in record_date:
            record_date = record_date.replace('/', '')[:8]

    # 生成唯一文件名，避免并发冲突
    base_filename = f"{company_name}-运维文档-{form_type}_{record_date}"
    base_filename = base_filename.replace(':', '_').replace('/', '_').replace('\\', '_')

    # 确保文件名唯一性
    filename = generate_unique_filename(base_filename, current_app.config['EXCEL_GENERATED_FOLDER'])
    filepath = os.path.join(current_app.config['EXCEL_GENERATED_FOLDER'], filename)

    # 使用安全的文件保存方式
    save_file_safe(wb, filepath)

    # 记录性能指标
    end_time = time.time()
    duration = end_time - start_time

    # 获取文件大小
    try:
        file_size = os.path.getsize(filepath)
    except:
        file_size = 0

    # 记录到性能监控系统
    record_excel_generation_performance(
        form_type=form_type,
        duration=duration,
        file_size=file_size,
        cache_hit=True  # 这里可以根据实际缓存命中情况调整
    )

    return filename

def calculate_row_height(text, min_height=25, chars_per_line=60, height_per_line=20, extra_space=10):
    """
    计算单元格行高

    参数:
    - text: 单元格文本内容
    - min_height: 最小行高
    - chars_per_line: 每行平均字符数
    - height_per_line: 每行高度
    - extra_space: 额外空间

    返回:
    - 计算后的行高
    """
    if not text:
        return min_height

    # 计算换行符数量
    newline_count = text.count('\n')

    # 如果没有换行符，根据文本长度估算行数
    if newline_count == 0:
        lines = max(1, len(text) // chars_per_line + (1 if len(text) % chars_per_line > 0 else 0))
    else:
        # 有换行符，计算每行的长度
        text_lines = text.split('\n')
        lines = 0
        for line in text_lines:
            lines += max(1, len(line) // chars_per_line + (1 if len(line) % chars_per_line > 0 else 0))

    # 计算行高
    return max(min_height, lines * height_per_line + extra_space)

def parse_split_placeholder(placeholder):
    # 解析如 'split:col,分隔符'，返回('col', '分隔符')
    m = re.search(r'split:(col|row)(?:,([^|}]+))?', placeholder)
    if m:
        mode = m.group(1)
        sep = m.group(2) if m.group(2) else ','
        return mode, sep
    return None, None

def parse_placeholder(cell_value):
    """
    解析占位符，返回字段名、指令链（如 [('split', 'col', ';'), ('range', '服务器信息')]）
    """
    import re
    m = re.match(r'{{\s*(item\.)?([^|}\s]+)((?:\s*\|[^}}]+)*)\s*}}', cell_value)
    if not m:
        return None, None, []
    field = m.group(2)
    pipe_str = m.group(3)
    # 解析所有 | 指令
    instrs = []
    for part in pipe_str.split('|'):
        part = part.strip()
        if part.startswith('split:'):
            # split:col,; 或 split:row,分隔符
            m2 = re.match(r'split:(col|row)(?:,([^|}]+))?', part)
            if m2:
                instrs.append(('split', m2.group(1), m2.group(2) if m2.group(2) else ','))
        elif part.startswith('range:'):
            instrs.append(('range', part[6:].strip()))
        elif part.startswith('join:'):
            # join:, 或 join:; 等
            separator = part[5:].strip() if len(part) > 5 else ','
            instrs.append(('join', separator))
    return field, instrs, m.group(0)

def render_range_rows(sheet, data_list, field_map, template_row_idx, all_data):
    from copy import copy

    # 检查是否需要展开运维用户（只有在没有join指令时才展开）
    has_ops_user_fields = any(
        field in ['运维用户.用户名', '运维用户.密码', '运维用户.用户类型']
        and not any(instr[0] == 'join' for instr in instrs)
        for col, (field, instrs, raw) in field_map.items()
    )

    if has_ops_user_fields:
        # 展开运维用户数据
        expanded_data = []
        for item in data_list:
            if isinstance(item, dict) and '运维用户' in item:
                ops_users = item.get('运维用户', [])
                if isinstance(ops_users, list):
                    for user in ops_users:
                        if isinstance(user, dict):
                            # 创建展开的数据项
                            expanded_item = item.copy()
                            expanded_item['运维用户.用户名'] = user.get('用户名', '')
                            expanded_item['运维用户.密码'] = user.get('密码', '')
                            expanded_item['运维用户.用户类型'] = 'Root' if user.get('用户名') == 'root' else '运维用户'
                            expanded_data.append(expanded_item)
                else:
                    expanded_data.append(item)
            else:
                expanded_data.append(item)
        data_list = expanded_data

    template_row = [sheet.cell(row=template_row_idx, column=col) for col in range(1, sheet.max_column + 1)]
    template_height = sheet.row_dimensions[template_row_idx].height
    merged_ranges = []
    for merged in sheet.merged_cells.ranges:
        if merged.min_row == template_row_idx:
            merged_ranges.append(merged)


    # 普通动态行（无split，仅range）
    for i, item in enumerate(data_list):
        insert_idx = template_row_idx + 1 + i  # 修正插入索引，确保首行数据不会遗漏
        sheet.insert_rows(insert_idx)
        for col, tmpl_cell in enumerate(template_row, 1):
            cell = sheet.cell(row=insert_idx, column=col)
            cell._style = copy(tmpl_cell._style)
            cell.font = copy(tmpl_cell.font)
            cell.border = copy(tmpl_cell.border)
            cell.fill = copy(tmpl_cell.fill)
            cell.number_format = copy(tmpl_cell.number_format)
            cell.protection = copy(tmpl_cell.protection)
            cell.alignment = copy(tmpl_cell.alignment)
            # 动态行占位符替换
            if col in field_map:
                field, instrs, _ = field_map[col]

                # 处理运维用户join指令
                if field.startswith('运维用户.') and any(instr[0] == 'join' for instr in instrs):
                    # 获取join分隔符
                    join_sep = ','
                    for instr in instrs:
                        if instr[0] == 'join':
                            join_sep = instr[1]
                            # 处理转义字符
                            join_sep = join_sep.replace('\\n', '\n').replace('\\r\\n', '\r\n').replace('\\t', '\t')
                            break

                    # 从运维用户数组中提取字段值
                    ops_users = item.get('运维用户', [])
                    field_name = field.split('.')[1]  # 获取用户名、密码等
                    values = []

                    if isinstance(ops_users, list):
                        for user in ops_users:
                            if isinstance(user, dict):
                                value = user.get(field_name, '')
                                if value:
                                    values.append(str(value))

                    result_value = join_sep.join(values)
                    cell.value = result_value

                    # 如果包含换行符，设置单元格自动换行
                    if '\n' in result_value or '\r' in result_value:
                        from openpyxl.styles import Alignment
                        current_alignment = cell.alignment
                        cell.alignment = Alignment(
                            horizontal=current_alignment.horizontal,
                            vertical=current_alignment.vertical,
                            text_rotation=current_alignment.text_rotation,
                            wrap_text=True,
                            shrink_to_fit=current_alignment.shrink_to_fit,
                            indent=current_alignment.indent
                        )
                else:
                    v = item.get(field, '')
                    # 如果值是列表，将其转换为字符串
                    if isinstance(v, list):
                        v = ', '.join(str(x) for x in v)
                    cell.value = v
            else:
                # 普通占位符替换
                if tmpl_cell.value and isinstance(tmpl_cell.value, str):
                    v = tmpl_cell.value
                    for key, value in {**all_data, **item}.items():
                        placeholder = f"{{{{{key}}}}}"
                        if placeholder in v:
                            v = v.replace(placeholder, str(value))
                    cell.value = v
                else:
                    cell.value = tmpl_cell.value

        # 自动调整行高：检查是否包含运维信息并自动拉伸
        row_height = template_height
        for cell in sheet[insert_idx]:
            if cell.value and isinstance(cell.value, str):
                # 检查是否包含运维信息占位符
                if '运维信息' in cell.value and '\n' in cell.value:
                    # 计算行数并设置合适的行高
                    line_count = cell.value.count('\n') + 1
                    # 每行约20像素，最小高度30，最大高度150
                    calculated_height = max(30, min(150, line_count * 20 + 10))
                    row_height = max(row_height, calculated_height)
                elif '运维信息' in cell.value:
                    # 单行运维信息也适当增加高度
                    row_height = max(row_height, 40)

        sheet.row_dimensions[insert_idx].height = row_height
    # 删除模板行
    sheet.delete_rows(template_row_idx, 1)

def render_ops_users_range(sheet, data, field_map, template_row_idx):
    """
    渲染运维用户嵌套数组
    支持类似这样的模板：
    | {{ item.IP地址 | range:服务器信息 }} | Root | root | {{ item.Root密码 | range:服务器信息 }} |
    | {{ item.IP地址 | range:服务器信息 }} | 运维用户 | {{ item.运维用户名 | range:服务器信息 }} | {{ item.运维密码 | range:服务器信息 }} |
    """
    from copy import copy

    # 获取服务器信息
    server_list = data.get('服务器信息', [])
    if not isinstance(server_list, list) or not server_list:
        # 如果没有服务器信息，删除模板行
        sheet.delete_rows(template_row_idx, 1)
        return

    # 获取模板行
    template_row = [sheet.cell(row=template_row_idx, column=col) for col in range(1, sheet.max_column + 1)]
    template_height = sheet.row_dimensions[template_row_idx].height

    # 保存合并单元格信息
    merged_ranges = []
    for merged in sheet.merged_cells.ranges:
        if merged.min_row == template_row_idx:
            merged_ranges.append(merged)

    # 收集所有运维用户数据
    all_ops_users = []

    for server in server_list:
        if not isinstance(server, dict):
            continue

        server_ip = server.get('IP地址', '')
        ops_users = server.get('运维用户', [])

        if isinstance(ops_users, list):
            for user in ops_users:
                if isinstance(user, dict):
                    # 为每个运维用户创建一行数据
                    user_data = {
                        'IP地址': server_ip,
                        'SSH端口': server.get('SSH端口', ''),
                        'Root密码': server.get('Root密码', ''),
                        '运维信息': server.get('运维信息', ''),
                        '用户名': user.get('用户名', ''),
                        '密码': user.get('密码', ''),
                        '用户类型': 'Root' if user.get('用户名') == 'root' else '运维用户',
                        '运维用户名': user.get('用户名', '') if user.get('用户名') != 'root' else '',
                        '运维密码': user.get('密码', '') if user.get('用户名') != 'root' else ''
                    }
                    all_ops_users.append(user_data)

    if not all_ops_users:
        # 如果没有运维用户数据，删除模板行
        sheet.delete_rows(template_row_idx, 1)
        return

    # 插入新行
    for i, user_data in enumerate(all_ops_users):
        new_row_idx = template_row_idx + i
        if i > 0:
            sheet.insert_rows(new_row_idx, 1)
            if template_height:
                sheet.row_dimensions[new_row_idx].height = template_height

        # 复制模板行的样式和内容
        for col_idx, template_cell in enumerate(template_row, 1):
            new_cell = sheet.cell(row=new_row_idx, column=col_idx)

            # 复制样式
            if template_cell.has_style:
                new_cell.font = copy(template_cell.font)
                new_cell.border = copy(template_cell.border)
                new_cell.fill = copy(template_cell.fill)
                new_cell.number_format = template_cell.number_format
                new_cell.protection = copy(template_cell.protection)
                new_cell.alignment = copy(template_cell.alignment)

            # 处理单元格内容
            if col_idx in field_map:
                # 使用field_map中的字段映射
                field, instrs, raw = field_map[col_idx]

                # 检查指令类型来决定数据来源
                range_type = None
                for instr in instrs:
                    if instr[0] == 'range':
                        range_type = instr[1]
                        break

                if range_type == '运维用户' and field in user_data:
                    # 运维用户字段，使用user_data
                    new_cell.value = user_data[field]
                elif range_type == '服务器信息':
                    # 服务器信息字段，从对应的服务器数据中获取
                    server_ip = user_data.get('IP地址', '')
                    server_list = data.get('服务器信息', [])
                    server_data = next((s for s in server_list if s.get('IP地址') == server_ip), {})
                    if field in server_data:
                        new_cell.value = server_data[field]
                    else:
                        new_cell.value = template_cell.value
                else:
                    new_cell.value = template_cell.value
            elif template_cell.value and isinstance(template_cell.value, str):
                cell_value = template_cell.value

                # 检查是否包含运维用户相关的占位符
                if '{{ item.' in cell_value and '| range:运维用户' in cell_value:
                    # 解析占位符
                    field, instrs, raw = parse_placeholder(cell_value.strip())
                    if field and field in user_data:
                        new_cell.value = user_data[field]
                    else:
                        new_cell.value = cell_value
                else:
                    # 处理固定值（如 "Root", "运维用户"）
                    if cell_value == 'Root' and user_data.get('用户类型') != 'Root':
                        new_cell.value = '运维用户'
                    elif cell_value == 'root' and user_data.get('用户名') != 'root':
                        new_cell.value = user_data.get('运维用户名', '')
                    else:
                        new_cell.value = cell_value
            else:
                new_cell.value = template_cell.value

    # 处理合并单元格
    for merged in merged_ranges:
        for i in range(len(all_ops_users)):
            new_row = template_row_idx + i
            new_range = f"{merged.coord}".replace(str(template_row_idx), str(new_row))
            try:
                sheet.merge_cells(new_range)
            except:
                pass  # 忽略合并失败的情况

    # 删除原模板行（现在在最后一个新行之后）
    final_template_row = template_row_idx + len(all_ops_users)
    sheet.delete_rows(final_template_row, 1)

def process_dynamic_range(sheet, data, component_aliases=None):
    # 如果没有传入组件别名，使用空字典
    if component_aliases is None:
        component_aliases = {}

    # 收集所有模板行信息，支持split/range指令
    template_rows = []
    for row in sheet.iter_rows():
        range_cells = []
        for cell in row:
            if cell.value and isinstance(cell.value, str):
                # 只要有 | range:xxx 就收集
                if '| range:' in cell.value:
                    field, instrs, raw = parse_placeholder(cell.value.strip())
                    if not field or not instrs:
                        continue
                    for instr in instrs:
                        if instr[0] == 'range':
                            range_cells.append((cell, (field, instrs, raw), instr[1]))

        if range_cells:
            template_row_idx = range_cells[0][0].row

            # 按range类型分组处理
            range_groups = {}
            for cell, field_info, data_type in range_cells:
                if data_type not in range_groups:
                    range_groups[data_type] = []
                range_groups[data_type].append((cell, field_info))

            # 为每个range类型创建一个模板行记录
            for data_type, cells in range_groups.items():
                field_map = {cell.column: field_info for cell, field_info in cells}
                template_rows.append((template_row_idx, data_type, field_map))

    # 🔧 重要修复：创建数据副本，避免修改原始数据
    # 使用安全的数据拷贝方式，避免深拷贝导致的引用问题
    import copy
    data_copy = copy.deepcopy(data)  # 使用标准库的深拷贝，更安全

    # 预处理数据，特别是组件端口对象和部署数量
    # 创建组件部署数量统计字典
    component_counts = {}

    # 创建一个字典，用于存储需要添加的新键值对
    new_data = {}

    # 首先统计每个组件的部署数量
    server_info_list = data_copy.get('服务器信息', [])
    if isinstance(server_info_list, list):
        for item in server_info_list:
            if '部署应用' in item and isinstance(item['部署应用'], list):
                for app in item['部署应用']:
                    if isinstance(app, str):  # 确保应用名称是字符串
                        component_counts[app] = component_counts.get(app, 0) + 1

    # 为每个组件创建部署数量占位符
    # 格式: 组件名称_COUNT
    for component_name, count in component_counts.items():
        count_key = f"{component_name}_COUNT"
        new_data[count_key] = str(count)

    # 处理组件端口对象和运维字段
    for data_type, data_list in list(data_copy.items()):
        if isinstance(data_list, list):
            for item in data_list:
                # 确保item是字典类型，如果是字符串则跳过
                if not isinstance(item, dict):
                    continue

                # 特殊处理组件端口对象
                if '组件端口' in item and isinstance(item['组件端口'], dict) and '部署应用' in item:
                    # 将组件端口对象转换为字符串
                    port_items = []
                    for app in item['部署应用']:
                        if isinstance(app, str):  # 确保应用名称是字符串
                            port = item['组件端口'].get(app, '无')
                            # 使用别名显示组件名称
                            display_name = component_aliases.get(app, app)
                            port_items.append(f"{display_name}: {port}")

                            # 为每个组件创建一个特殊的端口占位符
                            # 格式: 组件名称_PORT
                            port_key = f"{app}_PORT"
                            new_data[port_key] = port

                            # 也为别名创建端口占位符
                            if display_name != app:
                                alias_port_key = f"{display_name}_PORT"
                                new_data[alias_port_key] = port

                    # 将组件端口对象转换为字符串
                    item['组件端口_str'] = "\n".join(port_items)

                # 处理运维用户数组，生成运维信息汇总（保持运维用户数组结构）
                if '运维用户' in item and isinstance(item['运维用户'], list):
                    ops_users = item['运维用户']

                    # 创建运维信息汇总（换行分隔格式）
                    ops_info_parts = []

                    # 为了兼容旧模板，同时生成扁平化字段
                    root_user = next((user for user in ops_users if user.get('用户名') == 'root'), None)
                    if root_user:
                        item['Root密码'] = root_user.get('密码', '')
                        ops_info_parts.append(f"Root密码：{root_user.get('密码', '')}")

                    # 提取非Root运维用户
                    non_root_users = [user for user in ops_users if user.get('用户名') != 'root']

                    # 处理所有非Root运维用户
                    for user in non_root_users:
                        if user.get('用户名') or user.get('密码'):
                            user_info = f"运维用户：{user.get('用户名', '')} / {user.get('密码', '')}"
                            ops_info_parts.append(user_info)

                    # 生成运维信息（换行分隔）
                    item['运维信息'] = '\n'.join(ops_info_parts)

                # 处理运维字段，确保空值显示为空字符串而不是None
                ops_fields = ['SSH端口', 'Root密码']
                for field in ops_fields:
                    if field in item and item[field] is None:
                        item[field] = ''
                    elif field not in item:
                        item[field] = ''

    # 处理维护记录数据，为普通占位符创建格式化文本
    maintenance_records = data.get('维护记录', [])
    if isinstance(maintenance_records, list) and maintenance_records:
        # 创建格式化的维护记录文本
        maintenance_text_parts = []
        for i, record in enumerate(maintenance_records, 1):
            time_str = record.get('time', '未知时间')
            staff_str = record.get('staff', '未知人员')
            type_str = record.get('type', '未知类型')
            content_str = record.get('content', '无内容')
            ones_link = record.get('onesLink', '')

            # 格式化单条维护记录
            record_text = f"{i}. 【{type_str}】{time_str} - {staff_str}\n   {content_str}"
            if ones_link:
                record_text += f"\n   任务链接: {ones_link}"

            maintenance_text_parts.append(record_text)

        # 将所有维护记录合并为一个文本块，用于普通占位符
        new_data['维护记录_文本'] = '\n\n'.join(maintenance_text_parts)

    # 处理客户APP数据，为普通占位符创建格式化文本
    client_apps = data.get('客户APP', [])
    if isinstance(client_apps, list) and client_apps:
        # 创建格式化的客户APP文本
        app_text_parts = []
        for i, app in enumerate(client_apps, 1):
            if isinstance(app, dict):
                appid = app.get('appid', '')
                name = app.get('name', '')
                package_name = app.get('packageName', '')

                # 兼容旧版本数据结构
                if not appid and not package_name:
                    # 旧版本只有name和version
                    name = app.get('name', '')
                    version = app.get('version', '')
                    if name or version:
                        app_text = f"{i}. {name}"
                        if version:
                            app_text += f" (版本: {version})"
                        app_text_parts.append(app_text)
                else:
                    # 新版本有appid、name、packageName、platforms
                    if appid or name or package_name:
                        app_text = f"{i}. {name or '未命名应用'}"
                        if appid:
                            app_text += f"\n   APP ID: {appid}"
                        if package_name:
                            app_text += f"\n   包名: {package_name}"

                        # 添加平台信息
                        platforms = app.get('platforms', [])
                        if platforms:
                            platforms_str = ', '.join(platforms)
                            app_text += f"\n   平台: {platforms_str}"

                        app_text_parts.append(app_text)

        # 将所有客户APP合并为一个文本块，用于普通占位符
        if app_text_parts:
            new_data['客户APP_文本'] = '\n\n'.join(app_text_parts)
        else:
            new_data['客户APP_文本'] = '暂无客户APP信息'

    # 将新的键值对添加到数据副本中（而不是原始数据）
    data_copy.update(new_data)

    # 按行分组处理，收集同一行的所有字段映射
    rows_by_index = {}
    for template_row_idx, data_type, field_map in template_rows:
        if template_row_idx not in rows_by_index:
            rows_by_index[template_row_idx] = {}
        rows_by_index[template_row_idx][data_type] = field_map

    # 按行索引倒序处理
    for template_row_idx in sorted(rows_by_index.keys(), reverse=True):
        row_data_types = rows_by_index[template_row_idx]

        # 如果这一行包含运维用户，使用特殊处理
        if '运维用户' in row_data_types:
            # 合并所有字段映射
            all_field_map = {}
            for data_type, field_map in row_data_types.items():
                all_field_map.update(field_map)
            render_ops_users_range(sheet, data_copy, all_field_map, template_row_idx)
            continue

        # 否则按原来的方式处理每个数据类型
        for data_type, field_map in row_data_types.items():
            data_list = data_copy.get(data_type, [])
            current_app.logger.debug(f"识别到Jinja2 range占位符: 行{template_row_idx}, 类型: {data_type}, 字段映射: {field_map}, 数据: {data_list}")

            # 检查是否有组件端口字段，如果有，使用转换后的字符串版本
            for col, (field, instrs, raw) in field_map.items():
                if field == '组件端口':
                    for item in data_list:
                        # 确保item是字典类型
                        if isinstance(item, dict) and '组件端口_str' in item:
                            item['组件端口'] = item['组件端口_str']

            render_range_rows(sheet, data_list, field_map, template_row_idx, data_copy)

def generate_security_monitoring_excel(data, template_path=None):
    """
    支持 {{xxx | range}} 动态插入行渲染。
    集成性能监控，记录生成时间和缓存命中率。
    """
    from datetime import datetime
    import os
    from flask import current_app

    # 开始性能计时
    start_time = time.time()

    # 构建组件信息字典（组件名 -> 字段dict），包含从数据库获取的信息
    form_type = data.get('文档后缀', '安全监测')
    db_components = ComponentConfig.query.filter_by(form_type=form_type, is_active=True).all()
    component_map = {}
    for comp in db_components:
        # 获取关联的分类信息
        category = ComponentCategory.query.filter_by(key=comp.category_key).first()
        category_info = {
            'display_name': category.display_name if category else '',
            'color': category.color if category else '',
            'icon': category.icon if category else '',
            'order': category.order if category else 0
        }

        component_map[comp.name] = {
            'id': comp.id,
            'name': comp.name,
            'display_name': comp.display_name,
            'version': comp.version or '',
            'port': comp.default_port or '',
            'default_port': comp.default_port or '',
            'description': comp.description or '',
            'protocol': comp.protocol or '',
            'alias': comp.alias or '',
            'category_key': comp.category_key or '',
            'form_type': comp.form_type or '',
            'is_active': comp.is_active,
            'created_at': comp.created_at.strftime('%Y-%m-%d %H:%M:%S') if comp.created_at else '',
            'updated_at': comp.updated_at.strftime('%Y-%m-%d %H:%M:%S') if comp.updated_at else '',
            # 添加扁平化的分类信息
            'category_display_name': category_info['display_name'],
            'category_color': category_info['color'],
            'category_icon': category_info['icon'],
            'category_order': category_info['order'],
            # 'category_info': category_info, # 暂不添加嵌套对象，前端占位符不易直接访问
        }

    # 统计组件部署数量，并添加到 component_map 中
    component_counts = {}
    server_info_list = data.get('服务器信息', [])
    if isinstance(server_info_list, list):
        for server in server_info_list:
            if '部署应用' in server and isinstance(server['部署应用'], list):
                for app in server['部署应用']:
                     if isinstance(app, str):
                        # 使用数据库中的组件名称作为key进行统计
                        db_comp = ComponentConfig.query.filter_by(name=app, form_type=form_type).first()
                        comp_name = db_comp.name if db_comp else app # 如果数据库中不存在，使用原始名称
                        component_counts[comp_name] = component_counts.get(comp_name, 0) + 1

    # 将统计数量添加到 component_map 中
    for comp_name, count in component_counts.items():
        if comp_name in component_map:
            component_map[comp_name]['count'] = count
        else:
             # 如果有未在数据库中配置的组件，也添加到map中（仅含数量）
             component_map[comp_name] = {'count': count, 'name': comp_name}

    # 正则匹配 {{组件名.字段}} 和 {{组件名.count}}
    # 支持组件名中的连字符、下划线等字符
    comp_field_pattern = re.compile(r'{{\s*([\w\-]+)\.([\w\-]+)\s*}}')

    if template_path is None:
        template_path = os.path.join(current_app.config['EXCEL_FOLDER'], 'templates', '安全监测-运维信息登记模板.xlsx')

    # 使用安全的模板加载方式
    wb = load_template_safe(template_path)
    if wb is None:
        current_app.logger.error(f"加载安全监测模板失败: {template_path}")
        return None

    # 预处理服务器信息，生成运维信息汇总（保持运维用户数组结构）
    if '服务器信息' in data and isinstance(data['服务器信息'], list):
        for server in data['服务器信息']:
            if isinstance(server, dict) and '运维用户' in server and isinstance(server['运维用户'], list):
                ops_users = server['运维用户']

                # 创建运维信息汇总（换行分隔格式）
                ops_info_parts = []

                # 为了兼容旧模板，同时生成扁平化字段
                root_user = next((user for user in ops_users if user.get('用户名') == 'root'), None)
                if root_user:
                    server['Root密码'] = root_user.get('密码', '')
                    ops_info_parts.append(f"Root密码：{root_user.get('密码', '')}")

                # 提取非Root运维用户
                non_root_users = [user for user in ops_users if user.get('用户名') != 'root']

                # 处理所有非Root运维用户
                for user in non_root_users:
                    if user.get('用户名') or user.get('密码'):
                        user_info = f"运维用户：{user.get('用户名', '')} / {user.get('密码', '')}"
                        ops_info_parts.append(user_info)

                # 生成运维信息（换行分隔）
                server['运维信息'] = '\n'.join(ops_info_parts)

    # 处理组件列表工作表 (保留原有逻辑，但可能需要调整以使用新的component_map)
    if '组件列表' in wb.sheetnames and '服务器信息' in data and data['服务器信息']:
        component_sheet = wb['组件列表']

        # 更新组件部署数量
        component_counts = {}
        for server in data['服务器信息']:
            if '部署应用' in server:
                for app in server['部署应用']:
                    component_counts[app] = component_counts.get(app, 0) + 1

        # 更新组件列表中的部署数量和端口信息
        for row in range(2, component_sheet.max_row + 1):
            component_name = component_sheet.cell(row=row, column=1).value
            if component_name in component_counts:
                # 更新部署数量
                component_sheet.cell(row=row, column=2).value = component_counts[component_name]

                # 更新端口信息
                for server in data['服务器信息']:
                    if '部署应用' in server and '组件端口' in server and component_name in server['部署应用']:
                        port = server['组件端口'].get(component_name, '无')
                        if port and port != '无':
                            component_sheet.cell(row=row, column=5).value = port
                            break

    for sheet in wb.worksheets:
        # 先处理动态range (保留原有逻辑)
        process_dynamic_range(sheet, data)
        # 再处理普通占位符（全表）
        rows_to_delete = []

        for row in sheet.iter_rows():
            for cell in row:
                 if cell.value and isinstance(cell.value, str) and '| range' not in cell.value and '| split:' not in cell.value:
                    original_value = cell.value
                    new_value = original_value
                    has_empty_placeholder = False

                    # 1. 处理 {{组件名.字段}} 和 {{组件名.count}} 占位符
                    def comp_field_replacer(match):
                        comp_name, field = match.group(1), match.group(2)
                        # 从 component_map 中获取值
                        return str(component_map.get(comp_name, {}).get(field, ''))
                    new_value = comp_field_pattern.sub(comp_field_replacer, new_value)


                    # 2. 处理原有 {{key}} 占位符
                    for key, value in data.items():
                        placeholder = f"{{{{{key}}}}}"
                        if placeholder in new_value:
                             # 特殊处理客户APP，优先使用格式化文本
                             if key == '客户APP' and '客户APP_文本' in data:
                                 formatted_value = data['客户APP_文本']
                                 new_value = new_value.replace(placeholder, str(formatted_value))
                             elif not value or (isinstance(value, str) and value.strip() == ''):
                                has_empty_placeholder = True
                             else:
                                if isinstance(value, list):
                                    # 对于客户APP列表，如果没有格式化文本，使用简单格式
                                    if key == '客户APP':
                                        app_names = []
                                        for app in value:
                                            if isinstance(app, dict):
                                                name = app.get('name', app.get('appid', '未命名'))
                                                app_names.append(name)
                                            else:
                                                app_names.append(str(app))
                                        value = ', '.join(app_names)
                                    else:
                                        value = ', '.join(str(x) for x in value)
                                new_value = new_value.replace(placeholder, str(value))

                    cell.value = new_value

                    # 如果有空占位符且整行只包含占位符，标记删除该行
                    stripped_original = original_value.strip()
                    stripped_new = cell.value.strip()
                    is_single_placeholder = stripped_original.startswith("{{") and stripped_original.endswith("}}")

                    # 如果是单个占位符，并且为空或未被替换（替换后的值与原始值相同，或者替换后仍然包含占位符）
                    if has_empty_placeholder and is_single_placeholder:
                        if re.search(r"{{.*?}}", cell.value):  # 替换后仍含占位符
                            if cell.row not in rows_to_delete:
                                rows_to_delete.append(cell.row)
                                current_app.logger.debug(f"标记删除空占位符行 (替换后仍有占位符) {cell.row}: {original_value}")
                        elif stripped_original == stripped_new: # 替换前后一致，说明未真正替换
                            if cell.row not in rows_to_delete:
                                rows_to_delete.append(cell.row)
                                current_app.logger.debug(f"标记删除空占位符行 (无替换发生但有空占位符) {cell.row}: {original_value}")

        # 从后往前删除行，避免行号变化影响
        for row_num in sorted(rows_to_delete, reverse=True):
            sheet.delete_rows(row_num, 1)

    # 添加部署图sheet (保留原有逻辑)
    add_deployment_diagram_sheet(wb, data)

    # 处理文件名 - 添加并发安全机制
    company_name = data.get('公司名称', '客户公司名称')
    record_date = data.get('记录日期', datetime.now().strftime('%Y%m%d'))

    # 生成唯一文件名，避免并发冲突
    base_filename = f"{company_name}-运维文档-安全监测_{record_date}"
    base_filename = base_filename.replace(':', '_').replace('/', '_').replace('\\', '_')

    # 确保文件名唯一性
    filename = generate_unique_filename(base_filename, current_app.config['EXCEL_GENERATED_FOLDER'])
    filepath = os.path.join(current_app.config['EXCEL_GENERATED_FOLDER'], filename)

    # 使用安全的文件保存方式
    save_file_safe(wb, filepath)

    # 记录性能指标
    end_time = time.time()
    duration = end_time - start_time

    # 获取文件大小
    try:
        file_size = os.path.getsize(filepath)
    except:
        file_size = 0

    # 记录到性能监控系统
    record_excel_generation_performance(
        form_type=form_type,
        duration=duration,
        file_size=file_size,
        cache_hit=True  # 这里可以根据实际缓存命中情况调整
    )

    return filename


def get_component_categories_by_form_type(form_type):
    """
    根据表单类型获取组件分类映射（从数据库获取）
    """
    try:
        from app.models.models import ComponentConfig, ComponentCategory

        current_app.logger.info(f"正在从数据库获取 {form_type} 的组件分类映射...")

        # 获取指定表单类型的所有组件
        components = ComponentConfig.query.filter_by(
            form_type=form_type,
            is_active=True
        ).all()

        # 创建组件名称到分类显示名称的映射
        component_categories = {}
        category_display_names = {}

        for component in components:
            # 获取分类信息
            category = ComponentCategory.query.filter_by(key=component.category_key).first()
            if category:
                component_categories[component.name] = category.display_name
                category_display_names[component.category_key] = category.display_name
            else:
                # 如果分类不存在，使用默认值
                component_categories[component.name] = '其他组件'

        current_app.logger.info(f"成功获取 {len(component_categories)} 个组件的分类映射")
        current_app.logger.debug(f"分类映射: {component_categories}")

        return component_categories

    except Exception as e:
        current_app.logger.error(f"从数据库获取组件分类映射失败: {str(e)}")
        # 如果数据库获取失败，回退到静态配置
        return get_static_component_categories_by_form_type(form_type)


def get_static_component_categories_by_form_type(form_type):
    """
    静态组件分类映射（作为回退方案）
    """
    if form_type == '安全监测':
        # 安全监测表单的组件分类：app、server、ops、数据库
        return {
            # app - 应用组件
            'analyzer-dev': '应用组件',
            'app-sender': '应用组件',
            'cleaner': '应用组件',
            'init': '应用组件',
            'receiver': '应用组件',
            'security-event': '应用组件',
            'threat': '应用组件',
            'threat-index': '应用组件',
            'transfer': '应用组件',
            'crash': '应用组件',
            'nginx': '应用组件',

            # server - 服务器组件
            'postgres': '服务器组件',
            'redis': '服务器组件',
            'minio': '服务器组件',
            'zookeeper': '服务器组件',
            'kafka': '服务器组件',
            'hbase': '服务器组件',
            'elasticsearchMaster': '服务器组件',
            'elasticsearchClient': '服务器组件',
            'kibana': '服务器组件',
            'web-service-nginx': '服务器组件',
            'web-service': '服务器组件',

            # ops - 运维组件
            'docker': '运维组件',
            'docker-compose': '运维组件',
            'monitor': '运维组件',

            # 数据库组件
            'mongodb': '数据库'
        }
    elif form_type == '应用加固':
        # 应用加固表单的组件分类
        return {
            # 基础组件
            'docker': '基础组件',
            'docker-compose': '基础组件',

            # 平台组件
            'front-ssp-admin': '平台组件',
            'front-ssp-user': '平台组件',
            'luna': '平台组件',

            # 加固引擎
            'jiagu-engine': '加固引擎',

            # 服务组件
            'nginx': '服务组件',
            'postgres': '服务组件',
            'redis': '服务组件'
        }
    elif form_type == '安全测评':
        # 安全测评表单的组件分类
        return {
            # 基础组件
            'docker': '基础组件',
            'docker-compose': '基础组件',
            'adb': '基础组件',

            # 数据库组件
            'mongo': '数据库组件',
            'redis': '数据库组件',

            # 工具组件
            'frida': '工具组件',
            'objection': '工具组件',

            # 服务组件
            'nginx': '服务组件',
            'web-service': '服务组件',

            # 引擎组件
            'analysis-engine': '引擎组件',
            'scan-engine': '引擎组件',

            # 前端组件
            'web-frontend': '前端组件',

            # 后端组件
            'api-backend': '后端组件'
        }
    else:
        # 默认返回空映射
        return {}


def get_category_color(category):
    """
    根据组件分类获取对应的颜色
    优先从数据库获取分类颜色配置，如果没有则使用默认映射
    """
    from openpyxl.styles import PatternFill
    from flask import current_app

    try:
        # 首先尝试从数据库获取分类颜色
        category_obj = ComponentCategory.query.filter_by(display_name=category).first()
        if category_obj and category_obj.color:
            # 从Bootstrap颜色类转换为Excel颜色代码
            excel_color = extract_color_from_class(category_obj.color)
            current_app.logger.info(f"从数据库获取分类 '{category}' 的颜色: {category_obj.color} -> {excel_color}")
            return PatternFill(start_color=excel_color, end_color=excel_color, fill_type="solid")
    except Exception as e:
        current_app.logger.warning(f"从数据库获取分类颜色失败: {e}")

    # 如果数据库中没有找到，使用静态颜色映射
    color_mapping = {
        # 安全监测表单分类
        'app': "E6F3FF",      # 浅蓝色
        'server': "E6FFE6",   # 浅绿色
        'ops': "FFFFE6",      # 浅黄色
        '应用组件': "E6F3FF",   # 浅蓝色
        '服务器组件': "E6FFE6",   # 浅绿色
        '运维组件': "E6F9FF",   # 浅蓝绿色
        '数据库': "FFF8E6",     # 浅黄色

        # 应用加固表单分类
        '基础组件': "E6E6E6",   # 浅灰色
        '平台组件': "E6F3FF",   # 浅蓝色
        '加固引擎': "FFE6E6",   # 浅红色
        '服务组件': "E6FFE6",   # 浅绿色

        # 安全测评表单分类 - 传统分类
        '引擎组件': "FFE6E6",   # 浅红色
        '数据库组件': "E6FFE6", # 浅绿色
        '工具组件': "E6F9FF",   # 浅蓝绿色
        '前端组件': "FFF8E6",   # 浅黄色
        '后端组件': "F5F5F5",   # 浅灰色

        # AIMRSK新增分类
        'AIMRSK依赖组件': "E6F9FF",   # 浅蓝绿色 (对应 bg-info)
        'AIMRSK引擎组件': "FFF8E6",   # 浅黄色 (对应 bg-warning)
        'AIMRSK Web组件': "E6FFE6",   # 浅绿色 (对应 bg-success)
        '工具平台组件': "E6F3FF",     # 浅蓝色 (对应 bg-primary)

        # 默认颜色
        '其他组件': "F5F5F5"    # 浅灰色
    }

    color = color_mapping.get(category, "F5F5F5")  # 默认浅灰色
    current_app.logger.info(f"使用静态映射为分类 '{category}' 获取颜色: {color}")
    return PatternFill(start_color=color, end_color=color, fill_type="solid")


def get_legend_items_by_form_type(form_type):
    """
    根据表单类型获取图例项（从数据库获取）
    """
    try:
        from app.models.models import ComponentCategory

        current_app.logger.info(f"正在从数据库获取 {form_type} 的图例项...")

        # 获取指定表单类型的所有分类（使用新的单一表单类型设计）
        categories = ComponentCategory.query.filter_by(
            form_type=form_type,
            is_active=True
        ).order_by(ComponentCategory.order).all()

        # 创建图例项
        legend_items = []
        for category in categories:
            # 跳过未分组分类，不在图例中显示
            if category.key.startswith('uncategorized'):
                continue

            # 从color字段提取颜色，如果没有则使用默认颜色
            color = extract_color_from_class(category.color) if category.color else "F5F5F5"
            legend_items.append((category.display_name, color))
            current_app.logger.info(f"添加图例项: {category.display_name} -> {color} (来源: {category.color})")

        # 添加其他组件图例
        legend_items.append(('其他组件', "F5F5F5"))

        current_app.logger.info(f"成功获取 {len(legend_items)} 个图例项")
        return legend_items

    except Exception as e:
        current_app.logger.error(f"从数据库获取图例项失败: {str(e)}")
        # 如果数据库获取失败，回退到静态配置
        return get_static_legend_items_by_form_type(form_type)


def extract_color_from_class(color_class):
    """
    从Bootstrap颜色类中提取对应的颜色代码
    """
    color_mapping = {
        'bg-primary': "E6F3FF",    # 蓝色
        'bg-success': "E6FFE6",    # 绿色
        'bg-info': "E6F9FF",       # 浅蓝色
        'bg-warning': "FFF8E6",    # 黄色
        'bg-danger': "FFE6E6",     # 红色
        'bg-secondary': "F5F5F5",  # 灰色
        'bg-dark': "E6E6E6",       # 深灰色
        'bg-light': "FAFAFA"       # 浅灰色
    }

    return color_mapping.get(color_class, "F5F5F5")


def get_static_legend_items_by_form_type(form_type):
    """
    静态图例项配置（作为回退方案）
    """
    if form_type == '安全监测':
        return [
            ('应用组件', "E6F3FF"),
            ('服务器组件', "E6FFE6"),
            ('运维组件', "E6F9FF"),
            ('数据库', "FFF8E6"),
            ('其他组件', "F5F5F5")
        ]
    elif form_type == '应用加固':
        return [
            ('基础组件', "E6E6E6"),
            ('平台组件', "E6F3FF"),
            ('加固引擎', "FFE6E6"),
            ('服务组件', "E6FFE6"),
            ('其他组件', "F5F5F5")
        ]
    elif form_type == '安全测评':
        return [
            ('基础组件', "E6E6E6"),
            ('服务组件', "E6F3FF"),
            ('引擎组件', "FFE6E6"),
            ('AIMRSK依赖组件', "E6F9FF"),
            ('AIMRSK引擎组件', "FFF8E6"),
            ('AIMRSK Web组件', "E6FFE6"),
            ('工具平台组件', "E6F3FF"),
            ('其他组件', "F5F5F5")
        ]
    else:
        # 默认图例
        return [
            ('基础组件', "E6F3FF"),
            ('平台组件', "E6FFE6"),
            ('引擎组件', "F0E6FF"),
            ('服务组件', "FFFFE6"),
            ('其他组件', "F5F5F5")
        ]


def add_deployment_diagram_sheet(wb, data):
    """
    为Excel文件添加部署图sheet页面
    展现组件分类、组件、机器IP的关系，并插入架构图片
    """
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter

    # 检查是否有服务器信息，如果没有则不生成部署图
    server_info_list = data.get('服务器信息', [])
    if not server_info_list or not isinstance(server_info_list, list):
        current_app.logger.info("没有服务器信息，跳过部署图生成")
        return None

    # 检查是否有有效的部署应用
    has_deployed_apps = False
    for server in server_info_list:
        deployed_apps = server.get('部署应用', [])
        if deployed_apps and len(deployed_apps) > 0:
            has_deployed_apps = True
            break

    if not has_deployed_apps:
        current_app.logger.info("没有部署应用信息，跳过部署图生成")
        return None

    # 创建新的工作表
    if '部署架构图' in wb.sheetnames:
        wb.remove(wb['部署架构图'])

    diagram_sheet = wb.create_sheet('部署架构图')
    current_app.logger.info(f"正在为 {len(server_info_list)} 台服务器生成部署架构图")

    # 设置标题
    title_cell = diagram_sheet.cell(row=1, column=1)
    title_cell.value = "系统部署架构图"
    title_cell.font = Font(name='微软雅黑', size=16, bold=True)
    title_cell.alignment = Alignment(horizontal='center', vertical='center')

    # 收集所有组件信息用于转置表格
    all_components = []
    all_servers = []

    # 先收集所有数据
    for server in server_info_list:
        server_ip = server.get('IP地址', '')
        deployed_apps = server.get('部署应用', [])
        component_ports = server.get('组件端口', {})

        if not deployed_apps or not server_ip:
            continue

        if server_ip not in all_servers:
            all_servers.append(server_ip)

        for app in deployed_apps:
            if app and app not in all_components:
                all_components.append(app)

    # 如果没有有效数据，返回
    if not all_components or not all_servers:
        current_app.logger.info("没有有效的组件或服务器数据，跳过部署图生成")
        return None

    # 计算表格大小并合并标题行
    total_cols = len(all_servers) + 3  # 组件分类 + 组件名称 + 端口 + 服务器列
    diagram_sheet.merge_cells(f'A1:{get_column_letter(total_cols)}1')

    # 设置新的表头结构
    # 第一行：标题（已设置）
    # 第二行：表头

    # 设置列表头（第二行）
    headers = ['组件分类', '组件名称', '端口'] + all_servers
    for col, header in enumerate(headers, 1):
        cell = diagram_sheet.cell(row=2, column=col)
        cell.value = header
        cell.font = Font(name='微软雅黑', size=12, bold=True)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
        cell.font = Font(name='微软雅黑', size=12, bold=True, color="FFFFFF")
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

    # 根据表单类型获取组件分类映射
    form_type = data.get('文档后缀', '通用')
    component_categories = get_component_categories_by_form_type(form_type)

    # 创建服务器到组件端口的映射
    server_component_ports = {}
    for server in server_info_list:
        server_ip = server.get('IP地址', '')
        deployed_apps = server.get('部署应用', [])
        component_ports = server.get('组件端口', {})

        if not server_ip or not deployed_apps:
            continue

        server_component_ports[server_ip] = {}

        for app in deployed_apps:
            if not app:
                continue

            # 获取端口信息
            port = '无'

            # 处理字典格式的组件端口
            if isinstance(component_ports, dict) and app in component_ports:
                port_value = component_ports[app]
                if port_value and str(port_value).strip() and str(port_value).strip() != '无':
                    port = str(port_value)
            # 处理字符串格式的组件端口
            elif isinstance(component_ports, str) and component_ports:
                port_lines = component_ports.split('\n')
                for line in port_lines:
                    if ':' in line:
                        component_name, port_value = line.split(':', 1)
                        component_name = component_name.strip()
                        port_value = port_value.strip()
                        if component_name == app and port_value and port_value != '无':
                            port = port_value
                            break

            server_component_ports[server_ip][app] = port

    # 按组件分类对组件进行分组和排序
    components_by_category = {}
    for component in all_components:
        category = component_categories.get(component, '其他组件')
        if category not in components_by_category:
            components_by_category[category] = []
        components_by_category[category].append(component)

    # 对分类进行排序（可以根据需要调整排序规则）
    sorted_categories = sorted(components_by_category.keys())

    # 填充表格数据
    current_row = 3  # 第3行开始填充数据（第1行标题，第2行表头）

    for category in sorted_categories:
        components_in_category = sorted(components_by_category[category])  # 对组件名称也进行排序
        category_start_row = current_row

        for i, component in enumerate(components_in_category):
            # 填充组件分类（只在第一行填充，后续行留空用于合并）
            if i == 0:
                category_cell = diagram_sheet.cell(row=current_row, column=1)
                category_cell.value = category
                category_cell.font = Font(name='微软雅黑', size=11, bold=True)
                category_cell.alignment = Alignment(horizontal='center', vertical='center')
                category_cell.fill = get_category_color(category)
                category_cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            else:
                # 为后续行设置边框，但不填充内容（用于合并）
                category_cell = diagram_sheet.cell(row=current_row, column=1)
                category_cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

            # 填充组件名称
            component_cell = diagram_sheet.cell(row=current_row, column=2)
            component_cell.value = component
            component_cell.font = Font(name='微软雅黑', size=11)
            component_cell.alignment = Alignment(horizontal='center', vertical='center')
            component_cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 填充端口信息（第3列）
            # 获取该组件的默认端口（从任一部署了该组件的服务器获取）
            default_port = '无'
            for server_ip in all_servers:
                if server_ip in server_component_ports and component in server_component_ports[server_ip]:
                    port_value = server_component_ports[server_ip][component]
                    if port_value and port_value != '无':
                        default_port = port_value
                        break

            port_cell = diagram_sheet.cell(row=current_row, column=3)
            port_cell.value = default_port
            port_cell.font = Font(name='微软雅黑', size=11)
            port_cell.alignment = Alignment(horizontal='center', vertical='center')
            port_cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 填充每个服务器的部署状态（打勾或空白）
            for col, server_ip in enumerate(all_servers, 4):  # 从第4列开始（组件分类、组件名称、端口之后）
                is_deployed = False

                # 检查该服务器是否部署了这个组件
                if server_ip in server_component_ports and component in server_component_ports[server_ip]:
                    is_deployed = True

                deploy_cell = diagram_sheet.cell(row=current_row, column=col)
                deploy_cell.value = '✓' if is_deployed else ''
                deploy_cell.font = Font(name='微软雅黑', size=14, bold=True, color="00AA00")  # 绿色勾选
                deploy_cell.alignment = Alignment(horizontal='center', vertical='center')
                deploy_cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # 如果部署了，设置浅绿色背景
                if is_deployed:
                    deploy_cell.fill = PatternFill(start_color="E6FFE6", end_color="E6FFE6", fill_type="solid")

            current_row += 1

        # 合并组件分类单元格（如果该分类有多个组件）
        if len(components_in_category) > 1:
            category_end_row = current_row - 1
            diagram_sheet.merge_cells(f'A{category_start_row}:A{category_end_row}')

            # 为合并后的单元格重新设置样式
            merged_cell = diagram_sheet.cell(row=category_start_row, column=1)
            merged_cell.fill = get_category_color(category)
            merged_cell.font = Font(name='微软雅黑', size=11, bold=True)
            merged_cell.alignment = Alignment(horizontal='center', vertical='center')

    # 设置列宽
    # 组件分类列
    diagram_sheet.column_dimensions['A'].width = 15
    # 组件名称列
    diagram_sheet.column_dimensions['B'].width = 25
    # 服务器IP列（动态设置）
    for col, server_ip in enumerate(all_servers, 3):
        diagram_sheet.column_dimensions[get_column_letter(col)].width = 15

    # 添加图例
    legend_start_row = current_row + 2
    legend_cell = diagram_sheet.cell(row=legend_start_row, column=1)
    legend_cell.value = "图例："
    legend_cell.font = Font(name='微软雅黑', size=12, bold=True)

    # 根据表单类型获取图例项
    legend_items = get_legend_items_by_form_type(form_type)

    for i, (category, color) in enumerate(legend_items):
        row = legend_start_row + 1 + i

        # 颜色块
        color_cell = diagram_sheet.cell(row=row, column=1)
        color_cell.value = "  "
        color_cell.fill = PatternFill(start_color=color, end_color=color, fill_type="solid")
        color_cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 分类名称
        name_cell = diagram_sheet.cell(row=row, column=2)
        name_cell.value = category
        name_cell.font = Font(name='微软雅黑', size=11)
        name_cell.alignment = Alignment(horizontal='left', vertical='center')

    # 添加统计信息
    stats_start_row = legend_start_row + len(legend_items) + 2
    stats_cell = diagram_sheet.cell(row=stats_start_row, column=1)
    stats_cell.value = "统计信息："
    stats_cell.font = Font(name='微软雅黑', size=12, bold=True)

    # 统计各类组件数量
    category_counts = {}
    total_components = 0

    for server in server_info_list:
        deployed_apps = server.get('部署应用', [])
        for app in deployed_apps:
            if app:
                category = component_categories.get(app, '其他组件')
                category_counts[category] = category_counts.get(category, 0) + 1
                total_components += 1

    # 显示统计信息
    stats_row = stats_start_row + 1
    for category, count in category_counts.items():
        cell = diagram_sheet.cell(row=stats_row, column=1)
        cell.value = f"{category}: {count} 个组件"
        cell.font = Font(name='微软雅黑', size=11)
        stats_row += 1

    # 总计
    total_cell = diagram_sheet.cell(row=stats_row, column=1)
    total_cell.value = f"总计: {total_components} 个组件实例"
    total_cell.font = Font(name='微软雅黑', size=11, bold=True)

    # 添加架构图片到右侧区域
    add_architecture_images(diagram_sheet, 3, data, total_cols + 2)  # 从第3行开始，放在表格右侧

    return diagram_sheet


def add_architecture_images(sheet, start_row, data, start_col=1):
    """
    在部署架构图sheet中添加架构图片

    Args:
        sheet: Excel工作表对象
        start_row: 开始插入图片的行号
        data: 表单数据，用于判断表单类型
        start_col: 开始插入图片的列号（默认为1，即A列）
    """
    from openpyxl.drawing.image import Image as OpenpyxlImage
    from openpyxl.styles import Font, Alignment
    import os

    try:
        # 获取表单类型
        form_type = data.get('文档后缀', '安全监测')

        # 只为安全监测表单添加架构图
        if form_type != '安全监测':
            return

        # 定义图片路径 - 使用相对路径避免Flask上下文问题
        # 获取当前文件的目录，然后构建到static目录的路径
        current_file_dir = os.path.dirname(os.path.abspath(__file__))  # backend/app/excel/
        backend_dir = os.path.dirname(os.path.dirname(current_file_dir))  # backend/
        static_dir = os.path.join(backend_dir, 'static', 'images', 'architecture')

        # 确保目录存在
        os.makedirs(static_dir, exist_ok=True)

        # 定义图片文件名（需要将您提供的图片保存到这些路径）
        image_files = [
            {
                'filename': 'security_monitoring_cluster.png',
                'title': '移动应用安全监测平台-部署示意图（集群版）',
                'description': '集群版部署架构，支持高可用和负载均衡'
            },
            {
                'filename': 'security_monitoring_network.png',
                'title': '移动应用安全监测平台-网络架构图',
                'description': '网络拓扑和数据流向示意图'
            }
        ]

        current_row = start_row

        # 设置列宽以适应图片显示
        from openpyxl.utils import get_column_letter
        # 标题和描述列
        title_col_letter = get_column_letter(start_col)
        sheet.column_dimensions[title_col_letter].width = 30
        # 图片显示列，设置合适的宽度
        for col_idx in range(start_col + 1, start_col + 8):  # 图片区域列
            col_letter = get_column_letter(col_idx)
            sheet.column_dimensions[col_letter].width = 12

        # 添加架构图区域标题 - 优化样式
        title_cell = sheet.cell(row=current_row, column=start_col)
        title_cell.value = "🏛️ 系统架构图"
        title_cell.font = Font(name='微软雅黑', size=14, bold=True, color="1565C0")  # 材料设计蓝色
        title_cell.alignment = Alignment(horizontal='left', vertical='center')

        # 优化标题背景和边框
        from openpyxl.styles import PatternFill, Border, Side
        title_cell.fill = PatternFill(start_color="BBDEFB", end_color="BBDEFB", fill_type="solid")
        title_cell.border = Border(
            left=Side(style='medium', color="1565C0"),
            bottom=Side(style='thin', color="1565C0")
        )
        sheet.row_dimensions[current_row].height = 32

        current_row += 2  # 留出适当空白

        # 遍历每个图片
        for i, image_info in enumerate(image_files):
            image_path = os.path.join(static_dir, image_info['filename'])

            # 检查图片文件是否存在
            if not os.path.exists(image_path):
                # 如果图片不存在，添加美化的占位符
                placeholder_cell = sheet.cell(row=current_row, column=start_col)
                placeholder_cell.value = f"🖼️ [图片占位符] {image_info['title']}"
                placeholder_cell.font = Font(name='微软雅黑', size=12, bold=True, color="FF6B35")  # 橙红色
                placeholder_cell.alignment = Alignment(horizontal='left', vertical='center')
                placeholder_cell.fill = PatternFill(start_color="FFF2E6", end_color="FFF2E6", fill_type="solid")
                sheet.row_dimensions[current_row].height = 25

                desc_cell = sheet.cell(row=current_row + 1, column=start_col)
                desc_cell.value = f"📝 说明: {image_info['description']}"
                desc_cell.font = Font(name='微软雅黑', size=10, color="666666", italic=True)
                desc_cell.alignment = Alignment(horizontal='left', vertical='center')
                sheet.row_dimensions[current_row + 1].height = 20

                # 添加提示信息
                tip_cell = sheet.cell(row=current_row + 2, column=start_col)
                tip_cell.value = f"💡 请将图片文件保存为: {image_info['filename']}"
                tip_cell.font = Font(name='微软雅黑', size=9, color="999999")
                tip_cell.alignment = Alignment(horizontal='left', vertical='center')
                sheet.row_dimensions[current_row + 2].height = 18

                current_row += 2  # 减少占位符后的空白
                continue

            try:
                # 添加图片标题 - 优化样式和位置
                img_title_cell = sheet.cell(row=current_row, column=start_col)
                img_title_cell.value = f"🏗️ {image_info['title']}"  # 添加建筑图标
                img_title_cell.font = Font(name='微软雅黑', size=12, bold=True, color="1976D2")  # 材料设计蓝色
                img_title_cell.alignment = Alignment(horizontal='left', vertical='center')
                # 优化标题背景色 - 更柔和的蓝色
                img_title_cell.fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")
                # 设置行高
                sheet.row_dimensions[current_row].height = 28
                current_row += 1

                # 添加图片描述 - 优化样式
                desc_cell = sheet.cell(row=current_row, column=start_col)
                desc_cell.value = f"📋 {image_info['description']}"
                desc_cell.font = Font(name='微软雅黑', size=9, color="757575", italic=True)  # 更柔和的灰色
                desc_cell.alignment = Alignment(horizontal='left', vertical='center')
                # 设置行高
                sheet.row_dimensions[current_row].height = 22
                current_row += 1

                # 插入图片
                img = OpenpyxlImage(image_path)

                # 获取原始图片尺寸
                original_width = img.width
                original_height = img.height

                # 优化图片大小 - 根据Excel列宽和实际需求调整
                # Excel列宽约为64像素，右侧区域有约8-10列可用空间
                max_available_width = 600  # 约9-10列的宽度，更适合Excel显示
                max_available_height = 450  # 适中的高度，不会占用太多行

                if 'cluster' in image_info['filename']:
                    # 集群版部署图 - 适中尺寸，保持清晰度
                    target_width = min(max_available_width, 580)
                    target_height = int(original_height * (target_width / original_width)) if original_width > 0 else 400
                    # 限制最大高度
                    if target_height > max_available_height:
                        target_height = max_available_height
                        target_width = int(original_width * (target_height / original_height)) if original_height > 0 else 580
                else:
                    # 网络架构图 - 稍小一些，更紧凑
                    target_width = min(max_available_width, 520)
                    target_height = int(original_height * (target_width / original_width)) if original_width > 0 else 350
                    # 限制最大高度
                    if target_height > max_available_height:
                        target_height = max_available_height
                        target_width = int(original_width * (target_height / original_height)) if original_height > 0 else 520

                img.width = target_width
                img.height = target_height

                # 优化图片位置 - 锚定到右侧区域，留出适当边距
                # 从第二列开始放置图片，留出一列作为边距
                img_col_letter = get_column_letter(start_col + 1)
                img.anchor = f'{img_col_letter}{current_row}'

                # 添加图片到工作表
                sheet.add_image(img)

                # 优化列宽以适应图片 - 设置图片所在列的宽度
                for col_offset in range(1, 6):  # 调整图片区域的列宽
                    col_letter = get_column_letter(start_col + col_offset)
                    if col_offset == 1:
                        # 图片主要列设置较宽
                        sheet.column_dimensions[col_letter].width = 25
                    else:
                        # 其他列设置适中宽度
                        sheet.column_dimensions[col_letter].width = 15

                # 添加图片说明框（在图片下方，位置更精确）
                # 根据实际图片高度计算说明位置
                pixels_per_row = 20  # Excel行高约20像素
                note_row = current_row + int(target_height / pixels_per_row) + 1
                note_cell = sheet.cell(row=note_row, column=start_col + 1)
                note_cell.value = f"📐 {target_width}×{target_height}px | 📁 {image_info['filename']}"
                note_cell.font = Font(name='微软雅黑', size=8, color="999999")
                note_cell.alignment = Alignment(horizontal='left', vertical='center')
                sheet.row_dimensions[note_row].height = 16

                # 优化行间距计算 - 更精确的空间计算
                pixels_per_row = 20  # Excel标准行高
                rows_needed = max(15, int(target_height / pixels_per_row) + 3)  # 图片高度转换为行数，加3行缓冲
                current_row += rows_needed + 2  # 图片间留适当空白

                current_app.logger.info(f"成功插入图片: {image_info['filename']} (尺寸: {target_width}×{target_height})")

            except Exception as img_error:
                current_app.logger.error(f"插入图片 {image_info['filename']} 时出错: {str(img_error)}")
                # 添加美化的错误占位符
                error_cell = sheet.cell(row=current_row, column=start_col)
                error_cell.value = f"❌ [图片加载失败] {image_info['title']}"
                error_cell.font = Font(name='微软雅黑', size=12, bold=True, color="DC3545")  # 红色
                error_cell.alignment = Alignment(horizontal='left', vertical='center')
                error_cell.fill = PatternFill(start_color="FFEBEE", end_color="FFEBEE", fill_type="solid")
                sheet.row_dimensions[current_row].height = 25

                error_desc_cell = sheet.cell(row=current_row + 1, column=start_col)
                error_desc_cell.value = f"🔧 错误信息: {str(img_error)}"
                error_desc_cell.font = Font(name='微软雅黑', size=9, color="666666")
                error_desc_cell.alignment = Alignment(horizontal='left', vertical='center')
                sheet.row_dimensions[current_row + 1].height = 20

                current_row += 1  # 减少错误信息后的空白

        current_app.logger.info("架构图片添加完成，已放置在右侧区域")

    except Exception as e:
        current_app.logger.error(f"添加架构图片时出错: {str(e)}")
        # 添加错误信息到Excel
        error_cell = sheet.cell(row=start_row, column=start_col)
        error_cell.value = f"架构图加载失败: {str(e)}"
        error_cell.font = Font(name='微软雅黑', size=12, color="FF0000")