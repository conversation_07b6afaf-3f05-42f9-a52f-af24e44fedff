import { ref, reactive, onMounted, onUnmounted } from 'vue'

/**
 * 拖拽功能 Composable
 * 提供流畅的拖拽体验和位置管理
 */
export function useDraggable(containerRef) {
  // 状态管理
  const position = reactive({ x: 0, y: 0 })
  const isDragging = ref(false)
  const isDragCandidate = ref(false)
  const justDragged = ref(false)
  
  // 拖拽相关数据
  const dragState = reactive({
    startX: 0,
    startY: 0,
    offsetX: 0,
    offsetY: 0,
    threshold: 3 // 拖拽触发阈值
  })

  // 常量配置
  const BALL_SIZE = 60
  const MARGIN = 10
  const DRAG_CLEAR_DELAY = 100

  /**
   * 获取视口尺寸
   */
  const getViewportSize = () => ({
    width: document.documentElement.clientWidth || window.innerWidth,
    height: document.documentElement.clientHeight || window.innerHeight
  })

  /**
   * 约束位置在视口范围内
   */
  const constrainPosition = () => {
    const viewport = getViewportSize()
    const maxX = viewport.width - BALL_SIZE - MARGIN
    const maxY = viewport.height - BALL_SIZE - MARGIN

    position.x = Math.max(MARGIN, Math.min(maxX, position.x))
    position.y = Math.max(MARGIN, Math.min(maxY, position.y))
  }

  /**
   * 设置初始位置
   */
  const initPosition = () => {
    const viewport = getViewportSize()
    
    // 默认位置：右侧中间
    position.x = viewport.width - BALL_SIZE - 20
    position.y = Math.floor((viewport.height - BALL_SIZE) / 2)
    
    constrainPosition()
    
    console.log('🎯 悬浮球初始位置:', {
      viewport,
      position: { x: position.x, y: position.y }
    })
  }

  /**
   * 开始拖拽
   */
  const startDrag = (event) => {
    const clientX = event.clientX || event.touches?.[0]?.clientX
    const clientY = event.clientY || event.touches?.[0]?.clientY
    
    if (!clientX || !clientY) return

    // 记录起始位置
    dragState.startX = clientX
    dragState.startY = clientY
    
    // 计算偏移（使用悬浮球中心）
    dragState.offsetX = BALL_SIZE / 2
    dragState.offsetY = BALL_SIZE / 2
    
    isDragCandidate.value = true
    
    // 设置拖拽样式
    document.body.style.userSelect = 'none'
    document.body.style.cursor = 'grab'
    
    console.log('🚀 开始拖拽准备:', {
      start: { x: dragState.startX, y: dragState.startY },
      offset: { x: dragState.offsetX, y: dragState.offsetY }
    })
  }

  /**
   * 处理拖拽移动
   */
  const handleDragMove = (event) => {
    if (!isDragCandidate.value) return

    const clientX = event.clientX || event.touches?.[0]?.clientX
    const clientY = event.clientY || event.touches?.[0]?.clientY
    
    if (!clientX || !clientY) return

    // 计算移动距离
    const deltaX = clientX - dragState.startX
    const deltaY = clientY - dragState.startY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    // 超过阈值才开始真正拖拽
    if (distance > dragState.threshold && !isDragging.value) {
      isDragging.value = true
      document.body.style.cursor = 'grabbing'
      console.log('✨ 开始真正拖拽')
    }

    // 如果正在拖拽，更新位置
    if (isDragging.value) {
      position.x = clientX - dragState.offsetX
      position.y = clientY - dragState.offsetY
      constrainPosition()
    }
  }

  /**
   * 结束拖拽
   */
  const endDrag = () => {
    if (!isDragCandidate.value) return

    // 如果发生了拖拽，设置标记防止触发点击
    if (isDragging.value) {
      justDragged.value = true
      setTimeout(() => {
        justDragged.value = false
      }, DRAG_CLEAR_DELAY)
      
      console.log('🎯 拖拽结束，位置:', { x: position.x, y: position.y })
    }

    // 重置状态
    isDragging.value = false
    isDragCandidate.value = false

    // 恢复样式
    document.body.style.userSelect = ''
    document.body.style.cursor = ''
  }

  /**
   * 处理窗口大小变化
   */
  const handleResize = () => {
    constrainPosition()
    console.log('📐 窗口大小变化，调整位置:', { x: position.x, y: position.y })
  }

  /**
   * 事件监听器
   */
  const addEventListeners = () => {
    // 鼠标事件
    document.addEventListener('mousemove', handleDragMove, { passive: false })
    document.addEventListener('mouseup', endDrag)

    // 触摸事件
    document.addEventListener('touchmove', handleDragMove, { passive: false })
    document.addEventListener('touchend', endDrag)

    // 窗口事件
    window.addEventListener('resize', handleResize)
  }

  const removeEventListeners = () => {
    document.removeEventListener('mousemove', handleDragMove)
    document.removeEventListener('mouseup', endDrag)
    document.removeEventListener('touchmove', handleDragMove)
    document.removeEventListener('touchend', endDrag)
    window.removeEventListener('resize', handleResize)
  }

  // 生命周期管理
  onMounted(() => {
    addEventListeners()
  })

  onUnmounted(() => {
    removeEventListeners()
  })

  return {
    position,
    isDragging,
    justDragged,
    initPosition,
    cleanup: removeEventListeners
  }
}
