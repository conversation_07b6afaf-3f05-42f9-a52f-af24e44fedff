/**
 * 公共字段管理工具类
 * 用于管理和复用表单字段配置
 */

import { 
  getAllCommonFields, 
  getCommonField, 
  getAllStandardGroups, 
  getStandardGroup 
} from '@/config/commonFields.js'

/**
 * 公共字段管理器
 */
export class CommonFieldManager {
  constructor() {
    this.commonFields = getAllCommonFields()
    this.standardGroups = getAllStandardGroups()
  }

  /**
   * 获取字段配置
   * @param {string} fieldName - 字段名
   * @returns {Object|null} - 字段配置对象
   */
  getFieldConfig(fieldName) {
    return getCommonField(fieldName)
  }

  /**
   * 获取分组配置
   * @param {string} groupName - 分组名
   * @returns {Object|null} - 分组配置对象
   */
  getGroupConfig(groupName) {
    return getStandardGroup(groupName)
  }

  /**
   * 创建标准表单配置
   * @param {string} formType - 表单类型
   * @returns {Object} - 完整的表单配置
   */
  createStandardFormConfig(formType) {
    const config = {
      form_type: formType,
      groups: [],
      fields: []
    }

    // 添加标准分组
    this.standardGroups.forEach(groupConfig => {
      const group = {
        ...groupConfig,
        form_type: formType
      }
      config.groups.push(group)

      // 添加分组的默认字段
      if (groupConfig.default_fields && groupConfig.default_fields.length > 0) {
        groupConfig.default_fields.forEach((fieldName, index) => {
          const fieldConfig = this.getFieldConfig(fieldName)
          if (fieldConfig) {
            const field = {
              ...fieldConfig,
              form_type: formType,
              group_name: groupConfig.group_name,
              display_order: index + 1
            }
            config.fields.push(field)
          }
        })
      }
    })

    return config
  }

  /**
   * 检查字段是否为公共字段
   * @param {string} fieldName - 字段名
   * @returns {boolean} - 是否为公共字段
   */
  isCommonField(fieldName) {
    return this.commonFields.hasOwnProperty(fieldName)
  }

  /**
   * 获取字段的验证规则
   * @param {string} fieldName - 字段名
   * @returns {Object|null} - 验证规则对象
   */
  getFieldValidationRules(fieldName) {
    const field = this.getFieldConfig(fieldName)
    return field ? field.validation_rules : null
  }

  /**
   * 获取字段的默认值
   * @param {string} fieldName - 字段名
   * @returns {any} - 默认值
   */
  getFieldDefaultValue(fieldName) {
    const field = this.getFieldConfig(fieldName)
    if (!field) return null

    if (typeof field.default_value === 'function') {
      return field.default_value()
    }
    return field.default_value
  }

  /**
   * 合并字段配置
   * @param {string} fieldName - 字段名
   * @param {Object} customConfig - 自定义配置
   * @returns {Object} - 合并后的字段配置
   */
  mergeFieldConfig(fieldName, customConfig = {}) {
    const commonConfig = this.getFieldConfig(fieldName)
    if (!commonConfig) {
      return customConfig
    }

    return {
      ...commonConfig,
      ...customConfig,
      // 验证规则需要深度合并
      validation_rules: {
        ...(commonConfig.validation_rules || {}),
        ...(customConfig.validation_rules || {})
      }
    }
  }

  /**
   * 获取所有可用的公共字段列表
   * @returns {Array} - 字段列表
   */
  getAvailableFields() {
    return Object.keys(this.commonFields).map(fieldName => ({
      name: fieldName,
      label: this.commonFields[fieldName].field_label,
      type: this.commonFields[fieldName].field_type,
      required: this.commonFields[fieldName].is_required,
      description: this.commonFields[fieldName].field_description
    }))
  }

  /**
   * 按分类获取字段
   * @returns {Object} - 按分类分组的字段
   */
  getFieldsByCategory() {
    return {
      basic: ['companyName', 'recordDate', 'customer'],
      access: ['platformUrl', 'platformVersion'],
      server: ['serverIp', 'serverOs', 'serverConfig', 'sshPort'],
      maintenance: ['maintenanceDate', 'maintenanceContent', 'maintenanceBy']
    }
  }

  /**
   * 验证字段配置
   * @param {Object} fieldConfig - 字段配置
   * @returns {Object} - 验证结果
   */
  validateFieldConfig(fieldConfig) {
    const errors = []
    const warnings = []

    // 必填字段检查
    if (!fieldConfig.field_name) {
      errors.push('字段名不能为空')
    }
    if (!fieldConfig.field_label) {
      errors.push('字段标签不能为空')
    }
    if (!fieldConfig.field_type) {
      errors.push('字段类型不能为空')
    }

    // 字段名格式检查
    if (fieldConfig.field_name && !/^[a-zA-Z][a-zA-Z0-9_]*$/.test(fieldConfig.field_name)) {
      warnings.push('字段名建议使用驼峰命名法')
    }

    // 公共字段一致性检查
    if (this.isCommonField(fieldConfig.field_name)) {
      const commonConfig = this.getFieldConfig(fieldConfig.field_name)
      if (fieldConfig.field_type !== commonConfig.field_type) {
        warnings.push(`字段类型与公共字段定义不一致，建议使用 ${commonConfig.field_type}`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 生成字段配置的API格式
   * @param {Object} fieldConfig - 字段配置
   * @param {string} formType - 表单类型
   * @param {number} groupId - 分组ID
   * @returns {Object} - API格式的字段配置
   */
  toApiFormat(fieldConfig, formType, groupId) {
    return {
      form_type: formType,
      group_id: groupId,
      field_name: fieldConfig.field_name,
      field_label: fieldConfig.field_label,
      field_type: fieldConfig.field_type,
      field_description: fieldConfig.field_description || '',
      placeholder: fieldConfig.placeholder || '',
      default_value: fieldConfig.default_value || '',
      is_required: fieldConfig.is_required || false,
      is_readonly: fieldConfig.is_readonly || false,
      is_auto_fill: fieldConfig.is_auto_fill || false,
      display_order: fieldConfig.display_order || 0,
      validation_rules: JSON.stringify(fieldConfig.validation_rules || {}),
      css_classes: fieldConfig.css_classes || '',
      grid_columns: fieldConfig.grid_columns || 12
    }
  }

  /**
   * 从API格式转换为前端格式
   * @param {Object} apiFieldConfig - API格式的字段配置
   * @returns {Object} - 前端格式的字段配置
   */
  fromApiFormat(apiFieldConfig) {
    return {
      id: apiFieldConfig.id,
      field_name: apiFieldConfig.field_name,
      field_label: apiFieldConfig.field_label,
      field_type: apiFieldConfig.field_type,
      field_description: apiFieldConfig.field_description,
      placeholder: apiFieldConfig.placeholder,
      default_value: apiFieldConfig.default_value,
      is_required: apiFieldConfig.is_required,
      is_readonly: apiFieldConfig.is_readonly,
      is_auto_fill: apiFieldConfig.is_auto_fill,
      display_order: apiFieldConfig.display_order,
      validation_rules: apiFieldConfig.validation_rules ? 
        JSON.parse(apiFieldConfig.validation_rules) : {},
      css_classes: apiFieldConfig.css_classes,
      grid_columns: apiFieldConfig.grid_columns,
      form_type: apiFieldConfig.form_type,
      group_id: apiFieldConfig.group_id
    }
  }
}

// 创建单例实例
export const commonFieldManager = new CommonFieldManager()

// 导出便捷函数
export const getFieldConfig = (fieldName) => commonFieldManager.getFieldConfig(fieldName)
export const getGroupConfig = (groupName) => commonFieldManager.getGroupConfig(groupName)
export const createStandardFormConfig = (formType) => commonFieldManager.createStandardFormConfig(formType)
export const isCommonField = (fieldName) => commonFieldManager.isCommonField(fieldName)
export const validateFieldConfig = (fieldConfig) => commonFieldManager.validateFieldConfig(fieldConfig)
