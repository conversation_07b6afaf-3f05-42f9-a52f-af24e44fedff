<template>
  <input
    type="date"
    :class="inputClasses"
    :id="id"
    v-model="dateValue"
    :readonly="readonly"
    :style="style"
    @input="updateValue"
    @focus="onFocus"
    @blur="onBlur"
  />
</template>

<script>
export default {
  name: 'DateInput',
  props: {
    id: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    required: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    },
    helpText: {
      type: String,
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    },
    style: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    }
  },
  computed: {
    // 动态计算输入框的 CSS 类
    inputClasses() {
      return [
        'form-control',
        'enhanced-input',
        {
          'has-value': this.hasValue
        }
      ]
    },
    // 判断是否有值
    hasValue() {
      return this.dateValue && this.dateValue !== ''
    }
  },
  data() {
    return {
      dateValue: this.value
    }
  },
  watch: {
    value(newVal) {
      this.dateValue = newVal
    }
  },
  methods: {
    updateValue() {
      this.$emit('update:value', this.dateValue)
    },
    onFocus() {
      // 焦点事件处理（如果需要）
    },
    onBlur() {
      // 失焦事件处理（如果需要）
    }
  }
}
</script>

<style>
/* 移除 scoped，确保样式能正确应用到 floating labels */
.enhanced-input {
  transition: all 0.3s ease;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

.enhanced-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.enhanced-input:readonly {
  background-color: #f8f9fa;
  color: #6c757d;
}

/* Floating label 样式 for date input */
.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown),
.form-floating > .form-control[type="date"]:not([value=""]) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-control[type="date"]:not([value=""]) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  padding: 1rem 0.75rem;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}
</style>
