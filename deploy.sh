#!/bin/bash

# Export Excel 应用部署脚本
# 支持开发环境和生产环境部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 检查前端环境配置文件
check_frontend_env() {
    local env_type=$1

    log_info "检查前端环境配置文件..."

    if [[ "$env_type" == "prod" ]]; then
        if [[ ! -f "frontend/.env.production" ]]; then
            log_error "生产环境配置文件 frontend/.env.production 不存在"
            log_info "请确保该文件存在并包含正确的 VUE_APP_API_BASE_URL 配置"
            exit 1
        fi

        # 检查生产环境配置
        if ! grep -q "VUE_APP_API_BASE_URL" frontend/.env.production; then
            log_error "frontend/.env.production 中缺少 VUE_APP_API_BASE_URL 配置"
            exit 1
        fi

        local api_url=$(grep "VUE_APP_API_BASE_URL" frontend/.env.production | cut -d'=' -f2)
        log_info "生产环境 API 配置: $api_url"

        if [[ "$api_url" != "/api" ]]; then
            log_warning "生产环境 API 配置不是 '/api'，请确认这是正确的配置"
        fi
    else
        if [[ ! -f "frontend/.env" ]]; then
            log_error "开发环境配置文件 frontend/.env 不存在"
            log_info "请确保该文件存在并包含正确的 VUE_APP_API_URL 配置"
            exit 1
        fi

        # 检查开发环境配置
        if ! grep -q "VUE_APP_API_URL" frontend/.env; then
            log_error "frontend/.env 中缺少 VUE_APP_API_URL 配置"
            exit 1
        fi

        local api_url=$(grep "VUE_APP_API_URL" frontend/.env | cut -d'=' -f2)
        log_info "开发环境 API 配置: $api_url"
    fi

    log_success "前端环境配置检查完成"
}

# 显示帮助信息
show_help() {
    echo "Export Excel 应用部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -e, --env ENV       指定环境 (dev|prod) 默认: prod"
    echo "  -p, --port PORT     指定 nginx 端口 默认: 9999"
    echo "  -b, --backend PORT  指定后端端口 默认: 5000"
    echo "  --skip-frontend     跳过前端构建"
    echo "  --skip-backend      跳过后端安装"
    echo "  --skip-nginx        跳过 nginx 配置"
    echo "  --auto-start        自动启动后端服务"
    echo ""
    echo "环境配置文件:"
    echo "  开发环境: frontend/.env (VUE_APP_API_URL=http://127.0.0.1:5000)"
    echo "  生产环境: frontend/.env.production (VUE_APP_API_BASE_URL=/api)"
    echo ""
    echo "示例:"
    echo "  $0                           # 生产环境部署"
    echo "  $0 -e dev                    # 开发环境部署"
    echo "  $0 -p 8080 -b 3000          # 自定义端口"
    echo "  $0 --skip-frontend           # 跳过前端构建"
    echo "  $0 --auto-start              # 自动启动后端"
}

# 默认配置
ENVIRONMENT="prod"
NGINX_PORT="9999"
BACKEND_PORT="5000"
SKIP_FRONTEND=false
SKIP_BACKEND=false
SKIP_NGINX=false
AUTO_START=false
PROJECT_DIR=$(pwd)
NGINX_CONF_DIR="/etc/nginx/conf.d"
NGINX_CONF_FILE="export_excel.conf"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--port)
            NGINX_PORT="$2"
            shift 2
            ;;
        -b|--backend)
            BACKEND_PORT="$2"
            shift 2
            ;;
        --skip-frontend)
            SKIP_FRONTEND=true
            shift
            ;;
        --skip-backend)
            SKIP_BACKEND=true
            shift
            ;;
        --skip-nginx)
            SKIP_NGINX=true
            shift
            ;;
        --auto-start)
            AUTO_START=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    log_error "环境参数必须是 'dev' 或 'prod'"
    exit 1
fi

log_info "开始部署 Export Excel 应用..."
log_info "环境: $ENVIRONMENT"
log_info "Nginx 端口: $NGINX_PORT"
log_info "后端端口: $BACKEND_PORT"
log_info "项目目录: $PROJECT_DIR"

echo ""

# 检查必要的命令
log_info "检查系统依赖..."
if [[ "$SKIP_FRONTEND" == false ]]; then
    check_command "node"
    check_command "npm"
fi

if [[ "$SKIP_BACKEND" == false ]]; then
    check_command "python"
    check_command "pip3.11"
fi

if [[ "$SKIP_NGINX" == false ]]; then
    check_command "nginx"
fi

log_success "系统依赖检查完成"

# 检查项目结构
log_info "检查项目结构..."
if [[ ! -d "frontend" ]]; then
    log_error "frontend 目录不存在"
    exit 1
fi

if [[ ! -d "backend" ]]; then
    log_error "backend 目录不存在"
    exit 1
fi

if [[ ! -f "nginx.conf" ]]; then
    log_error "nginx.conf 配置文件不存在"
    exit 1
fi

log_success "项目结构检查完成"

# 检查前端环境配置
if [[ "$SKIP_FRONTEND" == false ]]; then
    check_frontend_env "$ENVIRONMENT"
fi

echo ""

# 1. 构建前端
if [[ "$SKIP_FRONTEND" == false ]]; then
    log_info "步骤 1: 构建前端应用..."

    cd frontend

    # 检查 package.json
    if [[ ! -f "package.json" ]]; then
        log_error "frontend/package.json 不存在"
        exit 1
    fi

    # 安装依赖
    log_info "安装前端依赖..."
    npm install

    # 设置环境变量和构建
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        export NODE_ENV=production
        log_info "设置生产环境构建"
        log_info "使用生产环境配置文件: .env.production"

        # 确保生产环境配置文件存在
        if [[ ! -f ".env.production" ]]; then
            log_error ".env.production 文件不存在"
            exit 1
        fi

        # 显示当前生产环境配置
        log_info "生产环境 API 配置:"
        grep "VUE_APP_API_BASE_URL" .env.production || log_warning "未找到 VUE_APP_API_BASE_URL 配置"

        # 构建生产版本
        log_info "构建前端应用 (生产环境)..."
        npm run build
    else
        export NODE_ENV=development
        log_info "设置开发环境构建"
        log_info "使用开发环境配置文件: .env 和 .env.development"

        # 确保开发环境配置文件存在
        if [[ ! -f ".env" ]]; then
            log_error ".env 文件不存在"
            exit 1
        fi

        # 显示当前开发环境配置
        log_info "开发环境 API 配置:"
        grep "VUE_APP_API_URL" .env || log_warning "未找到 VUE_APP_API_URL 配置"

        # 构建开发版本（通常开发环境不需要构建，但这里为了统一）
        log_info "构建前端应用 (开发环境)..."
        npm run build
    fi

    # 检查构建结果
    if [[ ! -d "dist" ]]; then
        log_error "前端构建失败，dist 目录不存在"
        exit 1
    fi

    cd ..
    log_success "前端构建完成"
else
    log_warning "跳过前端构建"
fi

# 2. 安装后端依赖
if [[ "$SKIP_BACKEND" == false ]]; then
    log_info "步骤 2: 安装后端依赖..."

    cd backend

    # 检查 requirements.txt
    if [[ ! -f "requirements.txt" ]]; then
        log_error "backend/requirements.txt 不存在"
        exit 1
    fi

    # 检查是否有虚拟环境
    if [[ -d ".venv" ]]; then
        log_info "发现虚拟环境，激活中..."
        source .venv/bin/activate
    elif [[ -d "../.venv" ]]; then
        log_info "发现项目虚拟环境，激活中..."
        source ../.venv/bin/activate
    else
        log_warning "未发现虚拟环境，使用系统 Python"
    fi

    # 安装依赖
    log_info "安装 Python 依赖..."
    pip3.11 install -r requirements.txt

    # 初始化数据库（如果需要）
    # if [[ -f "init_db.py" ]]; then
    #     log_info "初始化数据库..."
    #     python init_db.py
    # fi

    cd ..
    log_success "后端依赖安装完成"
else
    log_warning "跳过后端安装"
fi

echo ""

# 3. 配置 nginx
if [[ "$SKIP_NGINX" == false ]]; then
    log_info "步骤 3: 配置 nginx..."

    # 创建临时配置文件，替换端口
    TEMP_NGINX_CONF="/tmp/export_excel_nginx.conf"
    cp nginx.conf "$TEMP_NGINX_CONF"

    # 替换端口配置
    sed -i "s/listen 9999;/listen $NGINX_PORT;/g" "$TEMP_NGINX_CONF"
    sed -i "s/proxy_pass http:\/\/127.0.0.1:5000;/proxy_pass http:\/\/127.0.0.1:$BACKEND_PORT;/g" "$TEMP_NGINX_CONF"

    # 替换项目路径
    sed -i "s|/opt/export_excel|$PROJECT_DIR|g" "$TEMP_NGINX_CONF"

    # 复制配置文件
    log_info "复制 nginx 配置文件..."
    sudo cp "$TEMP_NGINX_CONF" "${NGINX_CONF_DIR}/${NGINX_CONF_FILE}"

    # 清理临时文件
    rm "$TEMP_NGINX_CONF"

    log_info "nginx 配置文件已复制到 ${NGINX_CONF_DIR}/${NGINX_CONF_FILE}"

    # 测试 nginx 配置
    log_info "测试 nginx 配置..."
    if sudo nginx -t; then
        log_success "nginx 配置测试通过"

        # 重新加载 nginx
        log_info "重新加载 nginx..."
        sudo nginx -s reload
        log_success "nginx 重新加载完成"
    else
        log_error "nginx 配置测试失败"
        exit 1
    fi
else
    log_warning "跳过 nginx 配置"
fi

# 创建日志目录（在启动服务前）
if [[ ! -d "logs" ]]; then
    mkdir -p logs
    log_info "创建日志目录: $PROJECT_DIR/logs"
fi

# 4. 启动后端服务
if [[ "$AUTO_START" == true ]]; then
    log_info "步骤 4: 启动后端服务..."

    cd backend

    # 检查是否有虚拟环境
    if [[ -d ".venv" ]]; then
        source .venv/bin/activate
    elif [[ -d "../.venv" ]]; then
        source ../.venv/bin/activate
    fi

    # 设置环境变量
    export FLASK_APP=run.py
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        export FLASK_ENV=production
    else
        export FLASK_ENV=development
    fi

    # 检查端口是否被占用
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 $BACKEND_PORT 已被占用，尝试停止现有进程..."
        pkill -f "python.*run.py" || true
        pkill -f ".venv/bin/python.*run.py" || true
        sleep 2
    fi

    # 启动服务
    log_info "启动 Flask 应用 (端口 $BACKEND_PORT)..."

    # 检查是否在Jenkins环境中
    if [[ -n "$BUILD_ID" && "$BUILD_ID" == "dontKillMe" ]]; then
        log_info "检测到Jenkins环境，使用强化进程保护..."
        # Jenkins环境下使用最强的进程保护
        setsid bash -c "
            export BUILD_ID=dontKillMe
            export JENKINS_NODE_COOKIE=dontKillMe
            export JENKINS_SERVER_COOKIE=dontKillMe
            cd $(pwd)
            exec nohup .venv/bin/python run.py > ../logs/backend.log 2>&1
        " &
        BACKEND_PID=$!
        # 完全脱离当前shell
        disown $BACKEND_PID 2>/dev/null || true
    else
        log_info "普通环境，使用标准启动方式..."
        # 普通环境下使用标准nohup
        nohup .venv/bin/python run.py > ../logs/backend.log 2>&1 &
        BACKEND_PID=$!
    fi

    # 等待服务启动
    sleep 3

    # 检查服务是否启动成功
    if ps -p $BACKEND_PID > /dev/null; then
        log_success "后端服务启动成功 (PID: $BACKEND_PID)"
        echo $BACKEND_PID > ../backend.pid
    else
        log_error "后端服务启动失败"
        exit 1
    fi

    cd ..
else
    log_info "步骤 4: 后端服务启动说明"
    log_warning "需要手动启动后端服务："
    echo "  cd $PROJECT_DIR/backend"
    if [[ -d "backend/.venv" || -d ".venv" ]]; then
        echo "  source .venv/bin/activate  # 激活虚拟环境"
    fi
    echo "  export FLASK_APP=run.py"
    echo "  export FLASK_ENV=$ENVIRONMENT"
    echo "  .venv/bin/python run.py  # 使用虚拟环境中的Python"
fi

echo ""

# 部署完成总结
log_success "🎉 部署完成！"
echo ""
echo "📋 部署信息:"
echo "  环境: $ENVIRONMENT"
echo "  项目目录: $PROJECT_DIR"
echo "  访问地址: http://localhost:$NGINX_PORT"
echo ""
echo "🏗️ 服务架构:"
echo "  ├── 前端: nginx 静态文件服务 (端口 $NGINX_PORT)"
echo "  ├── 后端: Flask 应用 (端口 $BACKEND_PORT)"
echo "  └── 代理: nginx 将 /api/ 请求代理到后端"
echo ""
echo "📁 重要路径:"
echo "  ├── 前端文件: $PROJECT_DIR/frontend/dist"
echo "  ├── 后端代码: $PROJECT_DIR/backend"
echo "  ├── Excel 文件: $PROJECT_DIR/backend/excel_files"
echo "  ├── 日志文件: $PROJECT_DIR/logs"
echo "  └── Nginx 配置: ${NGINX_CONF_DIR}/${NGINX_CONF_FILE}"
echo ""

if [[ "$AUTO_START" == true ]]; then
    echo "🔧 服务管理:"
    echo "  停止后端: kill \$(cat $PROJECT_DIR/backend.pid)"
    echo "  查看日志: tail -f $PROJECT_DIR/logs/backend.log"
    echo "  重启 nginx: sudo nginx -s reload"
    echo ""
fi

echo "🚀 快速测试:"
echo "  curl http://localhost:$NGINX_PORT"
echo "  curl http://localhost:$NGINX_PORT/api/health"
echo ""

log_info "部署脚本执行完成！"