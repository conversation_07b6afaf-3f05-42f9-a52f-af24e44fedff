<template>
  <collapsible-card card-class="border-secondary" storage-key="client-app-section">
    <template #header>
      <i class="bi bi-phone me-2"></i>客户APP（安卓/iOS/鸿蒙）
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-secondary">APP数量: {{ appList.length }}</span>
        <span v-for="(item, index) in appList" :key="'app-summary-' + index" class="badge bg-light text-dark border">
          {{ item.name || '未命名' }} {{ item.appid ? '(' + item.appid + ')' : '' }}
        </span>
      </div>
    </template>

    <!-- 客户APP列表 -->
    <div class="app-content-section">
      <div class="section-header mb-3">
        <h6 class="section-title">
          <i class="bi bi-list-ul me-2 text-secondary"></i>
          APP列表
        </h6>
      </div>

      <!-- 空状态提示 -->
      <div v-if="savedAppList.length === 0 && !isAdding && !isEditing && batchAddingItems.length === 0" class="empty-state-compact">
        <i class="bi bi-phone me-2 text-muted"></i>
        <span class="text-muted">暂无客户APP信息，点击下方按钮添加</span>
      </div>

      <!-- APP表格 -->
      <div v-if="savedAppList.length > 0 || isAdding || isEditing || batchAddingItems.length > 0" class="app-table-container">
        <table class="table table-bordered table-hover app-table">
          <thead class="table-light">
            <tr>
              <th width="60">序号</th>
              <th width="150">APP ID</th>
              <th width="120">应用名称</th>
              <th width="150">包名</th>
              <th width="120">平台</th>
              <th width="100">操作</th>
            </tr>
          </thead>
          <tbody>
            <!-- 已保存的记录 -->
            <tr v-for="(item, index) in savedAppList" :key="'saved-app-' + index"
                v-show="!isEditing || editingIndex !== index">
              <td class="text-center">{{ index + 1 }}</td>
              <td>
                <span class="app-id-text">{{ item.appid || '-' }}</span>
              </td>
              <td>
                <span class="app-name-text">{{ item.name || '-' }}</span>
              </td>
              <td>
                <span class="package-name-text">{{ item.packageName || '-' }}</span>
              </td>
              <td>
                <div class="platform-badges">
                  <span v-for="platform in (item.platforms || [])" :key="platform"
                        class="badge platform-badge" :class="getPlatformBadgeClass(platform)">
                    {{ platform }}
                  </span>
                  <span v-if="!item.platforms || item.platforms.length === 0" class="text-muted">-</span>
                </div>
              </td>
              <td>
                <div class="action-buttons">
                  <button type="button" class="btn btn-sm btn-outline-primary me-1"
                          @click="editAppItem(index)" title="编辑">
                    <i class="bi bi-pencil"></i>
                  </button>
                  <button type="button" class="btn btn-sm btn-outline-danger"
                          @click="removeAppItem(index)" title="删除">
                    <i class="bi bi-trash"></i>
                  </button>
                </div>
              </td>
            </tr>

            <!-- 编辑表单行 -->
            <tr v-if="isEditing && editingIndex >= 0" class="editing-row">
              <td class="text-center">{{ editingIndex + 1 }}</td>
              <td>
                <input type="text" class="form-control form-control-sm"
                       v-model="newAppItem.appid"
                       placeholder="APP ID">
              </td>
              <td>
                <input type="text" class="form-control form-control-sm"
                       v-model="newAppItem.name"
                       placeholder="应用名称">
              </td>
              <td>
                <input type="text" class="form-control form-control-sm"
                       v-model="newAppItem.packageName"
                       placeholder="包名">
              </td>
              <td>
                <div class="platform-selector">
                  <div class="platform-checkboxes">
                    <div v-for="platform in availablePlatforms" :key="platform" class="form-check form-check-inline">
                      <input class="form-check-input" type="checkbox"
                             :id="'platform-edit-' + platform"
                             :value="platform"
                             v-model="newAppItem.platforms">
                      <label class="form-check-label" :for="'platform-edit-' + platform">
                        {{ platform }}
                      </label>
                    </div>
                  </div>
                </div>
              </td>
              <td>
                <div class="action-buttons">
                  <button type="button" class="btn btn-sm btn-success me-1"
                          @click="saveEditAppItem" title="保存">
                    <i class="bi bi-check"></i>
                  </button>
                  <button type="button" class="btn btn-sm btn-secondary"
                          @click="cancelEditAppItem" title="取消">
                    <i class="bi bi-x"></i>
                  </button>
                </div>
              </td>
            </tr>

            <!-- 批量添加表单行 -->
            <tr v-for="(item, index) in batchAddingItems" :key="'batch-add-' + index" class="adding-row">
              <td class="text-center">{{ savedAppList.length + index + 1 }}</td>
              <td>
                <input type="text" class="form-control form-control-sm"
                       v-model="item.appid"
                       placeholder="APP ID">
              </td>
              <td>
                <input type="text" class="form-control form-control-sm"
                       v-model="item.name"
                       placeholder="应用名称">
              </td>
              <td>
                <input type="text" class="form-control form-control-sm"
                       v-model="item.packageName"
                       placeholder="包名">
              </td>
              <td>
                <div class="platform-selector">
                  <div class="platform-checkboxes">
                    <div v-for="platform in availablePlatforms" :key="platform" class="form-check form-check-inline">
                      <input class="form-check-input" type="checkbox"
                             :id="'platform-batch-' + index + '-' + platform"
                             :value="platform"
                             v-model="item.platforms">
                      <label class="form-check-label" :for="'platform-batch-' + index + '-' + platform">
                        {{ platform }}
                      </label>
                    </div>
                  </div>
                </div>
              </td>
              <td>
                <div class="action-buttons">
                  <button type="button" class="btn btn-sm btn-danger"
                          @click="removeBatchItem(index)" title="删除此行">
                    <i class="bi bi-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 添加按钮区域 -->
      <div v-if="!isEditing" class="add-button-section">
        <div v-if="batchAddingItems.length === 0" class="single-add-buttons">
          <button type="button" class="btn btn-add-app me-2" @click="addSingleApp">
            <i class="bi bi-plus-circle me-2"></i>
            <span>添加单个APP</span>
          </button>
          <button type="button" class="btn btn-add-batch" @click="addBatchApps">
            <i class="bi bi-plus-square me-2"></i>
            <span>批量添加APP</span>
          </button>
        </div>

        <div v-else class="batch-add-controls">
          <div class="batch-info mb-2">
            <span class="text-muted">
              <i class="bi bi-info-circle me-1"></i>
              正在批量添加 {{ batchAddingItems.length }} 个APP，填写完成后点击保存
            </span>
          </div>
          <div class="batch-buttons">
            <button type="button" class="btn btn-success me-2" @click="saveBatchApps">
              <i class="bi bi-check-lg me-1"></i>
              <span>保存全部</span>
            </button>
            <button type="button" class="btn btn-outline-primary me-2" @click="addMoreRows">
              <i class="bi bi-plus me-1"></i>
              <span>再添加3行</span>
            </button>
            <button type="button" class="btn btn-secondary" @click="cancelBatchAdd">
              <i class="bi bi-x-lg me-1"></i>
              <span>取消</span>
            </button>
          </div>
        </div>
      </div>
    </div>


  </collapsible-card>
</template>

<script>

import CollapsibleCard from '../common/CollapsibleCard.vue'
import { createNewAppItem } from '@/config/formDataConfig'

/**
 * 客户APP部分组件
 * 用于管理客户APP信息
 */
export default {
  name: 'ClientAppSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 客户APP列表
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isAdding: false, // 是否正在添加新项
      isEditing: false, // 是否正在编辑
      editingIndex: -1, // 正在编辑的项目索引
      newAppItem: {
        appid: '',
        name: '',
        packageName: '',
        platforms: []
      },
      // 批量添加的空行数据
      batchAddingItems: [],
      // 可选平台列表
      availablePlatforms: ['安卓', 'iOS', '鸿蒙', '鸿蒙Next']
    }
  },
  computed: {
    /**
     * APP列表
     * 用于双向绑定
     */
    appList: {
      get() {
        return this.modelValue
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue)
      }
    },
    /**
     * 已保存的APP列表
     */
    savedAppList() {
      return this.appList.filter(item => item.appid || item.name || item.packageName)
    }
  },
  methods: {
    /**
     * 获取平台徽章的CSS类
     */
    getPlatformBadgeClass(platform) {
      const platformClasses = {
        '安卓': 'bg-success',
        'iOS': 'bg-primary',
        '鸿蒙': 'bg-warning text-dark',
        '鸿蒙Next': 'bg-danger'
      }
      return platformClasses[platform] || 'bg-secondary'
    },

    /**
     * 创建新的空APP项
     */
    createEmptyAppItem() {
      return {
        appid: '',
        name: '',
        packageName: '',
        platforms: []
      }
    },

    /**
     * 添加单个APP
     */
    addSingleApp() {
      this.batchAddingItems = [this.createEmptyAppItem()]
    },

    /**
     * 批量添加APP（默认3行）
     */
    addBatchApps() {
      this.batchAddingItems = [
        this.createEmptyAppItem(),
        this.createEmptyAppItem(),
        this.createEmptyAppItem()
      ]
    },

    /**
     * 添加更多行
     */
    addMoreRows() {
      this.batchAddingItems.push(
        this.createEmptyAppItem(),
        this.createEmptyAppItem(),
        this.createEmptyAppItem()
      )
    },

    /**
     * 移除批量添加中的某一行
     */
    removeBatchItem(index) {
      this.batchAddingItems.splice(index, 1)
      if (this.batchAddingItems.length === 0) {
        this.cancelBatchAdd()
      }
    },

    /**
     * 保存批量添加的APP
     */
    saveBatchApps() {
      // 过滤出有效的APP项（至少填写了一个字段）
      const validItems = this.batchAddingItems.filter(item =>
        item.appid || item.name || item.packageName
      )

      if (validItems.length === 0) {
        alert('请至少填写一个APP的信息')
        return
      }

      // 验证必填字段
      const invalidItems = validItems.filter(item =>
        !item.appid || !item.name || !item.packageName
      )

      if (invalidItems.length > 0) {
        alert(`有 ${invalidItems.length} 个APP信息不完整，请填写完整的APP ID、应用名称和包名`)
        return
      }

      // 添加到APP列表
      const newAppList = [...this.appList]
      validItems.forEach(item => {
        newAppList.push({
          appid: item.appid,
          name: item.name,
          packageName: item.packageName,
          platforms: [...item.platforms]
        })
      })
      this.appList = newAppList

      // 清空批量添加状态
      this.cancelBatchAdd()
    },

    /**
     * 取消批量添加
     */
    cancelBatchAdd() {
      this.batchAddingItems = []
    },

    /**
     * 开始添加客户APP项（保留原方法兼容性）
     */
    addAppItem() {
      // 如果正在编辑，先取消编辑
      if (this.isEditing) {
        this.cancelEditAppItem()
      }

      // 如果当前表单有内容，先自动保存
      if (this.newAppItem.appid || this.newAppItem.name || this.newAppItem.packageName) {
        const newAppList = [...this.appList]
        newAppList.push({
          appid: this.newAppItem.appid,
          name: this.newAppItem.name,
          packageName: this.newAppItem.packageName,
          platforms: [...this.newAppItem.platforms]
        })
        this.appList = newAppList
      }

      // 开始新的添加，清空表单
      this.isAdding = true
      this.newAppItem = {
        appid: '',
        name: '',
        packageName: '',
        platforms: []
      }
    },

    /**
     * 保存新的APP项
     */
    saveNewAppItem() {
      // 验证必填字段
      if (!this.newAppItem.appid || !this.newAppItem.name || !this.newAppItem.packageName) {
        alert('请填写完整的APP信息：APP ID、应用名称和包名都是必填项')
        return
      }

      const newAppList = [...this.appList]
      newAppList.push({
        appid: this.newAppItem.appid,
        name: this.newAppItem.name,
        packageName: this.newAppItem.packageName,
        platforms: [...this.newAppItem.platforms]
      })
      this.appList = newAppList

      // 保存成功后，清空表单并关闭添加状态
      this.isAdding = false
      this.newAppItem = {
        appid: '',
        name: '',
        packageName: '',
        platforms: []
      }
    },



    /**
     * 取消添加APP项
     */
    cancelAddAppItem() {
      this.isAdding = false
      this.newAppItem = {
        appid: '',
        name: '',
        packageName: '',
        platforms: []
      }
    },

    /**
     * 编辑客户APP项
     * @param {Number} index - 要编辑的项的索引
     */
    editAppItem(index) {
      // 如果正在添加，先取消添加
      if (this.isAdding) {
        this.cancelAddAppItem()
      }

      // 设置编辑状态
      this.isEditing = true
      this.editingIndex = index

      // 加载要编辑的数据
      const itemToEdit = this.appList[index]
      this.newAppItem = {
        appid: itemToEdit.appid || '',
        name: itemToEdit.name || '',
        packageName: itemToEdit.packageName || '',
        platforms: [...(itemToEdit.platforms || [])]
      }
    },

    /**
     * 保存编辑的APP项
     */
    saveEditAppItem() {
      // 验证必填字段
      if (!this.newAppItem.appid || !this.newAppItem.name || !this.newAppItem.packageName) {
        alert('请填写完整的APP信息：APP ID、应用名称和包名都是必填项')
        return
      }

      const newAppList = [...this.appList]
      newAppList[this.editingIndex] = {
        appid: this.newAppItem.appid,
        name: this.newAppItem.name,
        packageName: this.newAppItem.packageName,
        platforms: [...this.newAppItem.platforms]
      }
      this.appList = newAppList
      this.cancelEditAppItem()
    },

    /**
     * 取消编辑APP项
     */
    cancelEditAppItem() {
      this.isEditing = false
      this.editingIndex = -1
      this.newAppItem = {
        appid: '',
        name: '',
        packageName: '',
        platforms: []
      }
    },

    /**
     * 删除客户APP项
     * @param {Number} index - 要删除的项的索引
     */
    removeAppItem(index) {
      if (confirm('确定要删除这个APP吗？')) {
        const newAppList = [...this.appList]
        newAppList.splice(index, 1)
        this.appList = newAppList
      }
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

/* 客户APP分组样式 */
.app-content-section {
  position: relative;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.app-content-section:hover {
  border-color: #ced4da;
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.08);
}

/* 表格样式 */
.app-table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 1rem;
}

.app-table {
  margin-bottom: 0;
  font-size: 0.9rem;
}

.app-table th {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  font-weight: 600;
  color: #495057;
  text-align: center;
  vertical-align: middle;
  padding: 0.75rem 0.5rem;
}

.app-table td {
  vertical-align: middle;
  padding: 0.75rem 0.5rem;
  border-color: #dee2e6;
}

.app-table tbody tr:hover {
  background-color: #f8f9fa;
}

/* 平台徽章样式 */
.platform-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.platform-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

/* 平台选择器样式 */
.platform-selector {
  min-height: 40px;
}

.platform-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.platform-checkboxes .form-check {
  margin: 0;
  min-width: auto;
}

.platform-checkboxes .form-check-input {
  margin-top: 0;
}

.platform-checkboxes .form-check-label {
  font-size: 0.8rem;
  margin-left: 0.25rem;
  cursor: pointer;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 0.25rem;
  justify-content: center;
}

.action-buttons .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

/* 编辑和添加行样式 */
.editing-row,
.adding-row {
  background-color: #fff3cd !important;
}

.editing-row td,
.adding-row td {
  border-color: #ffeaa7 !important;
}

/* 表格内输入框样式 */
.app-table .form-control-sm {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

/* 文本显示样式 */
.app-id-text,
.app-name-text,
.package-name-text {
  font-size: 0.85rem;
  word-break: break-all;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #dee2e6;
}

.section-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.section-title i {
  font-size: 1.1rem;
}

/* 紧凑空状态样式 */
.empty-state-compact {
  text-align: center;
  padding: 2rem;
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  border: 1px dashed #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

/* APP项列表样式 */
.app-items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 记录显示样式 */
.app-record {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.app-record:hover {
  border-color: #6c757d;
  box-shadow: 0 2px 6px rgba(108, 117, 125, 0.1);
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
}

.record-actions {
  display: flex;
  gap: 0.25rem;
}

.record-actions .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 4px;
}

.record-number {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  flex-shrink: 0;
}

.record-title {
  font-weight: 600;
  color: #495057;
  flex-grow: 1;
  font-size: 0.9rem;
}

.version-badge {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

/* 新建表单样式 */
.app-item-form {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 2px solid #dee2e6;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.1);
  margin-top: 1rem;
}

.form-header {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-title {
  margin: 0;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
}

.form-content {
  padding: 1.5rem;
}

/* 应用信息分组样式 */
.app-info-section {
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.section-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
}

.section-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

/* 增强的输入框样式 */
.enhanced-input {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.enhanced-input:focus {
  border-color: #6c757d;
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.15);
  background-color: #ffffff;
}

.enhanced-input:hover:not(:focus) {
  border-color: #ced4da;
}

/* 标签样式增强 */
.form-floating > label {
  color: #6c757d;
  font-weight: 500;
}

.form-floating > .enhanced-input:focus ~ label,
.form-floating > .enhanced-input:not(:placeholder-shown) ~ label {
  color: #6c757d;
  font-weight: 600;
}

/* 表单提示文字样式 */
.form-text {
  font-size: 0.875rem;
  color: #6c757d;
  display: flex;
  align-items: center;
}

.form-text i {
  color: #17a2b8;
}

/* 添加按钮样式 */
.add-button-section {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  margin-top: 1rem;
}

.btn-add-app {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  border: none;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.2);
  display: inline-flex;
  align-items: center;
}

.btn-add-app:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(108, 117, 125, 0.3);
}

.btn-add-app:active {
  transform: translateY(0);
}

/* 批量添加按钮样式 */
.single-add-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.btn-add-batch {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  border: none;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
  display: inline-flex;
  align-items: center;
}

.btn-add-batch:hover {
  background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
}

.btn-add-batch:active {
  transform: translateY(0);
}

/* 批量添加控制区域 */
.batch-add-controls {
  text-align: center;
}

.batch-info {
  padding: 0.75rem 1rem;
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.batch-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

/* 聚焦状态的分组高亮 */
.app-content-section:focus-within {
  border-color: #6c757d;
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.1);
}

/* 原有的浮动标签样式 */
.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-content-section {
    padding: 1rem;
  }

  .section-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .app-record {
    padding: 0.5rem 0.75rem;
  }

  .record-header {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .version-badge {
    order: 3;
    margin-top: 0.25rem;
  }

  .form-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .form-content {
    padding: 1rem;
  }

  .app-info-section {
    padding: 0.75rem;
  }

  .empty-state-compact {
    padding: 1.5rem;
    font-size: 0.85rem;
  }

  .add-button-section {
    padding: 1rem;
  }

  .btn-add-app {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* 动画效果 */
.app-record {
  animation: slideInUp 0.3s ease-out;
}

.app-item-form {
  animation: expandIn 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
