from flask import request, jsonify, send_file, current_app
from app.excel import bp
from app import db
from app.models.models import TemplateVersion
from app.utils.cache_utils import TemplateCacheManager
from werkzeug.utils import secure_filename
import os
import re
from datetime import datetime
import traceback
from urllib.parse import quote

@bp.route('/templates', methods=['GET'])
def get_templates():
    """
    获取所有可用的模板文件
    支持按表单类型筛选: ?form_type=安全测评
    """
    try:
        templates_dir = current_app.config['EXCEL_TEMPLATES_FOLDER']
        form_type = request.args.get('form_type', '')

        # 确保目录存在
        os.makedirs(templates_dir, exist_ok=True)

        # 🔧 首先获取所有模板版本信息（用于检查新文件）
        all_template_versions = TemplateVersion.query.all()

        # 然后根据需要筛选
        if form_type:
            # 按表单类型筛选
            template_versions = [t for t in all_template_versions if t.form_type == form_type]
            current_app.logger.info(f"按表单类型筛选模板: {form_type}, 找到 {len(template_versions)} 个模板")
        else:
            # 获取所有模板
            template_versions = all_template_versions

        # 如果数据库中没有模板记录，初始化它们
        if not all_template_versions:
            current_app.logger.info("数据库中没有模板记录，正在初始化...")
            # 导入init_template_versions函数
            import sys
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            from init_db import init_template_versions
            init_template_versions()
            # 重新查询所有模板
            all_template_versions = TemplateVersion.query.all()
            # 重新筛选
            if form_type:
                template_versions = [t for t in all_template_versions if t.form_type == form_type]
            else:
                template_versions = all_template_versions

        # 检查templates目录中是否有新的模板文件（使用所有模板来检查，避免重复）
        db_filenames = {template.filename for template in all_template_versions}
        disk_files = [f for f in os.listdir(templates_dir) if f.endswith('.xlsx')]

        # 添加新发现的模板文件到数据库
        new_templates_added = False
        for filename in disk_files:
            if filename not in db_filenames:
                file_path = os.path.join(templates_dir, filename)

                # 🔧 再次检查数据库中是否已存在该文件名的记录（避免并发问题）
                existing_check = TemplateVersion.query.filter_by(filename=filename).first()
                if existing_check:
                    current_app.logger.warning(f"模板文件 {filename} 已存在于数据库中，跳过添加")
                    template_versions.append(existing_check)
                    continue

                # 确定模板类型
                template_type = '未知'
                if '安全测评' in filename:
                    template_type = '安全测评'
                elif '安全监测' in filename:
                    template_type = '安全监测'
                elif '应用加固' in filename:
                    template_type = '应用加固'

                # 设置别名
                alias = filename.replace('.xlsx', '').replace('-运维信息登记模板', '')
                if not alias:
                    alias = f"模板 {filename}"

                try:
                    # 添加到数据库
                    new_template = TemplateVersion(
                        filename=filename,
                        template_type=template_type,
                        is_active=False,  # 新发现的模板默认不是活动模板
                        alias=alias,
                        form_type=template_type,
                        created_at=datetime.fromtimestamp(os.path.getctime(file_path)),
                        last_modified=datetime.fromtimestamp(os.path.getmtime(file_path))
                    )
                    db.session.add(new_template)
                    current_app.logger.info(f"添加新发现的模板: {filename}")
                    new_templates_added = True

                    # 更新所有模板版本列表
                    all_template_versions.append(new_template)
                    # 如果符合筛选条件，也添加到筛选后的列表
                    if not form_type or new_template.form_type == form_type:
                        template_versions.append(new_template)
                except Exception as e:
                    current_app.logger.error(f"添加模板 {filename} 失败: {str(e)}")
                    db.session.rollback()
                    # 尝试从数据库重新获取该模板
                    existing_template = TemplateVersion.query.filter_by(filename=filename).first()
                    if existing_template:
                        all_template_versions.append(existing_template)
                        # 如果符合筛选条件，也添加到筛选后的列表
                        if not form_type or existing_template.form_type == form_type:
                            template_versions.append(existing_template)

        # 提交数据库更改
        if new_templates_added:
            try:
                db.session.commit()
                current_app.logger.info("新模板添加成功")
            except Exception as e:
                current_app.logger.error(f"提交新模板失败: {str(e)}")
                db.session.rollback()

        # 转换为前端需要的格式
        templates = []
        current_app.logger.info(f"模板目录: {templates_dir}")
        current_app.logger.info(f"找到 {len(template_versions)} 个数据库记录")

        for template in template_versions:
            file_path = os.path.join(templates_dir, template.filename)
            current_app.logger.info(f"检查模板文件: {file_path}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                current_app.logger.warning(f"模板文件不存在: {file_path}")
                # 🔧 即使文件不存在，也添加到列表中，但标记为不可用
                templates.append({
                    'id': template.id,
                    'filename': template.filename,
                    'displayName': template.filename,
                    'type': template.template_type,
                    'formType': template.form_type,
                    'lastModified': template.last_modified.timestamp() if template.last_modified else 0,
                    'size': 0,
                    'isActive': template.is_active,
                    'alias': template.alias or template.filename,
                    'fileExists': False,
                    'error': '文件不存在'
                })
                continue

            # 获取文件大小
            size = os.path.getsize(file_path)
            current_app.logger.info(f"模板文件存在，大小: {size} 字节")

            # 添加到模板列表
            templates.append({
                'id': template.id,
                'filename': template.filename,
                'displayName': template.filename,
                'type': template.template_type,
                'formType': template.form_type,
                'lastModified': template.last_modified.timestamp() if template.last_modified else 0,
                'size': size,
                'isActive': template.is_active,
                'alias': template.alias or template.filename,
                'fileExists': True
            })

        # 按类型和活动状态排序
        templates.sort(key=lambda x: (x['type'], not x['isActive'], -x['lastModified']))

        return jsonify({
            'status': 'success',
            'data': templates
        })
    except Exception as e:
        current_app.logger.error(f"获取模板列表失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取模板列表失败: {str(e)}'
        }), 500

@bp.route('/templates/download/<path:filename>', methods=['GET'])
def download_template(filename):
    """
    下载模板文件（支持从模板目录或备份目录下载）
    """
    try:
        # 检查是否是备份文件
        is_backup = '_backup_' in filename

        if is_backup:
            # 从备份目录下载
            file_path = os.path.join(current_app.config['EXCEL_FOLDER'], 'template_backups', filename)
        else:
            # 从模板目录下载
            file_path = os.path.join(current_app.config['EXCEL_TEMPLATES_FOLDER'], filename)

        if not os.path.exists(file_path):
            return jsonify({
                'status': 'error',
                'message': '模板文件不存在'
            }), 404

        # 处理中文文件名编码
        response = send_file(
            file_path,
            as_attachment=True,
            download_name=filename
        )

        # 设置正确的Content-Disposition头
        encoded_filename = quote(filename.encode('utf-8'))
        response.headers['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'{encoded_filename}'

        return response
    except Exception as e:
        current_app.logger.error(f"下载模板文件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'下载模板文件失败: {str(e)}'
        }), 500

@bp.route('/templates/upload', methods=['POST'])
def upload_template():
    """
    上传模板文件，支持冲突检测和用户确认
    """
    try:
        if 'file' not in request.files:
            return jsonify({
                'status': 'error',
                'message': '没有上传文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'status': 'error',
                'message': '没有选择文件'
            }), 400

        if not file.filename.endswith('.xlsx'):
            return jsonify({
                'status': 'error',
                'message': '只支持上传.xlsx格式的Excel文件'
            }), 400

        # 获取模板类型和表单类型
        template_type = request.form.get('type', '')
        form_type = request.form.get('formType', template_type)  # 默认与模板类型相同
        alias = request.form.get('alias', '')  # 可选的别名
        confirm_overwrite = request.form.get('confirm', '').lower() == 'true'  # 是否确认覆盖

        # 调试日志
        current_app.logger.info(f"📋 上传参数: type={template_type}, alias='{alias}', confirm='{request.form.get('confirm', '')}', confirm_overwrite={confirm_overwrite}")

        if not template_type:
            return jsonify({
                'status': 'error',
                'message': '请指定模板类型'
            }), 400

        # 改进的文件名生成逻辑：文件名 + 别名
        def generate_safe_filename_with_alias(original_name, template_type, alias):
            """
            生成安全的文件名，支持中文文件名，并在文件名后添加别名
            """

            # 首先尝试使用secure_filename
            secured_name = secure_filename(original_name)

            # 检查是否只剩下扩展名、空字符串或无意义的字符
            is_invalid_filename = (
                not secured_name or
                secured_name in ['.xlsx', 'xlsx', '.xls', 'xls'] or
                secured_name.startswith('.') or
                secured_name.startswith('-') or  # 处理 "-.xlsx" 的情况
                secured_name in ['-.xlsx', '--2024.xlsx'] or
                len(secured_name.replace('.xlsx', '').replace('-', '').strip()) == 0
            )

            if is_invalid_filename:
                # 原始文件名包含中文或特殊字符，被secure_filename清除了
                current_app.logger.info(f"检测到问题文件名 '{original_name}' -> '{secured_name}'，生成安全的替代文件名")

                # 提取原始文件名的有用信息
                base_name = original_name.replace('.xlsx', '').replace('.xls', '')

                # 如果原始文件名包含模板相关关键词，保留这些信息
                if any(keyword in base_name for keyword in ['模板', 'template', '登记', '信息']):
                    base_filename = f"{template_type}-运维信息登记模板"
                else:
                    # 使用通用名称，避免时间戳导致无法检测冲突
                    base_filename = f"{template_type}-模板"
            else:
                # secure_filename处理后仍有有效内容
                base_filename = secured_name.replace('.xlsx', '').replace('.xls', '')
                if template_type not in base_filename:
                    base_filename = f"{template_type}-{base_filename}"

            # 添加别名到文件名中
            if alias and alias.strip():
                # 清理别名中的特殊字符
                clean_alias = re.sub(r'[<>:"/\\|?*]', '-', alias.strip())
                final_filename = f"{base_filename}-{clean_alias}.xlsx"
            else:
                final_filename = f"{base_filename}.xlsx"

            return final_filename

        # 生成带别名的安全文件名
        filename = generate_safe_filename_with_alias(file.filename, template_type, alias)

        templates_dir = current_app.config['EXCEL_TEMPLATES_FOLDER']
        os.makedirs(templates_dir, exist_ok=True)

        template_path = os.path.join(templates_dir, filename)

        # 检查完全一致的文件名冲突（只有完全相同的文件名才算冲突）
        file_exists = os.path.exists(template_path)
        existing_template = TemplateVersion.query.filter_by(filename=filename).first()

        # 准备冲突信息
        conflict_info = {
            'has_conflict': False,
            'conflict_type': None,
            'existing_info': None,
            'action_taken': 'created'
        }

        # 只有当文件名完全一致时才认为是冲突
        if file_exists or existing_template:
            conflict_info['has_conflict'] = True
            current_app.logger.info(f"检测到完全一致的文件名冲突: {filename}")

            if existing_template:
                conflict_info['existing_info'] = {
                    'filename': existing_template.filename,
                    'template_type': existing_template.template_type,
                    'form_type': existing_template.form_type,
                    'alias': existing_template.alias,
                    'is_active': existing_template.is_active,
                    'created_at': existing_template.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'last_modified': existing_template.last_modified.strftime('%Y-%m-%d %H:%M:%S')
                }

                if file_exists:
                    conflict_info['conflict_type'] = 'both'  # 文件和数据库都存在
                    conflict_info['action_taken'] = 'replaced'
                else:
                    conflict_info['conflict_type'] = 'database_only'  # 只有数据库记录
                    conflict_info['action_taken'] = 'updated'
            else:
                conflict_info['conflict_type'] = 'file_only'  # 只有文件存在
                conflict_info['action_taken'] = 'overwritten'

        # 如果有完全一致的文件名冲突且用户未确认，返回冲突信息让用户选择
        if conflict_info['has_conflict'] and not confirm_overwrite:
            return jsonify({
                'status': 'conflict',
                'message': f'检测到完全相同的模板文件名冲突: {filename}，请确认是否要覆盖现有模板',
                'data': {
                    'filename': filename,
                    'type': template_type,
                    'formType': form_type,
                    'conflict_info': conflict_info
                }
            }), 409  # 409 Conflict

        # 保存上传的文件
        file.save(template_path)
        current_app.logger.info(f"已上传新模板: {template_path}")

        if existing_template:
            # 更新现有记录
            existing_template.template_type = template_type
            existing_template.form_type = form_type
            existing_template.last_modified = datetime.now()
            if alias:
                existing_template.alias = alias
        else:
            # 创建新记录
            existing_template = TemplateVersion(
                filename=filename,
                template_type=template_type,
                is_active=False,  # 默认不激活
                alias=alias or filename.replace('.xlsx', ''),
                form_type=form_type,
                created_at=datetime.now(),
                last_modified=datetime.now()
            )
            db.session.add(existing_template)

        # 提交数据库更改
        db.session.commit()

        # 清除模板文件内存缓存，确保新上传的模板立即生效
        try:
            from app.excel.utils import clear_template_cache
            clear_template_cache()
            current_app.logger.info("模板上传后文件内存缓存已清空")
        except Exception as e:
            current_app.logger.warning(f"清除模板文件内存缓存失败: {str(e)}")

        # 构建响应消息
        if conflict_info['has_conflict']:
            if conflict_info['conflict_type'] == 'both':
                message = f"模板上传成功！已替换现有的同名模板文件和数据库记录"
            elif conflict_info['conflict_type'] == 'database_only':
                message = f"模板上传成功！已更新现有的数据库记录"
            elif conflict_info['conflict_type'] == 'file_only':
                message = f"模板上传成功！已覆盖现有的同名文件"
        else:
            message = "模板上传成功！"

        return jsonify({
            'status': 'success',
            'message': message,
            'data': {
                'id': existing_template.id,
                'filename': filename,
                'type': template_type,
                'formType': form_type,
                'alias': existing_template.alias,
                'conflict_info': conflict_info
            }
        })
    except Exception as e:
        current_app.logger.error(f"上传模板文件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'上传模板文件失败: {str(e)}'
        }), 500

@bp.route('/templates/set_alias', methods=['POST'])
def set_template_alias():
    """
    设置模板的别名
    """
    try:
        data = request.json
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400

        filename = data.get('filename')
        alias = data.get('alias')

        if not filename:
            return jsonify({
                'status': 'error',
                'message': '请指定模板文件名'
            }), 400

        if not alias:
            return jsonify({
                'status': 'error',
                'message': '请指定模板别名'
            }), 400

        # 从数据库查找模板
        template = TemplateVersion.query.filter_by(filename=filename).first()

        if not template:
            return jsonify({
                'status': 'error',
                'message': '模板在数据库中不存在'
            }), 404

        # 检查文件是否存在
        file_path = os.path.join(current_app.config['EXCEL_TEMPLATES_FOLDER'], filename)

        if not os.path.exists(file_path):
            return jsonify({
                'status': 'error',
                'message': '模板文件不存在'
            }), 404

        # 更新别名
        template.alias = alias
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '模板别名设置成功',
            'data': {
                'id': template.id,
                'filename': template.filename,
                'alias': template.alias
            }
        })
    except Exception as e:
        current_app.logger.error(f"设置模板别名失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'设置模板别名失败: {str(e)}'
        }), 500

@bp.route('/templates/activate', methods=['POST'])
def activate_template():
    """
    激活指定的模板版本（设置为当前活动模板）
    """
    try:
        data = request.json
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400

        template_id = data.get('id')
        form_type = data.get('formType')  # 可选，指定要关联的表单类型

        if not template_id:
            return jsonify({
                'status': 'error',
                'message': '请指定要激活的模板ID'
            }), 400

        # 从数据库查找要激活的模板
        template_to_activate = TemplateVersion.query.get(template_id)

        if not template_to_activate:
            return jsonify({
                'status': 'error',
                'message': '模板在数据库中不存在'
            }), 404

        # 检查文件是否存在
        templates_dir = current_app.config['EXCEL_TEMPLATES_FOLDER']
        template_path = os.path.join(templates_dir, template_to_activate.filename)

        if not os.path.exists(template_path):
            return jsonify({
                'status': 'error',
                'message': '模板文件不存在'
            }), 404

        # 如果指定了表单类型，则更新模板的关联表单类型
        if form_type:
            template_to_activate.form_type = form_type

        # 查找当前活动的同类型模板（如果指定了表单类型，则查找关联到该表单类型的活动模板）
        if form_type:
            # 查找关联到指定表单类型的活动模板
            current_active = TemplateVersion.query.filter_by(
                form_type=form_type,
                is_active=True
            ).all()
        else:
            # 查找同一模板类型的活动模板
            current_active = TemplateVersion.query.filter_by(
                template_type=template_to_activate.template_type,
                is_active=True
            ).all()

        # 同时更新表单类型，确保模板类型和表单类型一致
        if not form_type:
            template_to_activate.form_type = template_to_activate.template_type

        # 取消所有当前活动模板的活动状态
        for active_template in current_active:
            if active_template.id != template_to_activate.id:
                active_template.is_active = False

        # 设置新模板为活动状态
        template_to_activate.is_active = True

        # 提交数据库更改
        db.session.commit()

        # 清除相关模板缓存
        affected_form_types = set()
        if form_type:
            affected_form_types.add(form_type)
        if template_to_activate.form_type:
            affected_form_types.add(template_to_activate.form_type)

        # 清除所有受影响表单类型的模板缓存
        for ft in affected_form_types:
            TemplateCacheManager.clear_template_cache(ft)
            current_app.logger.debug(f"清除模板缓存: form_type={ft}")
        
        # 清除模板文件内存缓存，确保新模板生效
        try:
            from app.excel.utils import clear_template_cache
            clear_template_cache()
            current_app.logger.info("模板文件内存缓存已清空")
        except Exception as e:
            current_app.logger.warning(f"清除模板文件内存缓存失败: {str(e)}")

        return jsonify({
            'status': 'success',
            'message': '模板激活成功',
            'data': {
                'id': template_to_activate.id,
                'type': template_to_activate.template_type,
                'formType': template_to_activate.form_type,
                'filename': template_to_activate.filename,
                'alias': template_to_activate.alias
            }
        })
    except Exception as e:
        current_app.logger.error(f"激活模板版本失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'激活模板版本失败: {str(e)}'
        }), 500

@bp.route('/templates/fix-associations', methods=['GET'])
def fix_template_associations():
    """
    修复模板关联，确保每个模板的form_type与template_type一致
    """
    try:
        # 获取所有模板
        templates = TemplateVersion.query.all()
        fixed_count = 0

        for template in templates:
            if template.form_type != template.template_type:
                template.form_type = template.template_type
                fixed_count += 1

        # 提交更改
        if fixed_count > 0:
            db.session.commit()

        return jsonify({
            'status': 'success',
            'message': f'已修复 {fixed_count} 个模板关联',
            'data': {
                'fixed_count': fixed_count
            }
        })
    except Exception as e:
        current_app.logger.error(f"修复模板关联失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'修复模板关联失败: {str(e)}'
        }), 500

@bp.route('/templates/delete', methods=['POST'])
def delete_template():
    """
    删除指定的模板版本
    """
    try:
        data = request.json
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400

        template_id = data.get('id')

        if not template_id:
            return jsonify({
                'status': 'error',
                'message': '请指定要删除的模板ID'
            }), 400

        # 从数据库查找要删除的模板
        template_to_delete = TemplateVersion.query.get(template_id)

        if not template_to_delete:
            return jsonify({
                'status': 'error',
                'message': '模板在数据库中不存在'
            }), 404

        # 检查是否是活动模板
        if template_to_delete.is_active:
            return jsonify({
                'status': 'error',
                'message': '不能删除当前活动的模板，请先激活其他模板'
            }), 400

        # 获取文件路径
        templates_dir = current_app.config['EXCEL_TEMPLATES_FOLDER']
        template_path = os.path.join(templates_dir, template_to_delete.filename)

        # 删除文件（如果存在）
        if os.path.exists(template_path):
            os.remove(template_path)
            current_app.logger.info(f"已删除模板文件: {template_path}")

        # 记录要删除的模板信息，用于返回
        template_info = {
            'id': template_to_delete.id,
            'filename': template_to_delete.filename,
            'type': template_to_delete.template_type,
            'alias': template_to_delete.alias
        }

        # 保存表单类型用于清除缓存
        form_type_to_clear = template_to_delete.form_type

        # 从数据库中删除模板记录
        db.session.delete(template_to_delete)
        db.session.commit()

        # 清除相关模板缓存
        if form_type_to_clear:
            TemplateCacheManager.clear_template_cache(form_type_to_clear)
            current_app.logger.debug(f"清除模板缓存: form_type={form_type_to_clear}")
        
        # 清除模板文件内存缓存，确保删除的模板不再被使用
        try:
            from app.excel.utils import clear_template_cache
            clear_template_cache()
            current_app.logger.info("模板文件内存缓存已清空")
        except Exception as e:
            current_app.logger.warning(f"清除模板文件内存缓存失败: {str(e)}")

        return jsonify({
            'status': 'success',
            'message': '模板删除成功',
            'data': template_info
        })
    except Exception as e:
        current_app.logger.error(f"删除模板失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'删除模板失败: {str(e)}'
        }), 500


@bp.route('/templates/by-form-type/<form_type>', methods=['GET'])
def get_templates_by_form_type(form_type):
    """
    根据表单类型获取所有可用模板
    """
    try:
        current_app.logger.info(f"获取表单类型 {form_type} 的模板列表")

        # 查找指定表单类型的所有模板
        templates = TemplateVersion.query.filter_by(form_type=form_type).all()

        if not templates:
            current_app.logger.warning(f"未找到表单类型 {form_type} 的模板")
            return jsonify({
                'status': 'success',
                'data': [],
                'message': f'未找到表单类型 {form_type} 的模板'
            })

        templates_dir = current_app.config['EXCEL_TEMPLATES_FOLDER']
        template_list = []

        for template in templates:
            file_path = os.path.join(templates_dir, template.filename)

            # 检查文件是否存在
            if not os.path.exists(file_path):
                current_app.logger.warning(f"模板文件不存在: {file_path}")
                continue

            template_list.append({
                'id': template.id,
                'filename': template.filename,
                'alias': template.alias or template.filename.replace('.xlsx', ''),
                'is_active': template.is_active,
                'template_type': template.template_type,
                'form_type': template.form_type,
                'last_modified': template.last_modified.timestamp() if template.last_modified else None,
                'created_at': template.created_at.timestamp() if template.created_at else None
            })

        # 按活动状态和修改时间排序（活动模板在前）
        template_list.sort(key=lambda x: (not x['is_active'], -(x['last_modified'] or 0)))

        current_app.logger.info(f"找到 {len(template_list)} 个 {form_type} 类型的模板")

        return jsonify({
            'status': 'success',
            'data': template_list
        })

    except Exception as e:
        current_app.logger.error(f"获取表单类型模板失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取表单类型模板失败: {str(e)}'
        }), 500




@bp.route('/templates/statistics', methods=['GET'])
def get_templates_statistics():
    """
    获取模板统计信息，按表单类型分组
    """
    try:
        from app.models.models import FormType

        # 获取所有活动的表单类型
        form_types = FormType.query.filter_by(is_active=True).all()

        statistics = {}
        for form_type in form_types:
            # 检查是否有活动模板
            active_template = TemplateVersion.query.filter_by(
                form_type=form_type.name,
                is_active=True
            ).first()

            # 统计模板总数
            total_templates = TemplateVersion.query.filter_by(
                form_type=form_type.name
            ).count()

            statistics[form_type.name] = {
                'hasActiveTemplate': active_template is not None,
                'activeTemplateName': active_template.filename if active_template else None,
                'totalTemplates': total_templates
            }

        return jsonify({
            'status': 'success',
            'data': statistics
        })
    except Exception as e:
        current_app.logger.error(f"获取模板统计信息失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取模板统计信息失败: {str(e)}'
        }), 500