<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h2 class="text-center mb-4">
          <i class="bi bi-person-plus me-2"></i>
          用户注册
        </h2>
        <p class="text-center text-muted">创建您的账户</p>
      </div>
      
      <form @submit.prevent="handleRegister" class="register-form">
        <!-- 用户名 -->
        <div class="mb-3">
          <label for="username" class="form-label">
            <i class="bi bi-person me-1"></i>用户名 *
          </label>
          <input
            type="text"
            class="form-control"
            id="username"
            v-model="registerForm.username"
            :class="{ 'is-invalid': errors.username }"
            placeholder="3-20位字母、数字或下划线"
            required
          >
          <div v-if="errors.username" class="invalid-feedback">
            {{ errors.username }}
          </div>
        </div>
        
        <!-- 邮箱 -->
        <div class="mb-3">
          <label for="email" class="form-label">
            <i class="bi bi-envelope me-1"></i>邮箱 *
          </label>
          <input
            type="email"
            class="form-control"
            id="email"
            v-model="registerForm.email"
            :class="{ 'is-invalid': errors.email }"
            placeholder="请输入邮箱地址"
            required
          >
          <div v-if="errors.email" class="invalid-feedback">
            {{ errors.email }}
          </div>
        </div>
        
        <!-- 密码 -->
        <div class="mb-3">
          <label for="password" class="form-label">
            <i class="bi bi-lock me-1"></i>密码 *
          </label>
          <div class="input-group">
            <input
              :type="showPassword ? 'text' : 'password'"
              class="form-control"
              id="password"
              v-model="registerForm.password"
              :class="{ 'is-invalid': errors.password }"
              placeholder="至少6位字符"
              required
            >
            <button
              type="button"
              class="btn btn-outline-secondary"
              @click="togglePassword"
            >
              <i :class="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
            </button>
          </div>
          <div v-if="errors.password" class="invalid-feedback">
            {{ errors.password }}
          </div>
        </div>
        
        <!-- 确认密码 -->
        <div class="mb-3">
          <label for="confirmPassword" class="form-label">
            <i class="bi bi-lock-fill me-1"></i>确认密码 *
          </label>
          <input
            type="password"
            class="form-control"
            id="confirmPassword"
            v-model="registerForm.confirmPassword"
            :class="{ 'is-invalid': errors.confirmPassword }"
            placeholder="请再次输入密码"
            required
          >
          <div v-if="errors.confirmPassword" class="invalid-feedback">
            {{ errors.confirmPassword }}
          </div>
        </div>
        
        <!-- 真实姓名 -->
        <div class="mb-3">
          <label for="realName" class="form-label">
            <i class="bi bi-person-badge me-1"></i>真实姓名
          </label>
          <input
            type="text"
            class="form-control"
            id="realName"
            v-model="registerForm.real_name"
            placeholder="请输入真实姓名"
          >
        </div>
        
        <!-- 电话号码 -->
        <div class="mb-3">
          <label for="phone" class="form-label">
            <i class="bi bi-telephone me-1"></i>电话号码
          </label>
          <input
            type="tel"
            class="form-control"
            id="phone"
            v-model="registerForm.phone"
            placeholder="请输入电话号码"
          >
        </div>
        
        <!-- 部门 -->
        <div class="mb-3">
          <label for="department" class="form-label">
            <i class="bi bi-building me-1"></i>部门
          </label>
          <input
            type="text"
            class="form-control"
            id="department"
            v-model="registerForm.department"
            placeholder="请输入所属部门"
          >
        </div>
        
        <!-- 职位 -->
        <div class="mb-3">
          <label for="position" class="form-label">
            <i class="bi bi-briefcase me-1"></i>职位
          </label>
          <input
            type="text"
            class="form-control"
            id="position"
            v-model="registerForm.position"
            placeholder="请输入职位"
          >
        </div>
        
        <!-- 错误信息 -->
        <div v-if="errorMessage" class="alert alert-danger" role="alert">
          <i class="bi bi-exclamation-triangle me-2"></i>
          {{ errorMessage }}
        </div>
        
        <!-- 成功信息 -->
        <div v-if="successMessage" class="alert alert-success" role="alert">
          <i class="bi bi-check-circle me-2"></i>
          {{ successMessage }}
        </div>
        
        <!-- 注册按钮 -->
        <button
          type="submit"
          class="btn btn-primary w-100 mb-3"
          :disabled="loading"
        >
          <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
          <i v-else class="bi bi-person-plus me-2"></i>
          {{ loading ? '注册中...' : '注册' }}
        </button>
        
        <!-- 登录链接 -->
        <div class="text-center">
          <span class="text-muted">已有账户？</span>
          <router-link to="/login" class="text-decoration-none">
            立即登录
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'Register',
  data() {
    return {
      registerForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        real_name: '',
        phone: '',
        department: '',
        position: ''
      },
      showPassword: false,
      loading: false,
      errorMessage: '',
      successMessage: '',
      errors: {}
    }
  },
  mounted() {
    // 如果已经登录，重定向到首页
    if (this.$store.getters.isAuthenticated) {
      this.$router.push('/')
    }
  },
  methods: {
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    validateForm() {
      this.errors = {}
      
      // 验证用户名
      if (!this.registerForm.username.trim()) {
        this.errors.username = '请输入用户名'
      } else if (!/^[a-zA-Z0-9_]{3,20}$/.test(this.registerForm.username)) {
        this.errors.username = '用户名只能包含字母、数字和下划线，长度3-20位'
      }
      
      // 验证邮箱
      if (!this.registerForm.email.trim()) {
        this.errors.email = '请输入邮箱'
      } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(this.registerForm.email)) {
        this.errors.email = '邮箱格式不正确'
      }
      
      // 验证密码
      if (!this.registerForm.password) {
        this.errors.password = '请输入密码'
      } else if (this.registerForm.password.length < 6) {
        this.errors.password = '密码长度不能少于6位'
      }
      
      // 验证确认密码
      if (!this.registerForm.confirmPassword) {
        this.errors.confirmPassword = '请确认密码'
      } else if (this.registerForm.password !== this.registerForm.confirmPassword) {
        this.errors.confirmPassword = '两次输入的密码不一致'
      }
      
      return Object.keys(this.errors).length === 0
    },
    
    async handleRegister() {
      if (!this.validateForm()) {
        return
      }
      
      this.loading = true
      this.errorMessage = ''
      this.successMessage = ''
      
      try {
        const response = await axios.post('/auth/register', {
          username: this.registerForm.username.trim(),
          email: this.registerForm.email.trim(),
          password: this.registerForm.password,
          real_name: this.registerForm.real_name.trim(),
          phone: this.registerForm.phone.trim(),
          department: this.registerForm.department.trim(),
          position: this.registerForm.position.trim()
        })
        
        if (response.data.status === 'success') {
          this.successMessage = '注册成功！请使用您的账户登录。'
          
          // 3秒后跳转到登录页面
          setTimeout(() => {
            this.$router.push('/login')
          }, 3000)
        } else {
          this.errorMessage = response.data.message || '注册失败'
        }
        
      } catch (error) {
        if (error.response && error.response.data) {
          const errorData = error.response.data
          this.errorMessage = errorData.message || '注册失败，请稍后重试'

          // 如果是数据库连接错误，显示更详细的信息
          if (errorData.error_type === 'database_connection') {
            this.errorMessage += '\n\n' + (errorData.details || '')
          }
        } else {
          this.errorMessage = '网络错误，请稍后重试'
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 500px;
  animation: slideUp 0.5s ease-out;
  max-height: 90vh;
  overflow-y: auto;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.register-header h2 {
  color: #333;
  font-weight: 600;
}

.register-form .form-control {
  border-radius: 8px;
  border: 1px solid #ddd;
  padding: 12px 15px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.register-form .form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  transform: none;
  box-shadow: none;
}

.alert-danger {
  border-radius: 8px;
  border: none;
  background-color: #f8d7da;
  color: #721c24;
}

.alert-success {
  border-radius: 8px;
  border: none;
  background-color: #d1edff;
  color: #0c5460;
}

.input-group .btn-outline-secondary {
  border-color: #ddd;
  color: #6c757d;
}

.input-group .btn-outline-secondary:hover {
  background-color: #f8f9fa;
  border-color: #ddd;
  color: #495057;
}

@media (max-width: 576px) {
  .register-card {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .register-header h2 {
    font-size: 1.5rem;
  }
}
</style>
