"""
Excel文件导入解析器
支持从Excel文件中解析数据并生成表单提交记录
使用模板解析器来提取占位符对应的值
"""

import os
import re
import tempfile
import traceback
from datetime import datetime, date
from openpyxl import load_workbook
from flask import current_app
from app.models.models import FormSubmission, db
from .template_parser import TemplateParser
import json


class ExcelImporter:
    """Excel文件导入解析器"""
    
    # 字段映射配置
    FIELD_MAPPING = {
        '安全测评': {
            'excel_fields': {
                # 基本信息
                '公司名称': ['公司名称', '客户名称', '企业名称', '单位名称'],
                '客户标识': ['客户标识', '客户ID', '客户编号'],
                '记录日期': ['记录日期', '日期', '创建日期', '填写日期'],
                # 版本信息 - 安全测评专用字段
                '部署包版本': ['部署包版本', '版本信息', '版本', '系统版本', '软件版本'],
                # 访问信息
                '管理员页面IP': ['管理员页面IP', '管理IP', '后台IP', '管理员IP'],
                '用户页面IP': ['用户页面IP', '前台IP', '用户IP'],
                '升级页面IP': ['升级页面IP', '升级IP'],
                '对外服务端口': ['对外服务端口', '服务端口', '端口'],
                # 账号信息
                '超级管理员账号': ['超级管理员账号', 'root账号', '超管账号'],
                '超级管理员密码': ['超级管理员密码', 'root密码', '超管密码'],
                '管理员账号': ['管理员账号', '管理员用户名', 'admin账号', '后台账号'],
                '管理员密码': ['管理员密码', 'admin密码', '后台密码'],
                '用户账号': ['用户账号', '用户名'],
                '用户密码': ['用户密码'],
                # 功能信息
                '产品功能': ['产品功能', '功能描述'],
                '授权功能': ['授权功能', '授权内容'],
                '授权开始日期': ['授权开始日期', '授权开始'],
                '授权结束日期': ['授权结束日期', '授权结束'],
            },
            'required_fields': ['公司名称', '部署包版本', '记录日期'],
            'server_sheet_names': ['服务器信息', '服务器配置', '主机信息', '部署架构'],
            'component_sheet_names': ['组件部署', '组件信息', '部署组件']
        },
        '安全监测': {
            'excel_fields': {
                # 基本信息
                '公司名称': ['公司名称', '客户名称', '企业名称'],
                '客户标识': ['客户标识', '客户ID'],
                '记录日期': ['记录日期', '日期', '创建日期'],
                # 版本信息 - 安全监测专用字段
                '前端版本': ['前端版本', 'Web版本', '界面版本'],
                '后端版本': ['后端版本', '服务版本', '后台版本'],
                '标准或定制': ['标准/定制', '标准或定制', '版本类型'],
                '日活': ['日活', '日活跃用户'],
                # 访问信息
                'SDK外网流量入口': ['SDK外网流量入口', 'SDK入口'],
                'SDK流量转发到Nginx入口': ['SDK流量转发到Nginx入口', 'Nginx入口'],
                '业务功能页面地址': ['业务功能页面地址', '业务页面'],
                # 账号信息
                '超级管理员账号': ['超级管理员账号', '超管账号'],
                '超级管理员密码': ['超级管理员密码', '超管密码'],
                '客户管理员账号': ['客户管理员账号', '客户账号'],
                '客户管理员密码': ['客户管理员密码', '客户密码'],
                'init地址': ['init地址', 'init访问地址'],
                'init用户名': ['init用户名', 'init账号'],
                'init密码': ['init密码'],
                'kibana地址': ['kibana地址', 'kibana访问地址'],
                'kibana认证信息': ['kibana认证信息', 'kibana账号密码'],
            },
            'required_fields': ['公司名称', '前端版本', '后端版本', '记录日期'],
            'server_sheet_names': ['服务器信息', '服务器配置', '部署架构'],
            'component_sheet_names': ['组件部署', '组件信息']
        },
        '应用加固': {
            'excel_fields': {
                # 基本信息
                '公司名称': ['公司名称', '客户名称', '企业名称'],
                '客户': ['客户', '客户名称', '联系人'],
                '客户标识': ['客户标识', '客户ID'],
                '记录日期': ['记录日期', '日期', '创建日期'],
                # 版本信息 - 应用加固专用字段
                '部署的平台版本': ['部署的平台版本', '平台版本', '版本'],
                # 访问信息
                '平台访问地址': ['平台访问地址', '平台地址', '访问地址', 'URL'],
                '管理员信息': ['管理员信息', '账号信息'],
                '升级平台地址': ['升级平台地址', '升级平台信息', '升级信息'],
            },
            'required_fields': ['公司名称', '客户', '记录日期'],
            'server_sheet_names': ['服务器信息', '服务器配置', '部署架构'],
            'component_sheet_names': ['组件部署', '组件信息']
        }
    }
    
    def __init__(self, file_path, form_type=None, original_filename=None):
        """
        初始化导入器

        Args:
            file_path (str): Excel文件路径
            form_type (str): 指定的表单类型，如果为None则自动检测
            original_filename (str): 原始文件名，用于类型检测
        """
        self.file_path = file_path
        self.filename = original_filename or os.path.basename(file_path) if file_path else ""
        self.form_type = form_type
        self.workbook = None
        self.parsed_data = {}
        self.errors = []
        self.warnings = []
        self.template_parser = None
        
    def load_workbook(self):
        """加载Excel工作簿"""
        try:
            self.workbook = load_workbook(self.file_path, data_only=True)
            return True
        except Exception as e:
            self.errors.append(f"无法打开Excel文件: {str(e)}")
            return False
    
    def detect_form_type(self):
        """自动检测表单类型"""
        if not self.workbook:
            current_app.logger.warning("❌ 工作簿未加载，无法检测表单类型")
            return None

        current_app.logger.info(f"🔍 开始检测表单类型，文件名: {self.filename}")

        # 第一优先级：检查文件名
        if hasattr(self, 'filename') and self.filename:
            filename_lower = self.filename.lower()
            if '安全监测' in filename_lower or '监测' in filename_lower:
                current_app.logger.info("✅ 文件名匹配: 安全监测")
                return '安全监测'
            elif '应用加固' in filename_lower or '加固' in filename_lower:
                current_app.logger.info("✅ 文件名匹配: 应用加固")
                return '应用加固'
            elif '安全测评' in filename_lower or '测评' in filename_lower:
                current_app.logger.info("✅ 文件名匹配: 安全测评")
                return '安全测评'

        current_app.logger.info("❌ 文件名无匹配，检查sheet名称")

        # 第二优先级：检查sheet名称
        try:
            sheet_names = [name.lower() for name in self.workbook.sheetnames]
            current_app.logger.info(f"📄 Sheet名称: {sheet_names}")

            if any('监测' in name for name in sheet_names):
                current_app.logger.info("✅ Sheet名称匹配: 安全监测")
                return '安全监测'
            elif any('加固' in name for name in sheet_names):
                current_app.logger.info("✅ Sheet名称匹配: 应用加固")
                return '应用加固'
            elif any('测评' in name for name in sheet_names):
                current_app.logger.info("✅ Sheet名称匹配: 安全测评")
                return '安全测评'
        except Exception as e:
            current_app.logger.error(f"❌ 检查sheet名称时发生错误: {str(e)}")

        current_app.logger.info("❌ Sheet名称无匹配，检查内容特征")

        # 第三优先级：检查内容特征
        try:
            main_sheet = self.workbook.active
            content_text = ""

            # 收集前20行的所有文本内容
            for row in main_sheet.iter_rows(max_row=20):
                for cell in row:
                    if cell.value:
                        content_text += str(cell.value) + " "

            current_app.logger.info(f"📝 内容文本片段: {content_text[:100]}...")

            # 使用评分系统进行更准确的检测
            monitoring_score = 0
            assessment_score = 0
            hardening_score = 0

            # 安全监测特征评分
            if '前端版本' in content_text:
                monitoring_score += 2
            if '后端版本' in content_text:
                monitoring_score += 2
            if '日活' in content_text:
                monitoring_score += 3
            if 'kibana' in content_text.lower():
                monitoring_score += 2
            if '安全监测' in content_text:
                monitoring_score += 3
            if '客户标识' in content_text:
                monitoring_score += 1

            # 安全测评特征评分
            if '部署包版本' in content_text:
                assessment_score += 3
            if '产品功能' in content_text:
                assessment_score += 2
            if '授权功能' in content_text:
                assessment_score += 2
            if '授权开始日期' in content_text or '授权结束日期' in content_text:
                assessment_score += 1

            # 应用加固特征评分
            if '平台地址' in content_text or '平台访问地址' in content_text:
                hardening_score += 3
            if '客户' in content_text and '部署的平台版本' in content_text:
                hardening_score += 2
            if '管理员信息' in content_text:
                hardening_score += 1

            current_app.logger.info(f"🔍 特征评分: 监测={monitoring_score}, 测评={assessment_score}, 加固={hardening_score}")

            # 根据最高分判断类型
            max_score = max(monitoring_score, assessment_score, hardening_score)

            if max_score >= 2:  # 降低阈值，提高检测成功率
                if monitoring_score == max_score:
                    current_app.logger.info(f"✅ 内容特征匹配: 安全监测 (得分: {monitoring_score})")
                    return '安全监测'
                elif assessment_score == max_score:
                    current_app.logger.info(f"✅ 内容特征匹配: 安全测评 (得分: {assessment_score})")
                    return '安全测评'
                elif hardening_score == max_score:
                    current_app.logger.info(f"✅ 内容特征匹配: 应用加固 (得分: {hardening_score})")
                    return '应用加固'

            # 如果所有得分都很低，检查基本字段
            if '公司名称' in content_text and '记录日期' in content_text:
                current_app.logger.info("💡 检测到基本表单字段，默认为安全监测类型")
                return '安全监测'

            current_app.logger.warning("❌ 内容特征无匹配，使用默认类型: 安全监测")
            return '安全监测'  # 默认类型改为安全监测

        except Exception as e:
            current_app.logger.error(f"❌ 内容检测过程中发生错误: {str(e)}")
            current_app.logger.info("💡 发生错误时使用默认类型: 安全监测")
            return '安全监测'
    
    def find_field_value(self, sheet, field_name, possible_names):
        """在sheet中查找字段值"""
        for row in sheet.iter_rows(max_row=30):  # 只搜索前30行
            for i, cell in enumerate(row):
                if cell.value and isinstance(cell.value, str):
                    cell_text = cell.value.strip()

                    # 检查是否匹配任何可能的字段名
                    for possible_name in possible_names:
                        if possible_name in cell_text:
                            # 优先检查是否是"字段名：值"格式
                            if '：' in cell_text or ':' in cell_text:
                                # 分割字段名和值
                                separator = '：' if '：' in cell_text else ':'
                                parts = cell_text.split(separator, 1)
                                if len(parts) == 2 and possible_name in parts[0]:
                                    value = parts[1].strip()
                                    if value:
                                        return self.clean_value(value)

                            # 如果不是"字段名：值"格式，查找值：可能在右边的单元格或下一行
                            value = None

                            # 尝试右边的单元格
                            if i + 1 < len(row) and row[i + 1].value:
                                value = row[i + 1].value

                            # 如果右边没有，尝试下一行同一列
                            if not value:
                                next_row_index = cell.row + 1
                                if next_row_index <= sheet.max_row:
                                    next_cell = sheet.cell(row=next_row_index, column=cell.column)
                                    if next_cell.value:
                                        value = next_cell.value

                            if value:
                                return self.clean_value(value)

        return None
    
    def clean_value(self, value):
        """清理和标准化值"""
        if value is None:
            return None
        
        if isinstance(value, (int, float)):
            return str(value)
        
        if isinstance(value, datetime):
            return value.strftime('%Y-%m-%d')
        
        if isinstance(value, date):
            return value.strftime('%Y-%m-%d')
        
        # 字符串处理
        cleaned = str(value).strip()
        
        # 移除多余的空格
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        return cleaned
    
    def parse_date(self, date_value):
        """解析日期值"""
        if isinstance(date_value, (datetime, date)):
            return date_value.strftime('%Y-%m-%d')
        
        if isinstance(date_value, str):
            # 尝试多种日期格式
            date_formats = [
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%Y年%m月%d日',
                '%m/%d/%Y',
                '%d/%m/%Y'
            ]
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_value.strip(), fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
        
        # 如果无法解析，返回当前日期
        self.warnings.append(f"无法解析日期格式: {date_value}，使用当前日期")
        return datetime.now().strftime('%Y-%m-%d')
    
    def extract_basic_info(self, sheet):
        """提取基本信息"""
        basic_info = {}
        
        if not self.form_type or self.form_type not in self.FIELD_MAPPING:
            return basic_info
        
        field_mapping = self.FIELD_MAPPING[self.form_type]['excel_fields']
        
        for field_name, possible_names in field_mapping.items():
            value = self.find_field_value(sheet, field_name, possible_names)
            if value:
                if '日期' in field_name:
                    value = self.parse_date(value)
                basic_info[field_name] = value
        
        return basic_info
    
    def find_table_data(self, sheet, header_keywords):
        """查找表格数据"""
        # 查找表头行
        header_row = None
        header_col_start = None
        
        for row_idx, row in enumerate(sheet.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value and isinstance(cell.value, str):
                    cell_text = str(cell.value).strip()
                    # 更精确的匹配：关键词应该是完整的词或者在开头
                    for keyword in header_keywords:
                        if (cell_text == keyword or
                            cell_text.startswith(keyword + ' ') or
                            cell_text.startswith(keyword + '：') or
                            cell_text.startswith(keyword + ':')):
                            header_row = row_idx
                            header_col_start = col_idx
                            break
                    if header_row:
                        break
            if header_row:
                break
        
        if not header_row:
            return []
        
        # 解析表头
        headers = []
        header_row_cells = list(sheet.iter_rows(min_row=header_row, max_row=header_row))[0]

        # 从找到关键词的列开始，解析整行的表头
        for cell in header_row_cells[header_col_start-1:]:
            if cell.value and str(cell.value).strip():
                headers.append(str(cell.value).strip())
            else:
                # 遇到空单元格，检查是否还有后续的非空单元格
                # 如果有，继续；如果连续3个空单元格，则停止
                empty_count = 0
                for next_cell in header_row_cells[len(headers)+header_col_start-1:len(headers)+header_col_start+2]:
                    if not next_cell.value or not str(next_cell.value).strip():
                        empty_count += 1
                    else:
                        break
                if empty_count >= 3:
                    break
                headers.append('')  # 占位符
        
        if not headers:
            return []
        
        # 解析数据行
        data_rows = []
        for row in sheet.iter_rows(min_row=header_row + 1):
            row_data = {}
            has_data = False
            
            for i, cell in enumerate(row[header_col_start-1:header_col_start-1+len(headers)]):
                if i < len(headers):
                    value = self.clean_value(cell.value)
                    if value:
                        row_data[headers[i]] = value
                        has_data = True
            
            if has_data:
                data_rows.append(row_data)
            elif len(data_rows) > 0:
                # 如果已经有数据行，遇到空行就停止
                break
        
        return data_rows
    
    def extract_server_info(self):
        """提取服务器信息"""
        servers = []

        if not self.form_type or self.form_type not in self.FIELD_MAPPING:
            return servers

        sheet_names = self.FIELD_MAPPING[self.form_type]['server_sheet_names']

        # 尝试在指定的sheet中查找服务器信息
        for ws_name in self.workbook.sheetnames:
            if any(name in ws_name for name in sheet_names):
                sheet = self.workbook[ws_name]
                server_data = self.find_table_data(sheet, ['IP地址', 'IP', '主机IP'])
                if server_data:
                    servers.extend(server_data)
                    break

        # 如果没有找到专门的服务器sheet，在主sheet中查找
        if not servers:
            main_sheet = self.workbook.active
            servers = self.find_table_data(main_sheet, ['IP地址', 'IP', '主机IP'])

        # 处理服务器信息，转换运维字段为新格式
        for server in servers:
            self._process_server_ops_info(server)

        return servers

    def _process_server_ops_info(self, server):
        """处理服务器运维信息，转换为新的数据结构"""
        ops_users = []

        # 处理Root用户
        root_password = server.get('Root密码', server.get('root密码', ''))
        if root_password:
            ops_users.append({
                '用户名': 'root',
                '密码': root_password,
                'isDefault': True
            })

        # 处理其他运维用户（兼容旧格式数据）
        for i in range(1, 10):  # 支持最多9个运维用户的迁移
            user_key = f'运维用户{i}'
            password_key = f'运维用户{i}密码'

            ops_user = server.get(user_key, '')
            ops_password = server.get(password_key, '')

            if ops_user or ops_password:
                ops_users.append({
                    '用户名': ops_user,
                    '密码': ops_password,
                    'isDefault': False
                })

        # 设置运维用户数组
        if ops_users:
            server['运维用户'] = ops_users

        # 确保SSH端口字段存在
        if 'SSH端口' not in server:
            server['SSH端口'] = '22'  # 默认SSH端口

        # 清理旧的扁平化字段（保持兼容性，但优先使用数组格式）
        # 注意：不删除这些字段，因为模板渲染可能还需要它们

    def extract_maintenance_records(self):
        """提取维护记录信息"""
        maintenance_records = []

        # 尝试在所有sheet中查找维护记录
        for ws_name in self.workbook.sheetnames:
            sheet = self.workbook[ws_name]

            # 查找维护记录表格
            records = self.find_maintenance_table(sheet)
            if records:
                maintenance_records.extend(records)

        return maintenance_records

    def find_maintenance_table(self, sheet):
        """在sheet中查找维护记录表格"""
        records = []

        # 查找维护记录表头
        maintenance_headers = ['维护时间', '时间', '维护人员', '人员', '维护类型', '类型', '维护内容', '内容', '任务链接', '链接']
        header_row = None
        header_mapping = {}

        for row_idx, row in enumerate(sheet.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value and str(cell.value).strip() in maintenance_headers:
                    header_row = row_idx
                    break
            if header_row:
                break

        if not header_row:
            return records

        # 建立表头映射
        for col_idx, cell in enumerate(sheet[header_row], 1):
            if cell.value:
                header_text = str(cell.value).strip()
                if header_text in ['维护时间', '时间']:
                    header_mapping[col_idx] = 'time'
                elif header_text in ['维护人员', '人员']:
                    header_mapping[col_idx] = 'staff'
                elif header_text in ['维护类型', '类型']:
                    header_mapping[col_idx] = 'type'
                elif header_text in ['维护内容', '内容']:
                    header_mapping[col_idx] = 'content'
                elif header_text in ['任务链接', '链接']:
                    header_mapping[col_idx] = 'onesLink'

        # 提取数据行
        for row in sheet.iter_rows(min_row=header_row + 1):
            row_data = {}
            has_data = False

            for col_idx, cell in enumerate(row, 1):
                if col_idx in header_mapping and cell.value:
                    field_name = header_mapping[col_idx]
                    row_data[field_name] = str(cell.value).strip()
                    has_data = True

            if has_data:
                # 确保必要字段存在
                record = {
                    'time': row_data.get('time', ''),
                    'staff': row_data.get('staff', ''),
                    'type': row_data.get('type', '维护'),
                    'content': row_data.get('content', ''),
                    'onesLink': row_data.get('onesLink', '')
                }
                records.append(record)
            elif len(records) > 0:
                # 如果已经有数据行，遇到空行就停止
                break

        return records

    def extract_component_info(self):
        """提取组件信息"""
        component_info = {
            '部署应用': [],
            '组件端口': {},
            'selectedComponentDetails': {}
        }

        # 尝试在所有sheet中查找组件信息
        for ws_name in self.workbook.sheetnames:
            sheet = self.workbook[ws_name]

            # 查找组件部署表格
            components = self.find_component_table(sheet)
            if components:
                component_info['部署应用'].extend([comp['name'] for comp in components if comp.get('name')])

                # 构建组件端口映射和详细信息
                for comp in components:
                    comp_name = comp.get('name', '')
                    if comp_name:
                        # 组件端口映射
                        port = comp.get('port', comp.get('端口', ''))
                        if port and port != '无':
                            component_info['组件端口'][comp_name] = port

                        # 组件详细信息
                        component_info['selectedComponentDetails'][comp_name] = {
                            'name': comp_name,
                            'version': comp.get('version', comp.get('版本', '')),
                            'port': port,
                            'count': comp.get('count', comp.get('数量', '1')),
                            'defaultPort': port
                        }

        # 如果没有找到专门的组件表格，尝试从服务器信息中提取
        if not component_info['部署应用']:
            servers = self.extract_server_info()
            for server in servers:
                # 查找服务器中的部署应用字段
                deployed_apps = server.get('部署应用', server.get('部署组件', ''))
                if deployed_apps:
                    if isinstance(deployed_apps, str):
                        # 如果是字符串，按逗号或换行分割
                        apps = [app.strip() for app in deployed_apps.replace('\n', ',').split(',') if app.strip()]
                        component_info['部署应用'].extend(apps)
                    elif isinstance(deployed_apps, list):
                        component_info['部署应用'].extend(deployed_apps)

        # 去重
        component_info['部署应用'] = list(set(component_info['部署应用']))

        return component_info

    def find_component_table(self, sheet):
        """在sheet中查找组件信息表格"""
        components = []

        # 查找组件表头
        component_headers = ['组件名称', '组件', '应用名称', '应用', '端口', '版本', '数量']
        header_row = None
        header_mapping = {}

        for row_idx, row in enumerate(sheet.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value and str(cell.value).strip() in component_headers:
                    header_row = row_idx
                    break
            if header_row:
                break

        if not header_row:
            return components

        # 建立表头映射
        for col_idx, cell in enumerate(sheet[header_row], 1):
            if cell.value:
                header_text = str(cell.value).strip()
                if header_text in ['组件名称', '组件', '应用名称', '应用']:
                    header_mapping[col_idx] = 'name'
                elif header_text in ['端口']:
                    header_mapping[col_idx] = 'port'
                elif header_text in ['版本']:
                    header_mapping[col_idx] = 'version'
                elif header_text in ['数量']:
                    header_mapping[col_idx] = 'count'

        # 提取数据行
        for row in sheet.iter_rows(min_row=header_row + 1):
            row_data = {}
            has_data = False

            for col_idx, cell in enumerate(row, 1):
                if col_idx in header_mapping and cell.value:
                    field_name = header_mapping[col_idx]
                    row_data[field_name] = str(cell.value).strip()
                    has_data = True

            if has_data and row_data.get('name'):
                # 确保必要字段存在
                component = {
                    'name': row_data.get('name', ''),
                    'port': row_data.get('port', ''),
                    'version': row_data.get('version', ''),
                    'count': row_data.get('count', '1')
                }
                components.append(component)
            elif len(components) > 0:
                # 如果已经有数据行，遇到空行就停止
                break

        return components

    def validate_data(self, data):
        """验证解析的数据"""
        errors = []

        if not self.form_type or self.form_type not in self.FIELD_MAPPING:
            errors.append("未知的表单类型")
            return errors

        required_fields = self.FIELD_MAPPING[self.form_type]['required_fields']

        # 检查必填字段（改为警告，不阻止导入）
        for field in required_fields:
            if not data.get(field):
                self.warnings.append(f"建议填写必填字段: {field}")

        # 验证日期格式
        if data.get('记录日期'):
            try:
                datetime.strptime(data['记录日期'], '%Y-%m-%d')
            except ValueError:
                errors.append("记录日期格式错误")

        # 验证服务器信息（改为警告）
        servers = data.get('服务器信息', [])
        for i, server in enumerate(servers):
            if not server.get('IP地址'):
                self.warnings.append(f"第{i+1}台服务器缺少IP地址")

        return errors
    
    def parse_excel_with_template(self):
        """使用模板解析器解析Excel文件"""
        if not self.load_workbook():
            return None

        # 检测表单类型
        if not self.form_type:
            self.form_type = self.detect_form_type()

        if not self.form_type:
            self.errors.append("无法检测表单类型")
            return None

        # 初始化模板解析器
        try:
            current_app.logger.info(f"开始使用模板解析器解析 {self.form_type} 类型的文件: {self.file_path}")
            self.template_parser = TemplateParser(self.form_type)

            if not self.template_parser.template_path:
                current_app.logger.warning(f"未找到 {self.form_type} 的模板文件，使用传统解析方法")
                return self.parse_excel()

            current_app.logger.info(f"使用模板文件: {self.template_parser.template_path}")

            # 执行逆向解析：根据模板占位符从填充的Excel中提取数据
            extracted_data = self._reverse_parse_with_template()

            current_app.logger.info(f"模板解析完成，提取到 {len(extracted_data.get('basic_info', {}))} 个基本字段，{len(extracted_data.get('servers', []))} 台服务器")

            # 合并错误和警告
            self.errors.extend(extracted_data.get('errors', []))
            self.warnings.extend(extracted_data.get('warnings', []))

            # 转换数据格式以兼容现有代码
            parsed_data = self._convert_template_data(extracted_data)

            current_app.logger.info(f"数据转换完成，最终数据包含 {len(parsed_data)} 个字段")

            # 验证数据
            validation_errors = self.validate_data(parsed_data)
            self.errors.extend(validation_errors)

            self.parsed_data = parsed_data
            return parsed_data

        except Exception as e:
            current_app.logger.error(f"模板解析失败: {str(e)}")
            current_app.logger.error(f"异常详情: {traceback.format_exc()}")
            self.errors.append(f"模板解析失败: {str(e)}")
            # 回退到传统解析方法
            current_app.logger.warning("回退到传统解析方法")
            return self.parse_excel()

    def _reverse_parse_with_template(self):
        """根据模板进行逆向解析"""
        extracted_data = {
            'basic_info': {},
            'servers': [],
            'components': {},
            'range_data': {},
            'form_type': self.form_type,
            'errors': [],
            'warnings': []
        }

        try:
            current_app.logger.info("🔄 开始逆向解析：根据模板占位符从填充的Excel中提取数据")

            # 1. 逆向解析简单占位符（基本信息）
            current_app.logger.info("🔍 逆向解析基本信息字段...")
            basic_info = self._reverse_parse_basic_fields()
            extracted_data['basic_info'].update(basic_info)
            current_app.logger.info(f"✅ 提取到 {len(basic_info)} 个基本信息字段")

            # 2. 逆向解析range占位符（服务器信息等）
            current_app.logger.info("🔍 逆向解析服务器信息...")
            servers = self._reverse_parse_server_info()
            extracted_data['servers'] = servers
            current_app.logger.info(f"✅ 提取到 {len(servers)} 台服务器信息")

            # 3. 逆向解析组件信息
            current_app.logger.info("🔍 逆向解析组件信息...")
            components = self._reverse_parse_component_info()
            extracted_data['components'] = components
            current_app.logger.info(f"✅ 提取到 {len(components)} 个组件信息")

            # 4. 逆向解析维护记录
            current_app.logger.info("🔍 逆向解析维护记录...")
            maintenance_records = self._reverse_parse_maintenance_records()
            if maintenance_records:
                extracted_data['range_data']['维护记录'] = maintenance_records
                current_app.logger.info(f"✅ 提取到 {len(maintenance_records)} 条维护记录")

            current_app.logger.info("✅ 逆向解析完成")

        except Exception as e:
            current_app.logger.error(f"❌ 逆向解析过程中发生错误: {str(e)}")
            extracted_data['errors'].append(f"逆向解析错误: {str(e)}")

        return extracted_data

    def _reverse_parse_basic_fields(self):
        """逆向解析基本信息字段"""
        basic_info = {}

        try:
            # 遍历模板中的简单占位符
            for field_name, placeholder_info in self.template_parser.placeholders.items():
                if placeholder_info['type'] == 'simple':
                    # 根据模板中的位置从填充的Excel中提取值
                    value = self._extract_value_from_position(
                        placeholder_info['sheet'],
                        placeholder_info['row'],
                        placeholder_info['col'],
                        field_name,
                        placeholder_info
                    )

                    if value:
                        basic_info[field_name] = value
                        current_app.logger.debug(f"📋 提取字段 {field_name}: {value}")

            # 如果位置提取失败，使用回退方法
            if len(basic_info) < len(self.template_parser.placeholders) * 0.3:
                current_app.logger.warning("⚠️ 位置提取成功率较低，使用回退方法")
                fallback_info = self._fallback_parse_basic_fields()
                basic_info.update(fallback_info)

        except Exception as e:
            current_app.logger.error(f"❌ 基本字段逆向解析失败: {str(e)}")

        return basic_info

    def _extract_value_from_position(self, sheet_name, row, col, field_name, placeholder_info):
        """从指定位置提取值"""
        try:
            # 找到对应的sheet
            target_sheet = None
            if sheet_name in self.workbook.sheetnames:
                target_sheet = self.workbook[sheet_name]
            else:
                # 如果sheet不存在，使用主sheet
                target_sheet = self.workbook.active
                current_app.logger.debug(f"⚠️ Sheet '{sheet_name}' 不存在，使用主sheet")

            if target_sheet and row <= target_sheet.max_row and col <= target_sheet.max_column:
                cell = target_sheet.cell(row=row, column=col)

                if cell.value is not None:
                    cell_value = str(cell.value).strip()

                    # 检查是否是模板占位符（未填充）
                    if '{{' in cell_value and '}}' in cell_value:
                        current_app.logger.debug(f"⚠️ 位置 {sheet_name}[{row},{col}] 包含未填充的占位符: {cell_value}")
                        return None

                    # 如果是多占位符单元格，需要智能提取
                    original_template = placeholder_info.get('original_value', '')
                    if '{{' in original_template and original_template.count('{{') > 1:
                        # 多占位符单元格，需要智能解析
                        extracted_value = self._smart_extract_from_filled_template(
                            original_template, cell_value, field_name
                        )
                        return extracted_value
                    else:
                        # 单占位符单元格，直接返回值
                        return self._clean_extracted_value(cell_value)

            return None

        except Exception as e:
            current_app.logger.debug(f"❌ 从位置 {sheet_name}[{row},{col}] 提取值失败: {str(e)}")
            return None

    def _smart_extract_from_filled_template(self, template, filled_value, target_field):
        """从填充的模板中智能提取目标字段的值"""
        try:
            # 特殊处理标题格式
            if '运维文档' in template and '更新时间' in template:
                if target_field == '公司名称':
                    # 提取公司名称：从开头到第一个"-运维文档"
                    match = re.search(r'^([^-]+)(?=-运维文档)', filled_value)
                    if match:
                        return match.group(1).strip()
                elif target_field == '记录日期':
                    # 提取记录日期：从"更新时间："后到"）"前
                    match = re.search(r'更新时间：([^）]+)', filled_value)
                    if match:
                        return match.group(1).strip()

            # 通用多占位符解析
            # 构建正则表达式来匹配模板结构
            pattern = template
            placeholders = re.findall(r'\{\{\s*([^|}]+?)\s*\}\}', template)

            # 为每个占位符创建捕获组
            for i, placeholder in enumerate(placeholders):
                if placeholder == target_field:
                    # 目标字段使用捕获组
                    pattern = pattern.replace(f'{{{{{placeholder}}}}}', r'([^-（）\s]+(?:\s+[^-（）\s]+)*)', 1)
                else:
                    # 其他字段使用非捕获组
                    pattern = pattern.replace(f'{{{{{placeholder}}}}}', r'[^-（）\s]+(?:\s+[^-（）\s]+)*', 1)

            # 转义正则表达式特殊字符
            pattern = re.escape(pattern)
            # 恢复捕获组
            pattern = pattern.replace(r'\([^-（）\\s]\+\(\?\:\\s\+\[^-（）\\s]\+\)\*\)', r'([^-（）\s]+(?:\s+[^-（）\s]+)*)')

            # 尝试匹配
            match = re.search(pattern, filled_value)
            if match:
                return match.group(1).strip()

            return None

        except Exception as e:
            current_app.logger.debug(f"❌ 智能提取失败: {str(e)}")
            return None

    def _clean_extracted_value(self, value):
        """清理提取的值"""
        if not value:
            return None

        # 移除多余的空格
        cleaned = re.sub(r'\s+', ' ', str(value).strip())

        # 如果是日期格式，尝试标准化
        if re.match(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', cleaned):
            try:
                # 尝试解析并标准化日期格式
                from datetime import datetime
                for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y年%m月%d日']:
                    try:
                        date_obj = datetime.strptime(cleaned, fmt)
                        return date_obj.strftime('%Y-%m-%d')
                    except ValueError:
                        continue
            except:
                pass

        return cleaned

    def _fallback_parse_basic_fields(self):
        """回退方法：使用传统方式解析基本字段"""
        basic_info = {}

        try:
            main_sheet = self.workbook.active

            # 使用传统的字段搜索方法
            if self.form_type in self.FIELD_MAPPING:
                field_mapping = self.FIELD_MAPPING[self.form_type]['excel_fields']

                for field_name, possible_names in field_mapping.items():
                    value = self.find_field_value(main_sheet, field_name, possible_names)
                    if value:
                        basic_info[field_name] = value
                        current_app.logger.debug(f"🔄 回退方法提取字段 {field_name}: {value}")

        except Exception as e:
            current_app.logger.error(f"❌ 回退解析失败: {str(e)}")

        return basic_info

    def _reverse_parse_server_info(self):
        """逆向解析服务器信息"""
        servers = []

        try:
            # 查找服务器信息的range占位符
            server_range_fields = self.template_parser.range_placeholders.get('服务器信息', {})

            if server_range_fields:
                # 根据模板中的range占位符位置提取服务器表格
                servers = self._extract_range_data_from_template('服务器信息', server_range_fields)
            else:
                # 回退到传统方法
                current_app.logger.warning("⚠️ 未找到服务器信息的range占位符，使用传统方法")
                servers = self.extract_server_info()

            # 处理运维信息转换
            for server in servers:
                self._process_server_ops_info(server)

        except Exception as e:
            current_app.logger.error(f"❌ 服务器信息逆向解析失败: {str(e)}")

        return servers

    def _reverse_parse_component_info(self):
        """逆向解析组件信息"""
        components = {}

        try:
            # 查找组件相关的占位符
            component_placeholders = self.template_parser.component_placeholders

            if component_placeholders:
                # 根据组件占位符提取组件信息
                for comp_name, fields in component_placeholders.items():
                    component_data = {}

                    for field_name, placeholder_info in fields.items():
                        value = self._extract_value_from_position(
                            placeholder_info['sheet'],
                            placeholder_info['row'],
                            placeholder_info['col'],
                            field_name,
                            placeholder_info
                        )

                        if value:
                            component_data[field_name] = value

                    if component_data:
                        components[comp_name] = {
                            'name': comp_name,
                            **component_data
                        }
            else:
                # 回退到传统方法
                current_app.logger.warning("⚠️ 未找到组件占位符，使用传统方法")
                component_info = self.extract_component_info()
                # 转换格式
                if '部署应用' in component_info:
                    for app_name in component_info['部署应用']:
                        components[app_name] = {
                            'name': app_name,
                            'port': component_info.get('组件端口', {}).get(app_name, ''),
                            'version': '',
                            'count': '1'
                        }

        except Exception as e:
            current_app.logger.error(f"❌ 组件信息逆向解析失败: {str(e)}")

        return components

    def _reverse_parse_maintenance_records(self):
        """逆向解析维护记录"""
        maintenance_records = []

        try:
            # 查找维护记录的range占位符
            maintenance_range_fields = self.template_parser.range_placeholders.get('维护记录', {})

            if maintenance_range_fields:
                # 根据模板中的range占位符位置提取维护记录表格
                maintenance_records = self._extract_range_data_from_template('维护记录', maintenance_range_fields)
            else:
                # 回退到传统方法
                current_app.logger.warning("⚠️ 未找到维护记录的range占位符，使用传统方法")
                maintenance_records = self.extract_maintenance_records()

        except Exception as e:
            current_app.logger.error(f"❌ 维护记录逆向解析失败: {str(e)}")

        return maintenance_records

    def _extract_range_data_from_template(self, data_type, range_fields):
        """根据模板中的range占位符提取表格数据"""
        data_rows = []

        try:
            if not range_fields:
                return data_rows

            # 获取第一个字段的位置作为起始位置
            first_field = next(iter(range_fields.values()))
            sheet_name = first_field['sheet']
            start_row = first_field['row']

            # 找到对应的sheet
            target_sheet = None
            if sheet_name in self.workbook.sheetnames:
                target_sheet = self.workbook[sheet_name]
            else:
                target_sheet = self.workbook.active
                current_app.logger.debug(f"⚠️ Sheet '{sheet_name}' 不存在，使用主sheet")

            if not target_sheet:
                return data_rows

            # 构建字段到列的映射
            field_to_col = {}
            for field_name, field_info in range_fields.items():
                field_to_col[field_name] = field_info['col']

            # 从模板行的下一行开始提取数据
            current_row = start_row + 1

            while current_row <= target_sheet.max_row:
                row_data = {}
                has_data = False

                # 提取每个字段的值
                for field_name, col in field_to_col.items():
                    cell = target_sheet.cell(row=current_row, column=col)

                    if cell.value is not None:
                        value = self._clean_extracted_value(str(cell.value))
                        if value:
                            row_data[field_name] = value
                            has_data = True

                if has_data:
                    data_rows.append(row_data)
                    current_row += 1
                else:
                    # 遇到空行，停止提取
                    break

            current_app.logger.debug(f"📊 从模板位置提取到 {len(data_rows)} 行 {data_type} 数据")

        except Exception as e:
            current_app.logger.error(f"❌ 从模板提取 {data_type} 数据失败: {str(e)}")

        return data_rows

    def _convert_template_data(self, template_data):
        """将模板解析的数据转换为兼容格式"""
        basic_info = template_data.get('basic_info', {})
        servers = template_data.get('servers', [])

        # 提取维护记录
        maintenance_records = self._extract_maintenance_records(template_data)

        # 组装数据
        parsed_data = {
            **basic_info,
            '文档后缀': self.form_type,
            '服务器信息': servers,
            '维护记录': maintenance_records
        }

        # 处理组件信息，转换为表单期望的格式
        components = template_data.get('components', {})

        # 如果模板解析没有找到组件信息，尝试从其他地方提取
        if not components:
            components = self._extract_components_from_template_data(template_data)

        if components:
            # 设置选中的组件列表
            parsed_data['部署应用'] = list(components.keys())

            # 设置组件端口映射
            component_ports = {}
            selected_component_details = {}

            for comp_name, comp_data in components.items():
                # 提取端口信息
                port = comp_data.get('port', comp_data.get('default_port', comp_data.get('端口', '')))
                if port and port != '无':
                    component_ports[comp_name] = port

                # 构建统一的组件详细信息（整合原组件信息和selectedComponentDetails）
                selected_component_details[comp_name] = {
                    'name': comp_name,
                    'version': comp_data.get('version', comp_data.get('版本', '')),
                    'port': port,
                    'count': comp_data.get('count', comp_data.get('数量', '1')),
                    'defaultPort': port,
                    # 保留原始解析信息（如果需要调试）
                    '_original': comp_data if current_app.config.get('DEBUG', False) else None
                }

            parsed_data['组件端口'] = component_ports
            parsed_data['selectedComponentDetails'] = selected_component_details

            # 处理服务器的组件部署状态
            self._process_server_component_deployment(parsed_data, selected_component_details)
        else:
            # 如果没有找到组件信息，尝试从传统方法提取
            current_app.logger.warning("模板解析未找到组件信息，尝试传统方法提取")
            fallback_component_info = self.extract_component_info()
            if fallback_component_info:
                parsed_data.update(fallback_component_info)

        # 添加range数据
        range_data = template_data.get('range_data', {})
        if range_data:
            parsed_data.update(range_data)

        return parsed_data

    def _extract_maintenance_records(self, template_data):
        """从模板数据中提取维护记录"""
        maintenance_records = []

        # 从range_data中查找维护记录
        range_data = template_data.get('range_data', {})
        if '维护记录' in range_data:
            maintenance_records = range_data['维护记录']

        # 如果没有找到，尝试从其他可能的字段中查找
        if not maintenance_records:
            # 检查是否有维护相关的字段
            basic_info = template_data.get('basic_info', {})
            for key, value in basic_info.items():
                if '维护' in key and isinstance(value, list):
                    maintenance_records = value
                    break

        # 确保维护记录格式正确
        formatted_records = []
        for record in maintenance_records:
            if isinstance(record, dict):
                # 标准化字段名
                formatted_record = {
                    'time': record.get('time', record.get('维护时间', record.get('时间', ''))),
                    'staff': record.get('staff', record.get('维护人员', record.get('人员', ''))),
                    'type': record.get('type', record.get('维护类型', record.get('类型', ''))),
                    'content': record.get('content', record.get('维护内容', record.get('内容', ''))),
                    'onesLink': record.get('onesLink', record.get('任务链接', record.get('链接', '')))
                }
                formatted_records.append(formatted_record)
            elif isinstance(record, str):
                # 如果是字符串，尝试解析
                formatted_records.append({
                    'time': '',
                    'staff': '',
                    'type': '维护',
                    'content': record,
                    'onesLink': ''
                })

        return formatted_records

    def _extract_components_from_template_data(self, template_data):
        """从模板数据中提取组件信息"""
        components = {}

        # 1. 从range_data中查找部署架构图相关信息
        range_data = template_data.get('range_data', {})

        # 查找可能的组件表格
        component_table_names = ['部署架构', '组件部署', '组件信息', '系统架构', '架构图']
        for table_name in component_table_names:
            if table_name in range_data:
                component_list = range_data[table_name]
                if isinstance(component_list, list):
                    for comp_data in component_list:
                        if isinstance(comp_data, dict):
                            comp_name = comp_data.get('组件名称', comp_data.get('name', comp_data.get('组件', '')))
                            if comp_name:
                                components[comp_name] = {
                                    'name': comp_name,
                                    'version': comp_data.get('版本', comp_data.get('version', '')),
                                    'port': comp_data.get('端口', comp_data.get('port', '')),
                                    'count': comp_data.get('数量', comp_data.get('count', '1')),
                                    'default_port': comp_data.get('默认端口', comp_data.get('default_port', comp_data.get('端口', '')))
                                }

        # 2. 从basic_info中查找组件相关信息
        basic_info = template_data.get('basic_info', {})

        # 查找部署应用字段
        deployed_apps_field = None
        for key, value in basic_info.items():
            if '部署应用' in key or '组件' in key or '应用' in key:
                deployed_apps_field = value
                break

        if deployed_apps_field:
            # 解析部署应用列表
            if isinstance(deployed_apps_field, str):
                app_names = [app.strip() for app in deployed_apps_field.replace('\n', ',').split(',') if app.strip()]
            elif isinstance(deployed_apps_field, list):
                app_names = deployed_apps_field
            else:
                app_names = []

            # 为每个应用创建基本的组件信息
            for app_name in app_names:
                if app_name and app_name not in components:
                    components[app_name] = {
                        'name': app_name,
                        'version': '',
                        'port': '',
                        'count': '1',
                        'default_port': ''
                    }

        # 3. 尝试从服务器信息中提取组件部署信息
        servers = template_data.get('servers', [])
        for server in servers:
            if isinstance(server, dict):
                server_apps = server.get('部署应用', server.get('部署组件', ''))
                if server_apps:
                    if isinstance(server_apps, str):
                        app_names = [app.strip() for app in server_apps.replace('\n', ',').split(',') if app.strip()]
                    elif isinstance(server_apps, list):
                        app_names = server_apps
                    else:
                        app_names = []

                    for app_name in app_names:
                        if app_name and app_name not in components:
                            components[app_name] = {
                                'name': app_name,
                                'version': '',
                                'port': '',
                                'count': '1',
                                'default_port': ''
                            }

        current_app.logger.info(f"从模板数据中提取到 {len(components)} 个组件: {list(components.keys())}")
        return components

    def _process_server_component_deployment(self, parsed_data, selected_component_details):
        """处理服务器的组件部署状态"""
        servers = parsed_data.get('服务器信息', [])
        if not servers or not selected_component_details:
            return

        # 为每台服务器设置组件部署状态
        for server in servers:
            # 确保服务器有部署应用字段
            if '部署应用' not in server:
                # 默认所有组件都部署在所有服务器上
                server['部署应用'] = list(selected_component_details.keys())

            # 确保服务器有组件端口字段
            if '组件端口' not in server:
                server['组件端口'] = {}
                for comp_name, comp_details in selected_component_details.items():
                    port = comp_details.get('port', '')
                    if port and port != '无':
                        server['组件端口'][comp_name] = port

            # 确保服务器有selectedComponentDetails字段（直接引用统一的组件详情）
            if 'selectedComponentDetails' not in server:
                server['selectedComponentDetails'] = selected_component_details

    def parse_excel(self):
        """解析Excel文件的主方法（传统方法）"""
        if not self.load_workbook():
            return None

        # 检测表单类型
        if not self.form_type:
            self.form_type = self.detect_form_type()

        if not self.form_type:
            self.errors.append("无法检测表单类型")
            return None

        # 获取主sheet
        main_sheet = self.workbook.active

        # 提取基本信息
        basic_info = self.extract_basic_info(main_sheet)

        # 提取服务器信息
        server_info = self.extract_server_info()

        # 提取维护记录
        maintenance_records = self.extract_maintenance_records()

        # 提取组件信息
        component_info = self.extract_component_info()

        # 组装数据
        parsed_data = {
            **basic_info,
            '文档后缀': self.form_type,
            '服务器信息': server_info,
            '维护记录': maintenance_records,
            **component_info  # 包含部署应用、组件端口、selectedComponentDetails等
        }

        # 验证数据
        validation_errors = self.validate_data(parsed_data)
        self.errors.extend(validation_errors)

        self.parsed_data = parsed_data
        return parsed_data
    
    def create_form_submission(self):
        """创建表单提交记录"""
        if not self.parsed_data:
            return None

        # 只有严重错误才阻止创建记录，警告不影响
        if self.errors:
            return None
        
        try:
            # 创建FormSubmission记录
            submission = FormSubmission(
                company_name=self.parsed_data.get('公司名称', ''),
                form_type=self.form_type,
                record_date=datetime.strptime(
                    self.parsed_data.get('记录日期', datetime.now().strftime('%Y-%m-%d')), 
                    '%Y-%m-%d'
                ).date(),
                created_by='Excel导入',
                status='success'
            )
            
            # 设置表单数据
            submission.set_form_data(self.parsed_data)
            
            # 计算统计信息
            submission.calculate_statistics()
            
            # 保存到数据库
            db.session.add(submission)
            db.session.commit()
            
            return submission
            
        except Exception as e:
            self.errors.append(f"创建记录失败: {str(e)}")
            return None
    
    def get_import_summary(self):
        """获取导入摘要"""
        return {
            'form_type': self.form_type,
            'company_name': self.parsed_data.get('公司名称', '') if self.parsed_data else '',
            'record_date': self.parsed_data.get('记录日期', '') if self.parsed_data else '',
            'server_count': len(self.parsed_data.get('服务器信息', [])) if self.parsed_data else 0,
            'errors': self.errors,
            'warnings': self.warnings,
            'success': len(self.errors) == 0
        }
