from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, create_access_token
from datetime import datetime, timedelta
from app.auth import bp
from app.models.auth_models import User, Role, Permission, UserGroup
from app import db
from app.utils.error_handler import handle_database_error, create_success_response
import re


@bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供登录信息'
            }), 400
        
        username = data.get('username', '').strip()
        password = data.get('password', '')
        
        if not username or not password:
            return jsonify({
                'status': 'error',
                'message': '用户名和密码不能为空'
            }), 400
        
        # 查找用户（支持用户名或邮箱登录）
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()
        
        if not user or not user.check_password(password):
            return jsonify({
                'status': 'error',
                'message': '用户名或密码错误'
            }), 401
        
        if not user.is_active:
            return jsonify({
                'status': 'error',
                'message': '账户已被禁用，请联系管理员'
            }), 401
        
        # 更新登录信息
        user.last_login = datetime.utcnow()
        user.login_count += 1
        db.session.commit()
        
        # 生成token
        access_token = create_access_token(
            identity=str(user.id),
            expires_delta=timedelta(hours=24)  # 24小时过期
        )
        
        return jsonify({
            'status': 'success',
            'message': '登录成功',
            'data': {
                'access_token': access_token,
                'user': user.to_dict(include_permissions=True)
            }
        })
        
    except Exception as e:
        return handle_database_error(e, "登录")


@bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供注册信息'
            }), 400
        
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        real_name = data.get('real_name', '').strip()
        
        # 验证必填字段
        if not username or not email or not password:
            return jsonify({
                'status': 'error',
                'message': '用户名、邮箱和密码不能为空'
            }), 400
        
        # 验证用户名格式
        if not re.match(r'^[a-zA-Z0-9_]{3,20}$', username):
            return jsonify({
                'status': 'error',
                'message': '用户名只能包含字母、数字和下划线，长度3-20位'
            }), 400
        
        # 验证邮箱格式
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            return jsonify({
                'status': 'error',
                'message': '邮箱格式不正确'
            }), 400
        
        # 验证密码强度
        if len(password) < 6:
            return jsonify({
                'status': 'error',
                'message': '密码长度不能少于6位'
            }), 400
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            return jsonify({
                'status': 'error',
                'message': '用户名已存在'
            }), 400
        
        # 检查邮箱是否已存在
        if User.query.filter_by(email=email).first():
            return jsonify({
                'status': 'error',
                'message': '邮箱已被注册'
            }), 400
        
        # 创建新用户
        user = User(
            username=username,
            email=email,
            real_name=real_name,
            phone=data.get('phone', '').strip(),
            department=data.get('department', '').strip(),
            position=data.get('position', '').strip()
        )
        user.set_password(password)
        
        # 如果是第一个用户，设为管理员
        if User.query.count() == 0:
            user.is_admin = True
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '注册成功',
            'data': {
                'user': user.to_dict()
            }
        })
        
    except Exception as e:
        db.session.rollback()
        return handle_database_error(e, "注册")


@bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    try:
        # JWT token无状态，前端删除token即可
        return jsonify({
            'status': 'success',
            'message': '登出成功'
        })
        
    except Exception as e:
        current_app.logger.error(f"登出错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '登出失败'
        }), 500


@bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户信息"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(int(user_id))
        
        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404
        
        return jsonify({
            'status': 'success',
            'data': {
                'user': user.to_dict(include_permissions=True)
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户信息错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '获取用户信息失败'
        }), 500


@bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户信息"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(int(user_id))
        
        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404
        
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供更新信息'
            }), 400
        
        # 更新允许的字段
        if 'real_name' in data:
            user.real_name = data['real_name'].strip()
        if 'phone' in data:
            user.phone = data['phone'].strip()
        if 'department' in data:
            user.department = data['department'].strip()
        if 'position' in data:
            user.position = data['position'].strip()
        
        # 如果要更新邮箱，需要验证
        if 'email' in data:
            new_email = data['email'].strip()
            if new_email != user.email:
                if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', new_email):
                    return jsonify({
                        'status': 'error',
                        'message': '邮箱格式不正确'
                    }), 400
                
                if User.query.filter_by(email=new_email).first():
                    return jsonify({
                        'status': 'error',
                        'message': '邮箱已被使用'
                    }), 400
                
                user.email = new_email
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '信息更新成功',
            'data': {
                'user': user.to_dict()
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户信息错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '更新失败，请稍后重试'
        }), 500


@bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """修改密码"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(int(user_id))
        
        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404
        
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请提供密码信息'
            }), 400
        
        old_password = data.get('old_password', '')
        new_password = data.get('new_password', '')
        
        if not old_password or not new_password:
            return jsonify({
                'status': 'error',
                'message': '旧密码和新密码不能为空'
            }), 400
        
        # 验证旧密码
        if not user.check_password(old_password):
            return jsonify({
                'status': 'error',
                'message': '旧密码错误'
            }), 400
        
        # 验证新密码强度
        if len(new_password) < 6:
            return jsonify({
                'status': 'error',
                'message': '新密码长度不能少于6位'
            }), 400
        
        # 设置新密码
        user.set_password(new_password)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '密码修改成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修改密码错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '密码修改失败，请稍后重试'
        }), 500


@bp.route('/verify-token', methods=['POST'])
@jwt_required()
def verify_token():
    """验证token有效性"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(int(user_id))
        
        if not user or not user.is_active:
            return jsonify({
                'status': 'error',
                'message': 'Token无效或用户已被禁用'
            }), 401
        
        return jsonify({
            'status': 'success',
            'message': 'Token有效',
            'data': {
                'user': user.to_dict(include_permissions=True)
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"验证token错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'Token验证失败'
        }), 401


@bp.route('/check-permission', methods=['GET'])
@jwt_required()
def check_permission():
    """检查用户权限"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(int(user_id))

        if not user or not user.is_active:
            return jsonify({
                'status': 'error',
                'message': '用户不存在或已被禁用'
            }), 401

        permission_code = request.args.get('permission')
        if not permission_code:
            return jsonify({
                'status': 'error',
                'message': '请提供权限代码'
            }), 400

        has_permission = user.has_permission(permission_code)

        return jsonify({
            'status': 'success',
            'data': {
                'has_permission': has_permission,
                'permission': permission_code,
                'user_id': user.id,
                'is_admin': user.is_admin
            }
        })

    except Exception as e:
        current_app.logger.error(f"权限检查错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '权限检查失败'
        }), 500
