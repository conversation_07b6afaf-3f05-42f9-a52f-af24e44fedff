<template>
  <div class="template-selector-container">
    <div class="template-selector-header">
      <label class="form-label">
        <i class="bi bi-file-earmark-excel me-2"></i>
        选择模板文件
      </label>
      <button
        v-if="availableTemplates.length > 1"
        type="button"
        class="btn btn-sm btn-outline-info"
        @click="showTemplateInfo = !showTemplateInfo"
        :title="showTemplateInfo ? '隐藏模板信息' : '显示模板信息'"
      >
        <i class="bi" :class="showTemplateInfo ? 'bi-eye-slash' : 'bi-info-circle'"></i>
      </button>
    </div>

    <div class="template-selector-body">
      <select
        class="form-select"
        v-model="selectedTemplateId"
        @change="onTemplateChange"
        :disabled="loading || availableTemplates.length === 0"
      >
        <option value="" disabled>
          {{ loading ? '正在加载模板...' : (availableTemplates.length === 0 ? '暂无可用模板' : '请选择模板') }}
        </option>
        <option
          v-for="template in availableTemplates"
          :key="template.id"
          :value="template.id"
        >
          {{ template.alias || template.filename }}
          <span v-if="template.is_active" class="text-success">(当前活动)</span>
        </option>
      </select>

      <!-- 模板信息展示 -->
      <div v-if="showTemplateInfo && selectedTemplate" class="template-info mt-3">
        <div class="card border-info">
          <div class="card-header bg-info bg-opacity-10">
            <h6 class="mb-0">
              <i class="bi bi-info-circle me-2"></i>
              模板信息
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-2">
              <div class="col-md-6">
                <small class="text-muted">文件名:</small>
                <div class="fw-medium">{{ selectedTemplate.filename }}</div>
              </div>
              <div class="col-md-6">
                <small class="text-muted">别名:</small>
                <div class="fw-medium">{{ selectedTemplate.alias || '无' }}</div>
              </div>
              <div class="col-md-6">
                <small class="text-muted">表单类型:</small>
                <div class="fw-medium">{{ selectedTemplate.form_type }}</div>
              </div>
              <div class="col-md-6">
                <small class="text-muted">状态:</small>
                <div>
                  <span
                    class="badge"
                    :class="selectedTemplate.is_active ? 'bg-success' : 'bg-secondary'"
                  >
                    {{ selectedTemplate.is_active ? '活动模板' : '备用模板' }}
                  </span>
                </div>
              </div>
              <div class="col-12">
                <small class="text-muted">最后修改:</small>
                <div class="fw-medium">{{ formatDate(selectedTemplate.last_modified) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无模板提示 -->
      <div v-if="!loading && availableTemplates.length === 0" class="alert alert-warning mt-3">
        <i class="bi bi-exclamation-triangle me-2"></i>
        当前表单类型暂无可用模板，请联系管理员上传模板文件。
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="text-center mt-3">
        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        正在加载模板列表...
      </div>
    </div>
  </div>
</template>

<script>
import excelApi from '@/api/excel'

export default {
  name: 'TemplateSelector',
  props: {
    formType: {
      type: String,
      required: true
    },
    selectedTemplate: {
      type: Object,
      default: null
    }
  },
  emits: ['template-change'],
  data() {
    return {
      availableTemplates: [],
      selectedTemplateId: '',
      loading: false,
      showTemplateInfo: false
    }
  },
  computed: {
    selectedTemplateObject() {
      return this.availableTemplates.find(t => t.id === this.selectedTemplateId) || null
    }
  },
  watch: {
    formType: {
      handler(newFormType, oldFormType) {
        // 当表单类型发生变化时，清空当前选择的模板
        if (newFormType !== oldFormType && oldFormType !== undefined) {
          console.log(`表单类型从 ${oldFormType} 切换到 ${newFormType}，重置模板选择`)
          this.selectedTemplateId = ''
        }
        this.loadTemplates()
      },
      immediate: true
    },
    selectedTemplate: {
      handler(newTemplate) {
        if (newTemplate && newTemplate.id !== this.selectedTemplateId) {
          this.selectedTemplateId = newTemplate.id
        }
      },
      immediate: true
    }
  },
  methods: {
    async loadTemplates() {
      if (!this.formType) return

      this.loading = true
      try {
        const response = await excelApi.getTemplatesByFormType(this.formType)
        this.availableTemplates = response.data.data || []

        // 检查当前选中的模板是否属于新的表单类型
        const currentTemplateValid = this.selectedTemplateId &&
          this.availableTemplates.some(t => t.id === this.selectedTemplateId)

        // 如果当前模板无效或没有选中模板，自动选择默认模板
        if (!currentTemplateValid && this.availableTemplates.length > 0) {
          // 优先选择活动模板
          const activeTemplate = this.availableTemplates.find(t => t.is_active)
          if (activeTemplate) {
            this.selectedTemplateId = activeTemplate.id
            this.onTemplateChange()
          } else {
            // 如果没有活动模板，选择第一个
            this.selectedTemplateId = this.availableTemplates[0].id
            this.onTemplateChange()
          }
        } else if (currentTemplateValid) {
          // 如果当前模板有效，触发一次change事件确保父组件状态同步
          this.onTemplateChange()
        } else if (this.availableTemplates.length === 0) {
          // 如果没有可用模板，清空选择
          this.selectedTemplateId = ''
          this.$emit('template-change', null)
        }
      } catch (error) {
        console.error('加载模板列表失败:', error)
        this.availableTemplates = []
        this.selectedTemplateId = ''
        this.$emit('template-change', null)
      } finally {
        this.loading = false
      }
    },

    onTemplateChange() {
      const template = this.selectedTemplateObject
      console.log(`模板选择变更: ${template ? template.alias || template.filename : '无'}`)
      if (template) {
        this.$emit('template-change', template)
      } else {
        this.$emit('template-change', null)
      }
    },

    formatDate(timestamp) {
      if (!timestamp) return '未知'
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.template-selector-container {
  margin-bottom: 1rem;
}

.template-selector-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.template-selector-header .form-label {
  margin-bottom: 0;
  font-weight: 600;
  color: #495057;
}

.template-info .card {
  border-width: 1px;
}

.template-info .card-body {
  padding: 1rem;
}

.template-info .fw-medium {
  font-weight: 500;
  color: #212529;
}

.form-select:disabled {
  background-color: #e9ecef;
  opacity: 0.65;
}

@media (max-width: 768px) {
  .template-info .row .col-md-6 {
    margin-bottom: 0.5rem;
  }
}
</style>
