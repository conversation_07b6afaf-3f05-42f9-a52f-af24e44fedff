/**
 * 简单模态框工具 - 避免Bootstrap遮罩问题
 */

/**
 * 显示简单模态框（不使用Bootstrap backdrop）
 * @param {string} modalId - 模态框ID
 * @param {Object} options - 选项
 */
export function showSimpleModal(modalId, options = {}) {
  console.log(`显示简单模态框: ${modalId}`)
  
  // 先清理所有可能的遮罩
  cleanAllModals()
  
  const modalElement = document.getElementById(modalId)
  if (!modalElement) {
    console.error(`模态框元素未找到: ${modalId}`)
    return false
  }
  
  try {
    // 添加显示类
    modalElement.classList.add('show')
    modalElement.style.display = 'block'
    modalElement.setAttribute('aria-modal', 'true')
    modalElement.removeAttribute('aria-hidden')
    
    // 防止页面滚动
    document.body.classList.add('modal-open')
    document.body.style.overflow = 'hidden'
    
    // 自动聚焦到第一个输入框
    setTimeout(() => {
      const firstInput = modalElement.querySelector('input, textarea, select')
      if (firstInput) {
        firstInput.focus()
      }
    }, 100)
    
    console.log(`✅ 简单模态框 ${modalId} 已显示`)
    return true
    
  } catch (error) {
    console.error(`显示简单模态框失败:`, error)
    return false
  }
}

/**
 * 隐藏简单模态框
 * @param {string} modalId - 模态框ID
 */
export function hideSimpleModal(modalId) {
  console.log(`隐藏简单模态框: ${modalId}`)
  
  const modalElement = document.getElementById(modalId)
  if (!modalElement) {
    console.error(`模态框元素未找到: ${modalId}`)
    return false
  }
  
  try {
    // 移除显示类
    modalElement.classList.remove('show')
    modalElement.style.display = 'none'
    modalElement.setAttribute('aria-hidden', 'true')
    modalElement.removeAttribute('aria-modal')
    
    // 恢复页面滚动
    document.body.classList.remove('modal-open')
    document.body.style.overflow = ''
    
    console.log(`✅ 简单模态框 ${modalId} 已隐藏`)
    return true
    
  } catch (error) {
    console.error(`隐藏简单模态框失败:`, error)
    return false
  }
}

/**
 * 清理所有模态框状态
 */
export function cleanAllModals() {
  console.log('🧹 清理所有模态框状态')
  
  try {
    // 移除所有模态框的显示状态
    const modals = document.querySelectorAll('.modal')
    modals.forEach(modal => {
      modal.classList.remove('show', 'fade')
      modal.style.display = 'none'
      modal.setAttribute('aria-hidden', 'true')
      modal.removeAttribute('aria-modal')
    })
    
    // 移除所有Bootstrap backdrop
    const backdrops = document.querySelectorAll('.modal-backdrop')
    backdrops.forEach(backdrop => {
      backdrop.remove()
    })
    
    // 移除自定义backdrop
    const customBackdrops = document.querySelectorAll('.custom-modal-backdrop')
    customBackdrops.forEach(backdrop => {
      backdrop.remove()
    })
    
    // 清理body状态
    document.body.classList.remove('modal-open')
    document.body.style.overflow = ''
    document.body.style.paddingRight = ''
    document.body.style.marginRight = ''
    
    // 清理Bootstrap实例
    if (window.bootstrap && window.bootstrap.Modal) {
      modals.forEach(modal => {
        const instance = window.bootstrap.Modal.getInstance(modal)
        if (instance) {
          instance.dispose()
        }
      })
    }
    
    console.log('✅ 所有模态框状态已清理')
    return true
    
  } catch (error) {
    console.error('❌ 清理模态框状态失败:', error)
    return false
  }
}

/**
 * 添加ESC键关闭功能
 * @param {string} modalId - 模态框ID
 */
export function addEscapeHandler(modalId) {
  const handleEscape = (event) => {
    if (event.key === 'Escape') {
      hideSimpleModal(modalId)
      document.removeEventListener('keydown', handleEscape)
    }
  }
  
  document.addEventListener('keydown', handleEscape)
}

/**
 * 创建自定义backdrop
 * @param {string} modalId - 模态框ID
 * @param {boolean} clickToClose - 点击背景是否关闭
 */
export function createCustomBackdrop(modalId, clickToClose = false) {
  // 移除可能存在的backdrop
  const existingBackdrop = document.querySelector('.custom-modal-backdrop')
  if (existingBackdrop) {
    existingBackdrop.remove()
  }
  
  const backdrop = document.createElement('div')
  backdrop.className = 'custom-modal-backdrop show'
  
  if (clickToClose) {
    backdrop.addEventListener('click', () => {
      hideSimpleModal(modalId)
      backdrop.remove()
    })
  }
  
  document.body.appendChild(backdrop)
  return backdrop
}

// 暴露到全局（开发环境）
if (process.env.NODE_ENV === 'development') {
  window.simpleModal = {
    show: showSimpleModal,
    hide: hideSimpleModal,
    clean: cleanAllModals,
    addEscape: addEscapeHandler,
    createBackdrop: createCustomBackdrop
  }
  console.log('🔧 简单模态框工具已加载到 window.simpleModal')
}

export default {
  showSimpleModal,
  hideSimpleModal,
  cleanAllModals,
  addEscapeHandler,
  createCustomBackdrop
}
