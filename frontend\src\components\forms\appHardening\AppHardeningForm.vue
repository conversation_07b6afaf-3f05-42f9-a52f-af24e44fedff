<template>
  <base-form
    v-model="formData"
    form-type="应用加固"
    :component-groups="componentGroups"
    @save-form="handleSaveForm"
    @load-form="handleLoadForm"
    @refresh-components="$emit('refresh-components')"
    class="app-hardening-form"
  >
    <!-- 应用加固特有的访问信息提示 -->
    <template #access-info-hints>
      <div v-if="isAccessInfoAutoFilled" class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        平台访问地址已自动填充自secweb组件的IP和端口
      </div>
      <div v-if="isUpgradeInfoAutoFilled" class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        升级平台地址已自动填充自luna组件的IP和端口
      </div>
    </template>
  </base-form>
</template>

<script>
import BaseForm from '../common/BaseForm.vue'
import { DataMappingProcessor } from '@/utils/dataMapping'
import { formDataMappingMixin } from '@/mixins/formDataMapping'

/**
 * 应用加固表单组件
 * 使用BaseForm提供通用功能，保留特有的数据映射和自动填充逻辑
 */
export default {
  name: 'AppHardeningForm',
  mixins: [formDataMappingMixin],
  components: {
    BaseForm
  },
  data() {
    return {
      // 访问信息是否自动填充
      isAccessInfoAutoFilled: false,
      // 升级平台地址是否自动填充
      isUpgradeInfoAutoFilled: false,
      // 防止递归更新的标志（从混入继承，但需要在组件中声明）
      isUpdatingCompanyFields: false
    }
  },
  props: {
    modelValue: { type: Object, required: true },
    componentGroups: { type: Object, required: true }
  },
  emits: ['update:modelValue', 'save-form', 'load-form', 'refresh-components'],
  computed: {
    formData: {
      get() { return this.modelValue },
      set(value) { this.$emit('update:modelValue', value) }
    }
  },
  watch: {
    // 监听公司名称变化，同步更新客户字段
    'formData.公司名称': {
      handler(newVal, oldVal) {
        // 避免初始化时的无效同步
        if (newVal !== oldVal && !this.isUpdatingCompanyFields) {
          this.syncCompanyNameFields(newVal, '公司名称')
        }
      }
    },
    // 监听客户字段变化，同步更新公司名称
    'formData.客户': {
      handler(newVal, oldVal) {
        // 避免初始化时的无效同步
        if (newVal !== oldVal && !this.isUpdatingCompanyFields) {
          this.syncCompanyNameFields(newVal, '客户')
        }
      }
    }
  },
  mounted() {
    // 初始化数据映射处理器
    this.initDataMappingProcessor('应用加固')

    // 延迟进行初始数据映射验证，避免初始化时的递归
    this.$nextTick(() => {
      setTimeout(() => {
        this.debouncedValidateMapping()
      }, 2000) // 2秒后进行初始验证
    })
  },
  methods: {
    /**
     * 处理访问信息自动填充状态变化
     * @param {Boolean} isAutoFilled - 是否自动填充
     */
    handleAccessInfoAutoFilled(isAutoFilled) {
      this.isAccessInfoAutoFilled = isAutoFilled
    },

    /**
     * 处理升级平台地址自动填充状态变化
     * @param {Boolean} isAutoFilled - 是否自动填充
     */
    handleUpgradeInfoAutoFilled(isAutoFilled) {
      this.isUpgradeInfoAutoFilled = isAutoFilled
    },

    /**
     * 处理保存表单事件
     * 应用加固表单特有的保存逻辑
     */
    handleSaveForm() {
      this.$emit('save-form')
    },

    /**
     * 处理加载表单事件
     * 应用加固表单特有的加载逻辑
     */
    handleLoadForm() {
      this.$emit('load-form')
    }
  }
}
</script>

<style scoped>
/* 应用加固表单特有样式 */
.app-hardening-form {
  /* 基础样式由BaseForm提供 */
}

/* 访问信息提示样式 */
.app-hardening-form .alert {
  margin-bottom: 1rem;
  border-radius: 0.375rem;
}

.app-hardening-form .alert-info {
  background-color: #e3f2fd;
  border-color: #90caf9;
  color: #0d47a1;
}
</style>
