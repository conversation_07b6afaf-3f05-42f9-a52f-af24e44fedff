<template>
  <div class="file-field-component">
    <input 
      :id="id"
      type="file"
      :accept="acceptTypes"
      :multiple="isMultiple"
      :disabled="field.is_readonly"
      :required="field.is_required"
      :class="fieldClasses"
      @change="handleChange"
    >
    <div v-if="fileInfo" class="file-info mt-2">
      <small class="text-muted">
        <i class="bi bi-file-earmark me-1"></i>
        {{ fileInfo }}
      </small>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FileFieldComponent',
  props: {
    id: String,
    field: {
      type: Object,
      required: true
    },
    value: {
      type: [File, FileList, Array],
      default: null
    }
  },
  emits: ['update:value', 'field-change'],
  computed: {
    validation() {
      return this.field.validation_rules || {}
    },
    acceptTypes() {
      return this.validation.accept || ''
    },
    isMultiple() {
      return this.validation.multiple || false
    },
    fieldClasses() {
      let classes = ['form-control']
      
      if (this.field.css_classes) {
        classes.push(this.field.css_classes)
      }
      
      return classes.join(' ')
    },
    fileInfo() {
      if (!this.value) return null
      
      if (this.value instanceof FileList || Array.isArray(this.value)) {
        const count = this.value.length
        return count > 0 ? `已选择 ${count} 个文件` : null
      } else if (this.value instanceof File) {
        return `已选择: ${this.value.name}`
      }
      
      return null
    }
  },
  methods: {
    handleChange(event) {
      const files = event.target.files
      let value = null
      
      if (files && files.length > 0) {
        value = this.isMultiple ? files : files[0]
      }
      
      this.$emit('update:value', value)
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: value,
        event: 'change'
      })
    }
  }
}
</script>

<style scoped>
.file-info {
  padding: 0.25rem 0;
}
</style>
