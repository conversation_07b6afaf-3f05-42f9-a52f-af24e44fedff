from functools import wraps
from flask import jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.auth_models import User
from app.utils.cache_utils import UserCacheManager


def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        try:
            user_id = get_jwt_identity()
            user = User.query.get(int(user_id))
            
            if not user or not user.is_active:
                return jsonify({
                    'status': 'error',
                    'message': '用户未登录或已被禁用'
                }), 401
            
            return f(*args, **kwargs)
        except Exception as e:
            current_app.logger.error(f"登录验证错误: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': '认证失败'
            }), 401
    
    return decorated_function


def permission_required(permission_code):
    """权限验证装饰器"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            try:
                user_id = get_jwt_identity()
                user = User.query.get(int(user_id))
                
                if not user or not user.is_active:
                    return jsonify({
                        'status': 'error',
                        'message': '用户未登录或已被禁用'
                    }), 401
                
                if not user.has_permission(permission_code):
                    return jsonify({
                        'status': 'error',
                        'message': '权限不足，无法访问此功能'
                    }), 403
                
                return f(*args, **kwargs)
            except Exception as e:
                current_app.logger.error(f"权限验证错误: {str(e)}")
                return jsonify({
                    'status': 'error',
                    'message': '权限验证失败'
                }), 403
        
        return decorated_function
    return decorator


def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        try:
            user_id = get_jwt_identity()
            user = User.query.get(int(user_id))
            
            if not user or not user.is_active:
                return jsonify({
                    'status': 'error',
                    'message': '用户未登录或已被禁用'
                }), 401
            
            if not user.is_admin:
                return jsonify({
                    'status': 'error',
                    'message': '需要管理员权限'
                }), 403
            
            return f(*args, **kwargs)
        except Exception as e:
            current_app.logger.error(f"管理员权限验证错误: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': '权限验证失败'
            }), 403
    
    return decorated_function


def role_required(role_code):
    """角色验证装饰器"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            try:
                user_id = get_jwt_identity()
                user = User.query.get(int(user_id))
                
                if not user or not user.is_active:
                    return jsonify({
                        'status': 'error',
                        'message': '用户未登录或已被禁用'
                    }), 401
                
                if not user.has_role(role_code) and not user.is_admin:
                    return jsonify({
                        'status': 'error',
                        'message': f'需要{role_code}角色权限'
                    }), 403
                
                return f(*args, **kwargs)
            except Exception as e:
                current_app.logger.error(f"角色验证错误: {str(e)}")
                return jsonify({
                    'status': 'error',
                    'message': '角色验证失败'
                }), 403
        
        return decorated_function
    return decorator


def get_current_user():
    """获取当前登录用户（带缓存）"""
    try:
        user_id = get_jwt_identity()
        if user_id:
            user_id = int(user_id)

            # 尝试从缓存获取用户信息
            cached_info = UserCacheManager.get_user_info(user_id)
            if cached_info is not None:
                # 缓存命中，创建用户对象（简化版本，只包含基本信息）
                user = User()
                for key, value in cached_info.items():
                    if hasattr(user, key) and key not in ['roles', 'groups']:
                        setattr(user, key, value)
                return user

            # 缓存未命中，从数据库查询
            return User.query.get(user_id)
        return None
    except:
        return None


def check_permission(permission_code):
    """检查当前用户是否有指定权限"""
    user = get_current_user()
    if not user:
        return False
    return user.has_permission(permission_code)


def check_role(role_code):
    """检查当前用户是否有指定角色"""
    user = get_current_user()
    if not user:
        return False
    return user.has_role(role_code) or user.is_admin


def is_admin():
    """检查当前用户是否为管理员"""
    user = get_current_user()
    if not user:
        return False
    return user.is_admin
