"""
表单字段配置相关的数据模型
"""
from app import db
from datetime import datetime
import json


class FormFieldGroup(db.Model):
    """表单字段分组模型"""
    __tablename__ = 'form_field_groups'
    
    id = db.Column(db.Integer, primary_key=True)
    form_type = db.Column(db.String(50), nullable=False)
    group_name = db.Column(db.String(100), nullable=False)
    group_label = db.Column(db.String(100), nullable=False)
    group_description = db.Column(db.Text)
    display_order = db.Column(db.Integer, default=0)
    is_collapsible = db.Column(db.<PERSON>, default=True)
    is_expanded_by_default = db.Column(db.Bo<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    fields = db.relationship('FormFieldConfig', backref='group', lazy=True, cascade='all, delete-orphan')
    
    __table_args__ = (db.UniqueConstraint('form_type', 'group_name'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'form_type': self.form_type,
            'group_name': self.group_name,
            'group_label': self.group_label,
            'group_description': self.group_description,
            'display_order': self.display_order,
            'is_collapsible': self.is_collapsible,
            'is_expanded_by_default': self.is_expanded_by_default,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'field_count': len(self.fields) if self.fields else 0
        }


class FormFieldConfig(db.Model):
    """表单字段配置模型"""
    __tablename__ = 'form_field_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    form_type = db.Column(db.String(50), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('form_field_groups.id'), nullable=True)
    field_name = db.Column(db.String(100), nullable=False)
    field_label = db.Column(db.String(100), nullable=False)
    field_type = db.Column(db.String(50), nullable=False)
    field_description = db.Column(db.Text)
    placeholder = db.Column(db.String(200))
    default_value = db.Column(db.Text)
    is_required = db.Column(db.Boolean, default=False)
    is_readonly = db.Column(db.Boolean, default=False)
    is_auto_fill = db.Column(db.Boolean, default=False)
    display_order = db.Column(db.Integer, default=0)
    validation_rules = db.Column(db.Text)  # JSON格式
    field_options = db.Column(db.Text)     # JSON格式
    css_classes = db.Column(db.String(200))
    grid_columns = db.Column(db.Integer, default=12)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('form_type', 'field_name'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'form_type': self.form_type,
            'group_id': self.group_id,
            'field_name': self.field_name,
            'field_label': self.field_label,
            'field_type': self.field_type,
            'field_description': self.field_description,
            'placeholder': self.placeholder,
            'default_value': self.default_value,
            'is_required': self.is_required,
            'is_readonly': self.is_readonly,
            'is_auto_fill': self.is_auto_fill,
            'display_order': self.display_order,
            'validation_rules': json.loads(self.validation_rules) if self.validation_rules else {},
            'field_options': json.loads(self.field_options) if self.field_options else {},
            'css_classes': self.css_classes,
            'grid_columns': self.grid_columns,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'group_name': self.group.group_name if self.group else None,
            'group_label': self.group.group_label if self.group else None
        }
    
    def set_validation_rules(self, rules):
        """设置验证规则"""
        self.validation_rules = json.dumps(rules) if rules else None
    
    def set_field_options(self, options):
        """设置字段选项"""
        self.field_options = json.dumps(options) if options else None


class FormConfigVersion(db.Model):
    """表单配置版本模型"""
    __tablename__ = 'form_config_versions'
    
    id = db.Column(db.Integer, primary_key=True)
    form_type = db.Column(db.String(50), nullable=False)
    version_name = db.Column(db.String(100), nullable=False)
    version_description = db.Column(db.Text)
    config_data = db.Column(db.Text, nullable=False)  # JSON格式
    is_active = db.Column(db.Boolean, default=False)
    created_by = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('form_type', 'version_name'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'form_type': self.form_type,
            'version_name': self.version_name,
            'version_description': self.version_description,
            'config_data': json.loads(self.config_data) if self.config_data else {},
            'is_active': self.is_active,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def set_config_data(self, data):
        """设置配置数据"""
        self.config_data = json.dumps(data, ensure_ascii=False) if data else None


class FieldTypeDefinition(db.Model):
    """字段类型定义模型"""
    __tablename__ = 'field_type_definitions'
    
    id = db.Column(db.Integer, primary_key=True)
    type_name = db.Column(db.String(50), unique=True, nullable=False)
    type_label = db.Column(db.String(100), nullable=False)
    type_description = db.Column(db.Text)
    default_validation = db.Column(db.Text)  # JSON格式
    has_options = db.Column(db.Boolean, default=False)
    icon_class = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'type_name': self.type_name,
            'type_label': self.type_label,
            'type_description': self.type_description,
            'default_validation': json.loads(self.default_validation) if self.default_validation else {},
            'has_options': self.has_options,
            'icon_class': self.icon_class,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
