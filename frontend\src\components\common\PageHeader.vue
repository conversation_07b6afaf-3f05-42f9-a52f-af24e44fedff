<template>
  <div class="page-header">
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <div v-if="icon" class="page-icon me-3">
          <i :class="icon"></i>
        </div>
        <div>
          <h1 class="page-title mb-1">{{ title }}</h1>
          <p v-if="subtitle" class="page-subtitle mb-0">{{ subtitle }}</p>
        </div>
      </div>
      <div v-if="$slots.actions" class="page-actions">
        <slot name="actions"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PageHeader',
  props: {
    title: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped>
.page-header {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.page-title {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1.75rem;
  margin: 0;
}

.page-subtitle {
  color: #6c757d;
  font-size: 1rem;
}

.page-actions {
  display: flex;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .page-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
}
</style>
