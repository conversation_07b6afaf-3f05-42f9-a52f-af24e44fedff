<template>
  <div class="textarea-field-component">
    <textarea 
      :id="id"
      :value="value"
      :placeholder="field.placeholder"
      :readonly="field.is_readonly"
      :required="field.is_required"
      :rows="rows"
      :class="fieldClasses"
      @input="handleInput"
      @change="handleChange"
      @blur="handleBlur"
    ></textarea>
  </div>
</template>

<script>
export default {
  name: 'TextareaFieldComponent',
  props: {
    id: String,
    field: {
      type: Object,
      required: true
    },
    value: {
      type: String,
      default: ''
    }
  },
  emits: ['update:value', 'field-change'],
  computed: {
    rows() {
      // 从验证规则中获取行数，默认3行
      const validation = this.field.validation_rules || {}
      return validation.rows || 3
    },
    fieldClasses() {
      let classes = ['form-control']
      
      if (this.field.css_classes) {
        classes.push(this.field.css_classes)
      }
      
      if (this.field.is_readonly) {
        classes.push('readonly-field')
      }
      
      return classes.join(' ')
    }
  },
  methods: {
    handleInput(event) {
      this.$emit('update:value', event.target.value)
    },
    
    handleChange(event) {
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: event.target.value,
        event: 'change'
      })
    },
    
    handleBlur(event) {
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: event.target.value,
        event: 'blur'
      })
    }
  }
}
</script>

<style scoped>
.readonly-field {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.form-control {
  resize: vertical;
}
</style>
