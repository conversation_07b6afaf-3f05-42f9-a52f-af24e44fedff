# 后端
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
venv/
.venv/
backend/excel_files/*.xlsx
backend/excel_files/backups/*.xlsx
backend/excel_files/generated/*.xlsx

# 数据库文件
*.db
app.db

# 前端
node_modules/
/dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/test/unit/coverage/
/test/e2e/reports/
selenium-debug.log

# 编辑器目录和文件
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
.DS_Store

# 开发和调试工具文件（生产环境不需要）
backend/diagnose.py
backend/fix_*.py
backend/init_dev_*.py
backend/Makefile
docs/scripts/
frontend/src/views/JsonImport.vue
.promptx/resource/domain/frontend-engineer/frontend-engineer.role.md
.promptx/resource/domain/frontend-engineer/execution/code-quality.execution.md
.promptx/resource/domain/frontend-engineer/execution/frontend-development.execution.md
.promptx/resource/domain/frontend-engineer/knowledge/frontend-technologies.knowledge.md
.promptx/resource/domain/frontend-engineer/knowledge/web-development-best-practices.knowledge.md
.promptx/resource/domain/frontend-engineer/thought/frontend-thinking.thought.md
