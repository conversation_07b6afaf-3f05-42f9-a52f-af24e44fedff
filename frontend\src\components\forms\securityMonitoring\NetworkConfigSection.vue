<template>
  <collapsible-card card-class="border-info" storage-key="network-config-section">
    <template #header>
      <i class="bi bi-hdd-network me-2"></i>网络配置
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-info text-dark">外网入口: {{ sdkExternalEntry || '未填写' }}</span>
        <span class="badge bg-info text-dark">Nginx入口: {{ sdkNginxEntry || '未填写' }}</span>
      </div>
    </template>
    <div class="row mb-3">
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="sdkExternalEntry"
            v-model="sdkExternalEntry"
            placeholder="例如：https://sdk-api.example.com"
            @input="updateSdkExternalEntry"
          >
          <label for="sdkExternalEntry">SDK外网流量入口</label>
          <small class="form-text text-muted">SDK访问的外网域名，如 https://sdk-api.example.com</small>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="sdkNginxEntry"
            v-model="sdkNginxEntry"
            placeholder="例如：http://10.x.x.x:8080"
            @focus="autoFillNginxEntry"
            @input="updateSdkNginxEntry"
            :readonly="isAutoFilled"
          >
          <label for="sdkNginxEntry">SDK流量转发到Nginx入口</label>
          <small class="form-text text-muted">内部Nginx服务器地址，自动填充nginx组件所在的IP地址和端口</small>
        </div>
      </div>
    </div>
  </collapsible-card>
</template>

<script>
/**
 * 网络配置部分组件
 * 用于配置SDK流量入口和转发地址
 */
import CollapsibleCard from '../common/CollapsibleCard.vue'

export default {
  name: 'NetworkConfigSection',
  components: {
    CollapsibleCard
  },
  props: {
    // SDK外网流量入口
    sdkExternal: {
      type: String,
      default: ''
    },
    // SDK流量转发到Nginx入口
    sdkNginx: {
      type: String,
      default: ''
    },
    // 服务器信息列表，用于自动填充地址
    serverList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      sdkExternalEntry: this.sdkExternal,
      sdkNginxEntry: this.sdkNginx,
      isAutoFilled: false
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    sdkExternal(newVal) {
      this.sdkExternalEntry = newVal
    },
    sdkNginx(newVal) {
      this.sdkNginxEntry = newVal
    },
    // 监听服务器列表变化，自动更新nginx入口
    serverList: {
      handler() {
        this.autoFillNginxEntry(true)
      },
      deep: true
    }
  },
  mounted() {
    // 组件挂载时自动填充
    this.autoFillNginxEntry(true)
  },
  methods: {
    /**
     * 更新SDK外网流量入口
     * 向父组件发送更新事件
     */
    updateSdkExternalEntry() {
      this.$emit('update:sdkExternal', this.sdkExternalEntry)
    },

    /**
     * 更新SDK流量转发到Nginx入口
     * 向父组件发送更新事件
     */
    updateSdkNginxEntry() {
      this.$emit('update:sdkNginx', this.sdkNginxEntry)
    },

    /**
     * 自动填充nginx组件所在的IP地址和端口
     * 用于SDK流量转发到Nginx入口
     * @param {Boolean} force - 是否强制更新，即使已有值
     */
    autoFillNginxEntry(force = false) {
      // 如果已经有值且不是强制更新，不自动填充
      if (this.sdkNginxEntry && !force) return

      // 查找包含nginx组件的服务器
      const nginxServers = this.findServersWithComponent('nginx')
      if (nginxServers.length > 0) {
        // 如果有多个nginx服务器，使用逗号分隔它们的地址和端口
        const nginxEntries = nginxServers.map(server => {
          const ip = server.IP地址
          const port = server.组件端口['nginx'] || this.getComponentDefaultPort('nginx')
          return `http://${ip}:${port}`
        })

        // 使用逗号分隔多个nginx入口
        this.sdkNginxEntry = nginxEntries.join(',')
        this.isAutoFilled = true // 标记为已自动填充，使输入框变为只读
        this.updateSdkNginxEntry()
      } else {
        // 如果没有找到nginx组件，清空字段
        if (force && this.sdkNginxEntry) {
          this.sdkNginxEntry = ''
          this.isAutoFilled = false // 取消只读状态
          this.updateSdkNginxEntry()
        }
      }
    },

    /**
     * 查找包含指定组件的所有服务器
     * @param {String} componentName - 组件名称
     * @returns {Array} - 包含该组件的服务器对象数组
     */
    findServersWithComponent(componentName) {
      if (!this.serverList || this.serverList.length === 0) {
        return []
      }

      // 查找包含指定组件的所有服务器
      return this.serverList.filter(server =>
        server.部署应用 &&
        server.部署应用.includes(componentName) &&
        server.IP地址
      )
    },

    /**
     * 获取组件的默认端口
     * @param {String} componentName - 组件名称
     * @returns {String} - 组件的默认端口
     */
    getComponentDefaultPort(componentName) {
      // 默认端口映射
      const defaultPorts = {
        'nginx': '6279'
      }
      return defaultPorts[componentName] || '无'
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.text-danger {
  font-weight: bold;
}
</style>
