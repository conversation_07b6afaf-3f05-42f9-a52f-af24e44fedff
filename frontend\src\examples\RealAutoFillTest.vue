<template>
  <div class="real-auto-fill-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-server me-2"></i>
                真实场景自动填充测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 表单类型选择 -->
              <div class="row mb-4">
                <div class="col-md-6">
                  <label class="form-label fw-bold">表单类型</label>
                  <select v-model="selectedFormType" class="form-select" @change="loadFormType">
                    <option value="">请选择表单类型</option>
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">操作</label>
                  <div class="d-flex gap-2">
                    <button
                      class="btn btn-success btn-sm"
                      @click="addServerWithComponents"
                      :disabled="!selectedFormType"
                    >
                      添加服务器并勾选组件
                    </button>
                    <button
                      class="btn btn-primary btn-sm"
                      @click="triggerAutoFill"
                      :disabled="!selectedFormType || testFormData.服务器信息.length === 0"
                    >
                      手动触发自动填充
                    </button>
                    <button class="btn btn-warning btn-sm" @click="clearAll">
                      清空重置
                    </button>
                  </div>
                </div>
              </div>

              <!-- 使用完整的BaseForm测试 -->
              <div v-if="selectedFormType" class="row">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">{{ selectedFormType }} - 完整表单测试</h6>
                    </div>
                    <div class="card-body">
                      <base-form
                        v-model="testFormData"
                        :form-type="selectedFormType"
                        :component-groups="componentGroups"
                        :key="`base-form-${selectedFormType}-${refreshKey}`"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 调试信息 -->
              <div v-if="selectedFormType" class="row mt-4">
                <div class="col-12">
                  <div class="card bg-light">
                    <div class="card-header">
                      <h6 class="mb-0">调试信息</h6>
                    </div>
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-4">
                          <strong>服务器信息:</strong>
                          <pre class="small">{{ JSON.stringify(testFormData.服务器信息, null, 2) }}</pre>
                        </div>
                        <div class="col-md-4">
                          <strong>访问信息字段:</strong>
                          <div class="small">
                            <div v-for="(value, key) in accessInfoFields" :key="key" class="mb-1">
                              <strong>{{ key }}:</strong> {{ value || '(空)' }}
                            </div>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <strong>预期自动填充:</strong>
                          <div class="small">
                            <div v-if="selectedFormType === '安全测评'">
                              <div>front-ssp-admin → 管理员页面IP</div>
                              <div>front-ssp-user → 用户页面IP</div>
                              <div>luna → 升级页面IP</div>
                              <div>backend-ssp-user → 对外服务端口</div>
                            </div>
                            <div v-else-if="selectedFormType === '安全监测'">
                              <div>web-service-nginx → 业务功能页面地址</div>
                              <div>init → init地址</div>
                              <div>kibana → kibana地址</div>
                            </div>
                            <div v-else-if="selectedFormType === '应用加固'">
                              <div>secweb → 平台访问地址</div>
                              <div>luna → 升级平台地址</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseForm from '@/components/forms/common/BaseForm.vue'
import { getInitialFormData, createNewServerItem } from '@/config/formDataConfig'

export default {
  name: 'RealAutoFillTest',
  components: {
    BaseForm
  },
  data() {
    return {
      selectedFormType: '',
      testFormData: {},
      componentGroups: {},
      refreshKey: 0
    }
  },
  computed: {
    /**
     * 访问信息字段（从表单数据中提取）
     */
    accessInfoFields() {
      const fields = {}
      
      if (this.selectedFormType === '安全测评') {
        fields['管理员页面IP'] = this.testFormData.管理员页面IP
        fields['用户页面IP'] = this.testFormData.用户页面IP
        fields['升级页面IP'] = this.testFormData.升级页面IP
        fields['对外服务端口'] = this.testFormData.对外服务端口
      } else if (this.selectedFormType === '安全监测') {
        fields['业务功能页面地址'] = this.testFormData.业务功能页面地址
        fields['init地址'] = this.testFormData.init地址
        fields['kibana地址'] = this.testFormData.kibana地址
      } else if (this.selectedFormType === '应用加固') {
        fields['平台访问地址'] = this.testFormData.平台访问地址
        fields['升级平台地址'] = this.testFormData.升级平台地址
      }
      
      return fields
    }
  },
  methods: {
    /**
     * 加载表单类型
     */
    loadFormType() {
      if (!this.selectedFormType) return

      console.log(`加载表单类型: ${this.selectedFormType}`)

      // 获取初始表单数据
      this.testFormData = getInitialFormData(this.selectedFormType)

      // 加载组件分组数据
      this.loadComponentGroups()

      // 强制刷新组件
      this.refreshKey += 1

      console.log('表单类型加载完成:', {
        formType: this.selectedFormType,
        testFormData: this.testFormData
      })
    },

    /**
     * 加载组件分组数据
     */
    async loadComponentGroups() {
      try {
        // 尝试从实际的数据源加载组件分组
        const { getComponentGroupsFromDatabase } = await import('@/config/formDataConfig')
        this.componentGroups = await getComponentGroupsFromDatabase()
        console.log('从数据库加载的组件分组:', this.componentGroups)
      } catch (error) {
        console.warn('从数据库加载组件分组失败，使用模拟数据:', error)
        
        // 使用模拟数据作为后备
        this.componentGroups = {
          testing: {
            'aimrsk-engine': [
              { name: 'luna', description: 'Luna升级组件', port: '9001' },
              { name: 'front-ssp-admin', description: '前端管理界面', port: '8080' },
              { name: 'front-ssp-user', description: '前端用户界面', port: '8081' },
              { name: 'backend-ssp-user', description: '后端用户服务', port: '8082' }
            ]
          },
          security: {
            'security-monitor': [
              { name: 'web-service-nginx', description: 'Web服务', port: '443' },
              { name: 'init', description: '初始化服务', port: '8181' },
              { name: 'kibana', description: 'Kibana服务', port: '5601' }
            ]
          },
          hardening: {
            'hardening-platform': [
              { name: 'secweb', description: '安全Web平台', port: '8000' },
              { name: 'luna', description: 'Luna升级组件', port: '9001' }
            ]
          }
        }
      }
    },

    /**
     * 添加服务器并勾选组件
     */
    addServerWithComponents() {
      if (!this.selectedFormType) return

      const server = createNewServerItem()
      server.IP地址 = `192.168.1.${100 + this.testFormData.服务器信息.length}`

      // 根据表单类型添加对应的组件
      if (this.selectedFormType === '安全测评') {
        server.部署应用 = ['front-ssp-admin', 'front-ssp-user', 'luna', 'backend-ssp-user']
        server.组件端口 = {
          'front-ssp-admin': '8080',
          'front-ssp-user': '8081',
          'luna': '9001',
          'backend-ssp-user': '8082'
        }
      } else if (this.selectedFormType === '安全监测') {
        server.部署应用 = ['web-service-nginx', 'init', 'kibana']
        server.组件端口 = {
          'web-service-nginx': '443',
          'init': '8181',
          'kibana': '5601'
        }
      } else if (this.selectedFormType === '应用加固') {
        server.部署应用 = ['secweb', 'luna']
        server.组件端口 = {
          'secweb': '8000',
          'luna': '9001'
        }
      }

      this.testFormData.服务器信息.push(server)
      
      console.log('添加服务器并勾选组件:', server)
      console.log('当前服务器信息:', this.testFormData.服务器信息)
      
      // 手动触发一次数据更新
      this.$nextTick(() => {
        console.log('下一个tick后的访问信息字段:', this.accessInfoFields)
      })
    },

    /**
     * 手动触发自动填充
     */
    triggerAutoFill() {
      console.log('手动触发自动填充')
      // 通过ref调用BaseForm中的DynamicAccessInfoSection的forceAutoFill方法
      // 这里我们先通过事件的方式触发
      this.refreshKey += 1

      this.$nextTick(() => {
        console.log('刷新后的服务器信息:', this.testFormData.服务器信息)
      })
    },

    /**
     * 清空所有数据
     */
    clearAll() {
      if (this.selectedFormType) {
        this.testFormData = getInitialFormData(this.selectedFormType)
      }
      this.refreshKey += 1
      console.log('清空所有数据')
    }
  },
  watch: {
    // 监听服务器信息变化
    'testFormData.服务器信息': {
      handler(newServerList) {
        console.log('服务器信息变化:', newServerList)
      },
      deep: true
    },

    // 监听访问信息字段变化
    accessInfoFields: {
      handler(newFields) {
        console.log('访问信息字段变化:', newFields)
      },
      deep: true
    }
  },
  mounted() {
    console.log('RealAutoFillTest 组件已挂载')
  }
}
</script>

<style scoped>
.real-auto-fill-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
}

pre {
  max-height: 200px;
  overflow-y: auto;
  font-size: 0.75rem;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}
</style>
