/**
 * 数据映射功能测试
 * 验证表单数据映射和同步功能的正确性
 */

import { DataMappingProcessor, validateFormDataMapping, processFormDataMapping } from '@/utils/dataMapping'

describe('数据映射功能测试', () => {
  let processor

  beforeEach(() => {
    processor = new DataMappingProcessor('应用加固')
  })

  describe('DataMappingProcessor', () => {
    test('应该正确初始化处理器', () => {
      expect(processor.formType).toBe('应用加固')
      expect(processor.mappingConfig).toBeDefined()
    })

    test('应该正确处理公司名称映射', () => {
      const testData = {
        公司名称: '测试公司A',
        客户: '测试公司B',
        记录日期: '2024-01-01',
        编辑人: 'admin'
      }

      const result = processor.processDataMapping(testData)

      // 公司名称应该同步
      expect(result.公司名称).toBe(result.客户)
      expect(result.公司名称).toBe('测试公司A') // 应该使用第一个有值的字段
    })

    test('应该正确处理升级用户配置默认值', () => {
      const testData = {
        公司名称: '测试公司',
        记录日期: '2024-01-01',
        编辑人: 'admin'
      }

      const result = processor.processDataMapping(testData)

      expect(result.升级用户配置).toBeDefined()
      expect(result.升级用户配置.username).toBe('upgrader')
      expect(result.升级用户配置.password).toBe('upgrader@abc#2020')
    })

    test('应该正确处理管理员信息默认值', () => {
      const testData = {
        公司名称: '测试公司',
        记录日期: '2024-01-01',
        编辑人: 'admin'
      }

      const result = processor.processDataMapping(testData)

      expect(result.管理员信息).toBeDefined()
      expect(result.管理员信息).toContain('admin')
      expect(result.管理员信息).toContain('sadmin')
    })

    test('应该正确处理日期格式标准化', () => {
      const testData = {
        公司名称: '测试公司',
        记录日期: '2024/1/1',
        编辑人: 'admin'
      }

      const result = processor.processDataMapping(testData)

      expect(result.记录日期).toBe('2024-01-01')
    })
  })

  describe('安全监测表单数据映射', () => {
    beforeEach(() => {
      processor = new DataMappingProcessor('安全监测')
    })

    test('应该正确处理标准或定制字段默认值', () => {
      const testData = {
        公司名称: '测试公司',
        前端版本: '1.0.0',
        后端版本: '1.0.0',
        记录日期: '2024-01-01',
        编辑人: 'admin'
      }

      const result = processor.processDataMapping(testData)

      expect(result.标准或定制).toBe('标准版')
    })

    test('应该正确处理版本信息映射', () => {
      const testData = {
        公司名称: '测试公司',
        前端版本: '1.0.0',
        后端版本: '2.0.0',
        记录日期: '2024-01-01',
        编辑人: 'admin'
      }

      const result = processor.processDataMapping(testData)

      // 版本字段应该保持原值
      expect(result.前端版本).toBe('1.0.0')
      expect(result.后端版本).toBe('2.0.0')
    })
  })

  describe('安全测评表单数据映射', () => {
    beforeEach(() => {
      processor = new DataMappingProcessor('安全测评')
    })

    test('应该正确处理升级用户信息', () => {
      const testData = {
        公司名称: '测试公司',
        部署包版本: '1.0.0',
        记录日期: '2024-01-01',
        编辑人: 'admin'
      }

      const result = processor.processDataMapping(testData)

      expect(result.升级用户账号).toBe('upgrader')
      expect(result.升级用户密码).toBe('upgrader@abc#2020')
    })

    test('应该正确处理版本信息映射', () => {
      const testData = {
        公司名称: '测试公司',
        版本信息: '1.0.0',
        记录日期: '2024-01-01',
        编辑人: 'admin'
      }

      const result = processor.processDataMapping(testData)

      expect(result.部署包版本).toBe('1.0.0')
    })
  })

  describe('数据映射验证', () => {
    test('应该正确验证必填字段', () => {
      const testData = {
        公司名称: '测试公司',
        记录日期: '2024-01-01'
        // 缺少客户字段（应用加固表单必填）
      }

      const result = validateFormDataMapping(testData, '应用加固')

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('缺少必填字段: 客户')
    })

    test('应该正确验证公司名称一致性', () => {
      const testData = {
        公司名称: '测试公司A',
        客户: '测试公司B',
        记录日期: '2024-01-01'
      }

      const result = validateFormDataMapping(testData, '应用加固')

      expect(result.warnings).toContain('公司名称字段不一致: 测试公司A, 测试公司B')
    })

    test('应该通过完整数据的验证', () => {
      const testData = {
        公司名称: '测试公司',
        客户: '测试公司',
        记录日期: '2024-01-01'
      }

      const result = validateFormDataMapping(testData, '应用加固')

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('便捷函数测试', () => {
    test('processFormDataMapping应该正确工作', () => {
      const testData = {
        公司名称: '测试公司',
        记录日期: '2024-01-01',
        编辑人: 'admin'
      }

      const result = processFormDataMapping(testData, '应用加固')

      expect(result.公司名称).toBe('测试公司')
      expect(result.客户).toBe('测试公司')
      expect(result.升级用户配置).toBeDefined()
    })
  })

  describe('边界情况测试', () => {
    test('应该处理空数据', () => {
      const result = processor.processDataMapping({})

      expect(result).toBeDefined()
      expect(result.公司名称).toBe('客户公司名称')
    })

    test('应该处理null数据', () => {
      const result = processor.processDataMapping(null)

      expect(result).toBeNull()
    })

    test('应该处理undefined数据', () => {
      const result = processor.processDataMapping(undefined)

      expect(result).toBeUndefined()
    })

    test('应该处理包含特殊字符的公司名称', () => {
      const testData = {
        公司名称: '测试公司（北京）有限责任公司',
        记录日期: '2024-01-01',
        编辑人: 'admin'
      }

      const result = processor.processDataMapping(testData)

      expect(result.公司名称).toBe('测试公司（北京）有限责任公司')
      expect(result.客户).toBe('测试公司（北京）有限责任公司')
    })
  })

  describe('性能测试', () => {
    test('应该在合理时间内处理大量数据', () => {
      const testData = {
        公司名称: '测试公司',
        记录日期: '2024-01-01',
        编辑人: 'admin',
        服务器信息: Array(100).fill({
          IP地址: '***********',
          用途类型: '应用服务器',
          系统发行版: 'CentOS 7',
          内存: '16GB',
          CPU: '8核',
          磁盘: '500GB',
          SSH端口: '22',
          Root密码: 'password123',
          运维用户: [],
          部署应用: ['nginx', 'mysql'],
          组件端口: { nginx: '80', mysql: '3306' }
        })
      }

      const startTime = performance.now()
      const result = processor.processDataMapping(testData)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(100) // 应该在100ms内完成
      expect(result.服务器信息).toHaveLength(100)
    })
  })
})
