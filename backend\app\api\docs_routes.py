"""
API文档路由
提供API接口文档和在线调试功能
"""

from flask import Blueprint, jsonify, request, current_app
from app.auth.decorators import login_required, permission_required
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
from datetime import datetime
import json
import os

docs_bp = Blueprint('docs', __name__)

# 权限映射表
PERMISSION_MAP = {
    # 认证相关
    "/auth/login": None,
    "/auth/profile": None,

    # 表单管理
    "/excel/submit": "form.create",
    "/excel/form_submissions": "form.view",
    "/excel/form_submissions/{id}": "form.view",
    "/excel/form_submissions/{id}/edit": "form.edit",
    "/excel/form_submissions/{id}/edit_history": "form.view",
    "/excel/form_submissions/{id}": "form.delete",  # DELETE方法

    # 组件管理
    "/api/components/list": "component.view",
    "/api/components/categories": "component.view",

    # 用户权限
    "/rbac/users": "user.view",
    "/rbac/roles": "role.view",
    "/rbac/permissions": "permission.view",

    # 模板管理
    "/excel/templates": "template.view",
    "/excel/templates/upload": "template.upload",

    # 表单类型管理
    "/excel/form-types": "template.create",  # POST
    "/excel/form-types/{id}": "template.edit",  # PUT
    "/excel/form-types/clear-cache": "system.config",
    "/excel/form-types/one-click-init": "template.create",

    # 表单字段配置
    "/excel/form-field-configs": "system.config",  # POST
    "/excel/form-field-configs/{id}": "system.config",  # PUT/DELETE
    "/excel/form-field-configs/init-basic-fields/{form_type}": "system.config",

    # 性能监控
    "/api/performance/health-check": "system.view",
    "/api/performance/cache-stats": "system.view",
    "/api/performance/slow-queries": "system.view",

    # 表单快照
    "/api/form-snapshot/save": "form.create",
    "/api/form-snapshot/list": "form.view",
    "/api/form-snapshot/load": "form.view",

    # 文件下载
    "/excel/download/{file_id}": "form.download",

    # 缓存管理
    "/cache/status": "system.view",
    "/cache/clear": "system.config",
}

def get_permission_for_endpoint(path, method):
    """根据路径和方法获取所需权限"""
    # 特殊处理DELETE方法
    if method == "DELETE":
        if "form_submissions" in path:
            return "form.delete"
        elif "templates" in path:
            return "template.delete"

    return PERMISSION_MAP.get(path, "system.view")

# API文档数据
API_DOCS = {
    "info": {
        "title": "运维文档生成系统 API",
        "version": "1.0.0",
        "description": "提供表单管理、Excel生成、用户权限等功能的RESTful API接口"
    },
    "servers": [
        {
            "url": "http://127.0.0.1:5000",
            "description": "开发环境"
        }
    ],
    "categories": [
        {
            "name": "认证授权",
            "description": "用户登录、注册、权限验证相关接口",
            "endpoints": [
                {
                    "path": "/auth/login",
                    "method": "POST",
                    "summary": "用户登录",
                    "description": "用户通过用户名和密码进行登录认证",
                    "tags": ["auth"],
                    "permission": None,  # 登录接口无需权限
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "username": {"type": "string", "description": "用户名"},
                                        "password": {"type": "string", "description": "密码"}
                                    },
                                    "required": ["username", "password"]
                                },
                                "example": {
                                    "username": "admin",
                                    "password": "admin123"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "登录成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "登录成功",
                                        "data": {
                                            "access_token": "eyJhbGciOiJIUzI1NiIs...",
                                            "user": {
                                                "id": 1,
                                                "username": "admin",
                                                "real_name": "管理员"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "401": {
                            "description": "认证失败",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "error",
                                        "message": "用户名或密码错误"
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/auth/profile",
                    "method": "GET",
                    "summary": "获取用户信息",
                    "description": "获取当前登录用户的详细信息",
                    "tags": ["auth"],
                    "security": [{"bearerAuth": []}],
                    "permission": None,  # 获取自己的信息无需特殊权限
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "id": 1,
                                            "username": "admin",
                                            "real_name": "管理员",
                                            "email": "<EMAIL>",
                                            "is_admin": True,
                                            "permissions": ["user.view", "system.config"]
                                        }
                                    }
                                }
                            }
                        },
                        "401": {
                            "description": "未授权",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "error",
                                        "message": "Token已过期或无效"
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "表单管理",
            "description": "表单创建、提交、查看等功能接口",
            "endpoints": [
                {
                    "path": "/excel/submit",
                    "method": "POST",
                    "summary": "提交表单数据",
                    "description": "提交表单数据并生成Excel文件",
                    "tags": ["form"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.create",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "form_type": {"type": "string", "description": "表单类型"},
                                        "form_data": {"type": "object", "description": "表单数据"},
                                        "alias": {"type": "string", "description": "文件别名"}
                                    },
                                    "required": ["form_type", "form_data"]
                                },
                                "example": {
                                    "form_type": "应用加固",
                                    "alias": "测试项目",
                                    "form_data": {
                                        "basic_info": {
                                            "customer_company_name": "测试公司",
                                            "project_name": "测试项目"
                                        },
                                        "server_info": {
                                            "servers": [
                                                {
                                                    "ip": "*************",
                                                    "hostname": "web-server-01"
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "提交成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "表单提交成功",
                                        "data": {
                                            "file_id": "12345",
                                            "filename": "测试公司-运维文档-应用加固_20250106.xlsx",
                                            "download_url": "/excel/download/12345",
                                            "file_size": "2.5MB",
                                            "created_at": "2025-01-06T12:00:00Z"
                                        }
                                    }
                                }
                            }
                        },
                        "400": {
                            "description": "请求参数错误",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "error",
                                        "message": "表单数据验证失败",
                                        "errors": {
                                            "customer_company_name": "公司名称不能为空",
                                            "project_name": "项目名称不能为空"
                                        }
                                    }
                                }
                            }
                        },
                        "409": {
                            "description": "重复提交",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "error",
                                        "message": "检测到重复提交",
                                        "data": {
                                            "existing_file": "测试公司-运维文档-应用加固_20250105.xlsx",
                                            "suggestion": "请修改别名或确认覆盖现有记录"
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/excel/form_submissions",
                    "method": "GET",
                    "summary": "获取表单提交记录列表",
                    "description": "分页获取表单提交历史记录，支持筛选",
                    "tags": ["form"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.view",
                    "parameters": [
                        {
                            "name": "page",
                            "in": "query",
                            "description": "页码",
                            "required": False,
                            "schema": {"type": "integer", "default": 1}
                        },
                        {
                            "name": "per_page",
                            "in": "query",
                            "description": "每页数量",
                            "required": False,
                            "schema": {"type": "integer", "enum": [10, 20, 50, 100], "default": 20}
                        },
                        {
                            "name": "company_name",
                            "in": "query",
                            "description": "公司名称筛选",
                            "required": False,
                            "schema": {"type": "string"}
                        },
                        {
                            "name": "form_type",
                            "in": "query",
                            "description": "表单类型筛选",
                            "required": False,
                            "schema": {"type": "string"}
                        },
                        {
                            "name": "start_date",
                            "in": "query",
                            "description": "开始日期",
                            "required": False,
                            "schema": {"type": "string", "format": "date"}
                        },
                        {
                            "name": "end_date",
                            "in": "query",
                            "description": "结束日期",
                            "required": False,
                            "schema": {"type": "string", "format": "date"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "submissions": [
                                                {
                                                    "id": 3,
                                                    "company_name": "测试公司",
                                                    "form_type": "应用加固",
                                                    "record_date": "2025-01-06",
                                                    "excel_filename": "测试公司-运维文档-应用加固_20250106.xlsx",
                                                    "server_count": 2,
                                                    "component_count": 5,
                                                    "created_at": "2025-01-06T12:00:00",
                                                    "status": "success"
                                                }
                                            ],
                                            "pagination": {
                                                "page": 1,
                                                "per_page": 20,
                                                "total": 1,
                                                "pages": 1
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/excel/form_submissions/{id}",
                    "method": "GET",
                    "summary": "获取表单提交记录详情",
                    "description": "根据ID获取单个表单提交记录的详细信息",
                    "tags": ["form"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.view",
                    "parameters": [
                        {
                            "name": "id",
                            "in": "path",
                            "description": "表单提交记录ID",
                            "required": True,
                            "schema": {"type": "integer"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "id": 3,
                                            "company_name": "测试公司",
                                            "form_type": "应用加固",
                                            "record_date": "2025-01-06",
                                            "form_data": {
                                                "basic_info": {
                                                    "customer_company_name": "测试公司"
                                                },
                                                "server_info": {
                                                    "servers": []
                                                }
                                            },
                                            "excel_filename": "测试公司-运维文档-应用加固_20250106.xlsx",
                                            "server_count": 2,
                                            "component_count": 5,
                                            "created_at": "2025-01-06T12:00:00",
                                            "updated_at": "2025-01-06T12:00:00",
                                            "status": "success"
                                        }
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "记录不存在",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "error",
                                        "message": "表单提交记录不存在"
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/excel/form_submissions/{id}",
                    "method": "DELETE",
                    "summary": "删除表单提交记录",
                    "description": "删除指定的表单提交记录和关联的Excel文件",
                    "tags": ["form"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.delete",
                    "parameters": [
                        {
                            "name": "id",
                            "in": "path",
                            "description": "表单提交记录ID",
                            "required": True,
                            "schema": {"type": "integer"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "删除成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "表单提交记录删除成功"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "记录不存在",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "error",
                                        "message": "表单提交记录不存在"
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/excel/form_submissions/{id}/edit",
                    "method": "PUT",
                    "summary": "编辑表单提交记录",
                    "description": "编辑已提交的表单记录，会记录编辑历史",
                    "tags": ["form"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.edit",
                    "parameters": [
                        {
                            "name": "id",
                            "in": "path",
                            "description": "表单提交记录ID",
                            "required": True,
                            "schema": {"type": "integer"}
                        }
                    ],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "form_data": {"type": "object", "description": "更新后的表单数据"},
                                        "edit_reason": {"type": "string", "description": "编辑原因"},
                                        "edited_by": {"type": "string", "description": "编辑者"},
                                        "company_name": {"type": "string", "description": "公司名称"},
                                        "form_type": {"type": "string", "description": "表单类型"},
                                        "record_date": {"type": "string", "format": "date", "description": "记录日期"}
                                    },
                                    "required": ["form_data"]
                                },
                                "example": {
                                    "form_data": {
                                        "basic_info": {
                                            "customer_company_name": "更新后的公司名称"
                                        }
                                    },
                                    "edit_reason": "更新公司信息",
                                    "edited_by": "admin",
                                    "company_name": "更新后的公司名称",
                                    "form_type": "应用加固",
                                    "record_date": "2025-01-06"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "编辑成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "表单编辑成功",
                                        "data": {
                                            "submission_id": 3,
                                            "edit_id": 1,
                                            "changes_count": 2
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/excel/form_submissions/{id}/edit_history",
                    "method": "GET",
                    "summary": "获取表单编辑历史",
                    "description": "获取指定表单提交记录的编辑历史",
                    "tags": ["form"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.view",
                    "parameters": [
                        {
                            "name": "id",
                            "in": "path",
                            "description": "表单提交记录ID",
                            "required": True,
                            "schema": {"type": "integer"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "submission": {
                                                "id": 3,
                                                "company_name": "测试公司",
                                                "form_type": "应用加固"
                                            },
                                            "edit_history": [
                                                {
                                                    "id": 1,
                                                    "edit_reason": "更新公司信息",
                                                    "edited_by": "admin",
                                                    "created_at": "2025-01-06T12:30:00",
                                                    "changes_detail": {
                                                        "company_name": {
                                                            "old": "旧公司名称",
                                                            "new": "新公司名称"
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "组件管理",
            "description": "系统组件配置和管理接口",
            "endpoints": [
                {
                    "path": "/api/components/list",
                    "method": "GET",
                    "summary": "获取组件列表",
                    "description": "根据表单类型获取可用组件列表，支持按分类筛选",
                    "tags": ["component"],
                    "security": [{"bearerAuth": []}],
                    "permission": "component.view",
                    "parameters": [
                        {
                            "name": "form_type",
                            "in": "query",
                            "description": "表单类型，如：应用加固、安全监控",
                            "required": False,
                            "schema": {
                                "type": "string",
                                "enum": ["应用加固", "安全监控", "安全测试"]
                            }
                        },
                        {
                            "name": "category",
                            "in": "query",
                            "description": "组件分类，如：Web服务、数据库、中间件",
                            "required": False,
                            "schema": {"type": "string"}
                        },
                        {
                            "name": "enabled_only",
                            "in": "query",
                            "description": "是否只返回启用的组件",
                            "required": False,
                            "schema": {"type": "boolean", "default": True}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "Web服务": [
                                                {
                                                    "id": 1,
                                                    "name": "Nginx",
                                                    "default_port": 80,
                                                    "description": "高性能Web服务器",
                                                    "icon": "bi-server",
                                                    "enabled": True,
                                                    "category": "Web服务"
                                                },
                                                {
                                                    "id": 2,
                                                    "name": "Apache",
                                                    "default_port": 80,
                                                    "description": "Apache HTTP服务器",
                                                    "icon": "bi-server",
                                                    "enabled": True,
                                                    "category": "Web服务"
                                                }
                                            ],
                                            "数据库": [
                                                {
                                                    "id": 3,
                                                    "name": "MySQL",
                                                    "default_port": 3306,
                                                    "description": "关系型数据库",
                                                    "icon": "bi-database",
                                                    "enabled": True,
                                                    "category": "数据库"
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        },
                        "403": {
                            "description": "权限不足",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "error",
                                        "message": "没有查看组件的权限"
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "用户权限",
            "description": "用户管理和权限控制相关接口",
            "endpoints": [
                {
                    "path": "/rbac/users",
                    "method": "GET",
                    "summary": "获取用户列表",
                    "description": "获取系统中所有用户的列表信息",
                    "tags": ["rbac"],
                    "security": [{"bearerAuth": []}],
                    "permission": "user.view",
                    "parameters": [
                        {
                            "name": "page",
                            "in": "query",
                            "description": "页码",
                            "required": False,
                            "schema": {"type": "integer", "default": 1}
                        },
                        {
                            "name": "per_page",
                            "in": "query",
                            "description": "每页数量",
                            "required": False,
                            "schema": {"type": "integer", "default": 10}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "users": [
                                                {
                                                    "id": 1,
                                                    "username": "admin",
                                                    "real_name": "管理员",
                                                    "email": "<EMAIL>",
                                                    "is_admin": True,
                                                    "is_active": True
                                                }
                                            ],
                                            "pagination": {
                                                "page": 1,
                                                "per_page": 10,
                                                "total": 1
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/rbac/roles",
                    "method": "GET",
                    "summary": "获取角色列表",
                    "description": "获取系统中所有角色信息",
                    "tags": ["rbac"],
                    "security": [{"bearerAuth": []}],
                    "permission": "role.view",
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "roles": [
                                                {
                                                    "id": 1,
                                                    "name": "管理员",
                                                    "description": "系统管理员角色",
                                                    "permissions": ["user.view", "system.config"]
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/rbac/permissions",
                    "method": "GET",
                    "summary": "获取权限列表",
                    "description": "获取系统中所有权限点",
                    "tags": ["rbac"],
                    "security": [{"bearerAuth": []}],
                    "permission": "permission.view",
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "permissions": [
                                                {
                                                    "id": 1,
                                                    "name": "user.view",
                                                    "description": "查看用户",
                                                    "category": "用户管理"
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "缓存管理",
            "description": "系统缓存管理接口",
            "endpoints": [
                {
                    "path": "/cache/status",
                    "method": "GET",
                    "summary": "获取缓存状态",
                    "description": "获取Redis缓存连接状态和基本信息",
                    "tags": ["cache"],
                    "security": [{"bearerAuth": []}],
                    "permission": "system.view",
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "redis_connected": True,
                                            "cache_type": "redis",
                                            "total_keys": 156,
                                            "memory_usage": "2.5MB"
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/cache/clear",
                    "method": "POST",
                    "summary": "清理缓存",
                    "description": "清理指定类型或全部缓存",
                    "tags": ["cache"],
                    "security": [{"bearerAuth": []}],
                    "permission": "system.config",
                    "requestBody": {
                        "required": False,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "cache_type": {"type": "string", "description": "缓存类型，不指定则清理全部"}
                                    }
                                },
                                "example": {
                                    "cache_type": "user_info"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "清理成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "缓存清理成功",
                                        "data": {
                                            "cleared_keys": 25
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "历史记录",
            "description": "表单提交历史记录管理接口",
            "endpoints": [
                {
                    "path": "/api/history/list",
                    "method": "GET",
                    "summary": "获取历史记录列表",
                    "description": "分页获取表单提交历史记录",
                    "tags": ["history"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.view",
                    "parameters": [
                        {
                            "name": "page",
                            "in": "query",
                            "description": "页码",
                            "required": False,
                            "schema": {"type": "integer", "default": 1}
                        },
                        {
                            "name": "per_page",
                            "in": "query",
                            "description": "每页数量",
                            "required": False,
                            "schema": {"type": "integer", "default": 10}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "records": [
                                                {
                                                    "id": 1,
                                                    "filename": "测试公司-运维文档-应用加固_20250106.xlsx",
                                                    "alias": "测试项目",
                                                    "form_type": "应用加固",
                                                    "created_at": "2025-01-06T12:00:00",
                                                    "file_size": "2.5MB"
                                                }
                                            ],
                                            "pagination": {
                                                "page": 1,
                                                "per_page": 10,
                                                "total": 1
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/api/history/delete/{id}",
                    "method": "DELETE",
                    "summary": "删除历史记录",
                    "description": "删除指定的历史记录和对应文件",
                    "tags": ["history"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.delete",
                    "parameters": [
                        {
                            "name": "id",
                            "in": "path",
                            "description": "记录ID",
                            "required": True,
                            "schema": {"type": "integer"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "删除成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "记录删除成功"
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "模板管理",
            "description": "Excel模板文件管理接口",
            "endpoints": [
                {
                    "path": "/excel/templates",
                    "method": "GET",
                    "summary": "获取模板列表",
                    "description": "获取所有可用的Excel模板文件",
                    "tags": ["template"],
                    "security": [{"bearerAuth": []}],
                    "permission": "template.view",
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "templates": [
                                                {
                                                    "form_type": "应用加固",
                                                    "template_path": "templates/应用加固-运维信息登记模板.xlsx",
                                                    "is_active": True,
                                                    "last_modified": "2025-01-06T12:00:00"
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/excel/templates/upload",
                    "method": "POST",
                    "summary": "上传模板文件",
                    "description": "上传新的Excel模板文件",
                    "tags": ["template"],
                    "security": [{"bearerAuth": []}],
                    "permission": "template.upload",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "multipart/form-data": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "file": {"type": "string", "format": "binary"},
                                        "form_type": {"type": "string", "description": "表单类型"}
                                    },
                                    "required": ["file", "form_type"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "上传成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "模板上传成功",
                                        "data": {
                                            "filename": "新模板.xlsx",
                                            "form_type": "应用加固"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "性能监控",
            "description": "系统性能监控和统计接口",
            "endpoints": [
                {
                    "path": "/api/performance/health-check",
                    "method": "GET",
                    "summary": "系统健康检查",
                    "description": "获取系统健康状态和基本信息",
                    "tags": ["performance"],
                    "security": [{"bearerAuth": []}],
                    "permission": "system.view",
                    "responses": {
                        "200": {
                            "description": "检查成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "health_status": "healthy",
                                            "service": "performance-monitor",
                                            "version": "1.0.0",
                                            "timestamp": "2025-01-06T12:00:00"
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/api/performance/cache-stats",
                    "method": "GET",
                    "summary": "获取缓存统计",
                    "description": "获取系统缓存命中率和性能统计",
                    "tags": ["performance"],
                    "security": [{"bearerAuth": []}],
                    "permission": "system.view",
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "cache_types": {
                                                "user_info": {
                                                    "hits": 38,
                                                    "misses": 6,
                                                    "hit_rate": 0.86,
                                                    "total": 44
                                                }
                                            },
                                            "summary": {
                                                "total_hits": 38,
                                                "total_misses": 6,
                                                "overall_hit_rate": 0.86
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/api/performance/slow-queries",
                    "method": "GET",
                    "summary": "获取慢查询记录",
                    "description": "获取数据库慢查询监控记录",
                    "tags": ["performance"],
                    "security": [{"bearerAuth": []}],
                    "permission": "system.view",
                    "parameters": [
                        {
                            "name": "limit",
                            "in": "query",
                            "description": "返回记录数量",
                            "required": False,
                            "schema": {"type": "integer", "default": 10}
                        },
                        {
                            "name": "hours",
                            "in": "query",
                            "description": "时间范围（小时）",
                            "required": False,
                            "schema": {"type": "integer", "default": 1}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "queries": [
                                                {
                                                    "sql": "SELECT * FROM user WHERE id = ?",
                                                    "duration": 2.5,
                                                    "timestamp": "2025-01-06T12:00:00",
                                                    "parameters": "1"
                                                }
                                            ],
                                            "total": 1
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "表单快照",
            "description": "表单快照保存和恢复接口",
            "endpoints": [
                {
                    "path": "/api/form-snapshot/save",
                    "method": "POST",
                    "summary": "保存表单快照",
                    "description": "将表单数据保存为快照，存储在Redis中",
                    "tags": ["snapshot"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.create",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "form_type": {"type": "string", "description": "表单类型"},
                                        "snapshot_data": {"type": "object", "description": "快照数据"},
                                        "snapshot_name": {"type": "string", "description": "快照名称"}
                                    },
                                    "required": ["form_type", "snapshot_data", "snapshot_name"]
                                },
                                "example": {
                                    "form_type": "应用加固",
                                    "snapshot_data": {
                                        "basic_info": {
                                            "customer_company_name": "测试公司"
                                        },
                                        "server_info": {
                                            "servers": []
                                        }
                                    },
                                    "snapshot_name": "测试快照"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "保存成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "表单快照保存成功",
                                        "data": {
                                            "snapshot_key": "form_snapshot:应用加固:测试快照",
                                            "snapshot_name": "测试快照"
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/api/form-snapshot/list",
                    "method": "GET",
                    "summary": "获取表单快照列表",
                    "description": "获取指定表单类型的所有快照",
                    "tags": ["snapshot"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.view",
                    "parameters": [
                        {
                            "name": "form_type",
                            "in": "query",
                            "description": "表单类型",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "snapshots": [
                                                {
                                                    "name": "测试快照",
                                                    "created_at": "2025-01-06T12:00:00",
                                                    "form_type": "应用加固"
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/api/form-snapshot/load",
                    "method": "GET",
                    "summary": "加载表单快照",
                    "description": "根据快照名称加载表单数据",
                    "tags": ["snapshot"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.view",
                    "parameters": [
                        {
                            "name": "form_type",
                            "in": "query",
                            "description": "表单类型",
                            "required": True,
                            "schema": {"type": "string"}
                        },
                        {
                            "name": "snapshot_name",
                            "in": "query",
                            "description": "快照名称",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "加载成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "snapshot_data": {
                                                "basic_info": {
                                                    "customer_company_name": "测试公司"
                                                },
                                                "server_info": {
                                                    "servers": []
                                                }
                                            },
                                            "snapshot_name": "测试快照",
                                            "created_at": "2025-01-06T12:00:00"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "文件下载",
            "description": "文件下载和导出接口",
            "endpoints": [
                {
                    "path": "/excel/download/{file_id}",
                    "method": "GET",
                    "summary": "下载Excel文件",
                    "description": "根据文件ID下载生成的Excel文件",
                    "tags": ["download"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.download",
                    "parameters": [
                        {
                            "name": "file_id",
                            "in": "path",
                            "description": "文件ID",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "文件下载成功",
                            "content": {
                                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                                    "schema": {
                                        "type": "string",
                                        "format": "binary"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "文件不存在",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "error",
                                        "message": "文件不存在"
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "数据导入",
            "description": "Excel和JSON数据导入接口",
            "endpoints": [
                {
                    "path": "/excel/import",
                    "method": "POST",
                    "summary": "Excel文件导入",
                    "description": "导入Excel文件并解析为表单数据",
                    "tags": ["import"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.import",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "multipart/form-data": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "file": {"type": "string", "format": "binary"},
                                        "form_type": {"type": "string", "description": "表单类型"}
                                    },
                                    "required": ["file", "form_type"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "导入成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "Excel文件导入成功",
                                        "data": {
                                            "form_data": {
                                                "basic_info": {
                                                    "customer_company_name": "测试公司"
                                                }
                                            },
                                            "preview_url": "/fill-sheet?import_data=xxx"
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/json/import",
                    "method": "POST",
                    "summary": "JSON数据导入",
                    "description": "导入JSON格式的表单数据",
                    "tags": ["import"],
                    "security": [{"bearerAuth": []}],
                    "permission": "form.import",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "form_type": {"type": "string", "description": "表单类型"},
                                        "form_data": {"type": "object", "description": "表单数据"}
                                    },
                                    "required": ["form_type", "form_data"]
                                },
                                "example": {
                                    "form_type": "应用加固",
                                    "form_data": {
                                        "basic_info": {
                                            "customer_company_name": "测试公司",
                                            "project_name": "测试项目"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "导入成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "JSON数据导入成功",
                                        "data": {
                                            "preview_url": "/fill-sheet?import_data=xxx"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        },
        {
            "name": "系统配置",
            "description": "系统配置和管理接口",
            "endpoints": [
                {
                    "path": "/config/form-fields",
                    "method": "GET",
                    "summary": "获取表单字段配置",
                    "description": "获取指定表单类型的字段配置",
                    "tags": ["config"],
                    "security": [{"bearerAuth": []}],
                    "permission": "system.config",
                    "parameters": [
                        {
                            "name": "form_type",
                            "in": "query",
                            "description": "表单类型",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "获取成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "data": {
                                            "form_type": "应用加固",
                                            "groups": [
                                                {
                                                    "name": "基本信息",
                                                    "fields": [
                                                        {
                                                            "name": "customer_company_name",
                                                            "label": "客户公司名称",
                                                            "type": "text",
                                                            "required": True
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    "path": "/config/form-fields",
                    "method": "POST",
                    "summary": "保存表单字段配置",
                    "description": "保存或更新表单字段配置",
                    "tags": ["config"],
                    "security": [{"bearerAuth": []}],
                    "permission": "system.config",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "form_type": {"type": "string"},
                                        "config": {"type": "object"}
                                    },
                                    "required": ["form_type", "config"]
                                },
                                "example": {
                                    "form_type": "应用加固",
                                    "config": {
                                        "groups": [
                                            {
                                                "name": "基本信息",
                                                "fields": []
                                            }
                                        ]
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "保存成功",
                            "content": {
                                "application/json": {
                                    "example": {
                                        "status": "success",
                                        "message": "配置保存成功"
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        }
    ],
    "components": {
        "securitySchemes": {
            "bearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT"
            }
        }
    }
}

@docs_bp.route('/test', methods=['GET'])
def test_route():
    """测试路由是否正常"""
    return jsonify({
        'status': 'success',
        'message': 'API文档路由正常工作',
        'timestamp': datetime.now().isoformat()
    })

@docs_bp.route('/auth-debug', methods=['GET'])
def auth_debug():
    """调试认证状态"""
    try:
        # 检查请求头
        auth_header = request.headers.get('Authorization')
        current_app.logger.info(f'Authorization头: {auth_header}')

        if not auth_header:
            return jsonify({
                'status': 'error',
                'message': '缺少Authorization头',
                'headers': dict(request.headers)
            }), 401

        # 尝试验证JWT
        try:
            verify_jwt_in_request()
            user_id = get_jwt_identity()
            current_app.logger.info(f'JWT验证成功，用户ID: {user_id}')

            return jsonify({
                'status': 'success',
                'message': 'JWT验证成功',
                'user_id': user_id,
                'auth_header': auth_header[:50] + '...' if len(auth_header) > 50 else auth_header
            })
        except Exception as jwt_error:
            current_app.logger.error(f'JWT验证失败: {str(jwt_error)}')
            return jsonify({
                'status': 'error',
                'message': f'JWT验证失败: {str(jwt_error)}',
                'auth_header': auth_header[:50] + '...' if len(auth_header) > 50 else auth_header
            }), 401

    except Exception as e:
        current_app.logger.error(f'认证调试失败: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': f'认证调试失败: {str(e)}'
        }), 500

@docs_bp.route('/api-docs', methods=['GET'])
def get_api_docs():
    """获取API文档数据"""
    return jsonify({
        'status': 'success',
        'data': API_DOCS
    })

@docs_bp.route('/user-permissions', methods=['GET'])
def get_user_permissions():
    """获取当前用户的权限列表"""
    try:
        from app.models.auth_models import User

        # 手动验证JWT
        try:
            verify_jwt_in_request()
            current_user_id = get_jwt_identity()
            current_app.logger.info(f'JWT验证成功，用户ID: {current_user_id}')
        except Exception as jwt_error:
            current_app.logger.error(f'JWT验证失败: {str(jwt_error)}')
            return jsonify({
                'status': 'error',
                'message': '认证失败，请重新登录'
            }), 401

        user = User.query.get(int(current_user_id))

        if not user:
            current_app.logger.error(f'用户不存在: {current_user_id}')
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404

        if not user.is_active:
            current_app.logger.error(f'用户已被禁用: {current_user_id}')
            return jsonify({
                'status': 'error',
                'message': '用户已被禁用'
            }), 401

        # 获取用户权限 - 使用真实的权限系统
        permissions = user.get_all_permissions()

        current_app.logger.info(f'用户实际权限: {permissions}')

        # 如果是管理员但没有权限，给予基础权限
        if user.is_admin and not permissions:
            permissions = [
                'form.create', 'form.view', 'form.edit', 'form.delete', 'form.download',
                'component.view', 'component.edit',
                'template.view', 'template.create', 'template.edit', 'template.upload', 'template.delete',
                'user.view', 'user.edit', 'user.delete',
                'role.view', 'role.edit', 'role.delete',
                'permission.view',
                'system.view', 'system.config'
            ]
            current_app.logger.info(f'管理员默认权限: {permissions}')

        # 如果是普通用户但没有权限，给予基础权限
        elif not user.is_admin and not permissions:
            permissions = ['form.create', 'form.view', 'form.download']
            current_app.logger.info(f'普通用户默认权限: {permissions}')

        current_app.logger.info(f'用户权限获取成功: {user.username}, 管理员: {user.is_admin}, 权限数: {len(permissions)}')

        return jsonify({
            'status': 'success',
            'data': {
                'permissions': permissions,
                'is_admin': user.is_admin,
                'username': user.username
            }
        })

    except Exception as e:
        current_app.logger.error(f'获取权限失败: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': f'获取权限失败: {str(e)}'
        }), 500

@docs_bp.route('/debug-user-permissions', methods=['GET'])
def debug_user_permissions():
    """调试用户权限详情"""
    try:
        from app.models.auth_models import User, Permission

        # 手动验证JWT
        try:
            verify_jwt_in_request()
            current_user_id = get_jwt_identity()
        except Exception as jwt_error:
            return jsonify({
                'status': 'error',
                'message': f'认证失败: {str(jwt_error)}'
            }), 401

        user = User.query.get(int(current_user_id))
        if not user:
            return jsonify({
                'status': 'error',
                'message': '用户不存在'
            }), 404

        # 获取详细权限信息
        all_permissions = user.get_all_permissions()

        # 获取用户角色
        user_roles = [{'id': role.id, 'code': role.code, 'name': role.name, 'is_active': role.is_active} for role in user.roles]

        # 获取用户组
        user_groups = [{'id': group.id, 'code': group.code, 'name': group.name, 'is_active': group.is_active} for group in user.groups]

        # 获取系统中所有权限
        all_system_permissions = Permission.query.filter_by(is_active=True).all()
        system_permissions = [{'code': p.code, 'name': p.name, 'module': p.module} for p in all_system_permissions]

        return jsonify({
            'status': 'success',
            'data': {
                'user_info': {
                    'id': user.id,
                    'username': user.username,
                    'real_name': user.real_name,
                    'is_admin': user.is_admin,
                    'is_active': user.is_active
                },
                'user_permissions': all_permissions,
                'user_roles': user_roles,
                'user_groups': user_groups,
                'system_permissions': system_permissions,
                'permission_count': len(all_permissions),
                'system_permission_count': len(system_permissions)
            }
        })

    except Exception as e:
        current_app.logger.error(f'调试用户权限失败: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': f'调试失败: {str(e)}'
        }), 500

@docs_bp.route('/api-test', methods=['POST'])
@login_required
def test_api():
    """在线API测试接口"""
    try:
        data = request.get_json()
        method = data.get('method', 'GET')
        url = data.get('url', '')
        headers = data.get('headers', {})
        body = data.get('body', {})

        # 这里可以实现实际的API调用逻辑
        # 为了安全考虑，只允许调用本系统的API

        return jsonify({
            'status': 'success',
            'message': 'API测试功能开发中',
            'data': {
                'method': method,
                'url': url,
                'headers': headers,
                'body': body
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'API测试失败: {str(e)}'
        }), 500
