from app import create_app, db
from app.models.models import ExcelFile, HistoryRecord, TemplateVersion

app = create_app()

@app.shell_context_processor
def make_shell_context():
    return {
        'db': db,
        'ExcelFile': ExcelFile,
        'HistoryRecord': HistoryRecord,
        'TemplateVersion': TemplateVersion
    }

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
