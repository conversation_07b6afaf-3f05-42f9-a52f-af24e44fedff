import { ref, onUnmounted } from 'vue'

/**
 * Section检测功能 Composable
 * 自动检测页面中的表单分组并提供导航
 */
export function useSectionDetector() {
  const sections = ref([])
  
  // Section映射配置
  const SECTION_MAPPING = {
    'basic-info-section': { title: '基本信息', icon: 'bi-info-circle' },
    'customer-info-section': { title: '客户信息', icon: 'bi-person-badge' },
    'version-info-section': { title: '版本信息', icon: 'bi-tag' },
    'deployment-info-section': { title: '部署信息', icon: 'bi-cloud-upload' },
    'server-info-section': { title: '服务器信息', icon: 'bi-server' },
    'network-config-section': { title: '网络配置', icon: 'bi-hdd-network' },
    'access-info-section': { title: '访问信息', icon: 'bi-door-open' },
    'authorization-section': { title: '授权信息', icon: 'bi-shield-lock' },
    'custom-content-section': { title: '运维定制内容', icon: 'bi-gear' },
    'client-app-section': { title: '客户APP', icon: 'bi-phone' },
    'maintenance-record-section': { title: '维护记录', icon: 'bi-tools' },
    'maintenance-records-section': { title: '维护记录', icon: 'bi-tools' },
    'upgrade-info-section': { title: '升级平台信息', icon: 'bi-arrow-up-circle' }
  }

  /**
   * 检查元素是否可见
   */
  const isElementVisible = (element) => {
    if (!element) return false
    
    const style = window.getComputedStyle(element)
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0'
  }

  /**
   * 获取元素在页面中的位置
   */
  const getElementPosition = (element) => {
    const rect = element.getBoundingClientRect()
    return {
      top: rect.top + window.scrollY,
      left: rect.left + window.scrollX
    }
  }

  /**
   * 检测页面中的sections
   */
  const detectSections = () => {
    console.log('🔍 开始检测页面sections...')
    
    const foundSections = []

    // 遍历所有已知的section ID
    Object.entries(SECTION_MAPPING).forEach(([sectionId, config]) => {
      const element = document.getElementById(sectionId)
      
      if (element && isElementVisible(element)) {
        foundSections.push({
          id: sectionId,
          title: config.title,
          icon: config.icon,
          element: element,
          position: getElementPosition(element)
        })
      }
    })

    // 按照在页面中的位置排序
    foundSections.sort((a, b) => a.position.top - b.position.top)

    // 更新sections数组（只保留必要信息）
    sections.value = foundSections.map(section => ({
      id: section.id,
      title: section.title,
      icon: section.icon
    }))

    console.log('✅ Section检测完成:', {
      found: sections.value.length,
      sections: sections.value.map(s => s.title)
    })

    return sections.value
  }

  /**
   * 刷新sections（防抖处理）
   */
  let refreshTimeout = null
  const refreshSections = () => {
    if (refreshTimeout) {
      clearTimeout(refreshTimeout)
    }
    
    refreshTimeout = setTimeout(() => {
      detectSections()
    }, 300)
  }

  /**
   * 监听DOM变化
   */
  let observer = null
  const startObserving = () => {
    if (observer) return

    observer = new MutationObserver((mutations) => {
      let shouldRefresh = false

      mutations.forEach((mutation) => {
        // 检查是否有相关的DOM变化
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) { // Element node
              const id = node.id
              if (id && SECTION_MAPPING[id]) {
                shouldRefresh = true
              }
            }
          })
        }
        
        if (mutation.type === 'attributes' && 
            mutation.attributeName === 'style') {
          const target = mutation.target
          if (target.id && SECTION_MAPPING[target.id]) {
            shouldRefresh = true
          }
        }
      })

      if (shouldRefresh) {
        refreshSections()
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    })

    console.log('👀 开始监听DOM变化')
  }

  const stopObserving = () => {
    if (observer) {
      observer.disconnect()
      observer = null
      console.log('🛑 停止监听DOM变化')
    }
    
    if (refreshTimeout) {
      clearTimeout(refreshTimeout)
      refreshTimeout = null
    }
  }

  /**
   * 获取section状态
   */
  const getSectionState = (sectionId) => {
    const section = document.getElementById(sectionId)
    if (!section) return null

    const header = section.querySelector('.card-header')
    const collapse = section.querySelector('.collapse')

    if (!header) return null

    let isExpanded = false

    // 检查展开状态 - 支持Bootstrap和CollapsibleCard两种方式
    if (collapse) {
      // Bootstrap方式：检查.show类
      isExpanded = collapse.classList.contains('show')
    } else {
      // CollapsibleCard方式：检查header的collapsed类（反向逻辑）
      isExpanded = !header.classList.contains('collapsed')
    }

    return {
      id: sectionId,
      isExpanded,
      element: section,
      header,
      collapse
    }
  }

  /**
   * 切换section状态
   */
  const toggleSection = (sectionId) => {
    const state = getSectionState(sectionId)
    if (!state || !state.header) return false

    try {
      state.header.click()
      console.log(`🔄 切换section: ${sectionId}`)
      return true
    } catch (error) {
      console.error(`❌ 切换section失败: ${sectionId}`, error)
      return false
    }
  }

  // 自动开始监听
  startObserving()

  // 清理函数
  const cleanup = () => {
    stopObserving()
  }

  onUnmounted(() => {
    cleanup()
  })

  return {
    sections,
    detectSections,
    refreshSections,
    getSectionState,
    toggleSection,
    cleanup
  }
}
