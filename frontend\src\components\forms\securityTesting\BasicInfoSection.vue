<template>
  <collapsible-card storage-key="basic-info-section">
    <template #header>
      <i class="bi bi-info-circle me-2"></i>基本信息
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-primary">{{ companyName || '未填写公司名称' }}</span>
        <span class="badge bg-secondary">版本信息: {{ packageVer || '未填写' }}</span>
        <span class="badge bg-info">日期: {{ recordDate || '未填写' }}</span>
      </div>
    </template>
    <div class="row mb-3">
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="companyNameHardening"
            v-model="companyName"
            placeholder="客户公司名称"
            @input="updateCompanyName"
          >
          <label for="companyNameHardening">客户公司名称 <span class="text-danger">*</span></label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="packageVersion"
            v-model="packageVer"
            placeholder="例如：ver5.9.0SP2_aimrsk53_rel_20241224_1116"
            @input="updatePackageVersion"
          >
          <label for="packageVersion">版本信息 <span class="text-danger">*</span></label>
        </div>
      </div>
    </div>

    <div class="row mb-3">
      <div class="col-md-12">
        <div class="form-floating mb-3">
          <input
            type="date"
            class="form-control"
            id="recordDateHardening"
            v-model="recordDate"
            @input="updateRecordDate"
          >
          <label for="recordDateHardening">记录日期 <span class="text-danger">*</span></label>
        </div>
      </div>
    </div>
  </collapsible-card>
</template>

<script>
/**
 * 安全测评基本信息部分组件
 * 用于填写基本信息
 */
import CollapsibleCard from '../common/CollapsibleCard.vue'

export default {
  name: 'BasicInfoSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 公司名称
    company: {
      type: String,
      default: '客户公司名称'
    },
    // 部署包版本
    packageVersion: {
      type: String,
      default: ''
    },
    // 记录日期
    recordDay: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      companyName: this.company,
      packageVer: this.packageVersion,
      recordDate: this.recordDay
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    company(newVal) {
      this.companyName = newVal
    },
    packageVersion(newVal) {
      this.packageVer = newVal
    },
    recordDay(newVal) {
      this.recordDate = newVal
    }
  },
  methods: {
    /**
     * 更新公司名称
     * 向父组件发送更新事件
     */
    updateCompanyName() {
      this.$emit('update:company', this.companyName)
    },

    /**
     * 更新部署包版本
     * 向父组件发送更新事件
     */
    updatePackageVersion() {
      this.$emit('update:packageVersion', this.packageVer)
    },



    /**
     * 更新记录日期
     * 向父组件发送更新事件
     */
    updateRecordDate() {
      this.$emit('update:recordDay', this.recordDate)
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.text-danger {
  font-weight: bold;
}
</style>
