"""
表单快照API路由
提供表单快照的保存、获取、删除等功能
"""

from flask import Blueprint, jsonify, request, current_app
from app.auth.decorators import permission_required, get_current_user
from app.utils.cache_utils import FormSnapshotCacheManager
import traceback
import json
from datetime import datetime

# 创建表单快照蓝图
form_snapshot_bp = Blueprint('form_snapshot', __name__, url_prefix='/api/form-snapshot')


@form_snapshot_bp.route('/save', methods=['POST'])
def save_form_snapshot():
    """
    保存表单快照
    """
    try:
        # 尝试获取当前用户，如果没有则使用默认用户（开发模式）
        current_user = get_current_user()
        if not current_user:
            # 创建一个临时用户对象用于开发测试
            class TempUser:
                def __init__(self):
                    self.id = 1
                    self.username = 'test_user'
                    self.real_name = '测试用户'
            current_user = TempUser()
        
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400
        
        form_type = data.get('form_type')
        snapshot_data = data.get('snapshot_data')
        snapshot_name = data.get('snapshot_name', f'快照_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        
        if not form_type or not snapshot_data:
            return jsonify({
                'status': 'error',
                'message': '表单类型和快照数据不能为空'
            }), 400
        
        # 构建完整的快照数据
        full_snapshot = {
            'snapshot_name': snapshot_name,
            'form_type': form_type,
            'snapshot_data': snapshot_data,
            'created_at': datetime.now().isoformat(),
            'user_id': current_user.id,
            'user_name': current_user.real_name or current_user.username
        }
        
        # 保存到缓存
        success = FormSnapshotCacheManager.cache_form_snapshot(
            current_user.id, 
            form_type, 
            full_snapshot
        )
        
        if success:
            current_app.logger.info(f"用户 {current_user.username} 保存表单快照: {form_type}")
            return jsonify({
                'status': 'success',
                'message': '表单快照保存成功',
                'data': {
                    'snapshot_name': snapshot_name,
                    'form_type': form_type,
                    'created_at': full_snapshot['created_at']
                }
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '表单快照保存失败'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"保存表单快照失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'保存表单快照失败: {str(e)}'
        }), 500


@form_snapshot_bp.route('/load/<form_type>', methods=['GET'])
def load_form_snapshot(form_type):
    """
    加载表单类型的最新快照
    """
    try:
        # 尝试获取当前用户，如果没有则使用默认用户（开发模式）
        current_user = get_current_user()
        if not current_user:
            # 创建一个临时用户对象用于开发测试
            class TempUser:
                def __init__(self):
                    self.id = 1
                    self.username = 'test_user'
                    self.real_name = '测试用户'
            current_user = TempUser()

        # 从缓存获取快照
        snapshot_data = FormSnapshotCacheManager.get_form_snapshot(
            current_user.id,
            form_type
        )

        if snapshot_data:
            current_app.logger.info(f"用户 {current_user.username} 加载表单快照: {form_type}")
            return jsonify({
                'status': 'success',
                'message': '表单快照加载成功',
                'data': snapshot_data
            })
        else:
            return jsonify({
                'status': 'success',
                'message': '未找到表单快照',
                'data': None
            })

    except Exception as e:
        current_app.logger.error(f"加载表单快照失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'加载表单快照失败: {str(e)}'
        }), 500


@form_snapshot_bp.route('/load-by-id/<snapshot_id>', methods=['GET'])
def load_snapshot_by_id(snapshot_id):
    """
    根据快照ID加载特定快照
    """
    try:
        # 尝试获取当前用户，如果没有则使用默认用户（开发模式）
        current_user = get_current_user()
        if not current_user:
            # 创建一个临时用户对象用于开发测试
            class TempUser:
                def __init__(self):
                    self.id = 1
                    self.username = 'test_user'
                    self.real_name = '测试用户'
            current_user = TempUser()

        # 获取快照详细数据
        from app.utils.cache_utils import cache

        # 获取用户所有快照，找到对应的快照
        all_snapshots = FormSnapshotCacheManager.get_all_user_snapshots(current_user.id)
        target_snapshot = None

        for snapshot_info in all_snapshots:
            if snapshot_info['snapshot_id'] == snapshot_id:
                target_snapshot = snapshot_info
                break

        if not target_snapshot:
            return jsonify({
                'status': 'error',
                'message': '未找到指定的快照'
            }), 404

        # 获取快照详细数据
        snapshot_key = FormSnapshotCacheManager.get_snapshot_key(
            current_user.id,
            target_snapshot['form_type'],
            snapshot_id
        )

        snapshot_data = cache.get(snapshot_key)

        if snapshot_data:
            current_app.logger.info(f"用户 {current_user.username} 加载快照: {snapshot_id}")
            return jsonify({
                'status': 'success',
                'message': '快照加载成功',
                'data': snapshot_data
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '快照数据不存在'
            }), 404

    except Exception as e:
        current_app.logger.error(f"根据ID加载快照失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'加载快照失败: {str(e)}'
        }), 500


@form_snapshot_bp.route('/delete/<form_type>', methods=['DELETE'])
def delete_form_snapshot(form_type):
    """
    删除表单类型的所有快照
    """
    try:
        # 尝试获取当前用户，如果没有则使用默认用户（开发模式）
        current_user = get_current_user()
        if not current_user:
            # 创建一个临时用户对象用于开发测试
            class TempUser:
                def __init__(self):
                    self.id = 1
                    self.username = 'test_user'
                    self.real_name = '测试用户'
            current_user = TempUser()

        # 删除缓存中的快照
        success = FormSnapshotCacheManager.clear_form_snapshot(
            current_user.id,
            form_type
        )

        if success:
            current_app.logger.info(f"用户 {current_user.username} 删除表单快照: {form_type}")
            return jsonify({
                'status': 'success',
                'message': '表单快照删除成功'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '表单快照删除失败'
            }), 500

    except Exception as e:
        current_app.logger.error(f"删除表单快照失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'删除表单快照失败: {str(e)}'
        }), 500


@form_snapshot_bp.route('/delete-by-id/<snapshot_id>', methods=['DELETE'])
def delete_snapshot_by_id(snapshot_id):
    """
    根据快照ID删除特定快照
    """
    try:
        # 尝试获取当前用户，如果没有则使用默认用户（开发模式）
        current_user = get_current_user()
        if not current_user:
            # 创建一个临时用户对象用于开发测试
            class TempUser:
                def __init__(self):
                    self.id = 1
                    self.username = 'test_user'
                    self.real_name = '测试用户'
            current_user = TempUser()

        # 获取用户所有快照，找到对应的快照
        from app.utils.cache_utils import cache
        user_snapshots_key = FormSnapshotCacheManager.get_user_snapshots_key(current_user.id)
        user_snapshots = cache.get(user_snapshots_key) or {}

        # 查找并删除快照
        snapshot_found = False
        for form_type, snapshots in user_snapshots.items():
            for i, snapshot_info in enumerate(snapshots):
                if snapshot_info['snapshot_id'] == snapshot_id:
                    # 删除快照数据
                    snapshot_key = FormSnapshotCacheManager.get_snapshot_key(
                        current_user.id, form_type, snapshot_id
                    )
                    cache.delete(snapshot_key)

                    # 从索引中移除
                    snapshots.pop(i)
                    snapshot_found = True
                    break

            if snapshot_found:
                break

        if not snapshot_found:
            return jsonify({
                'status': 'error',
                'message': '未找到指定的快照'
            }), 404

        # 更新用户快照索引
        from app.utils.cache_utils import CacheManager
        cache.set(user_snapshots_key, user_snapshots, timeout=CacheManager.get_timeout('form_snapshot'))

        current_app.logger.info(f"用户 {current_user.username} 删除快照: {snapshot_id}")
        return jsonify({
            'status': 'success',
            'message': '快照删除成功'
        })

    except Exception as e:
        current_app.logger.error(f"根据ID删除快照失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'删除快照失败: {str(e)}'
        }), 500


@form_snapshot_bp.route('/list', methods=['GET'])
def list_form_snapshots():
    """
    获取用户的所有表单快照列表
    """
    try:
        # 尝试获取当前用户，如果没有则使用默认用户（开发模式）
        current_user = get_current_user()
        if not current_user:
            # 创建一个临时用户对象用于开发测试
            class TempUser:
                def __init__(self):
                    self.id = 1
                    self.username = 'test_user'
                    self.real_name = '测试用户'
            current_user = TempUser()
        
        # 获取用户所有快照
        snapshots = FormSnapshotCacheManager.get_all_user_snapshots(current_user.id)
        
        return jsonify({
            'status': 'success',
            'message': '获取快照列表成功',
            'data': snapshots
        })
        
    except Exception as e:
        current_app.logger.error(f"获取表单快照列表失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取表单快照列表失败: {str(e)}'
        }), 500


@form_snapshot_bp.route('/health', methods=['GET'])
def snapshot_health():
    """
    表单快照系统健康检查（无需权限）
    """
    try:
        # 测试缓存连接
        from app.utils.cache_utils import cache
        cache.cache._write_client.ping()
        
        return jsonify({
            'status': 'success',
            'data': {
                'snapshot_system': 'Redis',
                'cache_status': 'connected',
                'features': [
                    'form_snapshot_save',
                    'form_snapshot_load', 
                    'form_snapshot_delete',
                    'form_snapshot_list'
                ]
            }
        })
    except Exception as e:
        current_app.logger.error(f"表单快照健康检查失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'表单快照系统异常: {str(e)}'
        }), 500


@form_snapshot_bp.route('/batch-delete', methods=['POST'])
def batch_delete_snapshots():
    """
    批量删除快照
    """
    try:
        # 尝试获取当前用户，如果没有则使用默认用户（开发模式）
        current_user = get_current_user()
        if not current_user:
            # 创建一个临时用户对象用于开发测试
            class TempUser:
                def __init__(self):
                    self.id = 1
                    self.username = 'test_user'
                    self.real_name = '测试用户'
            current_user = TempUser()

        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400

        snapshot_ids = data.get('snapshot_ids', [])
        if not snapshot_ids:
            return jsonify({
                'status': 'error',
                'message': '请提供要删除的快照ID列表'
            }), 400

        # 获取用户所有快照
        from app.utils.cache_utils import cache
        user_snapshots_key = FormSnapshotCacheManager.get_user_snapshots_key(current_user.id)
        user_snapshots = cache.get(user_snapshots_key) or {}

        deleted_count = 0
        not_found_count = 0

        # 批量删除快照
        for snapshot_id in snapshot_ids:
            snapshot_found = False

            # 查找并删除快照
            for form_type, snapshots in user_snapshots.items():
                for i, snapshot_info in enumerate(snapshots):
                    if snapshot_info['snapshot_id'] == snapshot_id:
                        # 删除快照数据
                        snapshot_key = FormSnapshotCacheManager.get_snapshot_key(
                            current_user.id, form_type, snapshot_id
                        )
                        cache.delete(snapshot_key)

                        # 从索引中移除
                        snapshots.pop(i)
                        snapshot_found = True
                        deleted_count += 1
                        break

                if snapshot_found:
                    break

            if not snapshot_found:
                not_found_count += 1

        # 更新用户快照索引
        from app.utils.cache_utils import CacheManager
        cache.set(user_snapshots_key, user_snapshots, timeout=CacheManager.get_timeout('form_snapshot'))

        current_app.logger.info(f"用户 {current_user.username} 批量删除快照: 成功={deleted_count}, 未找到={not_found_count}")

        return jsonify({
            'status': 'success',
            'message': f'批量删除完成',
            'data': {
                'deleted_count': deleted_count,
                'not_found_count': not_found_count,
                'total_requested': len(snapshot_ids)
            }
        })

    except Exception as e:
        current_app.logger.error(f"批量删除快照失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'批量删除快照失败: {str(e)}'
        }), 500


@form_snapshot_bp.route('/clear-by-condition', methods=['POST'])
def clear_snapshots_by_condition():
    """
    按条件清理快照
    """
    try:
        # 尝试获取当前用户，如果没有则使用默认用户（开发模式）
        current_user = get_current_user()
        if not current_user:
            # 创建一个临时用户对象用于开发测试
            class TempUser:
                def __init__(self):
                    self.id = 1
                    self.username = 'test_user'
                    self.real_name = '测试用户'
            current_user = TempUser()

        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400

        # 获取清理条件
        form_type = data.get('form_type')  # 按表单类型清理
        days_old = data.get('days_old')    # 清理N天前的快照
        name_pattern = data.get('name_pattern')  # 按名称模式清理

        if not any([form_type, days_old, name_pattern]):
            return jsonify({
                'status': 'error',
                'message': '请至少提供一个清理条件'
            }), 400

        # 获取用户所有快照
        from app.utils.cache_utils import cache
        from datetime import datetime, timedelta
        import re

        user_snapshots_key = FormSnapshotCacheManager.get_user_snapshots_key(current_user.id)
        user_snapshots = cache.get(user_snapshots_key) or {}

        deleted_count = 0
        to_delete = []

        # 遍历所有快照，找到符合条件的
        for current_form_type, snapshots in user_snapshots.items():
            # 按表单类型过滤
            if form_type and current_form_type != form_type:
                continue

            for snapshot_info in snapshots[:]:  # 使用切片复制，避免修改时出错
                should_delete = True

                # 按时间过滤
                if days_old:
                    try:
                        created_at = datetime.fromisoformat(snapshot_info['created_at'].replace('Z', '+00:00'))
                        cutoff_date = datetime.now() - timedelta(days=days_old)
                        if created_at > cutoff_date:
                            should_delete = False
                    except:
                        # 如果时间解析失败，跳过时间条件
                        pass

                # 按名称模式过滤
                if name_pattern and should_delete:
                    try:
                        if not re.search(name_pattern, snapshot_info['snapshot_name']):
                            should_delete = False
                    except:
                        # 如果正则表达式无效，跳过名称条件
                        should_delete = False

                if should_delete:
                    to_delete.append({
                        'form_type': current_form_type,
                        'snapshot_info': snapshot_info
                    })

        # 执行删除
        for item in to_delete:
            form_type_key = item['form_type']
            snapshot_info = item['snapshot_info']
            snapshot_id = snapshot_info['snapshot_id']

            # 删除快照数据
            snapshot_key = FormSnapshotCacheManager.get_snapshot_key(
                current_user.id, form_type_key, snapshot_id
            )
            cache.delete(snapshot_key)

            # 从索引中移除
            user_snapshots[form_type_key].remove(snapshot_info)
            deleted_count += 1

        # 更新用户快照索引
        from app.utils.cache_utils import CacheManager
        cache.set(user_snapshots_key, user_snapshots, timeout=CacheManager.get_timeout('form_snapshot'))

        current_app.logger.info(f"用户 {current_user.username} 按条件清理快照: 删除={deleted_count}")

        return jsonify({
            'status': 'success',
            'message': f'按条件清理完成，删除了 {deleted_count} 个快照',
            'data': {
                'deleted_count': deleted_count,
                'conditions': {
                    'form_type': form_type,
                    'days_old': days_old,
                    'name_pattern': name_pattern
                }
            }
        })

    except Exception as e:
        current_app.logger.error(f"按条件清理快照失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'按条件清理快照失败: {str(e)}'
        }), 500


@form_snapshot_bp.route('/clear-all', methods=['DELETE'])
def clear_all_snapshots():
    """
    清除用户所有快照
    """
    try:
        # 尝试获取当前用户，如果没有则使用默认用户（开发模式）
        current_user = get_current_user()
        if not current_user:
            # 创建一个临时用户对象用于开发测试
            class TempUser:
                def __init__(self):
                    self.id = 1
                    self.username = 'test_user'
                    self.real_name = '测试用户'
            current_user = TempUser()

        # 清除用户所有快照
        success = FormSnapshotCacheManager.clear_form_snapshot(current_user.id)

        if success:
            current_app.logger.info(f"用户 {current_user.username} 清除所有快照")
            return jsonify({
                'status': 'success',
                'message': '所有快照已清除'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '清除快照失败'
            }), 500

    except Exception as e:
        current_app.logger.error(f"清除所有快照失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'清除所有快照失败: {str(e)}'
        }), 500
