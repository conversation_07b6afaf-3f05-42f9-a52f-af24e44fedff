"""
缓存管理API路由
提供缓存状态查看、清除等管理功能
"""

from flask import Blueprint, jsonify, request, current_app
from app.auth.decorators import permission_required
from app.utils.cache_utils import (
    cache, 
    UserCacheManager, 
    ComponentCacheManager, 
    TemplateCacheManager, 
    FormConfigCacheManager,
    clear_all_cache
)
import traceback

# 创建缓存管理蓝图
cache_bp = Blueprint('cache', __name__, url_prefix='/api/cache')


@cache_bp.route('/health', methods=['GET'])
def cache_health():
    """
    缓存系统健康检查（无需权限）
    """
    try:
        # 测试Redis连接
        cache.cache._write_client.ping()
        redis_status = 'connected'
    except Exception as e:
        redis_status = 'disconnected'
        current_app.logger.error(f"Redis连接失败: {str(e)}")

    return jsonify({
        'status': 'success',
        'data': {
            'cache_system': 'Redis',
            'redis_status': redis_status,
            'cache_enabled': True
        }
    })


@cache_bp.route('/status', methods=['GET'])
@permission_required('system.cache.view')
def get_cache_status():
    """
    获取缓存状态信息
    """
    try:
        # 获取Redis连接信息
        redis_config = {
            'host': current_app.config.get('REDIS_HOST', 'localhost'),
            'port': current_app.config.get('REDIS_PORT', 6379),
            'db': current_app.config.get('REDIS_DB', 0),
            'password_configured': bool(current_app.config.get('REDIS_PASSWORD')),
            'url': current_app.config.get('REDIS_URL', 'redis://localhost:6379/0')
        }
        
        # 获取缓存配置
        cache_config = current_app.config.get('CACHE_CONFIG', {})
        
        # 尝试获取Redis连接状态
        try:
            # 测试Redis连接
            cache.cache._write_client.ping()
            redis_status = 'connected'
            redis_info = cache.cache._write_client.info()
            redis_memory = redis_info.get('used_memory_human', 'Unknown')
            redis_keys = cache.cache._write_client.dbsize()
        except Exception as e:
            redis_status = 'disconnected'
            redis_memory = 'Unknown'
            redis_keys = 0
            current_app.logger.error(f"Redis连接失败: {str(e)}")
        
        return jsonify({
            'status': 'success',
            'data': {
                'redis_config': redis_config,
                'redis_status': redis_status,
                'redis_memory_usage': redis_memory,
                'redis_keys_count': redis_keys,
                'cache_config': cache_config,
                'cache_types': {
                    'user_permissions': '用户权限缓存',
                    'components': '组件配置缓存',
                    'templates': '模板配置缓存',
                    'form_config': '表单字段配置缓存',
                    'user_info': '用户信息缓存',
                    'form_snapshot': '表单快照缓存',
                    'history_records': '历史记录缓存',
                    'duplicate_check': '重复提交检测缓存',
                    'rate_limit': '限流缓存'
                }
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取缓存状态失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取缓存状态失败: {str(e)}'
        }), 500


@cache_bp.route('/clear', methods=['POST'])
@permission_required('system.cache.manage')
def clear_cache():
    """
    清除缓存
    支持清除指定类型的缓存或全部缓存
    """
    try:
        data = request.get_json() or {}
        cache_type = data.get('type', 'all')
        target_key = data.get('key')  # 可选的具体缓存键
        
        cleared_count = 0
        
        if cache_type == 'all':
            # 清除所有缓存
            success = clear_all_cache()
            if success:
                cleared_count = 'all'
                message = '已清除所有缓存'
            else:
                return jsonify({
                    'status': 'error',
                    'message': '清除所有缓存失败'
                }), 500
                
        elif cache_type == 'user_permissions':
            # 清除用户权限缓存
            if target_key:
                # 清除指定用户的权限缓存
                try:
                    user_id = int(target_key)
                    success = UserCacheManager.clear_user_cache(user_id)
                    if success:
                        cleared_count = 1
                        message = f'已清除用户 {user_id} 的缓存'
                    else:
                        message = f'清除用户 {user_id} 的缓存失败'
                except ValueError:
                    return jsonify({
                        'status': 'error',
                        'message': '无效的用户ID'
                    }), 400
            else:
                # TODO: 清除所有用户权限缓存
                message = '清除所有用户权限缓存功能待实现'
                
        elif cache_type == 'components':
            # 清除组件配置缓存
            if target_key:
                # 清除指定表单类型的组件缓存
                success = ComponentCacheManager.clear_component_cache(target_key)
                if success:
                    cleared_count = 1
                    message = f'已清除表单类型 {target_key} 的组件缓存'
                else:
                    message = f'清除表单类型 {target_key} 的组件缓存失败'
            else:
                # TODO: 清除所有组件缓存
                message = '清除所有组件缓存功能待实现'
                
        elif cache_type == 'templates':
            # 清除模板配置缓存
            if target_key:
                # 清除指定表单类型的模板缓存
                success = TemplateCacheManager.clear_template_cache(target_key)
                if success:
                    cleared_count = 1
                    message = f'已清除表单类型 {target_key} 的模板缓存'
                else:
                    message = f'清除表单类型 {target_key} 的模板缓存失败'
            else:
                # TODO: 清除所有模板缓存
                message = '清除所有模板缓存功能待实现'
                
        elif cache_type == 'form_config':
            # 清除表单配置缓存
            if target_key:
                # 清除指定表单类型的表单配置缓存
                success = FormConfigCacheManager.clear_form_config_cache(target_key)
                if success:
                    cleared_count = 1
                    message = f'已清除表单类型 {target_key} 的表单配置缓存'
                else:
                    message = f'清除表单类型 {target_key} 的表单配置缓存失败'
            else:
                # TODO: 清除所有表单配置缓存
                message = '清除所有表单配置缓存功能待实现'
                
        elif cache_type == 'user_info':
            # 清除用户信息缓存
            if target_key:
                # 清除指定用户的信息缓存
                try:
                    user_id = int(target_key)
                    success = UserCacheManager.clear_user_cache(user_id)
                    if success:
                        cleared_count = 1
                        message = f'已清除用户 {user_id} 的信息缓存'
                    else:
                        message = f'清除用户 {user_id} 的信息缓存失败'
                except ValueError:
                    return jsonify({
                        'status': 'error',
                        'message': '无效的用户ID'
                    }), 400
            else:
                # TODO: 清除所有用户信息缓存
                message = '清除所有用户信息缓存功能待实现'

        elif cache_type == 'form_snapshot':
            # 清除表单快照缓存
            if target_key:
                # target_key格式: "user_id:form_type"
                try:
                    parts = target_key.split(':')
                    if len(parts) == 2:
                        user_id, form_type = int(parts[0]), parts[1]
                        from app.utils.cache_utils import FormSnapshotCacheManager
                        success = FormSnapshotCacheManager.clear_form_snapshot(user_id, form_type)
                        if success:
                            cleared_count = 1
                            message = f'已清除用户 {user_id} 的 {form_type} 表单快照'
                        else:
                            message = f'清除表单快照失败'
                    else:
                        return jsonify({
                            'status': 'error',
                            'message': '无效的快照键格式，应为 user_id:form_type'
                        }), 400
                except ValueError:
                    return jsonify({
                        'status': 'error',
                        'message': '无效的用户ID'
                    }), 400
            else:
                # TODO: 清除所有表单快照缓存
                message = '清除所有表单快照缓存功能待实现'

        elif cache_type == 'history_records':
            # 清除历史记录缓存
            if target_key:
                # 清除指定用户的历史记录缓存
                try:
                    user_id = int(target_key)
                    from app.utils.cache_utils import HistoryRecordsCacheManager
                    success = HistoryRecordsCacheManager.clear_history_cache(user_id)
                    if success:
                        cleared_count = 1
                        message = f'已清除用户 {user_id} 的历史记录缓存'
                    else:
                        message = f'清除历史记录缓存失败'
                except ValueError:
                    return jsonify({
                        'status': 'error',
                        'message': '无效的用户ID'
                    }), 400
            else:
                # 清除所有历史记录缓存
                from app.utils.cache_utils import HistoryRecordsCacheManager
                success = HistoryRecordsCacheManager.clear_history_cache()
                if success:
                    cleared_count = 'all'
                    message = '已清除所有历史记录缓存'
                else:
                    message = '清除所有历史记录缓存失败'

        elif cache_type == 'duplicate_check':
            # 清除重复检测缓存
            if target_key:
                # target_key格式: "company_name:form_type"
                try:
                    parts = target_key.split(':')
                    if len(parts) == 2:
                        company_name, form_type = parts[0], parts[1]
                        from app.utils.cache_utils import DuplicateCheckCacheManager
                        success = DuplicateCheckCacheManager.clear_duplicate_check(company_name, form_type)
                        if success:
                            cleared_count = 1
                            message = f'已清除 {company_name} 的 {form_type} 重复检测记录'
                        else:
                            message = f'清除重复检测记录失败'
                    else:
                        return jsonify({
                            'status': 'error',
                            'message': '无效的检测键格式，应为 company_name:form_type'
                        }), 400
                except Exception as e:
                    return jsonify({
                        'status': 'error',
                        'message': f'清除重复检测记录失败: {str(e)}'
                    }), 400
            else:
                # TODO: 清除所有重复检测缓存
                message = '清除所有重复检测缓存功能待实现'

        elif cache_type == 'rate_limit':
            # 清除限流缓存
            if target_key:
                # target_key格式: "identifier:action"
                try:
                    parts = target_key.split(':')
                    if len(parts) == 2:
                        identifier, action = parts[0], parts[1]
                        from app.utils.cache_utils import RateLimitCacheManager
                        success = RateLimitCacheManager.reset_rate_limit(identifier, action)
                        if success:
                            cleared_count = 1
                            message = f'已重置 {identifier} 的 {action} 限流计数'
                        else:
                            message = f'重置限流计数失败'
                    else:
                        return jsonify({
                            'status': 'error',
                            'message': '无效的限流键格式，应为 identifier:action'
                        }), 400
                except Exception as e:
                    return jsonify({
                        'status': 'error',
                        'message': f'重置限流计数失败: {str(e)}'
                    }), 400
            else:
                # TODO: 清除所有限流缓存
                message = '清除所有限流缓存功能待实现'
        else:
            return jsonify({
                'status': 'error',
                'message': f'不支持的缓存类型: {cache_type}'
            }), 400
        
        current_app.logger.info(f"缓存清除操作: type={cache_type}, key={target_key}, cleared={cleared_count}")
        
        return jsonify({
            'status': 'success',
            'message': message,
            'data': {
                'cache_type': cache_type,
                'target_key': target_key,
                'cleared_count': cleared_count
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"清除缓存失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'清除缓存失败: {str(e)}'
        }), 500


@cache_bp.route('/keys', methods=['GET'])
@permission_required('system.cache.view')
def get_cache_keys():
    """
    获取缓存键列表
    支持按模式筛选
    """
    try:
        pattern = request.args.get('pattern', '*')
        limit = int(request.args.get('limit', 100))
        
        try:
            # 获取匹配的缓存键
            keys = cache.cache._write_client.keys(pattern)
            
            # 限制返回数量
            if len(keys) > limit:
                keys = keys[:limit]
                truncated = True
            else:
                truncated = False
            
            # 获取键的详细信息
            key_details = []
            for key in keys:
                try:
                    # 获取键的TTL
                    ttl = cache.cache._write_client.ttl(key)
                    # 获取键的类型
                    key_type = cache.cache._write_client.type(key).decode('utf-8')
                    
                    key_details.append({
                        'key': key.decode('utf-8') if isinstance(key, bytes) else key,
                        'type': key_type,
                        'ttl': ttl if ttl > 0 else None
                    })
                except Exception as e:
                    current_app.logger.warning(f"获取键 {key} 的详细信息失败: {str(e)}")
                    key_details.append({
                        'key': key.decode('utf-8') if isinstance(key, bytes) else key,
                        'type': 'unknown',
                        'ttl': None
                    })
            
            return jsonify({
                'status': 'success',
                'data': {
                    'keys': key_details,
                    'total_count': len(key_details),
                    'truncated': truncated,
                    'pattern': pattern,
                    'limit': limit
                }
            })
            
        except Exception as e:
            current_app.logger.error(f"Redis操作失败: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'Redis操作失败: {str(e)}'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"获取缓存键失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取缓存键失败: {str(e)}'
        }), 500


@cache_bp.route('/test', methods=['POST'])
@permission_required('system.cache.manage')
def test_cache():
    """
    测试缓存功能
    """
    try:
        # 测试缓存写入和读取
        test_key = 'cache_test_key'
        test_value = 'cache_test_value'
        
        # 写入测试数据
        cache.set(test_key, test_value, timeout=60)
        
        # 读取测试数据
        retrieved_value = cache.get(test_key)
        
        # 删除测试数据
        cache.delete(test_key)
        
        if retrieved_value == test_value:
            return jsonify({
                'status': 'success',
                'message': '缓存功能测试通过',
                'data': {
                    'test_key': test_key,
                    'test_value': test_value,
                    'retrieved_value': retrieved_value
                }
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '缓存功能测试失败：读取的值与写入的值不匹配',
                'data': {
                    'test_value': test_value,
                    'retrieved_value': retrieved_value
                }
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"缓存功能测试失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'缓存功能测试失败: {str(e)}'
        }), 500
