<template>
  <div class="component-alias-manager">
    <!-- 别名管理模态框 -->
    <div
      class="modal fade show"
      style="display: block"
      tabindex="-1"
      @click.self="$emit('close')"
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-tags me-2"></i>组件别名管理
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="$emit('close')"
            ></button>
          </div>
          <div class="modal-body">
            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              您可以为组件设置自定义别名，别名将在组件选择和显示时使用。
            </div>

            <!-- 按分类显示组件 -->
            <div v-for="(categoryComponents, categoryKey) in categorizedComponents" :key="categoryKey" class="mb-4">
              <h6 class="text-primary border-bottom pb-2">
                <i :class="getCategoryIcon(categoryKey)" class="me-2"></i>
                {{ getCategoryName(categoryKey) }}
              </h6>

              <div class="row g-3">
                <div
                  v-for="component in categoryComponents"
                  :key="component.name"
                  class="col-md-6"
                >
                  <div class="card h-100">
                    <div class="card-body p-3">
                      <div class="d-flex align-items-center mb-2">
                        <strong class="text-dark">{{ component.name }}</strong>
                        <span class="badge bg-light text-dark ms-2 small">{{ component.port || '无端口' }}</span>
                      </div>

                      <div class="mb-2">
                        <label class="form-label small mb-1">自定义别名：</label>
                        <input
                          type="text"
                          class="form-control form-control-sm"
                          :value="getComponentAlias(component.name)"
                          @input="updateComponentAlias(component.name, $event.target.value)"
                          :placeholder="component.name"
                        />
                      </div>

                      <div class="small text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        {{ component.description || '无描述' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              @click="resetAllAliases"
            >
              <i class="bi bi-arrow-clockwise me-1"></i>重置所有别名
            </button>
            <button
              type="button"
              class="btn btn-primary"
              @click="saveAliases"
            >
              <i class="bi bi-check-lg me-1"></i>保存设置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框背景 -->
    <div
      class="modal-backdrop fade show"
      @click="$emit('close')"
    ></div>
  </div>
</template>

<script>
// 组件别名管理器现在使用数据库组件

export default {
  name: 'ComponentAliasManager',
  props: {
    formType: {
      type: String,
      default: '安全测评'
    },
    componentGroups: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      componentAliases: {} // 存储组件别名
    }
  },
  computed: {
    /**
     * 获取当前表单类型的所有组件
     * 从父组件传入的组件分组中获取
     */
    allComponents() {
      const components = []

      // 从父组件传入的组件分组中提取所有组件
      if (this.componentGroups) {
        Object.values(this.componentGroups).forEach(group => {
          if (Array.isArray(group)) {
            components.push(...group)
          }
        })
      }

      return components
    },

    /**
     * 按分类组织的组件
     */
    categorizedComponents() {
      const categorized = {}

      this.allComponents.forEach(component => {
        const category = component.category_key || component.category || 'other'
        if (!categorized[category]) {
          categorized[category] = []
        }
        categorized[category].push(component)
      })

      return categorized
    }
  },
  mounted() {
    this.loadAliases()
  },
  methods: {
    /**
     * 获取分类名称
     */
    getCategoryName(categoryKey) {
      const categoryNames = {
        'base': '基础组件',
        'database': '数据库组件',
        'tools': '工具组件',
        'service': '服务组件',
        'engine': '引擎组件',
        'frontend': '前端组件',
        'backend': '后端组件',
        'app': '应用组件',
        'server': '服务器组件',
        'ops': '运维组件',
        'platform': '平台组件',
        'other': '其他组件'
      }
      return categoryNames[categoryKey] || categoryKey
    },

    /**
     * 获取分类图标
     */
    getCategoryIcon(categoryKey) {
      const categoryIcons = {
        'base': 'bi-box',
        'database': 'bi-database',
        'tools': 'bi-tools',
        'service': 'bi-gear',
        'engine': 'bi-cpu',
        'frontend': 'bi-display',
        'backend': 'bi-server',
        'app': 'bi-app',
        'server': 'bi-hdd-rack',
        'ops': 'bi-wrench',
        'platform': 'bi-layers',
        'other': 'bi-question-circle'
      }
      return categoryIcons[categoryKey] || 'bi-question-circle'
    },

    /**
     * 获取组件别名
     */
    getComponentAlias(componentName) {
      return this.componentAliases[componentName] || ''
    },

    /**
     * 更新组件别名
     */
    updateComponentAlias(componentName, alias) {
      this.componentAliases[componentName] = alias.trim()
    },

    /**
     * 保存别名设置
     */
    saveAliases() {
      // 保存到本地存储
      const storageKey = `component_aliases_${this.formType}`
      localStorage.setItem(storageKey, JSON.stringify(this.componentAliases))

      // 触发事件通知父组件
      this.$emit('aliases-updated', this.componentAliases)

      this.$emit('close')
      this.$emit('show-toast', '组件别名设置已保存', '成功', 'success')
    },

    /**
     * 加载别名设置
     */
    loadAliases() {
      const storageKey = `component_aliases_${this.formType}`
      const saved = localStorage.getItem(storageKey)
      if (saved) {
        try {
          this.componentAliases = JSON.parse(saved)
        } catch (e) {
          console.warn('加载组件别名失败:', e)
          this.componentAliases = {}
        }
      }
    },

    /**
     * 重置所有别名
     */
    resetAllAliases() {
      if (confirm('确定要重置所有组件别名吗？此操作不可撤销。')) {
        this.componentAliases = {}
        this.$emit('show-toast', '所有组件别名已重置', '提示', 'info')
      }
    }
  }
}
</script>

<style scoped>
.component-alias-manager {
  position: relative;
}

.modal {
  z-index: 1055;
}

.modal-backdrop {
  z-index: 1050;
}

.card {
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.form-control-sm {
  font-size: 0.875rem;
}

.badge {
  font-size: 0.7rem;
}
</style>
