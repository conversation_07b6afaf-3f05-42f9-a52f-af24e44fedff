<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-question-circle me-2"></i>
            {{ title }}
          </h5>
          <button type="button" class="btn-close" @click="$emit('cancel')"></button>
        </div>
        
        <div class="modal-body">
          <div class="d-flex align-items-start">
            <div class="me-3">
              <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
            </div>
            <div>
              <p class="mb-0">{{ message }}</p>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('cancel')">
            取消
          </button>
          <button type="button" class="btn btn-primary" @click="$emit('confirm')">
            确认
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 确认对话框组件
 * 用于显示确认操作的模态框
 */
export default {
  name: 'ConfirmModal',
  
  props: {
    title: {
      type: String,
      default: '确认操作'
    },
    message: {
      type: String,
      required: true
    }
  },
  
  emits: ['confirm', 'cancel']
}
</script>

<style scoped>
.modal {
  z-index: 1070;
}

.modal-content {
  border: none;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
}
</style>
