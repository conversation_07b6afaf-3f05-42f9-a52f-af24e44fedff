<template>
  <div class="history-data-page">
    <div class="container-fluid">
      <!-- 页面标题 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <h2 class="page-title">
              <i class="bi bi-clock-history me-2"></i>
              表单提交历史
            </h2>
            <div class="d-flex gap-2">
              <button class="btn btn-outline-primary" @click="refreshData">
                <i class="bi bi-arrow-clockwise me-1"></i>
                刷新
              </button>
              <button class="btn btn-primary" @click="showStatistics">
                <i class="bi bi-bar-chart me-1"></i>
                统计信息
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="modern-card">
            <div class="modern-card-header">
              <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>
                筛选条件
              </h5>
            </div>
            <div class="modern-card-body">
              <div class="row g-3">
                <div class="col-md-3">
                  <label class="form-label">公司名称</label>
                  <input
                    type="text"
                    class="modern-input"
                    v-model="filters.companyName"
                    placeholder="输入公司名称"
                    @input="debouncedSearch"
                  >
                </div>
                <div class="col-md-2">
                  <label class="form-label">表单类型</label>
                  <select class="modern-select" v-model="filters.formType" @change="searchData">
                    <option value="">全部类型</option>
                    <option value="安全监测">安全监测</option>
                    <option value="安全测评">安全测评</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <label class="form-label">开始日期</label>
                  <input
                    type="date"
                    class="modern-input"
                    v-model="filters.startDate"
                    @change="searchData"
                  >
                </div>
                <div class="col-md-2">
                  <label class="form-label">结束日期</label>
                  <input
                    type="date"
                    class="modern-input"
                    v-model="filters.endDate"
                    @change="searchData"
                  >
                </div>
                <div class="col-md-2">
                  <label class="form-label">编辑人</label>
                  <select class="modern-select" v-model="filters.editor" @change="searchData">
                    <option value="">全部编辑人</option>
                    <option v-for="editor in uniqueEditors" :key="editor" :value="editor">{{ editor }}</option>
                  </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                  <button class="btn-modern btn-modern-secondary w-100" @click="clearFilters">
                    <i class="bi bi-x-circle"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据列表 -->
      <div class="row">
        <div class="col-12">
          <div class="modern-card">
            <div class="modern-card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>
                提交记录 (共 {{ pagination.total }} 条)
              </h5>
              <div class="d-flex gap-2">
                <button
                  class="btn-modern btn-modern-sm status-badge-danger"
                  @click="deleteSelected"
                  :disabled="selectedItems.length === 0"
                >
                  <i class="bi bi-trash me-1"></i>
                  删除选中 ({{ selectedItems.length }})
                </button>
              </div>
            </div>
            <div class="modern-card-body p-0">
              <!-- 加载状态 -->
              <div v-if="loading" class="loading-container">
                <div class="loading-spinner-modern"></div>
                <p class="text-muted">正在加载数据...</p>
              </div>

              <!-- 数据表格 -->
              <div v-else-if="submissions.length > 0" class="table-responsive">
                <table class="modern-table mb-0 no-wrap-table">
                  <thead class="table-light">
                    <tr>
                      <th width="50" class="text-center">
                        <input
                          type="checkbox"
                          class="form-check-input"
                          v-model="selectAll"
                          @change="toggleSelectAll"
                        >
                      </th>
                      <th width="200" class="text-nowrap">公司名称</th>
                      <th width="100" class="text-center text-nowrap">表单类型</th>
                      <th width="100" class="text-center text-nowrap">编辑人</th>
                      <th width="100" class="text-center text-nowrap">记录日期</th>
                      <th width="80" class="text-center text-nowrap">服务器数</th>
                      <th width="80" class="text-center text-nowrap">组件数</th>
                      <th width="160" class="text-center text-nowrap">
                        <i class="bi bi-plus-circle me-1 text-success"></i>
                        提交时间
                      </th>
                      <th width="160" class="text-center text-nowrap">
                        <i class="bi bi-arrow-repeat me-1 text-warning"></i>
                        更新时间
                      </th>
                      <th width="80" class="text-center text-nowrap">状态</th>
                      <th width="240" class="text-center text-nowrap">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="submission in submissions" :key="submission.id">
                      <td class="text-center">
                        <input
                          type="checkbox"
                          class="form-check-input"
                          :value="submission.id"
                          v-model="selectedItems"
                        >
                      </td>
                      <td class="text-nowrap">
                        <div class="d-flex align-items-center">
                          <strong class="text-truncate" style="max-width: 120px;" :title="getBaseCompanyName(submission.company_name)">
                            {{ getBaseCompanyName(submission.company_name) }}
                          </strong>
                          <span v-if="hasAlias(submission.company_name)"
                                class="alias-badge ms-2"
                                :title="`别名: ${getAlias(submission.company_name)}`">
                            {{ getAlias(submission.company_name) }}
                          </span>
                        </div>
                      </td>
                      <td class="text-center text-nowrap">
                        <span class="status-badge" :class="getFormTypeBadgeClass(submission.form_type)">
                          {{ submission.form_type }}
                        </span>
                      </td>
                      <td class="text-center text-nowrap">
                        <div class="d-flex align-items-center justify-content-center">
                          <i class="bi bi-person-check me-1 text-muted"></i>
                          <span class="text-truncate" style="max-width: 60px;" :title="submission.editor || '未知'">
                            {{ submission.editor || '未知' }}
                          </span>
                        </div>
                      </td>
                      <td class="text-center text-nowrap">{{ formatDate(submission.record_date) }}</td>
                      <td class="text-center text-nowrap">
                        <span class="status-badge status-badge-info">{{ submission.server_count }}</span>
                      </td>
                      <td class="text-center text-nowrap">
                        <span class="status-badge status-badge-success">{{ submission.component_count }}</span>
                      </td>
                      <td class="text-center text-nowrap">
                        <div class="d-flex align-items-center justify-content-center">
                          <i class="bi bi-plus-circle me-1 text-success"></i>
                          <span class="small">{{ formatDateTime(submission.created_at) }}</span>
                        </div>
                      </td>
                      <td class="text-center text-nowrap">
                        <div class="d-flex align-items-center justify-content-center">
                          <i class="bi bi-arrow-repeat me-1"
                             :class="submission.updated_at !== submission.created_at ? 'text-warning' : 'text-muted'"
                             :title="submission.updated_at !== submission.created_at ? '已编辑' : '未编辑'"></i>
                          <span class="small" :class="submission.updated_at !== submission.created_at ? 'text-warning' : 'text-muted'">
                            {{ formatDateTime(submission.updated_at) }}
                          </span>
                        </div>
                      </td>
                      <td class="text-center text-nowrap">
                        <span class="status-badge" :class="getStatusBadgeClass(submission.status)">
                          {{ getStatusText(submission.status) }}
                        </span>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button
                            class="btn btn-outline-primary"
                            @click="viewDetail(submission)"
                            title="查看详情"
                          >
                            <i class="bi bi-eye"></i>
                          </button>
                          <button
                            class="btn btn-outline-info"
                            @click="viewEditHistory(submission)"
                            title="查看编辑日志"
                          >
                            <i class="bi bi-journal-text"></i>
                          </button>
                          <button
                            class="btn btn-outline-warning"
                            @click="editSubmission(submission)"
                            title="编辑表单"
                          >
                            <i class="bi bi-pencil-square"></i>
                          </button>
                          <button
                            class="btn btn-outline-success"
                            @click="showTemplateSelectionForSubmission(submission)"
                            title="选择模板重新生成Excel"
                          >
                            <i class="bi bi-file-earmark-excel"></i>
                          </button>
                          <button
                            class="btn btn-outline-danger"
                            @click="deleteSubmission(submission)"
                            title="删除记录"
                          >
                            <i class="bi bi-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-state">
                <div class="empty-state-icon">
                  <i class="bi bi-inbox"></i>
                </div>
                <h4 class="empty-state-title">暂无数据</h4>
                <p class="empty-state-description">没有找到符合条件的表单提交记录</p>
              </div>
            </div>


          </div>
        </div>
      </div>

      <!-- 分页和每页显示 -->
      <div class="row mt-3" v-if="submissions.length > 0">
        <div class="col-12">
          <div class="modern-card">
            <div class="modern-card-body py-3">
              <div class="d-flex justify-content-between align-items-center">
                <!-- 分页导航 -->
                <nav v-if="pagination.totalPages > 1">
                  <div class="modern-pagination">
                    <button
                      class="modern-pagination-item"
                      :class="{ disabled: pagination.currentPage === 1 }"
                      @click="changePage(pagination.currentPage - 1)"
                    >
                      <i class="bi bi-chevron-left"></i>
                    </button>

                    <button
                      v-for="page in getPageNumbers()"
                      :key="page"
                      class="modern-pagination-item"
                      :class="{ active: page === pagination.currentPage }"
                      @click="changePage(page)"
                    >
                      {{ page }}
                    </button>

                    <button
                      class="modern-pagination-item"
                      :class="{ disabled: pagination.currentPage === pagination.totalPages }"
                      @click="changePage(pagination.currentPage + 1)"
                    >
                      <i class="bi bi-chevron-right"></i>
                    </button>
                  </div>
                </nav>

                <!-- 没有分页时的占位 -->
                <div v-else></div>

                <!-- 每页显示 -->
                <div class="d-flex align-items-center gap-2">
                  <label class="form-label mb-0 text-muted">
                    <i class="bi bi-list-ol me-1"></i>
                    每页显示
                  </label>
                  <select class="modern-select" style="width: auto; min-width: 100px;" v-model="pagination.perPage" @change="searchData">
                    <option value="10">10 条</option>
                    <option value="20">20 条</option>
                    <option value="50">50 条</option>
                    <option value="100">100 条</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情模态框 -->
    <submission-detail-modal
      v-if="selectedSubmission"
      :submission="selectedSubmission"
      @close="selectedSubmission = null"
      @regenerate="regenerateExcel"
    />

    <!-- 统计信息模态框 -->
    <statistics-modal
      v-if="showStatsModal"
      @close="showStatsModal = false"
    />

    <!-- 编辑历史模态框 -->
    <edit-history-modal
      v-if="showEditHistoryModal"
      :submission-id="selectedSubmissionId"
      @close="showEditHistoryModal = false"
      @restore="handleRestore"
    />

    <!-- 模板选择模态框 -->
    <template-selection-modal
      v-if="showTemplateSelectionModal"
      :form-data="selectedSubmissionForTemplate"
      @close="showTemplateSelectionModal = false"
      @confirm="handleTemplateSelectionConfirm"
    />

    <!-- Toast通知 -->
    <toast-notification ref="toast" />
  </div>
</template>

<script>
import SubmissionDetailModal from '@/components/modals/SubmissionDetailModal.vue'
import StatisticsModal from '@/components/modals/StatisticsModal.vue'
import EditHistoryModal from '@/components/modals/EditHistoryModal.vue'
import TemplateSelectionModal from '@/components/modals/TemplateSelectionModal.vue'
import ToastNotification from '@/components/common/ToastNotification.vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { parseFilenameFromContentDisposition, downloadBlob } from '@/utils/fileUtils'
import { debounce } from 'lodash'
import axios from 'axios'

// 统一的API基础URL配置
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

/**
 * 表单提交历史数据页面
 * 提供历史数据的查询、查看、管理功能
 */
export default {
  name: 'HistoryData',
  components: {
    SubmissionDetailModal,
    StatisticsModal,
    EditHistoryModal,
    TemplateSelectionModal,
    ToastNotification,
    PageHeader
  },
  data() {
    return {
      loading: false,
      submissions: [],
      selectedSubmission: null,
      showStatsModal: false,
      showEditHistoryModal: false,
      selectedSubmissionId: null,
      selectedItems: [],
      selectAll: false,

      // 筛选条件
      filters: {
        companyName: '',
        formType: '',
        editor: '',
        startDate: '',
        endDate: ''
      },

      // 分页信息
      pagination: {
        currentPage: 1,
        perPage: 20,
        total: 0,
        totalPages: 0
      },

      // 模板选择相关状态
      showTemplateSelectionModal: false,
      selectedSubmissionForTemplate: null
    }
  },

  computed: {
    /**
     * 防抖搜索函数
     */
    debouncedSearch() {
      return debounce(this.searchData, 500)
    },

    /**
     * 获取唯一的编辑人列表
     */
    uniqueEditors() {
      const editors = this.submissions
        .map(submission => submission.editor)
        .filter(editor => editor && editor.trim() !== '')
      return [...new Set(editors)].sort()
    }
  },

  mounted() {
    this.loadData()
  },

  methods: {
    /**
     * 加载数据
     */
    async loadData() {
      this.loading = true
      try {
        const params = new URLSearchParams({
          page: this.pagination.currentPage,
          per_page: this.pagination.perPage
        })

        // 添加筛选条件
        if (this.filters.companyName) {
          params.append('company_name', this.filters.companyName)
        }
        if (this.filters.formType) {
          params.append('form_type', this.filters.formType)
        }
        if (this.filters.editor) {
          params.append('editor', this.filters.editor)
        }
        if (this.filters.startDate) {
          params.append('start_date', this.filters.startDate)
        }
        if (this.filters.endDate) {
          params.append('end_date', this.filters.endDate)
        }

        const response = await fetch(`${API_BASE_URL}/excel/form_submissions?${params}`)
        const result = await response.json()

        if (result.status === 'success') {
          this.submissions = result.data.submissions
          this.pagination = {
            ...this.pagination,
            ...result.data.pagination
          }
        } else {
          this.showToast(result.message || '加载数据失败', '错误', 'error')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.showToast('加载数据失败', '错误', 'error')
      } finally {
        this.loading = false
      }
    },

    /**
     * 搜索数据
     */
    searchData() {
      this.pagination.currentPage = 1
      this.loadData()
    },

    /**
     * 刷新数据
     */
    refreshData() {
      this.loadData()
    },

    /**
     * 清空筛选条件
     */
    clearFilters() {
      this.filters = {
        companyName: '',
        formType: '',
        editor: '',
        startDate: '',
        endDate: ''
      }
      this.searchData()
    },

    /**
     * 切换页码
     */
    changePage(page) {
      if (page >= 1 && page <= this.pagination.totalPages) {
        this.pagination.currentPage = page
        this.loadData()
      }
    },

    /**
     * 获取页码数组
     */
    getPageNumbers() {
      const pages = []
      const current = this.pagination.currentPage
      const total = this.pagination.totalPages

      // 显示当前页前后2页
      const start = Math.max(1, current - 2)
      const end = Math.min(total, current + 2)

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    },

    /**
     * 全选/取消全选
     */
    toggleSelectAll() {
      if (this.selectAll) {
        this.selectedItems = this.submissions.map(item => item.id)
      } else {
        this.selectedItems = []
      }
    },

    /**
     * 查看详情
     */
    viewDetail(submission) {
      this.selectedSubmission = submission
    },

    /**
     * 查看编辑历史
     */
    viewEditHistory(submission) {
      this.selectedSubmissionId = submission.id
      this.showEditHistoryModal = true
    },

    /**
     * 处理从编辑历史恢复数据
     */
    handleRestore(restoreData) {
      this.showToast('数据恢复成功', '成功', 'success')
      this.showEditHistoryModal = false
      this.loadData() // 刷新列表
    },

    /**
     * 编辑记录
     */
    editSubmission(submission) {
      this.$router.push(`/edit-form-submission/${submission.id}`)
    },

    /**
     * 显示模板选择模态框
     */
    showTemplateSelectionForSubmission(submission) {
      console.log('🎯 显示模板选择模态框 - 表单历史列表')
      console.log('📋 选择的submission:', submission)

      this.selectedSubmissionForTemplate = submission
      this.showTemplateSelectionModal = true
    },

    /**
     * 处理模板选择确认
     */
    async handleTemplateSelectionConfirm(selection) {
      console.log('🎯 模板选择确认 - 表单历史列表:', selection)

      // 关闭模态框
      this.showTemplateSelectionModal = false

      // 执行重新生成Excel
      await this.regenerateExcelWithTemplate(
        this.selectedSubmissionForTemplate,
        selection.templateId,
        selection.templateType,
        selection.templateInfo,
        selection.filename
      )

      // 清理状态
      this.selectedSubmissionForTemplate = null
    },

    /**
     * 使用指定模板重新生成Excel
     */
    async regenerateExcelWithTemplate(submission, templateId, templateType, templateInfo, filename) {
      // 空值检查
      if (!submission || !submission.form_type || !submission.company_name) {
        this.showToast('表单数据不完整，无法重新生成Excel', '错误', 'error')
        return
      }

      const confirmMessage = templateType === submission.form_type
        ? `确定要重新生成 ${submission.company_name} 的Excel文件吗？`
        : `确定要使用 "${templateType}" 模板重新生成 ${submission.company_name} 的Excel文件吗？\n\n注意：模板类型与原始表单类型不同，某些字段可能需要数据映射处理。`

      if (!confirm(confirmMessage)) {
        return
      }

      try {
        // 显示加载状态
        const loadingMessage = templateType === submission.form_type
          ? '正在重新生成Excel文件...'
          : `正在使用 "${templateType}" 模板重新生成Excel文件...`
        this.showToast(loadingMessage, '处理中', 'info')

        // 构建请求参数
        const requestBody = {
          template_id: templateId,
          template_type: templateType,
          filename: filename
        }

        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${submission.id}/regenerate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        })

        // 检查响应类型
        const contentType = response.headers.get('content-type')

        if (contentType && contentType.includes('application/json')) {
          // JSON响应，说明有错误
          const result = await response.json()
          this.showToast(result.message || '重新生成失败', '错误', 'error')
        } else {
          // 文件响应，直接下载
          const blob = await response.blob()

          // 从响应头获取文件名
          const templateSuffix = templateType !== submission.form_type ? `_${templateType}模板` : ''
          const defaultFilename = `${submission.company_name}-运维文档-${templateType}${templateSuffix}_重新生成.xlsx`
          const contentDisposition = response.headers.get('content-disposition')
          const filename = parseFilenameFromContentDisposition(contentDisposition, defaultFilename)

          // 下载文件
          downloadBlob(blob, filename)

          const successMessage = templateType !== submission.form_type
            ? `Excel文件已使用 "${templateType}" 模板重新生成并下载成功`
            : 'Excel文件重新生成并下载成功'

          this.showToast(successMessage, '成功', 'success')
        }
      } catch (error) {
        console.error('重新生成Excel失败:', error)
        this.showToast('重新生成Excel失败', '错误', 'error')
      }
    },

    /**
     * 重新生成Excel（保留原方法作为兼容）
     */
    async regenerateExcel(submission) {
      // 空值检查
      if (!submission || !submission.form_type || !submission.company_name) {
        this.showToast('表单数据不完整，无法重新生成Excel', '错误', 'error')
        return
      }

      if (!confirm(`确定要重新生成 ${submission.company_name} 的Excel文件吗？`)) {
        return
      }

      try {
        // 显示加载状态
        this.showToast('正在重新生成Excel文件...', '处理中', 'info')

        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${submission.id}/regenerate`, {
          method: 'POST'
        })

        // 检查响应类型
        const contentType = response.headers.get('content-type')

        if (contentType && contentType.includes('application/json')) {
          // JSON响应，说明有错误
          const result = await response.json()
          this.showToast(result.message || '重新生成失败', '错误', 'error')
        } else {
          // 文件响应，直接下载
          const blob = await response.blob()

          // 从响应头获取文件名
          const defaultFilename = `${submission.company_name}-运维文档-${submission.form_type}_重新生成.xlsx`
          const contentDisposition = response.headers.get('content-disposition')
          const filename = parseFilenameFromContentDisposition(contentDisposition, defaultFilename)

          console.log('前端下载 - 最终使用的文件名:', filename)

          // 下载文件
          downloadBlob(blob, filename)

          this.showToast('Excel文件重新生成并下载成功', '成功', 'success')
        }
      } catch (error) {
        console.error('重新生成Excel失败:', error)
        this.showToast('重新生成Excel失败', '错误', 'error')
      }
    },

    /**
     * 删除单个记录
     */
    async deleteSubmission(submission) {
      if (!confirm(`确定要删除 ${submission.company_name} 的提交记录吗？此操作不可恢复。`)) {
        return
      }

      try {
        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${submission.id}`, {
          method: 'DELETE'
        })
        const result = await response.json()

        if (result.status === 'success') {
          this.showToast('记录删除成功', '成功', 'success')
          this.loadData() // 刷新列表
        } else {
          this.showToast(result.message || '删除失败', '错误', 'error')
        }
      } catch (error) {
        console.error('删除记录失败:', error)
        this.showToast('删除记录失败', '错误', 'error')
      }
    },

    /**
     * 批量删除选中记录
     */
    async deleteSelected() {
      if (this.selectedItems.length === 0) {
        return
      }

      if (!confirm(`确定要删除选中的 ${this.selectedItems.length} 条记录吗？此操作不可恢复。`)) {
        return
      }

      try {
        const deletePromises = this.selectedItems.map(id =>
          fetch(`${API_BASE_URL}/excel/form_submissions/${id}`, { method: 'DELETE' })
        )

        await Promise.all(deletePromises)

        this.showToast(`成功删除 ${this.selectedItems.length} 条记录`, '成功', 'success')
        this.selectedItems = []
        this.selectAll = false
        this.loadData() // 刷新列表
      } catch (error) {
        console.error('批量删除失败:', error)
        this.showToast('批量删除失败', '错误', 'error')
      }
    },

    /**
     * 显示统计信息
     */
    showStatistics() {
      this.showStatsModal = true
    },

    /**
     * 获取表单类型徽章样式
     */
    getFormTypeBadgeClass(formType) {
      const classes = {
        '安全监测': 'status-badge-info',
        '安全测评': 'status-badge-success',
        '应用加固': 'status-badge-warning'
      }
      return classes[formType] || 'status-badge-secondary'
    },

    /**
     * 获取状态徽章样式
     */
    getStatusBadgeClass(status) {
      const classes = {
        'success': 'status-badge-success',
        'failed': 'status-badge-danger',
        'processing': 'status-badge-warning'
      }
      return classes[status] || 'status-badge-secondary'
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const texts = {
        'success': '成功',
        'failed': '失败',
        'processing': '处理中'
      }
      return texts[status] || '未知'
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString('zh-CN')
    },

    /**
     * 格式化日期时间
     */
    formatDateTime(dateString) {
      if (!dateString) return '-'

      // 如果是 YYYY-MM-DD HH:MM:SS 格式（已经是北京时间），直接显示
      if (typeof dateString === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateString)) {
        return dateString
      }

      // 否则按照原来的方式处理
      return new Date(dateString).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    /**
     * 显示Toast通知
     */
    showToast(message, title = '提示', type = 'info') {
      this.$refs.toast.showToast(message, title, type)
    },

    /**
     * 检查公司名称是否包含别名
     */
    hasAlias(companyName) {
      return companyName && companyName.includes('-') && companyName.split('-').length > 1
    },

    /**
     * 获取基础公司名称（去除别名部分）
     */
    getBaseCompanyName(companyName) {
      if (!companyName) return ''
      if (this.hasAlias(companyName)) {
        return companyName.split('-')[0]
      }
      return companyName
    },

    /**
     * 获取别名部分
     */
    getAlias(companyName) {
      if (!companyName || !this.hasAlias(companyName)) return ''
      const parts = companyName.split('-')
      return parts.slice(1).join('-')
    }
  }
}
</script>

<style scoped>
/* ==================== 页面基础样式 ==================== */
.history-data-page {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 1rem;
}

.page-title {
  color: #1e40af;
  font-weight: 600;
  font-size: 1.5rem;
}

/* ==================== 表格优化 ==================== */
.table-hover tbody tr:hover {
  background-color: rgba(30, 64, 175, 0.05);
}

/* ==================== 别名徽章 ==================== */
.alias-badge {
  background-color: #6366f1;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* ==================== 时间信息样式 ==================== */
.time-info.is-updated {
  color: #f59e0b;
  font-weight: 500;
}

/* ==================== 操作按钮优化 ==================== */
.btn-group-sm .btn {
  padding: 0.375rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 4px;
  margin: 0 1px;
  transition: all 0.15s ease;
}

.btn-group-sm .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

/* ==================== 响应式优化 ==================== */
@media (max-width: 768px) {
  .history-data-page {
    padding: 0.5rem;
  }

  .page-title {
    font-size: 1.25rem;
  }

  .btn-group-sm .btn {
    padding: 0.25rem 0.375rem;
    font-size: 0.7rem;
  }

  .btn-group-sm .btn:hover {
    transform: none;
  }
}
</style>
