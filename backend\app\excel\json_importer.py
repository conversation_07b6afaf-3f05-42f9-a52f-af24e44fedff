#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON数据导入器模块

该模块提供了从JSON格式数据导入表单信息的功能，支持多种表单类型：
- 🔒 安全测评：企业安全评估相关信息
- 🛡️ 安全监测：安全监控系统部署信息
- 🔧 应用加固：应用程序安全加固信息

主要功能：
✨ JSON数据解析和验证
🎯 表单类型自动检测
🧹 数据清理和标准化
💾 数据库记录创建
📊 导入统计和摘要

作者: 系统管理员
创建时间: 2024
最后更新: 2025-06-15
"""

import json
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional

from flask import current_app
from app.models.models import FormSubmission, db


# ==================== 常量定义 ====================

# 支持的表单类型
FORM_TYPES = {
    '安全测评': {
        'name': '安全测评',
        'icon': '🔒',
        'description': '企业安全评估相关信息',
        'required_fields': ['公司名称', '记录日期', '部署包版本'],
        'optional_fields': ['客户标识', '管理员账号', '管理员密码']
    },
    '安全监测': {
        'name': '安全监测',
        'icon': '🛡️',
        'description': '安全监控系统部署信息',
        'required_fields': ['公司名称', '记录日期', '前端版本', '后端版本'],
        'optional_fields': ['客户标识']
    },
    '应用加固': {
        'name': '应用加固',
        'icon': '🔧',
        'description': '应用程序安全加固信息',
        'required_fields': ['公司名称', '记录日期', '客户'],
        'optional_fields': ['客户标识', '平台访问地址', '管理员信息']
    }
}

# 基础必需字段
BASE_REQUIRED_FIELDS = ['公司名称', '记录日期']

# 日期格式
DATE_FORMATS = ['%Y-%m-%d', '%Y%m%d', '%Y/%m/%d']


# ==================== 主要类定义 ====================

class JsonImporter:
    """
    🚀 JSON数据导入器

    这是一个功能强大的JSON数据导入器，专门用于处理表单提交历史记录。
    支持多种表单类型的自动识别、数据验证和标准化处理。

    特性：
    - 🎯 智能表单类型检测
    - 🧹 自动数据清理和标准化
    - ✅ 全面的数据验证
    - 📊 详细的导入统计
    - 🔄 灵活的错误处理

    使用示例：
        >>> importer = JsonImporter('{"公司名称": "测试公司", "文档后缀": "安全监测"}')
        >>> result = importer.parse_json()
        >>> if result:
        ...     submission = importer.create_form_submission()
    """

    def __init__(self, json_data: str, original_filename: str = None):
        """
        🔧 初始化JSON导入器

        Args:
            json_data (str): 待解析的JSON字符串数据
            original_filename (str, optional): 原始文件名，用于日志记录

        Attributes:
            json_data (str): 原始JSON数据
            original_filename (str): 文件名
            parsed_data (dict): 解析后的数据
            form_type (str): 检测到的表单类型
            errors (list): 错误信息列表
            warnings (list): 警告信息列表
        """
        self.json_data = json_data
        self.original_filename = original_filename or 'imported_data.json'
        self.parsed_data = None
        self.form_type = None
        self.errors = []
        self.warnings = []

        # 记录初始化信息
        self._log_info(f"🎯 初始化JSON导入器，文件名: {self.original_filename}")
        self._log_info(f"📊 数据大小: {len(json_data)} 字符")

    # ==================== 辅助方法 ====================

    def _log_info(self, message: str) -> None:
        """记录信息日志"""
        try:
            current_app.logger.info(message)
        except RuntimeError:
            print(message)

    def _log_error(self, message: str, exception: Exception = None) -> None:
        """记录错误日志"""
        try:
            current_app.logger.error(message)
            if exception:
                current_app.logger.error(traceback.format_exc())
        except RuntimeError:
            print(f"❌ {message}")
            if exception:
                print(f"❌ 错误堆栈: {traceback.format_exc()}")

    def _log_warning(self, message: str) -> None:
        """记录警告日志"""
        try:
            current_app.logger.warning(message)
        except RuntimeError:
            print(f"⚠️ {message}")

    # ==================== 核心方法 ====================

    def parse_json(self) -> Optional[Dict[str, Any]]:
        """
        🔍 解析JSON数据

        这是核心解析方法，负责：
        1. 解析JSON字符串
        2. 检测表单类型
        3. 验证必要字段
        4. 数据清理和标准化

        Returns:
            Optional[Dict[str, Any]]: 解析成功返回数据字典，失败返回None
        """
        self._log_info("� 开始解析JSON数据...")

        try:
            # 第一步：解析JSON字符串
            self._log_info(f"📝 JSON数据预览: {self.json_data[:200]}...")
            data = json.loads(self.json_data)

            # 验证数据结构
            if not isinstance(data, dict):
                error_msg = "❌ JSON数据必须是一个对象"
                self.errors.append(error_msg)
                self._log_error(error_msg)
                return None

            self._log_info(f"✅ JSON解析成功，包含 {len(data)} 个字段")
            self._log_info(f"� 数据字段: {list(data.keys())}")

            # 第二步：检测表单类型
            if not self._detect_form_type(data):
                return None

            # 第三步：验证基础必需字段
            if not self._validate_required_fields(data):
                return None

            # 第四步：数据清理和标准化
            self.parsed_data = self._clean_and_standardize_data(data)

            # 第五步：最终验证
            self._final_validation()

            form_icon = FORM_TYPES.get(self.form_type, {}).get('icon', '📄')
            self._log_info(f"🎉 JSON解析完成！表单类型: {form_icon} {self.form_type}")

            return self.parsed_data

        except json.JSONDecodeError as e:
            error_msg = f"JSON格式错误: {str(e)}"
            self.errors.append(error_msg)
            self._log_error(error_msg, e)
            return None

        except Exception as e:
            error_msg = f"解析JSON数据失败: {str(e)}"
            self.errors.append(error_msg)
            self._log_error(error_msg, e)
            return None

    def _detect_form_type(self, data: Dict[str, Any]) -> bool:
        """🎯 检测表单类型"""
        form_type = data.get('文档后缀', '').strip()

        if not form_type:
            error_msg = "❌ JSON数据中缺少'文档后缀'字段"
            self.errors.append(error_msg)
            self._log_error(f"{error_msg}，可用字段: {list(data.keys())}")
            return False

        if form_type not in FORM_TYPES:
            warning_msg = f"⚠️ 未知的表单类型: {form_type}，将继续处理"
            self.warnings.append(warning_msg)
            self._log_warning(warning_msg)

        self.form_type = form_type
        form_info = FORM_TYPES.get(form_type, {})
        icon = form_info.get('icon', '📄')
        description = form_info.get('description', '未知类型')

        self._log_info(f"🎯 检测到表单类型: {icon} {form_type} - {description}")
        return True

    def _validate_required_fields(self, data: Dict[str, Any]) -> bool:
        """✅ 验证必需字段"""
        missing_fields = []

        for field in BASE_REQUIRED_FIELDS:
            if field not in data or not str(data[field]).strip():
                missing_fields.append(field)

        if missing_fields:
            error_msg = f"❌ 缺少必要字段: {', '.join(missing_fields)}"
            self.errors.append(error_msg)
            self._log_error(error_msg)
            return False

        self._log_info("✅ 基础必需字段验证通过")
        return True

    def _final_validation(self) -> None:
        """🔍 最终验证"""
        if not self.parsed_data:
            return

        # 确保表单类型一致性
        parsed_form_type = self.parsed_data.get('文档后缀', '')
        if self.form_type != parsed_form_type:
            self._log_warning(f"⚠️ 表单类型不一致！当前: {self.form_type}, 解析后: {parsed_form_type}")
            self.form_type = parsed_form_type
            self._log_info(f"✅ 已修正表单类型为: {self.form_type}")
    
    def _clean_and_standardize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        🧹 清理和标准化数据

        执行以下清理操作：
        1. 📅 标准化日期格式
        2. 📋 确保列表字段的正确性
        3. 🔧 处理组件信息
        4. 📊 生成统计信息

        Args:
            data: 原始数据字典

        Returns:
            Dict[str, Any]: 清理后的数据字典
        """
        self._log_info("🧹 开始数据清理和标准化...")
        cleaned_data = data.copy()

        # 第一步：标准化日期格式
        self._standardize_date_format(cleaned_data)

        # 第二步：确保必要的列表字段
        self._ensure_list_fields(cleaned_data)

        # 第三步：处理组件信息
        self._process_component_info(cleaned_data)

        # 第四步：生成统计信息
        self._generate_statistics(cleaned_data)

        self._log_info("✅ 数据清理和标准化完成")
        return cleaned_data

    def _standardize_date_format(self, data: Dict[str, Any]) -> None:
        """📅 标准化日期格式"""
        if '记录日期' not in data:
            return

        record_date = data['记录日期']
        if not isinstance(record_date, str):
            return

        # 尝试不同的日期格式
        for date_format in DATE_FORMATS:
            try:
                dt = datetime.strptime(record_date, date_format)
                standardized_date = dt.strftime('%Y-%m-%d')
                if standardized_date != record_date:
                    data['记录日期'] = standardized_date
                    self._log_info(f"📅 日期格式已标准化: {record_date} → {standardized_date}")
                return
            except ValueError:
                continue

        # 如果所有格式都失败，记录警告
        warning_msg = f"⚠️ 日期格式可能不正确: {record_date}"
        self.warnings.append(warning_msg)
        self._log_warning(warning_msg)

    def _ensure_list_fields(self, data: Dict[str, Any]) -> None:
        """📋 确保列表字段的正确性"""
        list_fields = {
            '服务器信息': '🖥️ 服务器信息',
            '维护记录': '📝 维护记录',
            '部署应用': '🚀 部署应用'
        }

        for field, description in list_fields.items():
            if field not in data:
                data[field] = []
                self._log_info(f"➕ 创建空的{description}列表")
            elif not isinstance(data[field], list):
                data[field] = []
                self._log_warning(f"⚠️ {description}不是列表格式，已重置为空列表")

        # 确保字典字段
        dict_fields = {
            '组件端口': '🔌 组件端口',
            'selectedComponentDetails': '🔧 组件详细信息'
        }

        for field, description in dict_fields.items():
            if field not in data:
                data[field] = {}
                self._log_info(f"➕ 创建空的{description}字典")
            elif not isinstance(data[field], dict):
                data[field] = {}
                self._log_warning(f"⚠️ {description}不是字典格式，已重置为空字典")

    def _process_component_info(self, data: Dict[str, Any]) -> None:
        """🔧 处理组件信息"""
        # 处理重复的组件信息字段
        if '组件信息' in data and 'selectedComponentDetails' in data:
            if not data['selectedComponentDetails'] and data['组件信息']:
                self._migrate_component_info_to_selected_details(data)
                self._log_info("🔄 已将组件信息迁移到selectedComponentDetails")

            # 删除重复字段
            del data['组件信息']
            self._log_info("🗑️ 已清理重复的组件信息字段")

    def _generate_statistics(self, data: Dict[str, Any]) -> None:
        """📊 生成统计信息"""
        server_count = len(data.get('服务器信息', []))
        component_count = len(data.get('部署应用', []))
        maintenance_count = len(data.get('维护记录', []))

        stats_msg = f"📊 数据统计: {server_count}台服务器, {component_count}个组件, {maintenance_count}条维护记录"
        self._log_info(stats_msg)

    def _migrate_component_info_to_selected_details(self, data: Dict[str, Any]) -> None:
        """
        🔄 将组件信息迁移到selectedComponentDetails格式

        这个方法处理旧版本的组件信息格式，将其转换为新的标准格式。
        """
        component_info = data.get('组件信息', {})
        if not component_info:
            return

        selected_details = {}
        component_ports = {}

        for comp_name, comp_data in component_info.items():
            # 获取端口信息
            port = comp_data.get('port', comp_data.get('default_port', ''))

            # 构建组件详细信息
            selected_details[comp_name] = {
                'name': comp_name,
                'version': comp_data.get('version', ''),
                'port': port,
                'count': comp_data.get('count', '1'),
                'defaultPort': port
            }

            # 构建端口映射
            if port and port != '无':
                component_ports[comp_name] = port

        # 更新数据
        data['selectedComponentDetails'] = selected_details

        # 更新部署应用列表（如果为空）
        if not data.get('部署应用'):
            data['部署应用'] = list(component_info.keys())
            self._log_info(f"🚀 从组件信息中提取部署应用: {len(component_info)} 个")

        # 更新组件端口映射（如果为空）
        if not data.get('组件端口'):
            data['组件端口'] = component_ports
            self._log_info(f"🔌 从组件信息中提取端口映射: {len(component_ports)} 个")

    # ==================== 验证方法 ====================

    def validate_data(self, data: Dict[str, Any]) -> List[str]:
        """
        ✅ 验证数据完整性

        执行全面的数据验证，包括：
        1. 基础字段验证
        2. 表单类型特定字段验证
        3. 数据格式验证

        Args:
            data: 待验证的数据字典

        Returns:
            List[str]: 验证错误列表
        """
        self._log_info("🔍 开始数据验证...")
        validation_errors = []

        # 基础字段验证
        basic_errors = self._validate_basic_fields(data)
        validation_errors.extend(basic_errors)

        # 表单类型特定验证
        self._validate_form_type_specific_fields(data)

        if validation_errors:
            self._log_error(f"❌ 发现 {len(validation_errors)} 个验证错误")
        else:
            self._log_info("✅ 数据验证通过")

        return validation_errors

    def _validate_basic_fields(self, data: Dict[str, Any]) -> List[str]:
        """验证基础字段"""
        errors = []

        for field in BASE_REQUIRED_FIELDS:
            if not data.get(field, '').strip():
                errors.append(f"{field}不能为空")

        return errors

    def _validate_form_type_specific_fields(self, data: Dict[str, Any]) -> None:
        """验证表单类型特定字段"""
        form_type = data.get('文档后缀', '')
        form_config = FORM_TYPES.get(form_type, {})

        if not form_config:
            return

        # 检查推荐字段
        recommended_fields = form_config.get('required_fields', [])
        for field in recommended_fields:
            if field not in BASE_REQUIRED_FIELDS and not data.get(field, '').strip():
                warning_msg = f"💡 建议填写字段: {field}"
                self.warnings.append(warning_msg)
                self._log_warning(warning_msg)
    
    # ==================== 数据库操作方法 ====================

    def create_form_submission(self) -> Optional[FormSubmission]:
        """
        💾 创建表单提交记录

        这是核心的数据库操作方法，负责：
        1. 最终数据验证
        2. 创建FormSubmission记录
        3. 保存到数据库
        4. 计算统计信息

        Returns:
            Optional[FormSubmission]: 成功返回提交记录，失败返回None
        """
        self._log_info("💾 开始创建表单提交记录...")

        # 前置检查
        if not self.parsed_data:
            error_msg = "❌ 没有解析的数据"
            self.errors.append(error_msg)
            self._log_error(error_msg)
            return None

        # 最终验证
        validation_errors = self.validate_data(self.parsed_data)
        self.errors.extend(validation_errors)

        # 记录详细信息
        self._log_submission_details()

        # 检查是否有阻止性错误
        if self.errors:
            error_msg = f"❌ 由于验证错误，无法创建记录: {', '.join(self.errors)}"
            self._log_error(error_msg)
            return None

        # 创建数据库记录
        return self._create_database_record()

    def _log_submission_details(self) -> None:
        """记录提交详细信息"""
        form_icon = FORM_TYPES.get(self.form_type, {}).get('icon', '📄')

        details = [
            f"📋 表单类型: {form_icon} {self.form_type}",
            f"🏢 公司名称: {self.parsed_data.get('公司名称', '')}",
            f"📅 记录日期: {self.parsed_data.get('记录日期', '')}",
            f"🖥️ 服务器数量: {len(self.parsed_data.get('服务器信息', []))}",
            f"🚀 组件数量: {len(self.parsed_data.get('部署应用', []))}",
            f"❌ 错误数量: {len(self.errors)}",
            f"⚠️ 警告数量: {len(self.warnings)}"
        ]

        for detail in details:
            self._log_info(detail)

    def _create_database_record(self) -> Optional[FormSubmission]:
        """创建数据库记录"""
        try:
            # 解析记录日期
            record_date_str = self.parsed_data.get('记录日期', datetime.now().strftime('%Y-%m-%d'))
            record_date = datetime.strptime(record_date_str, '%Y-%m-%d').date()

            # 创建FormSubmission记录
            submission = FormSubmission(
                company_name=self.parsed_data.get('公司名称', ''),
                form_type=self.form_type,
                record_date=record_date,
                created_by='JSON导入',
                status='success'
            )

            # 设置表单数据
            submission.set_form_data(self.parsed_data)

            # 计算统计信息
            submission.calculate_statistics()

            # 保存到数据库
            db.session.add(submission)
            db.session.commit()

            success_msg = f"🎉 JSON导入成功！记录ID: {submission.id}"
            self._log_info(success_msg)

            return submission

        except Exception as e:
            # 回滚事务
            db.session.rollback()

            error_msg = f"创建数据库记录失败: {str(e)}"
            self.errors.append(error_msg)
            self._log_error(error_msg, e)

            return None
    
    # ==================== 摘要和报告方法 ====================

    def get_import_summary(self) -> Dict[str, Any]:
        """
        📊 获取导入摘要

        生成详细的导入摘要信息，包括：
        - 表单基本信息
        - 统计数据
        - 错误和警告信息
        - 导入状态

        Returns:
            Dict[str, Any]: 导入摘要字典
        """
        self._log_info("📊 生成导入摘要...")

        # 基本信息
        form_type = self.form_type or '未知'
        company_name = self.parsed_data.get('公司名称', '') if self.parsed_data else ''
        record_date = self.parsed_data.get('记录日期', '') if self.parsed_data else ''

        # 统计信息
        server_count = len(self.parsed_data.get('服务器信息', [])) if self.parsed_data else 0
        component_count = len(self.parsed_data.get('部署应用', [])) if self.parsed_data else 0
        maintenance_count = len(self.parsed_data.get('维护记录', [])) if self.parsed_data else 0

        # 状态信息
        has_errors = len(self.errors) > 0
        has_warnings = len(self.warnings) > 0
        success = not has_errors

        # 生成摘要
        summary = {
            'form_type': form_type,
            'company_name': company_name,
            'record_date': record_date,
            'server_count': server_count,
            'component_count': component_count,
            'maintenance_count': maintenance_count,
            'errors': self.errors,
            'warnings': self.warnings,
            'error_count': len(self.errors),
            'warning_count': len(self.warnings),
            'success': success,
            'status': '成功' if success else '失败',
            'message': self._generate_summary_message(success, has_warnings)
        }

        # 记录摘要信息
        form_icon = FORM_TYPES.get(form_type, {}).get('icon', '📄')
        status_icon = '✅' if success else '❌'

        self._log_info(f"{status_icon} 导入摘要: {form_icon} {form_type} | {company_name} | {server_count}台服务器 | {component_count}个组件")

        return summary

    def _generate_summary_message(self, success: bool, has_warnings: bool) -> str:
        """生成摘要消息"""
        if success:
            if has_warnings:
                return f"✅ 导入成功，但有 {len(self.warnings)} 个警告"
            else:
                return "🎉 导入完全成功！"
        else:
            return f"❌ 导入失败，发现 {len(self.errors)} 个错误"

    # ==================== 类方法 ====================

    @classmethod
    def import_form_data(cls, form_data: Dict[str, Any], form_type: str) -> Dict[str, Any]:
        """
        🚀 直接导入表单数据（便捷方法）

        这是一个便捷的类方法，用于直接导入已经是字典格式的表单数据，
        无需先转换为JSON字符串。适用于程序内部调用。

        Args:
            form_data (Dict[str, Any]): 表单数据字典
            form_type (str): 表单类型

        Returns:
            Dict[str, Any]: 导入结果字典，包含状态、消息和数据

        Example:
            >>> result = JsonImporter.import_form_data(
            ...     {"公司名称": "测试公司", "文档后缀": "安全监测"},
            ...     "安全监测"
            ... )
            >>> print(result['status'])  # 'success' or 'error'
        """
        try:
            # 创建临时的JsonImporter实例
            company_name = form_data.get('公司名称', 'unknown')
            json_data = json.dumps(form_data, ensure_ascii=False)
            importer = cls(json_data, f"{company_name}_direct_import.json")

            # 记录开始信息
            form_icon = FORM_TYPES.get(form_type, {}).get('icon', '📄')
            importer._log_info(f"🚀 开始直接导入表单数据: {form_icon} {form_type}")

            # 直接设置解析后的数据，跳过JSON解析步骤
            importer.parsed_data = form_data
            importer.form_type = form_type

            # 执行标准的处理流程
            importer.parsed_data = importer._clean_and_standardize_data(form_data)
            validation_errors = importer.validate_data(importer.parsed_data)
            importer.errors.extend(validation_errors)

            # 创建表单提交记录
            submission = importer.create_form_submission()

            # 生成返回结果
            if submission:
                success_result = {
                    'status': 'success',
                    'message': '🎉 表单数据导入成功',
                    'data': {
                        'submission_id': submission.id,
                        'company_name': submission.company_name,
                        'form_type': submission.form_type,
                        'record_date': submission.record_date.strftime('%Y-%m-%d'),
                        'server_count': submission.server_count,
                        'component_count': submission.component_count,
                        'warnings': importer.warnings
                    }
                }
                importer._log_info(f"✅ 直接导入成功，记录ID: {submission.id}")
                return success_result
            else:
                error_result = {
                    'status': 'error',
                    'message': '❌ 表单数据导入失败',
                    'errors': importer.errors,
                    'warnings': importer.warnings
                }
                importer._log_error("❌ 直接导入失败")
                return error_result

        except Exception as e:
            error_msg = f"表单数据导入失败: {str(e)}"

            # 记录错误
            try:
                current_app.logger.error(error_msg)
                current_app.logger.error(traceback.format_exc())
            except RuntimeError:
                print(f"❌ {error_msg}")
                print(f"❌ 错误堆栈: {traceback.format_exc()}")

            return {
                'status': 'error',
                'message': f'❌ {error_msg}',
                'errors': [error_msg]
            }


# ==================== 模块级别的辅助函数 ====================

def get_supported_form_types() -> Dict[str, Dict[str, Any]]:
    """
    📋 获取支持的表单类型列表

    Returns:
        Dict[str, Dict[str, Any]]: 支持的表单类型配置
    """
    return FORM_TYPES.copy()


def validate_json_structure(json_data: str) -> Dict[str, Any]:
    """
    🔍 验证JSON结构（不执行完整导入）

    Args:
        json_data (str): JSON字符串

    Returns:
        Dict[str, Any]: 验证结果
    """
    try:
        data = json.loads(json_data)

        if not isinstance(data, dict):
            return {
                'valid': False,
                'error': 'JSON数据必须是一个对象'
            }

        form_type = data.get('文档后缀', '')
        if not form_type:
            return {
                'valid': False,
                'error': '缺少文档后缀字段'
            }

        return {
            'valid': True,
            'form_type': form_type,
            'company_name': data.get('公司名称', ''),
            'field_count': len(data),
            'has_servers': '服务器信息' in data,
            'has_components': '部署应用' in data
        }

    except json.JSONDecodeError as e:
        return {
            'valid': False,
            'error': f'JSON格式错误: {str(e)}'
        }
    except Exception as e:
        return {
            'valid': False,
            'error': f'验证失败: {str(e)}'
        }
