<template>
  <div class="api-docs">
    <div class="container-fluid">
      <!-- 页面标题 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h2 class="mb-1">
                <i class="bi bi-book me-2"></i>
                API 接口文档
              </h2>
              <p class="text-muted mb-0">运维文档生成系统 RESTful API 接口文档与在线调试工具</p>
            </div>
            <div class="d-flex align-items-center">
              <button class="btn btn-outline-secondary btn-sm me-2" @click="showSettings = !showSettings">
                <i class="bi bi-gear me-1"></i>
                设置
              </button>
              <span class="badge bg-success">v{{ apiInfo.version }}</span>
            </div>
          </div>

          <!-- 设置面板 -->
          <div v-if="showSettings" class="card mt-3">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-gear me-2"></i>
                API调试设置
              </h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <label class="form-label">后端服务地址</label>
                  <div class="input-group">
                    <input
                      type="text"
                      class="form-control"
                      v-model="apiBaseUrl"
                      placeholder="http://127.0.0.1:5000"
                    >
                    <button class="btn btn-outline-primary" @click="saveSettings">
                      <i class="bi bi-check me-1"></i>
                      保存
                    </button>
                  </div>
                  <small class="text-muted">设置API调试时使用的后端服务地址</small>
                </div>
                <div class="col-md-6">
                  <label class="form-label">当前状态</label>
                  <div class="d-flex align-items-center">
                    <span class="badge me-2" :class="connectionStatus.class">
                      <i class="bi" :class="connectionStatus.icon"></i>
                      {{ connectionStatus.text }}
                    </span>
                    <button class="btn btn-outline-info btn-sm me-2" @click="testConnection">
                      <i class="bi bi-arrow-clockwise me-1"></i>
                      测试连接
                    </button>
                    <button class="btn btn-outline-warning btn-sm me-2" @click="debugAuth">
                      <i class="bi bi-bug me-1"></i>
                      调试认证
                    </button>
                    <button class="btn btn-outline-success btn-sm" @click="debugPermissions">
                      <i class="bi bi-shield-check me-1"></i>
                      调试权限
                    </button>
                  </div>
                  <small class="text-muted">{{ connectionStatus.message }}</small>
                </div>
              </div>

              <!-- 权限概览 -->
              <div class="row mt-3">
                <div class="col-12">
                  <label class="form-label">权限概览</label>
                  <div v-if="!permissionsLoaded" class="d-flex align-items-center">
                    <span class="badge bg-secondary me-2">
                      <i class="bi bi-hourglass-split me-1"></i>
                      加载中...
                    </span>
                    <small class="text-muted">正在获取用户权限信息</small>
                  </div>
                  <div v-else class="d-flex align-items-center">
                    <span class="badge me-2" :class="isAdmin ? 'bg-success' : 'bg-info'">
                      <i class="bi me-1" :class="isAdmin ? 'bi-shield-check' : 'bi-person'"></i>
                      {{ isAdmin ? '管理员' : '普通用户' }}
                    </span>
                    <small class="text-muted">
                      拥有 {{ userPermissions.length }} 个权限
                      <span v-if="isAdmin">（管理员拥有所有权限）</span>
                    </small>
                  </div>
                  <div class="mt-2">
                    <details>
                      <summary class="text-muted" style="cursor: pointer;">
                        <small>查看详细权限</small>
                      </summary>
                      <div class="mt-2">
                        <span v-for="permission in userPermissions" :key="permission" class="badge bg-light text-dark me-1 mb-1">
                          {{ permission }}
                        </span>
                      </div>
                    </details>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <!-- 左侧导航 -->
        <div class="col-md-3">
          <div class="card sticky-top" style="top: 20px;">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-list me-2"></i>
                接口分类
              </h6>
            </div>
            <div class="card-body p-0">
              <div class="list-group list-group-flush">
                <a 
                  v-for="category in categories" 
                  :key="category.name"
                  href="#"
                  class="list-group-item list-group-item-action"
                  :class="{ active: selectedCategory === category.name }"
                  @click.prevent="selectCategory(category.name)"
                >
                  <div class="d-flex justify-content-between align-items-center">
                    <span>{{ category.name }}</span>
                    <span class="badge bg-secondary">{{ category.endpoints.length }}</span>
                  </div>
                  <small class="text-muted">{{ category.description }}</small>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧内容 -->
        <div class="col-md-9">
          <!-- 接口列表 -->
          <div v-if="selectedCategoryData" class="mb-4">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">{{ selectedCategoryData.name }}</h5>
                <small class="text-muted">{{ selectedCategoryData.description }}</small>
              </div>
              <div class="card-body p-0">
                <div class="accordion" :id="`accordion-${selectedCategory}`">
                  <div 
                    v-for="(endpoint, index) in selectedCategoryData.endpoints" 
                    :key="index"
                    class="accordion-item"
                  >
                    <h2 class="accordion-header">
                      <button 
                        class="accordion-button collapsed" 
                        type="button" 
                        data-bs-toggle="collapse" 
                        :data-bs-target="`#collapse-${selectedCategory}-${index}`"
                      >
                        <span class="method-badge me-3" :class="getMethodClass(endpoint.method)">
                          {{ endpoint.method }}
                        </span>
                        <div>
                          <div class="fw-bold">{{ endpoint.summary }}</div>
                          <small class="text-muted">{{ endpoint.path }}</small>
                        </div>
                      </button>
                    </h2>
                    <div 
                      :id="`collapse-${selectedCategory}-${index}`" 
                      class="accordion-collapse collapse"
                      :data-bs-parent="`#accordion-${selectedCategory}`"
                    >
                      <div class="accordion-body">
                        <!-- 接口详情 -->
                        <div class="endpoint-details">
                          <!-- 基本信息 -->
                          <div class="mb-4">
                            <h6>接口描述</h6>
                            <p>{{ endpoint.description }}</p>
                            
                            <div class="row">
                              <div class="col-md-6">
                                <strong>请求方法:</strong>
                                <span class="method-badge ms-2" :class="getMethodClass(endpoint.method)">
                                  {{ endpoint.method }}
                                </span>
                              </div>
                              <div class="col-md-6">
                                <strong>请求路径:</strong>
                                <code>{{ endpoint.path }}</code>
                              </div>
                            </div>

                            <!-- 权限要求显示 -->
                            <div class="row mt-2">
                              <div class="col-md-6">
                                <strong>权限要求:</strong>
                                <span v-if="getEndpointPermission(endpoint)" class="badge ms-2" :class="hasPermission(getEndpointPermission(endpoint)) ? 'bg-success' : 'bg-danger'">
                                  <i class="bi me-1" :class="hasPermission(getEndpointPermission(endpoint)) ? 'bi-check-circle' : 'bi-x-circle'"></i>
                                  {{ getEndpointPermission(endpoint) }}
                                </span>
                                <span v-else class="badge bg-info ms-2">
                                  <i class="bi bi-unlock me-1"></i>
                                  无需权限
                                </span>
                              </div>
                              <div class="col-md-6">
                                <strong>调试状态:</strong>
                                <span class="badge ms-2" :class="canDebugEndpoint(endpoint) ? 'bg-success' : 'bg-warning'">
                                  <i class="bi me-1" :class="canDebugEndpoint(endpoint) ? 'bi-check-circle' : 'bi-exclamation-triangle'"></i>
                                  {{ canDebugEndpoint(endpoint) ? '可调试' : '权限不足' }}
                                </span>
                              </div>
                            </div>
                          </div>

                          <!-- 请求参数 -->
                          <div v-if="endpoint.parameters" class="mb-4">
                            <h6>请求参数</h6>
                            <div class="table-responsive">
                              <table class="table table-sm">
                                <thead>
                                  <tr>
                                    <th>参数名</th>
                                    <th>位置</th>
                                    <th>类型</th>
                                    <th>必填</th>
                                    <th>说明</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr v-for="param in endpoint.parameters" :key="param.name">
                                    <td><code>{{ param.name }}</code></td>
                                    <td>{{ param.in }}</td>
                                    <td>{{ param.schema?.type || 'string' }}</td>
                                    <td>
                                      <span v-if="param.required" class="badge bg-danger">必填</span>
                                      <span v-else class="badge bg-secondary">可选</span>
                                    </td>
                                    <td>{{ param.description }}</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>

                          <!-- 请求体 -->
                          <div v-if="endpoint.requestBody" class="mb-4">
                            <h6>请求体</h6>
                            <div class="mb-2">
                              <span v-if="endpoint.requestBody.required" class="badge bg-danger">必填</span>
                              <span class="badge bg-info ms-2">application/json</span>
                            </div>
                            <div class="code-block">
                              <pre><code>{{ getExampleData(endpoint.requestBody?.content?.['application/json']?.example, '// 暂无请求体示例') }}</code></pre>
                            </div>
                          </div>

                          <!-- 响应示例 -->
                          <div v-if="endpoint.responses" class="mb-4">
                            <h6>响应示例</h6>
                            <div class="response-tabs">
                              <ul class="nav nav-tabs" :id="`response-tabs-${selectedCategory}-${index}`">
                                <li v-for="(response, code) in endpoint.responses" :key="code" class="nav-item">
                                  <a 
                                    class="nav-link" 
                                    :class="{ active: code === '200' }"
                                    :id="`tab-${selectedCategory}-${index}-${code}`"
                                    data-bs-toggle="tab" 
                                    :href="`#response-${selectedCategory}-${index}-${code}`"
                                  >
                                    <span :class="getStatusClass(code)">{{ code }}</span>
                                    {{ response.description }}
                                  </a>
                                </li>
                              </ul>
                              <div class="tab-content">
                                <div 
                                  v-for="(response, code) in endpoint.responses" 
                                  :key="code"
                                  class="tab-pane fade"
                                  :class="{ 'show active': code === '200' }"
                                  :id="`response-${selectedCategory}-${index}-${code}`"
                                >
                                  <div class="code-block mt-3">
                                    <pre><code>{{ getExampleData(response.content?.['application/json']?.example, '// 暂无响应示例') }}</code></pre>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 在线调试 -->
                          <div class="api-test-section">
                            <h6>在线调试</h6>
                            <div class="card bg-light">
                              <div class="card-body">
                                <!-- URL编辑区域 -->
                                <div class="row mb-3">
                                  <div class="col-md-8">
                                    <label class="form-label">请求URL</label>
                                    <div class="input-group">
                                      <span class="input-group-text method-badge" :class="getMethodClass(endpoint.method)">
                                        {{ endpoint.method }}
                                      </span>
                                      <input
                                        type="text"
                                        class="form-control"
                                        v-model="testApiUrl"
                                        :placeholder="getFullUrl(endpoint.path)"
                                        @focus="initializeTestUrl(endpoint)"
                                      >
                                    </div>
                                    <small class="text-muted">可以编辑路径参数，如将 {id} 替换为实际值</small>
                                  </div>
                                  <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <button
                                      class="btn w-100 d-block"
                                      :class="canDebugEndpoint(endpoint) ? 'btn-primary' : 'btn-secondary'"
                                      @click="testApiWithUrl(endpoint)"
                                      :disabled="testing || !canDebugEndpoint(endpoint)"
                                      :title="canDebugEndpoint(endpoint) ? '' : getPermissionMessage(endpoint)"
                                    >
                                      <i class="bi me-1" :class="canDebugEndpoint(endpoint) ? 'bi-play-circle' : 'bi-lock'"></i>
                                      {{ !canDebugEndpoint(endpoint) ? '权限不足' : (testing ? '测试中...' : '发送请求') }}
                                    </button>
                                  </div>
                                </div>

                                <!-- 路径参数编辑 -->
                                <div v-if="hasPathParameters(endpoint)" class="mb-3">
                                  <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label mb-0">路径参数</label>
                                    <button
                                      class="btn btn-outline-info btn-sm"
                                      @click="fillExamplePathParams(endpoint)"
                                    >
                                      <i class="bi bi-magic me-1"></i>
                                      填充示例
                                    </button>
                                  </div>
                                  <div class="row">
                                    <div v-for="param in getPathParameters(endpoint)" :key="param.name" class="col-md-6 mb-2">
                                      <div class="input-group input-group-sm">
                                        <span class="input-group-text">{{ param.name }}</span>
                                        <input
                                          type="text"
                                          class="form-control"
                                          v-model="pathParams[param.name]"
                                          :placeholder="param.description || `请输入${param.name}`"
                                          @input="updateTestUrl(endpoint)"
                                        >
                                      </div>
                                      <small class="text-muted">{{ param.description }}</small>
                                    </div>
                                  </div>
                                </div>

                                <!-- 查询参数编辑 -->
                                <div v-if="hasQueryParameters(endpoint)" class="mb-3">
                                  <label class="form-label">查询参数</label>
                                  <div class="row">
                                    <div v-for="param in getQueryParameters(endpoint)" :key="param.name" class="col-md-6 mb-2">
                                      <div class="input-group input-group-sm">
                                        <span class="input-group-text">{{ param.name }}</span>
                                        <input
                                          :type="getInputType(param.schema?.type)"
                                          class="form-control"
                                          v-model="queryParams[param.name]"
                                          :placeholder="param.description || `请输入${param.name}`"
                                          @input="updateTestUrl(endpoint)"
                                        >
                                      </div>
                                      <small v-if="param.required" class="text-danger">必填</small>
                                      <small v-else class="text-muted">可选</small>
                                    </div>
                                  </div>
                                </div>

                                <!-- 请求体编辑器 -->
                                <div v-if="endpoint.requestBody" class="mb-3">
                                  <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label mb-0">请求体 (JSON)</label>
                                    <button
                                      class="btn btn-outline-secondary btn-sm"
                                      :disabled="!hasExample(endpoint, 'request')"
                                      @click="loadExampleRequestBody(endpoint)"
                                    >
                                      <i class="bi bi-arrow-clockwise me-1"></i>
                                      {{ hasExample(endpoint, 'request') ? '加载示例' : '无示例' }}
                                    </button>
                                  </div>
                                  <textarea
                                    class="form-control code-editor"
                                    rows="8"
                                    v-model="testRequestBody"
                                    placeholder="请输入JSON格式的请求体，或点击'加载示例'按钮"
                                  ></textarea>
                                </div>

                                <!-- 测试结果 -->
                                <div v-if="testResult" class="test-result">
                                  <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label mb-0">响应结果</label>
                                    <div>
                                      <span v-if="testResult.statusCode" class="badge me-2" :class="getStatusBadgeClass(testResult.statusCode)">
                                        {{ testResult.statusCode }} {{ testResult.statusText }}
                                      </span>
                                      <span class="badge" :class="testResult.status === 'success' ? 'bg-success' : 'bg-danger'">
                                        {{ testResult.status === 'success' ? '成功' : '失败' }}
                                      </span>
                                    </div>
                                  </div>

                                  <!-- 请求信息 -->
                                  <div v-if="testResult.url" class="mb-3">
                                    <div class="row">
                                      <div class="col-12">
                                        <small class="text-muted">
                                          <strong>请求URL:</strong> {{ testResult.method }} {{ testResult.url }}
                                        </small>
                                      </div>
                                      <div v-if="testResult.requestBody && Object.keys(testResult.requestBody).length > 0" class="col-12 mt-1">
                                        <small class="text-muted">
                                          <strong>请求体:</strong> {{ JSON.stringify(testResult.requestBody) }}
                                        </small>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- 响应数据 -->
                                  <div class="code-block">
                                    <pre><code>{{ JSON.stringify(testResult.data || testResult, null, 2) }}</code></pre>
                                  </div>

                                  <!-- 响应头 -->
                                  <div v-if="testResult.headers" class="mt-3">
                                    <details>
                                      <summary class="text-muted" style="cursor: pointer;">
                                        <small>响应头信息</small>
                                      </summary>
                                      <div class="code-block mt-2">
                                        <pre><code>{{ JSON.stringify(testResult.headers, null, 2) }}</code></pre>
                                      </div>
                                    </details>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api'

export default {
  name: 'ApiDocs',
  data() {
    return {
      apiInfo: {
        title: 'API文档',
        version: '1.0.0'
      },
      categories: [],
      selectedCategory: '',
      testing: false,
      testRequestBody: '',
      testResult: null,
      showSettings: false,
      apiBaseUrl: 'http://127.0.0.1:5000',
      connectionStatus: {
        class: 'bg-secondary',
        icon: 'bi-question-circle',
        text: '未知',
        message: '点击测试连接检查后端服务状态'
      },
      // API测试相关
      testApiUrl: '',
      pathParams: {},
      queryParams: {},
      // 权限相关
      userPermissions: [],
      isAdmin: false,
      permissionsLoaded: false
    }
  },
  computed: {
    selectedCategoryData() {
      return this.categories.find(cat => cat.name === this.selectedCategory)
    }
  },
  async mounted() {
    this.loadSettings()
    await this.loadUserPermissions()
    await this.loadApiDocs()
    await this.testConnection()
  },
  methods: {
    async loadApiDocs() {
      try {
        const response = await api.get('/api/docs/api-docs')
        const data = response.data.data
        
        this.apiInfo = data.info
        this.categories = data.categories

        // 调试信息
        console.log('API文档加载成功:', {
          categories: this.categories.length,
          firstCategory: this.categories[0]?.name,
          firstEndpoint: this.categories[0]?.endpoints[0]
        })

        // 默认选择第一个分类
        if (this.categories.length > 0) {
          this.selectedCategory = this.categories[0].name
        }
      } catch (error) {
        console.error('加载API文档失败:', error)
        this.$message?.error('加载API文档失败')
      }
    },

    selectCategory(categoryName) {
      this.selectedCategory = categoryName
      this.testResult = null
      this.testRequestBody = ''
      this.testApiUrl = ''
      this.pathParams = {}
      this.queryParams = {}
    },

    loadExampleRequestBody(endpoint) {
      try {
        if (endpoint.requestBody?.content?.['application/json']?.example) {
          this.testRequestBody = JSON.stringify(
            endpoint.requestBody.content['application/json'].example,
            null,
            2
          )
          this.$message?.success('示例数据已加载')
        } else {
          this.$message?.warning('该接口暂无请求体示例')
        }
      } catch (error) {
        console.error('加载示例失败:', error)
        this.$message?.error('加载示例失败')
      }
    },

    getMethodClass(method) {
      const classes = {
        'GET': 'method-get',
        'POST': 'method-post',
        'PUT': 'method-put',
        'DELETE': 'method-delete',
        'PATCH': 'method-patch'
      }
      return classes[method] || 'method-default'
    },

    getStatusClass(code) {
      if (code.startsWith('2')) return 'text-success'
      if (code.startsWith('4')) return 'text-warning'
      if (code.startsWith('5')) return 'text-danger'
      return 'text-info'
    },

    getStatusBadgeClass(code) {
      if (code >= 200 && code < 300) return 'bg-success'
      if (code >= 400 && code < 500) return 'bg-warning'
      if (code >= 500) return 'bg-danger'
      return 'bg-info'
    },

    getFullUrl(path) {
      return `${this.apiBaseUrl}${path}`
    },

    // 安全地获取示例数据
    getExampleData(obj, defaultValue = '// 暂无示例数据') {
      try {
        return obj ? JSON.stringify(obj, null, 2) : defaultValue
      } catch (error) {
        console.error('JSON序列化失败:', error)
        return defaultValue
      }
    },

    // 检查是否有示例数据
    hasExample(endpoint, type = 'request') {
      if (type === 'request') {
        return !!(endpoint.requestBody?.content?.['application/json']?.example)
      }
      return false
    },

    // 参数处理方法
    hasPathParameters(endpoint) {
      return endpoint.parameters?.some(param => param.in === 'path') || false
    },

    hasQueryParameters(endpoint) {
      return endpoint.parameters?.some(param => param.in === 'query') || false
    },

    getPathParameters(endpoint) {
      return endpoint.parameters?.filter(param => param.in === 'path') || []
    },

    getQueryParameters(endpoint) {
      return endpoint.parameters?.filter(param => param.in === 'query') || []
    },

    getInputType(schemaType) {
      switch (schemaType) {
        case 'integer':
        case 'number':
          return 'number'
        case 'boolean':
          return 'checkbox'
        case 'date':
          return 'date'
        default:
          return 'text'
      }
    },

    // 初始化测试URL
    initializeTestUrl(endpoint) {
      if (!this.testApiUrl) {
        this.testApiUrl = this.getFullUrl(endpoint.path)
        // 初始化路径参数
        this.getPathParameters(endpoint).forEach(param => {
          if (!this.pathParams[param.name]) {
            this.pathParams[param.name] = ''
          }
        })
        // 初始化查询参数
        this.getQueryParameters(endpoint).forEach(param => {
          if (!this.queryParams[param.name]) {
            this.queryParams[param.name] = param.schema?.default || ''
          }
        })
      }
    },

    // 更新测试URL
    updateTestUrl(endpoint) {
      let url = this.getFullUrl(endpoint.path)

      // 替换路径参数
      Object.keys(this.pathParams).forEach(paramName => {
        const value = this.pathParams[paramName]
        if (value) {
          url = url.replace(`{${paramName}}`, encodeURIComponent(value))
        }
      })

      // 添加查询参数
      const queryString = Object.keys(this.queryParams)
        .filter(key => this.queryParams[key] !== '' && this.queryParams[key] !== null)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(this.queryParams[key])}`)
        .join('&')

      if (queryString) {
        url += (url.includes('?') ? '&' : '?') + queryString
      }

      this.testApiUrl = url
    },

    // 填充示例参数
    fillExamplePathParams(endpoint) {
      const pathParams = this.getPathParameters(endpoint)
      pathParams.forEach(param => {
        // 根据参数名称提供合理的示例值
        switch (param.name.toLowerCase()) {
          case 'id':
          case 'submission_id':
          case 'edit_id':
            this.pathParams[param.name] = '3'
            break
          case 'filename':
            this.pathParams[param.name] = 'example.xlsx'
            break
          case 'role_id':
            this.pathParams[param.name] = '1'
            break
          case 'user_id':
            this.pathParams[param.name] = '1'
            break
          case 'group_id':
            this.pathParams[param.name] = '1'
            break
          default:
            this.pathParams[param.name] = '1'
        }
      })
      this.updateTestUrl(endpoint)
      this.$message?.success('示例参数已填充')
    },

    // 权限相关方法
    async loadUserPermissions() {
      try {
        console.log('开始加载用户权限...')
        const response = await api.get('/api/docs/user-permissions')

        if (response.data.status === 'success') {
          const data = response.data.data
          this.userPermissions = data.permissions || []
          this.isAdmin = data.is_admin || false
          console.log('用户权限加载成功:', {
            permissions: this.userPermissions,
            isAdmin: this.isAdmin,
            username: data.username
          })
          this.permissionsLoaded = true
        } else {
          throw new Error(response.data.message || '获取权限失败')
        }
      } catch (error) {
        console.error('加载用户权限失败:', error)

        // 检查是否是认证错误
        if (error.response?.status === 401) {
          console.log('认证失败，可能需要重新登录')
          this.$message?.warning('认证已过期，请重新登录')
          // 可以选择跳转到登录页面
          // this.$router.push('/login')
        } else {
          this.$message?.error('加载用户权限失败')
        }

        // 如果获取权限失败，默认为空权限
        this.userPermissions = []
        this.isAdmin = false
      }
    },

    // 检查用户是否有指定权限
    hasPermission(permission) {
      if (!permission) return true  // 无需权限的接口
      if (this.isAdmin) return true  // 管理员拥有所有权限
      return this.userPermissions.includes(permission)
    },

    // 检查接口是否可以调试
    canDebugEndpoint(endpoint) {
      const permission = this.getEndpointPermission(endpoint)
      return this.hasPermission(permission)
    },

    // 获取接口所需权限
    getEndpointPermission(endpoint) {
      // 🔥 优先使用后端API文档数据中的权限字段
      if (endpoint.permission !== undefined) {
        return endpoint.permission
      }

      // 🔥 基于后端实际接口的权限映射表
      const permissionMap = {
        // ==================== 认证相关 (auth/routes.py) ====================
        '/auth/login': null,
        '/auth/register': null,
        '/auth/logout': null,
        '/auth/profile': null,
        '/auth/verify-token': null,
        '/auth/change-password': null,
        '/auth/check-permission': null,

        // ==================== Excel相关 (excel/routes.py) ====================
        '/excel/submit': 'form.create',
        '/excel/form_submissions': 'form.view',
        '/excel/form_submissions/{id}': endpoint.method === 'DELETE' ? 'form.delete' : 'form.view',
        '/excel/form_submissions/{id}/edit': 'form.edit',
        '/excel/form_submissions/{id}/edit_history': 'form.view',
        '/excel/history/{file_id}': 'form.view',
        '/excel/delete/{file_id}': 'form.delete',
        '/excel/delete_multiple': 'form.delete',
        '/excel/edit/{file_id}': 'form.edit',
        '/excel/update_content/{file_id}': 'form.edit',
        '/excel/download/{file_id}': 'form.download',
        '/excel/import': 'form.import',
        '/excel/import_excel/preview': 'form.import',
        '/excel/import_excel/confirm': 'form.import',
        '/excel/templates': 'template.view',
        '/excel/templates/upload': 'template.upload',

        // ==================== 组件管理 (excel/component_routes.py) ====================
        '/api/components/list': 'component.view',
        '/api/components/categories': 'component.view',
        '/api/components/create': 'component.create',
        '/api/components/{id}': endpoint.method === 'DELETE' ? 'component.delete' : 'component.edit',

        // ==================== RBAC权限管理 (rbac/routes.py) ====================
        '/rbac/users': endpoint.method === 'POST' ? 'user.create' : 'user.view',
        '/rbac/users/{id}': this.getRbacUserPermission(endpoint.method),
        '/rbac/roles': endpoint.method === 'POST' ? 'role.create' : 'role.view',
        '/rbac/roles/{id}': this.getRbacRolePermission(endpoint.method),
        '/rbac/permissions': endpoint.method === 'POST' ? 'permission.create' : 'permission.view',
        '/rbac/permissions/{id}': this.getRbacPermissionPermission(endpoint.method),
        '/rbac/groups': endpoint.method === 'POST' ? 'group.create' : 'group.view',
        '/rbac/groups/{id}': this.getRbacGroupPermission(endpoint.method),
        '/rbac/groups/{id}/members': 'group.edit',
        '/rbac/debug/user-permissions': null, // 调试接口，只需登录

        // ==================== 缓存管理 (api/cache_routes.py) ====================
        '/api/cache/health': null,
        '/api/cache/status': 'system.cache.view',
        '/api/cache/clear': 'system.cache.manage',
        '/api/cache/keys': 'system.cache.view',
        '/api/cache/test': 'system.cache.manage',

        // ==================== 表单快照 (api/form_snapshot_routes.py) ====================
        '/api/form-snapshot/save': 'form.snapshot.create',
        '/api/form-snapshot/load/{form_type}': 'form.snapshot.view',
        '/api/form-snapshot/load-by-id/{snapshot_id}': 'form.snapshot.view',
        '/api/form-snapshot/delete/{form_type}': 'form.snapshot.delete',
        '/api/form-snapshot/delete-by-id/{snapshot_id}': 'form.snapshot.delete',
        '/api/form-snapshot/list': 'form.snapshot.view',
        '/api/form-snapshot/health': null,
        '/api/form-snapshot/batch-delete': 'form.snapshot.delete',
        '/api/form-snapshot/clear-by-condition': 'form.snapshot.delete',
        '/api/form-snapshot/clear-all': 'form.snapshot.delete',

        // ==================== 性能监控 (api/performance_routes.py) ====================
        '/api/performance/stats': 'system.view',
        '/api/performance/report': 'system.view',
        '/api/performance/slow-queries': 'system.view',
        '/api/performance/cache-stats': 'system.view',
        '/api/performance/request-stats': 'system.view',
        '/api/performance/system-resources': 'system.view',
        '/api/performance/clear-data': 'system.config',
        '/api/performance/clear-slow-queries': 'system.config',
        '/api/performance/health-check': null,
        '/api/performance/recommendations': 'system.view',
        '/api/performance/basic-stats': null, // 只需登录
        '/api/performance/simple-health': null, // 只需登录
        '/api/performance/public-health': null, // 无需认证

        // ==================== 历史记录缓存 (api/history_cache_routes.py) ====================
        '/api/history-cache/search': 'history.view',
        '/api/history-cache/clear': 'history.cache.manage',
        '/api/history-cache/stats': 'history.cache.view',
        '/api/history-cache/health': null,

        // ==================== 重复检查 (api/duplicate_check_routes.py) ====================
        '/api/duplicate-check/check': 'form.submit',
        '/api/duplicate-check/mark': 'form.submit',
        '/api/duplicate-check/clear': 'form.duplicate.manage',
        '/api/duplicate-check/stats': 'form.duplicate.view',
        '/api/duplicate-check/health': null,

        // ==================== 限流管理 (api/rate_limit_routes.py) ====================
        '/api/rate-limit/check': 'system.rate_limit.view',
        '/api/rate-limit/reset': 'system.rate_limit.manage',
        '/api/rate-limit/config': 'system.rate_limit.view',
        '/api/rate-limit/stats': 'system.rate_limit.view',
        '/api/rate-limit/health': null,

        // ==================== API文档相关 (api/docs_routes.py) ====================
        '/api/docs/test': null,
        '/api/docs/api-docs': null,
        '/api/docs/user-permissions': null,
        '/api/docs/auth-debug': null,
        '/api/docs/debug-user-permissions': null,
      }

      // 默认权限：如果是认证相关或API文档相关，无需权限；其他接口根据模块判断
      const defaultPermission = this.getDefaultPermissionByPath(endpoint.path)
      return permissionMap[endpoint.path] || defaultPermission
    },

    // RBAC用户权限判断
    getRbacUserPermission(method) {
      switch(method) {
        case 'GET': return 'user.view'
        case 'PUT': return 'user.edit'
        case 'DELETE': return 'user.delete'
        default: return 'user.view'
      }
    },

    // RBAC角色权限判断
    getRbacRolePermission(method) {
      switch(method) {
        case 'GET': return 'role.view'
        case 'PUT': return 'role.edit'
        case 'DELETE': return 'role.delete'
        default: return 'role.view'
      }
    },

    // RBAC用户组权限判断
    getRbacGroupPermission(method) {
      switch(method) {
        case 'GET': return 'group.view'
        case 'PUT': return 'group.edit'
        case 'DELETE': return 'group.delete'
        default: return 'group.view'
      }
    },

    // RBAC权限权限判断
    getRbacPermissionPermission(method) {
      switch(method) {
        case 'GET': return 'permission.view'
        case 'PUT': return 'permission.edit'
        case 'DELETE': return 'permission.delete'
        default: return 'permission.view'
      }
    },

    // 根据路径判断默认权限
    getDefaultPermissionByPath(path) {
      if (path.includes('/auth/') || path.includes('/api/docs/')) {
        return null // 认证和API文档相关接口无需权限
      } else if (path.includes('/excel/') || path.includes('/api/history/')) {
        return 'form.view' // 表单和历史记录相关
      } else if (path.includes('/api/components/')) {
        return 'component.view' // 组件相关
      } else if (path.includes('/rbac/')) {
        return 'user.view' // 用户管理相关
      } else if (path.includes('/api/performance/') || path.includes('/cache/')) {
        return 'system.view' // 系统监控相关
      } else {
        return 'form.view' // 其他默认为表单查看权限
      }
    },

    // 获取权限提示信息
    getPermissionMessage(endpoint) {
      const permission = this.getEndpointPermission(endpoint)
      if (!permission) return ''
      return `需要权限: ${permission}`
    },

    async testApiWithUrl(endpoint) {
      this.testing = true
      this.testResult = null

      try {
        let requestBody = {}
        if (endpoint.requestBody && this.testRequestBody) {
          try {
            requestBody = JSON.parse(this.testRequestBody)
          } catch (e) {
            throw new Error('请求体JSON格式错误')
          }
        }

        // 构建请求配置
        const requestConfig = {
          method: endpoint.method,
          headers: {
            'Content-Type': 'application/json'
          }
        }

        // 添加认证头
        const token = this.$store?.state?.token || localStorage.getItem('access_token')
        if (token && endpoint.security) {
          requestConfig.headers['Authorization'] = `Bearer ${token}`
        }

        // 添加请求体
        if (endpoint.method !== 'GET' && endpoint.method !== 'DELETE' && Object.keys(requestBody).length > 0) {
          requestConfig.body = JSON.stringify(requestBody)
        }

        // 使用编辑后的URL
        const url = this.testApiUrl || this.getFullUrl(endpoint.path)
        const response = await fetch(url, requestConfig)

        let responseData
        try {
          responseData = await response.json()
        } catch (e) {
          responseData = await response.text()
        }

        this.testResult = {
          status: response.ok ? 'success' : 'error',
          statusCode: response.status,
          statusText: response.statusText,
          data: responseData,
          headers: Object.fromEntries(response.headers.entries()),
          url: url,
          method: endpoint.method,
          requestBody: requestBody
        }

      } catch (error) {
        this.testResult = {
          status: 'error',
          message: error.message || 'API调用失败',
          error: error.toString(),
          url: this.testApiUrl
        }
      } finally {
        this.testing = false
      }
    },

    // 保留原方法作为兼容
    async testApi(endpoint) {
      return this.testApiWithUrl(endpoint)
    },

    // 设置管理
    loadSettings() {
      const savedUrl = localStorage.getItem('api_docs_base_url')
      if (savedUrl) {
        this.apiBaseUrl = savedUrl
      }
    },

    saveSettings() {
      localStorage.setItem('api_docs_base_url', this.apiBaseUrl)
      this.$message?.success('设置已保存')
      this.testConnection()
    },

    async testConnection() {
      try {
        this.connectionStatus = {
          class: 'bg-warning',
          icon: 'bi-hourglass-split',
          text: '测试中...',
          message: '正在测试后端服务连接'
        }

        // 测试连接到后端服务
        const response = await fetch(`${this.apiBaseUrl}/api/docs/test`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 5000
        })

        if (response.ok) {
          const data = await response.json()
          this.connectionStatus = {
            class: 'bg-success',
            icon: 'bi-check-circle',
            text: '连接正常',
            message: `后端服务运行正常 (${data.timestamp || ''})`
          }
        } else {
          throw new Error(`HTTP ${response.status}`)
        }
      } catch (error) {
        this.connectionStatus = {
          class: 'bg-danger',
          icon: 'bi-x-circle',
          text: '连接失败',
          message: `无法连接到后端服务: ${error.message}`
        }
      }
    },

    async debugAuth() {
      try {
        console.log('开始调试认证...')

        // 检查本地token
        const storeToken = this.$store?.state?.token
        const accessToken = localStorage.getItem('access_token')
        const oldToken = localStorage.getItem('token')
        const userInfo = localStorage.getItem('user_info')

        console.log('Token检查:', {
          storeToken: storeToken ? storeToken.substring(0, 20) + '...' : null,
          accessToken: accessToken ? accessToken.substring(0, 20) + '...' : null,
          oldToken: oldToken ? oldToken.substring(0, 20) + '...' : null,
          userInfo: userInfo ? 'exists' : 'missing',
          currentToken: accessToken || oldToken || 'none'
        })

        // 调用认证调试接口
        const response = await api.get('/api/docs/auth-debug')
        console.log('认证调试结果:', response.data)

        if (response.data.status === 'success') {
          this.$message?.success('认证调试成功')
        } else {
          this.$message?.error('认证调试失败: ' + response.data.message)
        }

      } catch (error) {
        console.error('认证调试失败:', error)
        this.$message?.error('认证调试失败: ' + (error.response?.data?.message || error.message))
      }
    },

    async debugPermissions() {
      try {
        console.log('开始调试权限...')

        const response = await api.get('/api/docs/debug-user-permissions')
        console.log('权限调试结果:', response.data)

        if (response.data.status === 'success') {
          const data = response.data.data

          console.log('用户信息:', data.user_info)
          console.log('用户权限:', data.user_permissions)
          console.log('用户角色:', data.user_roles)
          console.log('用户组:', data.user_groups)
          console.log('系统权限:', data.system_permissions)

          // 显示详细信息
          const message = `
权限调试成功！
用户: ${data.user_info.username} (${data.user_info.is_admin ? '管理员' : '普通用户'})
拥有权限: ${data.permission_count} 个
系统总权限: ${data.system_permission_count} 个
角色数: ${data.user_roles.length} 个
用户组数: ${data.user_groups.length} 个

详细信息请查看控制台输出。
          `.trim()

          this.$message?.success(message)

          // 重新加载权限
          await this.loadUserPermissions()

        } else {
          this.$message?.error('权限调试失败: ' + response.data.message)
        }

      } catch (error) {
        console.error('权限调试失败:', error)
        this.$message?.error('权限调试失败: ' + (error.response?.data?.message || error.message))
      }
    }
  }
}
</script>

<style scoped>
.api-docs {
  padding: 20px 0;
}

/* 方法标签样式 */
.method-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75em;
  font-weight: bold;
  text-transform: uppercase;
  min-width: 60px;
  text-align: center;
}

.method-get {
  background-color: #28a745;
  color: white;
}

.method-post {
  background-color: #007bff;
  color: white;
}

.method-put {
  background-color: #ffc107;
  color: black;
}

.method-delete {
  background-color: #dc3545;
  color: white;
}

.method-patch {
  background-color: #6f42c1;
  color: white;
}

.method-default {
  background-color: #6c757d;
  color: white;
}

/* 代码块样式 */
.code-block {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  margin: 10px 0;
}

.code-block pre {
  margin: 0;
  font-size: 0.875em;
  line-height: 1.4;
}

.code-block code {
  color: #495057;
  background: none;
  padding: 0;
}

/* 代码编辑器样式 */
.code-editor {
  font-family: 'Courier New', monospace;
  font-size: 0.875em;
  line-height: 1.4;
}

/* 接口详情样式 */
.endpoint-details h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 2px solid #e9ecef;
}

/* 测试结果样式 */
.test-result {
  margin-top: 20px;
}

.test-result .code-block {
  background-color: #f1f3f4;
  border-color: #d1ecf1;
}

/* 响应标签页样式 */
.response-tabs .nav-link {
  font-size: 0.875em;
}

.response-tabs .nav-link.active {
  font-weight: 600;
}

/* 卡片样式优化 */
.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1rem;
}

.accordion-button {
  padding: 1rem 1.25rem;
}

.accordion-button:not(.collapsed) {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

/* 列表组样式 */
.list-group-item {
  border-left: none;
  border-right: none;
}

.list-group-item:first-child {
  border-top: none;
}

.list-group-item:last-child {
  border-bottom: none;
}

.list-group-item.active {
  background-color: #007bff;
  border-color: #007bff;
}

/* 表格样式 */
.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  font-size: 0.875em;
}

.table td {
  font-size: 0.875em;
  vertical-align: middle;
}

/* API测试区域样式 */
.api-test-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid #e9ecef;
}

.api-test-section .card {
  border: 1px solid #dee2e6;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .method-badge {
    min-width: 50px;
    font-size: 0.7em;
  }

  .accordion-button {
    padding: 0.75rem 1rem;
  }

  .code-block {
    padding: 10px;
  }

  .code-block pre {
    font-size: 0.8em;
  }
}
</style>
