import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import 'bootstrap/dist/css/bootstrap.min.css'
import './assets/css/floating-labels.css'
import './assets/css/design-system.css'
import './assets/css/disable-animations.css'
import './assets/css/performance.css'
import './assets/css/optimized-components.css'
import './styles/responsive-windows.css' // 引入响应式窗口样式
import { getPagePerformance, checkMemoryUsage } from './utils/performance'
import './utils/backdropKiller.js' // 引入强力遮罩清理器

// 同步导入Bootstrap并暴露到全局对象
import * as bootstrap from 'bootstrap'
window.bootstrap = bootstrap

// 创建Vue应用实例
const app = createApp(App)

// 性能优化配置
app.config.performance = true

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err)
  console.error('组件信息:', info)
  // 在生产环境可以发送错误到监控服务
}

// 添加全局属性以便在组件中访问Bootstrap
app.config.globalProperties.$bootstrap = bootstrap

// 确保Bootstrap完全加载后再挂载应用
document.addEventListener('DOMContentLoaded', () => {
  // 验证Bootstrap是否正确加载
  if (window.bootstrap && window.bootstrap.Collapse) {
    console.log('✅ Bootstrap已成功加载，包含Collapse组件')
  } else {
    console.error('❌ Bootstrap加载失败或缺少Collapse组件')
  }

  // 挂载Vue应用
  app.use(store).use(router).mount('#app')

  // 性能监控（仅在开发环境）
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      getPagePerformance()
      checkMemoryUsage()
    }, 1000)
  }
})
