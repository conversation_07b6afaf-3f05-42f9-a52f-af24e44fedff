<template>
  <div 
    ref="container"
    class="floating-ball-container" 
    :style="containerStyle"
  >
    <!-- 悬浮球主体 -->
    <div 
      ref="ball"
      class="floating-ball"
      :class="ballClasses"
      @mousedown="handleMouseDown"
      @touchstart="handleTouchStart"
      @click="handleClick"
    >
      <i :class="iconClass"></i>
    </div>

    <!-- 侧边栏内容 -->
    <transition name="sidebar-fade">
      <div v-show="isExpanded" class="sidebar-content">
        <div class="sidebar-header">
          <h6>
            <i class="bi bi-compass me-2"></i>
            表单导航
          </h6>
        </div>

        <!-- 快捷操作按钮 -->
        <div class="quick-actions">
          <div class="action-row">
            <button
              type="button"
              class="action-btn expand-btn"
              @click="$emit('expand-all')"
              title="展开所有部分"
            >
              <i class="bi bi-arrows-expand"></i>
            </button>
            <button
              type="button"
              class="action-btn collapse-btn"
              @click="$emit('collapse-all')"
              title="折叠所有部分"
            >
              <i class="bi bi-arrows-collapse"></i>
            </button>
          </div>
          <div class="action-row">
            <button
              type="button"
              class="action-btn save-btn"
              @click="$emit('save-form')"
              title="保存快照"
            >
              <i class="bi bi-save2"></i>
            </button>
            <button
              type="button"
              class="action-btn load-btn"
              @click="$emit('load-form')"
              title="快照管理"
            >
              <i class="bi bi-folder2-open"></i>
            </button>
          </div>

        </div>

        <!-- 导航链接 -->
        <div class="sidebar-nav">
          <div
            v-for="section in sections"
            :key="section.id"
            class="nav-item"
            @click="scrollToSection(section.id)"
          >
            <div class="nav-icon">
              <i :class="`bi ${section.icon}`"></i>
            </div>
            <span>{{ section.title }}</span>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-if="sections.length === 0" class="empty-state">
          <small class="text-muted">
            <i class="bi bi-exclamation-triangle me-1"></i>
            未检测到表单分组
          </small>
        </div>

        <!-- 使用提示 -->
        <div class="usage-tips">
          <small class="text-muted">
            <i class="bi bi-info-circle me-1"></i>
            容器外跟随滚动 | Ctrl+Shift+R 重置
          </small>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useSectionDetector } from './composables/useSectionDetector'

export default {
  name: 'FloatingBall',
  props: {
    formType: {
      type: String,
      default: '安全监测'
    }
  },
  emits: [
    'expand-all',
    'collapse-all', 
    'save-form',
    'load-form',
    'sections-updated'
  ],
  setup(props, { emit }) {
    // 响应式状态
    const isExpanded = ref(false)
    const container = ref(null)
    const ball = ref(null)

    // 拖拽相关状态
    const position = reactive({ x: 0, y: 0 })
    const isDragging = ref(false)
    const isDragCandidate = ref(false)
    const justDragged = ref(false)

    const dragStartPosition = reactive({ x: 0, y: 0 })
    const dragOffset = reactive({ x: 0, y: 0 })

    // 使用section检测功能
    const {
      sections,
      detectSections,
      cleanup: cleanupSections
    } = useSectionDetector()

    // 计算属性
    const containerStyle = computed(() => {
      const style = {
        position: 'fixed',
        top: `${position.y}px`,
        left: `${position.x}px`,
        zIndex: 999999,  // 提高z-index到更高值
        pointerEvents: 'auto',
        transform: 'translateZ(0)',  // 强制硬件加速，确保在最顶层
        isolation: 'isolate'  // 创建新的层叠上下文
      }

      return style
    })

    const ballClasses = computed(() => ({
      'expanded': isExpanded.value,
      'dragging': isDragging.value
    }))

    const iconClass = computed(() => 
      isExpanded.value ? 'bi bi-x-lg' : 'bi bi-list'
    )

    // 事件处理
    const handleMouseDown = (event) => {
      if (event.button !== 0) return

      // 防止重复处理
      if (isDragCandidate.value) {
        return
      }

      // 强制阻止事件传播
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()

      // 开始拖拽
      startDrag(event)
    }

    const startDrag = (event) => {
      const clientX = event.clientX || event.touches?.[0]?.clientX
      const clientY = event.clientY || event.touches?.[0]?.clientY

      if (!clientX || !clientY) {
        return
      }

      // 记录起始位置
      dragStartPosition.x = clientX
      dragStartPosition.y = clientY

      // 获取悬浮球的实际位置并计算偏移
      if (ball.value) {
        const ballRect = ball.value.getBoundingClientRect()

        // 计算鼠标相对于悬浮球左上角的偏移（视口坐标）
        // 这里保持使用视口坐标，因为我们要计算的是相对偏移
        dragOffset.x = clientX - ballRect.left
        dragOffset.y = clientY - ballRect.top


      } else {
        // 回退方案：使用悬浮球中心
        dragOffset.x = 30
        dragOffset.y = 30

      }

      isDragCandidate.value = true

      // 设置拖拽样式
      document.body.style.userSelect = 'none'
      document.body.style.cursor = 'grab'


    }

    const handleTouchStart = (event) => {
      // 触摸设备支持
      event.preventDefault()
      event.stopPropagation()

      // 开始拖拽
      startDrag(event)
    }

    const handleClick = (event) => {
      if (isDragging.value || justDragged.value) {
        return
      }

      event.preventDefault()
      event.stopPropagation()

      isExpanded.value = !isExpanded.value
    }

    const scrollToSection = (sectionId) => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
        isExpanded.value = false
      }
    }

    const resetPosition = () => {
      console.log('🔄 手动重置悬浮球位置')
      const formBounds = getFormBounds()
      const ballSize = 60
      const margin = 15

      // 重置到容器内右侧
      position.x = formBounds.right - ballSize - margin
      position.y = 100  // 距离容器顶部100px

      // 确保在容器边界内
      position.x = Math.max(margin, Math.min(position.x, formBounds.right - ballSize - margin))
      position.y = Math.max(margin, Math.min(position.y, formBounds.bottom - ballSize - margin))

      isExpanded.value = false
      console.log('✅ 位置已重置到容器内右侧:', { x: position.x, y: position.y, formBounds })
    }

    // 拖拽处理函数
    const handleDragMove = (event) => {
      if (!isDragCandidate.value) return

      const clientX = event.clientX || event.touches?.[0]?.clientX
      const clientY = event.clientY || event.touches?.[0]?.clientY

      if (!clientX || !clientY) return

      // 计算移动距离
      const deltaX = clientX - dragStartPosition.x
      const deltaY = clientY - dragStartPosition.y
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

      // 超过阈值才开始真正拖拽
      if (distance > 3 && !isDragging.value) {
        isDragging.value = true
        document.body.style.cursor = 'grabbing'
        console.log('✨ 开始真正拖拽，距离:', distance)
      }

      // 如果正在拖拽，更新位置
      if (isDragging.value) {
        // 计算新位置（使用视口坐标减去偏移，然后转换为页面坐标）
        const newViewportX = clientX - dragOffset.x
        const newViewportY = clientY - dragOffset.y

        // 转换为页面坐标
        const newX = newViewportX + window.scrollX
        const newY = newViewportY + window.scrollY

        // 获取页面尺寸进行约束
        const ballSize = 60
        const margin = 10

        const pageWidth = Math.max(
          document.body.scrollWidth,
          document.documentElement.scrollWidth,
          window.innerWidth
        )

        const pageHeight = Math.max(
          document.body.scrollHeight,
          document.documentElement.scrollHeight
        )

        // 约束位置在页面范围内
        const constrainedX = Math.max(margin, Math.min(pageWidth - ballSize - margin, newX))
        const constrainedY = Math.max(margin, Math.min(pageHeight - ballSize - margin, newY))

        // 更新位置
        position.x = constrainedX
        position.y = constrainedY

        console.log('🎯 拖拽移动 (坐标转换):', {
          client: { x: clientX, y: clientY },
          viewport: { x: newViewportX, y: newViewportY },
          page: { x: newX, y: newY },
          scroll: { x: window.scrollX, y: window.scrollY },
          constrained: { x: constrainedX, y: constrainedY },
          dragOffset: { x: dragOffset.x, y: dragOffset.y },
          pageSize: { width: pageWidth, height: pageHeight }
        })
      }
    }

    const endDrag = () => {
      if (!isDragCandidate.value) return

      // 如果发生了拖拽，设置标记防止触发点击
      if (isDragging.value) {
        justDragged.value = true
        setTimeout(() => {
          justDragged.value = false
        }, 100)

        // 确保悬浮球在可见区域内
        constrainPosition()

        // 更新相对位置，确保后续滚动时位置正确
        if (handleScroll) {
          handleScroll.initialRelativeY = position.y - window.scrollY
          console.log('🔄 更新相对位置:', {
            newRelativeY: handleScroll.initialRelativeY,
            currentY: position.y,
            scrollY: window.scrollY
          })
        }

        console.log('🎯 拖拽结束，位置:', { x: position.x, y: position.y })
      }

      // 重置状态
      isDragging.value = false
      isDragCandidate.value = false

      // 恢复样式
      document.body.style.userSelect = ''
      document.body.style.cursor = ''
    }

    const getTargetContainer = () => {
      // 查找目标容器：/html/body/div[1]/div/div/div[1]/div/div/div/div[2]
      const targetPath = 'body > div:nth-child(1) > div > div > div:nth-child(1) > div > div > div > div:nth-child(2)'
      let targetContainer = document.querySelector(targetPath)

      if (targetContainer) {
        console.log('🎯 找到目标容器:', targetPath, targetContainer)
        return targetContainer
      }

      // 备选方案：查找表单容器
      const formSelectors = [
        '.main-form-card .modern-card-body',
        '.main-form-card',
        '.modern-card',
        '.security-testing-form',
        '.security-monitoring-form',
        '.app-hardening-form',
        '.container-fluid .row',
        '.container-fluid',
        'form'
      ]

      for (const selector of formSelectors) {
        targetContainer = document.querySelector(selector)
        if (targetContainer) {
          console.log('📋 使用备选容器:', selector, targetContainer)
          return targetContainer
        }
      }

      console.log('⚠️ 未找到目标容器，使用body')
      return document.body
    }

    const getFormBounds = () => {
      const targetContainer = getTargetContainer()

      if (targetContainer) {
        const rect = targetContainer.getBoundingClientRect()

        // 返回容器的相对边界（相对于容器本身）
        const bounds = {
          left: 0,  // 相对于容器的左边界
          top: 0,   // 相对于容器的顶边界
          right: rect.width,   // 容器宽度
          bottom: rect.height, // 容器高度
          width: rect.width,
          height: rect.height,
          containerRect: rect  // 保存绝对位置信息
        }

        console.log('📋 目标容器边界:', bounds)
        return bounds
      }

      // 回退方案
      return {
        left: 0,
        top: 0,
        right: 400,
        bottom: 600,
        width: 400,
        height: 600
      }
    }

    const initPosition = () => {
      // 等待DOM渲染完成后再计算位置
      setTimeout(() => {
        // 将悬浮球挂载到body下，确保在容器外面
        if (container.value && container.value.parentNode !== document.body) {
          console.log('🔄 将悬浮球移动到body下')
          document.body.appendChild(container.value)
        }

        // 使用绝对定位，相对于整个页面定位
        const ballSize = 60
        const margin = 20

        // 获取页面右侧位置（相对于整个文档）
        const pageWidth = Math.max(
          document.body.scrollWidth,
          document.body.offsetWidth,
          document.documentElement.clientWidth,
          document.documentElement.scrollWidth,
          document.documentElement.offsetWidth
        )

        // 设置初始位置：页面右侧，当前视口顶部下方
        position.x = Math.min(pageWidth - ballSize - margin, window.innerWidth - ballSize - margin)
        position.y = window.scrollY + 150 // 当前视口顶部下方150px

        // 设置初始相对位置，用于滚动跟随
        if (handleScroll) {
          handleScroll.initialRelativeY = 150 // 相对于视口的固定位置
        }

        console.log('📍 初始位置设置 (容器外跟随滚动):', {
          x: position.x,
          y: position.y,
          pageWidth,
          scrollY: window.scrollY,
          initialRelativeY: 150,
          ballSize,
          margin
        })
      }, 100)
    }

    // 约束悬浮球位置在整个页面范围内
    const constrainPosition = () => {
      const ballSize = 60
      const margin = 10

      // 获取页面尺寸
      const pageWidth = Math.max(
        document.body.scrollWidth,
        document.body.offsetWidth,
        document.documentElement.clientWidth,
        document.documentElement.scrollWidth,
        document.documentElement.offsetWidth
      )

      const pageHeight = Math.max(
        document.body.scrollHeight,
        document.body.offsetHeight,
        document.documentElement.clientHeight,
        document.documentElement.scrollHeight,
        document.documentElement.offsetHeight
      )

      // 约束在整个页面范围内
      position.x = Math.max(margin, Math.min(position.x, pageWidth - ballSize - margin))
      position.y = Math.max(margin, Math.min(position.y, pageHeight - ballSize - margin))

      console.log('🔒 位置约束 (整个页面):', {
        position: { x: position.x, y: position.y },
        pageSize: { width: pageWidth, height: pageHeight },
        scrollY: window.scrollY
      })
    }

    const handleResize = () => {
      // 窗口大小改变时，保持悬浮球在页面右侧的相对位置
      const ballSize = 60
      const margin = 20

      // 如果悬浮球在右侧边缘附近，调整到新的右侧位置
      if (position.x > window.innerWidth - ballSize - margin - 100) {
        position.x = window.innerWidth - ballSize - margin
      }

      constrainPosition()

      console.log('📐 窗口大小改变，重新约束位置 (容器外):', {
        windowSize: { width: window.innerWidth, height: window.innerHeight },
        newPosition: { x: position.x, y: position.y },
        scrollY: window.scrollY
      })
    }

    // 重置悬浮球到安全位置
    const resetToSafePosition = () => {
      const ballSize = 60
      const margin = 20

      // 重置到页面右侧，当前视口位置
      position.x = window.innerWidth - ballSize - margin
      position.y = window.scrollY + 150 // 当前视口顶部下方150px

      // 重置相对位置
      if (handleScroll) {
        handleScroll.initialRelativeY = 150
      }

      constrainPosition()

      console.log('🔄 重置到安全位置 (容器外):', {
        x: position.x,
        y: position.y,
        scrollY: window.scrollY,
        viewportWidth: window.innerWidth,
        initialRelativeY: 150
      })
    }

    // 滚动处理 - 让悬浮球跟随页面滚动
    const handleScroll = () => {
      // 如果正在拖拽，不要更新位置
      if (isDragging.value || isDragCandidate.value) {
        console.log('🚫 正在拖拽，跳过滚动位置更新')
        return
      }

      // 记录初始相对位置（相对于视口）
      if (!handleScroll.initialRelativeY) {
        handleScroll.initialRelativeY = position.y - window.scrollY
      }

      // 更新悬浮球位置，使其跟随滚动
      const targetY = window.scrollY + handleScroll.initialRelativeY

      // 平滑更新位置
      position.y = targetY

      // 可选：在滚动时稍微调整透明度，提供视觉反馈
      if (ball.value) {
        const scrollSpeed = Math.abs(window.scrollY - (handleScroll.lastScrollY || 0))
        if (scrollSpeed > 5) {
          ball.value.style.opacity = '0.8'
          clearTimeout(handleScroll.fadeTimer)
          handleScroll.fadeTimer = setTimeout(() => {
            if (ball.value) ball.value.style.opacity = '1'
          }, 150)
        }
        handleScroll.lastScrollY = window.scrollY
      }


    }

    // 键盘快捷键处理
    const handleKeyDown = (event) => {
      // Ctrl + Shift + R 重置悬浮球位置
      if (event.ctrlKey && event.shiftKey && event.key === 'R') {
        console.log('⌨️ 键盘快捷键触发：重置悬浮球位置')
        resetToSafePosition()
        event.preventDefault()
      }
    }

    // 事件监听器管理
    const addEventListeners = () => {
      console.log('🎧 添加全局事件监听器')
      document.addEventListener('mousemove', handleDragMove, { passive: false })
      document.addEventListener('mouseup', endDrag)
      document.addEventListener('touchmove', handleDragMove, { passive: false })
      document.addEventListener('touchend', endDrag)
      window.addEventListener('resize', handleResize)
      window.addEventListener('scroll', handleScroll, { passive: true })
      document.addEventListener('keydown', handleKeyDown)

      // 添加调试事件监听器
      document.addEventListener('mousemove', (e) => {
        if (isDragCandidate.value) {
          console.log('🖱️ 全局鼠标移动事件:', { x: e.clientX, y: e.clientY, isDragCandidate: isDragCandidate.value })
        }
      })
    }

    const removeEventListeners = () => {
      document.removeEventListener('mousemove', handleDragMove)
      document.removeEventListener('mouseup', endDrag)
      document.removeEventListener('touchmove', handleDragMove)
      document.removeEventListener('touchend', endDrag)
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('scroll', handleScroll)
      document.removeEventListener('keydown', handleKeyDown)
    }

    // 生命周期
    onMounted(async () => {
      console.log('🚀 FloatingBall 组件已挂载')
      console.log('📍 容器元素:', container.value)
      console.log('⚽ 悬浮球元素:', ball.value)

      initPosition()
      addEventListeners()

      await nextTick()

      // 验证元素是否正确挂载
      if (ball.value) {
        console.log('✅ 悬浮球元素已正确挂载')
        console.log('📏 悬浮球位置:', ball.value.getBoundingClientRect())
      } else {
        console.error('❌ 悬浮球元素未找到')
      }

      setTimeout(() => {
        detectSections()
        emit('sections-updated', sections.value)

        // 检查悬浮球位置是否合理，如果不合理则重置
        const pageHeight = Math.max(document.body.scrollHeight, document.documentElement.scrollHeight)
        const pageWidth = Math.max(document.body.scrollWidth, document.documentElement.scrollWidth)

        if (position.x > pageWidth || position.y > pageHeight || position.x < 0 || position.y < 0) {
          console.log('⚠️ 检测到悬浮球位置异常，重置到安全位置')
          resetToSafePosition()
        }
      }, 500)
    })

    onUnmounted(() => {
      console.log('🧹 FloatingBall 组件开始清理')

      // 清理事件监听器
      removeEventListeners()

      // 清理sections
      cleanupSections()

      // 清理DOM元素 - 如果悬浮球被移动到body下，需要手动移除
      if (container.value && container.value.parentNode === document.body) {
        console.log('🗑️ 从body中移除悬浮球DOM元素')
        document.body.removeChild(container.value)
      }

      console.log('✅ FloatingBall 组件清理完成')
    })

    // 添加 refreshSections 方法
    const refreshSections = () => {
      console.log('🔄 手动刷新sections')
      detectSections()
      emit('sections-updated', sections.value)
    }

    return {
      // 响应式数据
      isExpanded,
      container,
      ball,
      position,
      isDragging,
      sections,

      // 计算属性
      containerStyle,
      ballClasses,
      iconClass,

      // 方法
      handleMouseDown,
      handleTouchStart,
      handleClick,
      scrollToSection,
      resetPosition,
      refreshSections
    }
  }
}
</script>

<style scoped>
/* 🎨 现代化悬浮球样式 */

.floating-ball-container {
  pointer-events: auto !important;  /* 强制允许事件传递 */
  user-select: none;
  z-index: 999999 !important;  /* 确保在最顶层 */
  position: absolute !important;  /* 绝对定位，跟随页面内容滚动 */
}

.floating-ball {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow:
    0 4px 20px rgba(0, 123, 255, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 20px;
  pointer-events: auto !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 999999 !important;  /* 确保在最顶层 */
  transform: translateZ(0);  /* 强制硬件加速 */
}

.floating-ball:hover {
  transform: scale(1.05);
  box-shadow:
    0 6px 25px rgba(0, 123, 255, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

.floating-ball.expanded {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  box-shadow:
    0 4px 20px rgba(220, 53, 69, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);
}

.floating-ball.dragging {
  cursor: grabbing !important;
  transform: scale(1.1);
  box-shadow:
    0 8px 30px rgba(0, 123, 255, 0.5),
    0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 10000;
}

/* 侧边栏内容 */
.sidebar-content {
  position: absolute;
  top: 0;
  right: 70px;
  width: 280px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 24px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
  pointer-events: auto;
  z-index: 99998;
}

.sidebar-header h6 {
  margin: 0;
  color: #333;
  font-weight: 600;
  font-size: 16px;
}

/* 快捷操作按钮 */
.quick-actions {
  margin: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-row {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: #f8f9fa;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.expand-btn:hover { background: #d4edda; color: #155724; }
.collapse-btn:hover { background: #f8d7da; color: #721c24; }
.save-btn:hover { background: #d1ecf1; color: #0c5460; }
.load-btn:hover { background: #fff3cd; color: #856404; }

/* 导航链接 */
.sidebar-nav {
  max-height: 300px;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin: 4px 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #495057;
}

.nav-item:hover {
  background: #f8f9fa;
  transform: translateX(4px);
}

.nav-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #007bff;
}

.nav-item span {
  font-size: 14px;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

/* 使用提示 */
.usage-tips {
  text-align: center;
  padding: 8px 12px;
  margin-top: 8px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 16px 16px;
}

.usage-tips small {
  font-size: 11px;
  color: #6c757d;
}

/* 动画效果 */
.sidebar-fade-enter-active,
.sidebar-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-fade-enter-from,
.sidebar-fade-leave-to {
  opacity: 0;
  transform: translateX(20px) scale(0.95);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-content {
    width: 260px;
    right: 65px;
  }
}

/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
</style>
