<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Bootstrap CSS 备用CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <title>梆梆安全-运维信息登记平台</title>
  </head>
  <body>
    <noscript>
      <strong>很抱歉，本网站需要启用JavaScript才能正常工作。请启用JavaScript后继续。</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->

    <!-- Bootstrap JS 备用CDN - 仅在ES6模块加载失败时使用 -->
    <script>
      // 检查是否已经通过ES6模块加载了Bootstrap
      window.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          if (!window.bootstrap) {
            console.log('ES6模块Bootstrap未加载，使用CDN版本')
            const script = document.createElement('script')
            script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'
            script.integrity = 'sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p'
            script.crossOrigin = 'anonymous'
            script.onload = function() {
              console.log('CDN Bootstrap已加载')
            }
            document.head.appendChild(script)
          } else {
            console.log('ES6模块Bootstrap已加载，跳过CDN版本')
          }
        }, 100)
      })
    </script>
  </body>
</html>
