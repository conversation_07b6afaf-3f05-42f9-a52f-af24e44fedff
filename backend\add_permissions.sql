-- 添加模板管理相关权限

-- 检查并添加 template.create 权限
INSERT IGNORE INTO permission (code, name, description, module, is_active, created_at, updated_at)
VALUES ('template.create', '创建模板', '创建新的表单模板和表单类型', 'template', 1, NOW(), NOW());

-- 检查并添加 template.edit 权限
INSERT IGNORE INTO permission (code, name, description, module, is_active, created_at, updated_at)
VALUES ('template.edit', '编辑模板', '编辑现有的表单模板和表单类型', 'template', 1, NOW(), NOW());

-- 显示所有模板相关权限
SELECT * FROM permission WHERE module = 'template' ORDER BY code;
