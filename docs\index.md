# 📚 梆梆安全-运维信息登记平台文档索引

## 🎯 快速导航

### 👤 按用户角色分类

#### 🆕 新用户
- [环境配置指南](deployment/environment-setup.md) - 系统环境配置
- [数据库配置](backend/database-setup.md) - 数据库环境设置
- [表单管理指南](user-guides/form-management.md) - 表单使用入门

#### 👨‍💻 开发者
- [编码规范](development/coding-standards.md) - 代码编写规范
- [API接口文档](backend/api-documentation.md) - 后端API说明
- [组件开发指南](frontend/component-guide.md) - 前端组件开发
- [测试指南](development/testing-guide.md) - 测试编写和执行

#### 👨‍🔧 系统管理员
- [部署脚本使用](deployment/deploy.sh) - 自动化部署
- [系统管理功能](user-guides/system-administration.md) - 系统管理
- [监控指南](operations/monitoring.md) - 系统监控
- [备份恢复](operations/backup-recovery.md) - 数据备份

#### 🔧 运维人员
- [故障排除指南](backend/troubleshooting.md) - 问题解决
- [性能调优](operations/performance-tuning.md) - 性能优化
- [安全配置](operations/security.md) - 安全设置

### 📋 按功能分类

#### 📝 表单功能
- [表单管理](user-guides/form-management.md) - 表单填写和管理
- [组件管理](user-guides/component-management.md) - 组件配置管理
- [Excel模板](user-guides/excel-templates.md) - 模板使用指南
- [错误处理](user-guides/error-handling.md) - 错误提示和故障排除

#### 🔐 权限管理
- [用户权限](user-guides/user-permissions.md) - 权限管理指南
- [API认证](backend/api-documentation.md#认证方式) - API认证说明

#### 🚀 部署运维
- [环境配置](deployment/environment-setup.md) - 环境搭建
- [Nginx配置](deployment/nginx.conf) - Web服务器配置
- [数据库配置](backend/database-setup.md) - 数据库设置

#### 💻 开发相关
- [编码规范](development/coding-standards.md) - 代码规范
- [贡献指南](development/contribution.md) - 项目贡献
- [文档维护](development/documentation-guide.md) - 文档管理

## 🔍 按问题类型查找

### ❓ 常见问题
- [连接问题](backend/troubleshooting.md#连接问题) - 数据库、Redis、API连接
- [环境问题](backend/troubleshooting.md#python环境问题) - Python、Node.js环境
- [权限问题](backend/troubleshooting.md#权限问题) - 文件、数据库权限

### 🐛 故障排除
- [数据库问题](backend/troubleshooting.md#数据库连接失败) - 数据库相关问题
- [前端问题](backend/troubleshooting.md#前端问题) - 前端构建和运行问题
- [Excel问题](backend/troubleshooting.md#excel相关问题) - Excel生成和下载问题

### ⚡ 性能优化
- [数据库优化](operations/performance-tuning.md#数据库优化) - 数据库性能
- [缓存优化](operations/performance-tuning.md#redis优化) - Redis缓存
- [应用优化](operations/performance-tuning.md#应用优化) - 应用性能

## 📊 文档统计

### 📁 目录结构
```
docs/
├── 📖 README.md (文档中心首页)
├── 🚀 deployment/ (部署相关 - 3个文档)
├── 🔧 backend/ (后端文档 - 4个文档)
├── 🎨 frontend/ (前端文档 - 3个文档)
├── 👥 user-guides/ (用户指南 - 5个文档)
├── 💻 development/ (开发文档 - 5个文档)
├── 🔧 operations/ (运维文档 - 4个文档)
└── 🛠️ scripts/ (工具脚本 - 1个脚本)
```

### 📈 文档覆盖范围
- ✅ 用户指南：完整覆盖
- ✅ 开发文档：完整覆盖
- ✅ 部署指南：完整覆盖
- ✅ 运维文档：完整覆盖
- ✅ API文档：完整覆盖
- ✅ 故障排除：完整覆盖

## 🔧 文档工具

### 📝 编辑工具
- **推荐编辑器**: VS Code + Markdown扩展
- **在线编辑**: GitHub Web编辑器
- **格式检查**: markdownlint

### 🔍 检查工具
```bash
# 运行文档检查脚本
./docs/scripts/doc-check.sh

# 检查内容：
# - 文档文件完整性
# - 内部链接有效性
# - 格式规范性
# - 代码块语法
# - 文档统计信息
```

### 📊 统计工具
```bash
# 文档统计
find docs/ -name "*.md" | wc -l          # 文档数量
find docs/ -name "*.md" -exec wc -l {} + # 总行数
find docs/ -name "*.md" -exec wc -w {} + # 总字数
```

## 🔄 文档更新流程

### 1. 确定更新内容
- 新功能文档
- 配置变更说明
- 问题修复记录
- 最佳实践更新

### 2. 选择文档位置
- **用户功能** → `user-guides/`
- **开发相关** → `development/`
- **部署运维** → `deployment/` 或 `operations/`
- **API变更** → `backend/api-documentation.md`

### 3. 编写和验证
- 遵循[文档维护指南](development/documentation-guide.md)
- 运行文档检查脚本
- 更新相关链接

### 4. 发布更新
- 提交代码变更
- 更新版本信息
- 通知相关人员

## 📞 获取帮助

### 📧 联系方式
- **技术支持**: <EMAIL>
- **文档反馈**: <EMAIL>
- **问题报告**: GitHub Issues

### 🔗 相关资源
- [项目主页](../README.md)
- [GitHub仓库](https://github.com/your-org/export_excel)
- [在线演示](https://demo.example.com)

---

> 📝 **文档维护**: 本文档会定期更新，最后更新时间：2024-12-19
> 
> 💡 **建议**: 将此页面加入书签，方便快速查找所需文档
