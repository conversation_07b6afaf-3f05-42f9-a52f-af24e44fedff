# 模板选择功能使用说明

## 功能概述

新增的模板选择功能允许用户在生成表单时选择要使用的模板文件，而不是只能使用系统默认的活动模板。

## 功能特点

1. **动态模板加载**: 根据当前选择的表单类型自动加载对应的模板列表
2. **智能默认选择**: 优先选择活动模板，如果没有活动模板则选择第一个可用模板
3. **模板信息展示**: 显示模板的详细信息，包括文件名、别名、状态等
4. **实时更新**: 表单类型变更时自动更新可用模板列表

## 使用方法

### 1. 在表单页面选择模板

1. 打开表单填写页面 (`/fill-sheet`)
2. 在表单头部可以看到"选择模板文件"选择器
3. 下拉选择器会显示当前表单类型的所有可用模板
4. 选择所需的模板，系统会自动保存选择

### 2. 模板信息查看

- 点击模板选择器旁边的信息按钮可以查看模板详细信息
- 信息包括：文件名、别名、表单类型、状态、最后修改时间

### 3. 表单提交

- 选择模板后正常填写表单
- 提交时系统会使用选择的模板生成Excel文件
- 如果没有选择模板，系统会使用默认的活动模板

## 技术实现

### 前端组件

1. **TemplateSelector.vue**: 模板选择器组件
   - 位置: `frontend/src/components/forms/common/TemplateSelector.vue`
   - 功能: 显示模板列表、处理模板选择

2. **FormHeader.vue**: 表单头部组件（已更新）
   - 集成了模板选择器
   - 处理模板变更事件

3. **FillSheet.vue**: 主表单页面（已更新）
   - 添加了模板选择状态管理
   - 在提交时传递选择的模板信息

### 后端API

1. **获取模板列表**: `GET /excel/templates/by-form-type/{form_type}`
   - 根据表单类型获取可用模板列表
   - 返回模板的详细信息

2. **表单提交**: `POST /excel/fill_sheet`（已更新）
   - 支持接收用户选择的模板ID
   - 优先使用用户选择的模板，回退到默认模板

### 数据流

1. 用户选择表单类型 → 自动加载对应模板列表
2. 用户选择模板 → 更新表单状态
3. 用户提交表单 → 后端使用选择的模板生成Excel

## 配置说明

### 模板管理

1. 通过模板管理页面 (`/template-manager`) 上传和管理模板
2. 每个模板都有对应的表单类型
3. 可以设置模板的活动状态和别名

### 权限控制

- 模板选择功能对所有有表单填写权限的用户开放
- 模板管理需要相应的管理权限

## 故障排除

### 常见问题

1. **模板列表为空**
   - 检查是否为当前表单类型上传了模板
   - 确认模板文件存在且未损坏

2. **模板选择无效**
   - 检查网络连接
   - 查看浏览器控制台是否有错误信息

3. **生成的Excel使用了错误的模板**
   - 确认选择的模板与表单类型匹配
   - 检查模板文件是否完整

### 调试信息

- 前端控制台会输出模板选择的调试信息
- 后端日志会记录模板使用情况

## 更新日志

### v1.0.0 (2025-01-21)
- 新增模板选择功能
- 支持动态模板加载
- 集成到现有表单系统

## 相关文档

- [模板管理说明](./template-management.md)
- [表单系统架构](./form-system-architecture.md)
- [API文档](./api-documentation.md)
