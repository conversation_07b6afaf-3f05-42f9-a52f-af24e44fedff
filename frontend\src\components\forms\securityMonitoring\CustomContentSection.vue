<template>
  <collapsible-card card-class="border-danger" storage-key="custom-content-section">
    <template #header>
      <i class="bi bi-gear me-2"></i>运维定制内容
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-danger">定制项数量: {{ customList.length }}</span>
        <span v-for="(item, index) in customList" :key="'custom-summary-' + index" class="badge bg-light text-dark border">
          {{ item.title || '未命名定制项' }}
        </span>
      </div>
    </template>

    <!-- 运维定制内容列表 -->
    <div class="custom-content-section">
      <div class="section-header mb-3">
        <h6 class="section-title">
          <i class="bi bi-list-ul me-2 text-danger"></i>
          定制内容列表
        </h6>
      </div>

      <!-- 空状态提示 -->
      <div v-if="savedCustomList.length === 0 && !isAdding" class="empty-state-compact">
        <i class="bi bi-gear-wide-connected me-2 text-muted"></i>
        <span class="text-muted">暂无运维定制内容，点击上方按钮添加</span>
      </div>

      <!-- 定制内容列表 -->
      <div v-if="savedCustomList.length > 0 || isAdding" class="custom-items-list">
        <!-- 已保存的记录（紧凑显示） -->
        <div v-for="(item, index) in savedCustomList" :key="'saved-custom-' + index" class="custom-record">
          <div class="record-header">
            <span class="record-number">{{ index + 1 }}</span>
            <span class="record-title">{{ item.title || '未命名定制项' }}</span>
            <div class="record-actions">
              <button type="button" class="btn btn-sm btn-outline-primary me-1" @click="editCustomItem(index)" title="编辑">
                <i class="bi bi-pencil"></i>
              </button>
              <button type="button" class="btn btn-sm btn-outline-danger" @click="removeCustomItem(index)" title="删除">
                <i class="bi bi-trash"></i>
              </button>
            </div>
          </div>
          <div class="record-content">
            {{ item.content || '暂无详细内容' }}
          </div>
        </div>

        <!-- 新建/编辑表单 -->
        <div v-if="isAdding || isEditing" class="custom-item-form">
          <div class="form-header">
            <h6 class="form-title">
              <i class="bi bi-gear me-2 text-danger"></i>
              {{ isEditing ? '编辑定制项' : '新建定制项' }}
            </h6>
            <div class="form-actions">
              <button type="button" class="btn btn-sm btn-success me-1" @click="isEditing ? saveEditCustomItem() : saveNewCustomItem()" title="保存">
                <i class="bi bi-check-lg me-1"></i>保存
              </button>
              <button type="button" class="btn btn-sm btn-secondary" @click="isEditing ? cancelEditCustomItem() : cancelAddCustomItem()" title="取消">
                <i class="bi bi-x-lg"></i>
              </button>
            </div>
          </div>

          <div class="form-content">
            <div class="content-section mb-3">
              <div class="content-header mb-2">
                <h6 class="content-title">
                  <i class="bi bi-tag me-2 text-primary"></i>
                  标题信息
                </h6>
              </div>
              <div class="form-floating">
                <input
                  type="text"
                  class="form-control enhanced-input"
                  id="newCustomTitle"
                  v-model="newCustomItem.title"
                  placeholder="请输入定制项标题，如：密码策略调整、权限配置等"
                >
                <label for="newCustomTitle">
                  <i class="bi bi-pencil me-1"></i>定制项标题
                </label>
              </div>
            </div>

            <div class="content-section">
              <div class="content-header mb-2">
                <h6 class="content-title">
                  <i class="bi bi-file-text me-2 text-success"></i>
                  详细内容
                </h6>
              </div>
              <div class="form-floating">
                <textarea
                  class="form-control enhanced-textarea"
                  id="newCustomContent"
                  v-model="newCustomItem.content"
                  style="height: 120px; resize: vertical;"
                  placeholder="请详细描述运维定制的具体内容、操作步骤、配置参数等..."
                ></textarea>
                <label for="newCustomContent">
                  <i class="bi bi-journal-text me-1"></i>定制项详细内容
                </label>
              </div>
              <div class="form-text mt-2">
                <i class="bi bi-lightbulb me-1"></i>
                建议包含：配置参数、操作步骤、注意事项、影响范围等信息
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加按钮 -->
      <div class="add-button-section">
        <button type="button" class="btn btn-add-custom" @click="addCustomItem">
          <i class="bi bi-plus-circle me-2"></i>
          <span>添加运维定制项</span>
        </button>
      </div>
    </div>


  </collapsible-card>
</template>

<script>

import CollapsibleCard from '../common/CollapsibleCard.vue'
import { createNewCustomItem } from '@/config/formDataConfig'

/**
 * 运维定制内容部分组件
 * 用于管理运维定制内容
 */
export default {
  name: 'CustomContentSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 运维定制内容列表
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isAdding: false, // 是否正在添加新项
      isEditing: false, // 是否正在编辑
      editingIndex: -1, // 正在编辑的项目索引
      newCustomItem: {
        title: '',
        content: ''
      }
    }
  },
  computed: {
    /**
     * 定制内容列表
     * 用于双向绑定
     */
    customList: {
      get() {
        return this.modelValue
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue)
      }
    },
    /**
     * 已保存的定制内容列表
     */
    savedCustomList() {
      return this.customList.filter(item => item.title || item.content)
    }
  },
  methods: {
    /**
     * 开始添加运维定制内容项
     */
    addCustomItem() {
      // 如果当前正在添加且有内容，先自动保存
      if (this.isAdding && (this.newCustomItem.title || this.newCustomItem.content)) {
        this.saveCurrentCustomItem()
      } else {
        // 开始新的添加
        this.isAdding = true
        this.newCustomItem = {
          title: '',
          content: ''
        }
      }
    },

    /**
     * 保存新的定制项
     */
    saveNewCustomItem() {
      if (this.newCustomItem.title || this.newCustomItem.content) {
        const newCustomList = [...this.customList]
        newCustomList.push({
          title: this.newCustomItem.title,
          content: this.newCustomItem.content
        })
        this.customList = newCustomList
      }
      this.cancelAddCustomItem()
    },

    /**
     * 仅保存当前内容，不关闭表单
     */
    saveCurrentCustomItem() {
      if (this.newCustomItem.title || this.newCustomItem.content) {
        const newCustomList = [...this.customList]
        newCustomList.push({
          title: this.newCustomItem.title,
          content: this.newCustomItem.content
        })
        this.customList = newCustomList

        // 清空当前表单但保持添加状态
        this.newCustomItem = {
          title: '',
          content: ''
        }
      }
    },

    /**
     * 取消添加定制项
     */
    cancelAddCustomItem() {
      this.isAdding = false
      this.newCustomItem = {
        title: '',
        content: ''
      }
    },

    /**
     * 删除运维定制内容项
     * @param {Number} index - 要删除的项的索引
     */
    removeCustomItem(index) {
      if (confirm('确定要删除这个定制项吗？')) {
        const newCustomList = [...this.customList]
        newCustomList.splice(index, 1)
        this.customList = newCustomList
      }
    },

    /**
     * 编辑运维定制内容项
     * @param {Number} index - 要编辑的项的索引
     */
    editCustomItem(index) {
      // 如果正在添加，先取消添加
      if (this.isAdding) {
        this.cancelAddCustomItem()
      }

      // 设置编辑状态
      this.isEditing = true
      this.editingIndex = index

      // 加载要编辑的数据
      const itemToEdit = this.customList[index]
      this.newCustomItem = {
        title: itemToEdit.title || '',
        content: itemToEdit.content || ''
      }
    },

    /**
     * 保存编辑的定制项
     */
    saveEditCustomItem() {
      if (this.newCustomItem.title || this.newCustomItem.content) {
        const newCustomList = [...this.customList]
        newCustomList[this.editingIndex] = {
          title: this.newCustomItem.title,
          content: this.newCustomItem.content
        }
        this.customList = newCustomList
      }
      this.cancelEditCustomItem()
    },

    /**
     * 取消编辑定制项
     */
    cancelEditCustomItem() {
      this.isEditing = false
      this.editingIndex = -1
      this.newCustomItem = {
        title: '',
        content: ''
      }
    }

  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

/* 运维定制内容分组样式 */
.custom-content-section {
  position: relative;
  padding: 1.5rem;
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #f8d7da;
  transition: all 0.3s ease;
}

.custom-content-section:hover {
  border-color: #f5c6cb;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f8d7da;
}

.section-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.section-title i {
  font-size: 1.1rem;
}

/* 紧凑空状态样式 */
.empty-state-compact {
  text-align: center;
  padding: 2rem;
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  border: 1px dashed #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

/* 定制项列表样式 */
.custom-items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.custom-item-wrapper {
  transition: all 0.3s ease;
}

/* 记录显示样式 */
.custom-record {
  background: white;
  border: 1px solid #f8d7da;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.custom-record:hover {
  border-color: #dc3545;
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.1);
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.record-number {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  flex-shrink: 0;
}

.record-title {
  font-weight: 600;
  color: #495057;
  margin-left: 0.75rem;
  flex-grow: 1;
  font-size: 0.9rem;
}

.record-actions {
  display: flex;
  gap: 0.25rem;
}

.record-actions .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 4px;
}

.record-content {
  color: #6c757d;
  font-size: 0.85rem;
  line-height: 1.4;
  margin-left: 2.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 新建表单样式 */
.custom-item-form {
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
  border: 2px solid #f8d7da;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.1);
  margin-top: 1rem;
}

.form-header {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-title {
  margin: 0;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
}

.form-content {
  padding: 1.5rem;
}

/* 内容分组样式 */
.content-section {
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.content-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
}

.content-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

/* 增强的输入框样式 */
.enhanced-input,
.enhanced-textarea {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.enhanced-input:focus,
.enhanced-textarea:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
  background-color: #ffffff;
}

.enhanced-input:hover:not(:focus),
.enhanced-textarea:hover:not(:focus) {
  border-color: #ced4da;
}

/* 标签样式增强 */
.form-floating > label {
  color: #6c757d;
  font-weight: 500;
}

.form-floating > .enhanced-input:focus ~ label,
.form-floating > .enhanced-input:not(:placeholder-shown) ~ label,
.form-floating > .enhanced-textarea:focus ~ label,
.form-floating > .enhanced-textarea:not(:placeholder-shown) ~ label {
  color: #dc3545;
  font-weight: 600;
}

/* 表单提示文字样式 */
.form-text {
  font-size: 0.875rem;
  color: #6c757d;
  display: flex;
  align-items: center;
}

.form-text i {
  color: #ffc107;
}

/* 添加按钮样式 */
.add-button-section {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  margin-top: 1rem;
}

.btn-add-custom {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  border: none;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
  display: inline-flex;
  align-items: center;
}

.btn-add-custom:hover {
  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(220, 53, 69, 0.3);
}

.btn-add-custom:active {
  transform: translateY(0);
}

/* 聚焦状态的分组高亮 */
.custom-content-section:focus-within {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.1);
}

/* 原有的浮动标签样式 */
.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .custom-content-section {
    padding: 1rem;
  }

  .section-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .custom-record {
    padding: 0.5rem 0.75rem;
  }

  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .record-title {
    margin-left: 0;
  }

  .record-content {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .form-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .form-content {
    padding: 1rem;
  }

  .content-section {
    padding: 0.75rem;
  }

  .empty-state-compact {
    padding: 1.5rem;
    font-size: 0.85rem;
  }

  .add-button-section {
    padding: 1rem;
  }

  .btn-add-custom {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* 动画效果 */
.custom-record {
  animation: slideInUp 0.3s ease-out;
}

.custom-item-form {
  animation: expandIn 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
