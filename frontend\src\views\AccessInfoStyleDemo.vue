<template>
  <div class="container-fluid py-4">
    <div class="row">
      <div class="col-12">
        <h2 class="mb-4">
          <i class="bi bi-palette me-2"></i>
          访问信息样式演示
        </h2>
        
        <!-- 样式对比说明 -->
        <div class="alert alert-info mb-4">
          <h5><i class="bi bi-info-circle me-2"></i>样式改进说明</h5>
          <p class="mb-2">新的访问信息样式具有以下特点：</p>
          <ul class="mb-0">
            <li><strong>统一设计语言</strong>：与详情页面保持一致的视觉风格</li>
            <li><strong>分组卡片</strong>：清晰的分组结构，每个访问类型独立展示</li>
            <li><strong>认证信息美化</strong>：账号密码字段采用专业的认证卡片样式</li>
            <li><strong>交互增强</strong>：悬停效果、复制按钮、密码显示/隐藏</li>
            <li><strong>响应式设计</strong>：适配不同屏幕尺寸</li>
          </ul>
        </div>

        <!-- 安全监测表单访问信息演示 -->
        <div class="demo-section mb-5">
          <h4 class="mb-3">
            <i class="bi bi-shield-check me-2 text-warning"></i>
            安全监测表单 - 访问信息
          </h4>
          <div class="demo-container">
            <!-- 业务功能页面访问 -->
            <div class="access-group-card access-success">
              <div class="access-group-header">
                <h6 class="access-group-title">
                  <i class="bi bi-globe text-primary"></i>
                  业务功能页面访问
                </h6>
              </div>
              <div class="card-body">
                <!-- URL地址部分 -->
                <div class="access-url-section">
                  <div class="access-url-label">
                    <i class="bi bi-link-45deg me-1"></i>访问地址
                  </div>
                  <div class="access-url-value">
                    <div class="form-floating-enhanced flex-grow-1">
                      <input
                        type="text"
                        class="form-control auto-filled"
                        value="https://*************:8443"
                        readonly
                      >
                      <label>业务功能页面地址 <span class="text-danger">*</span></label>
                      <div class="auto-filled-indicator">
                        <i class="bi bi-check"></i>
                      </div>
                    </div>
                    <a href="https://*************:8443" target="_blank" class="access-url-copy" title="打开链接">
                      <i class="bi bi-box-arrow-up-right"></i>
                    </a>
                    <button class="access-url-copy" title="复制地址">
                      <i class="bi bi-clipboard"></i>
                    </button>
                  </div>
                </div>
                
                <div class="access-info-tip">
                  <i class="bi bi-info-circle"></i>
                  管理后台访问地址，自动填充web-service-nginx组件所在的IP地址
                </div>

                <!-- 认证信息 -->
                <div class="row g-3 mt-2">
                  <div class="col-md-6">
                    <div class="auth-card">
                      <div class="auth-card-header">
                        <i class="bi bi-shield-lock text-danger"></i>
                        <span class="auth-card-title">超级管理员</span>
                      </div>
                      <div class="auth-card-body">
                        <div class="auth-field">
                          <div class="auth-field-label">
                            <i class="bi bi-person"></i>账号
                          </div>
                          <div class="auth-field-value">
                            <input
                              type="text"
                              class="auth-field-input"
                              value="admin"
                              placeholder="超级管理员账号"
                            >
                          </div>
                        </div>
                        <div class="auth-field">
                          <div class="auth-field-label">
                            <i class="bi bi-key"></i>密码
                          </div>
                          <div class="auth-field-value">
                            <input
                              type="password"
                              class="auth-field-input"
                              value="admin123"
                              placeholder="超级管理员密码"
                            >
                            <button class="password-toggle-btn" title="显示密码">
                              <i class="bi bi-eye"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="auth-card">
                      <div class="auth-card-header">
                        <i class="bi bi-person-gear text-success"></i>
                        <span class="auth-card-title">客户管理员</span>
                      </div>
                      <div class="auth-card-body">
                        <div class="auth-field">
                          <div class="auth-field-label">
                            <i class="bi bi-person"></i>账号
                          </div>
                          <div class="auth-field-value">
                            <input
                              type="text"
                              class="auth-field-input"
                              value="customer_admin"
                              placeholder="客户管理员账号"
                            >
                          </div>
                        </div>
                        <div class="auth-field">
                          <div class="auth-field-label">
                            <i class="bi bi-key"></i>密码
                          </div>
                          <div class="auth-field-value">
                            <input
                              type="password"
                              class="auth-field-input"
                              value="customer123"
                              placeholder="客户管理员密码"
                            >
                            <button class="password-toggle-btn" title="显示密码">
                              <i class="bi bi-eye"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- init访问 -->
            <div class="access-group-card access-warning">
              <div class="access-group-header">
                <h6 class="access-group-title">
                  <i class="bi bi-gear text-warning"></i>
                  init访问
                </h6>
              </div>
              <div class="card-body">
                <!-- URL地址部分 -->
                <div class="access-url-section">
                  <div class="access-url-label">
                    <i class="bi bi-link-45deg me-1"></i>访问地址
                  </div>
                  <div class="access-url-value">
                    <div class="form-floating-enhanced flex-grow-1">
                      <input
                        type="text"
                        class="form-control auto-filled"
                        value="http://*************:8181"
                        readonly
                      >
                      <label>init地址 <span class="text-danger">*</span></label>
                      <div class="auto-filled-indicator">
                        <i class="bi bi-check"></i>
                      </div>
                    </div>
                    <a href="http://*************:8181" target="_blank" class="access-url-copy" title="打开链接">
                      <i class="bi bi-box-arrow-up-right"></i>
                    </a>
                    <button class="access-url-copy" title="复制地址">
                      <i class="bi bi-clipboard"></i>
                    </button>
                  </div>
                </div>
                
                <div class="access-info-tip">
                  <i class="bi bi-info-circle"></i>
                  初始化配置页面地址，自动填充init组件所在的IP地址
                </div>

                <!-- 认证信息 -->
                <div class="row g-3 mt-2">
                  <div class="col-md-6">
                    <div class="auth-card">
                      <div class="auth-card-header">
                        <i class="bi bi-key text-warning"></i>
                        <span class="auth-card-title">init认证</span>
                      </div>
                      <div class="auth-card-body">
                        <div class="auth-field">
                          <div class="auth-field-label">
                            <i class="bi bi-person"></i>用户名
                          </div>
                          <div class="auth-field-value">
                            <input
                              type="text"
                              class="auth-field-input"
                              value="init_user"
                              placeholder="init用户名"
                            >
                          </div>
                        </div>
                        <div class="auth-field">
                          <div class="auth-field-label">
                            <i class="bi bi-key"></i>密码
                          </div>
                          <div class="auth-field-value">
                            <input
                              type="password"
                              class="auth-field-input"
                              value="init123"
                              placeholder="init密码"
                            >
                            <button class="password-toggle-btn" title="显示密码">
                              <i class="bi bi-eye"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
          <button class="btn btn-secondary" @click="$router.go(-1)">
            <i class="bi bi-arrow-left me-2"></i>返回
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AccessInfoStyleDemo'
}
</script>

<style scoped>
/* 访问信息统一样式 */
.access-group-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.access-group-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.access-group-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1rem 1.25rem;
  border-bottom: 2px solid #dee2e6;
  position: relative;
}

.access-group-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #007bff, #0056b3);
}

.access-group-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin: 0;
  display: flex;
  align-items: center;
}

.access-group-title i {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.access-url-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid #17a2b8;
}

.access-url-label {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.access-url-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.access-url-copy {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.access-url-copy:hover {
  background: #e9ecef;
  color: #495057;
}

.auth-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.auth-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
  transform: translateY(-1px);
}

.auth-card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-card-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.auth-card-body {
  padding: 1rem;
}

.auth-field {
  margin-bottom: 0.75rem;
}

.auth-field:last-child {
  margin-bottom: 0;
}

.auth-field-label {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.auth-field-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-field-input {
  flex: 1;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.auth-field-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.password-toggle-btn {
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.5rem;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
}

.password-toggle-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
  color: #495057;
}

.form-floating-enhanced {
  position: relative;
  margin-bottom: 1rem;
}

.form-floating-enhanced .form-control {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  padding: 1rem 0.75rem 0.5rem 0.75rem;
  transition: all 0.3s ease;
}

.form-floating-enhanced .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.form-floating-enhanced label {
  color: #6c757d;
  font-weight: 500;
}

.access-info-tip {
  background: linear-gradient(135deg, #e7f3ff 0%, #f0f8ff 100%);
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: #0c5460;
}

.access-info-tip i {
  color: #17a2b8;
  margin-right: 0.5rem;
}

.auto-filled {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  color: #6c757d !important;
  cursor: not-allowed;
}

.auto-filled-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #28a745;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
}

.access-success .access-group-header::before {
  background: linear-gradient(to bottom, #28a745, #1e7e34);
}

.access-warning .access-group-header::before {
  background: linear-gradient(to bottom, #ffc107, #e0a800);
}

.demo-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e9ecef;
}

.demo-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #dee2e6;
}
</style>
