<template>
  <collapsible-card card-class="border-warning" storage-key="multi-maintenance-record-section">
    <template #header>
      <i class="bi bi-tools me-2"></i>维护记录
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-warning text-dark">记录数量: {{ maintenanceList.length }}</span>
        <span v-for="(item, index) in maintenanceList.slice(0, 3)" :key="'maintenance-summary-' + index" class="badge bg-light text-dark border">
          {{ item.type || '未命名维护' }}
        </span>
        <span v-if="maintenanceList.length > 3" class="badge bg-secondary">+{{ maintenanceList.length - 3 }}...</span>
      </div>
    </template>

    <!-- 维护记录内容 -->
    <div class="maintenance-content-section">
      <!-- 空状态 -->
      <div v-if="savedMaintenanceList.length === 0 && !isAdding" class="empty-state-compact">
        <div class="text-muted">
          <i class="bi bi-tools me-2"></i>
          暂无维护记录，点击下方按钮添加维护记录
        </div>
      </div>

      <!-- 维护记录列表 -->
      <div v-if="savedMaintenanceList.length > 0 || isAdding" class="maintenance-items-list">
        <!-- 已保存的记录（紧凑显示） - 编辑时隐藏 -->
        <div v-if="!isEditing" v-for="(item, index) in savedMaintenanceList" :key="'saved-maintenance-' + index" class="maintenance-record" :data-maintenance-index="index">
          <div class="record-header">
            <span class="record-number">{{ index + 1 }}</span>
            <span class="record-title">{{ item.type || '未命名维护' }}</span>
            <div class="record-meta">
              <span class="record-time">{{ formatDateTime(item.time) }}</span>
              <span class="record-staff">{{ item.staff || '未知' }}</span>
            </div>
            <div class="record-actions">
              <button type="button" class="btn btn-sm btn-outline-primary me-1" @click="editMaintenanceItem(index)" title="编辑">
                <i class="bi bi-pencil"></i>
              </button>
              <button type="button" class="btn btn-sm btn-outline-danger" @click="removeMaintenanceItem(index)" title="删除">
                <i class="bi bi-trash"></i>
              </button>
            </div>
          </div>
          <div class="record-content">
            {{ item.content || '暂无详细内容' }}
          </div>
          <div v-if="item.onesLink" class="record-link">
            <i class="bi bi-link-45deg me-1"></i>
            <a :href="item.onesLink" target="_blank" class="text-decoration-none">{{ item.onesLink }}</a>
          </div>
        </div>

        <!-- 新建/编辑维护记录表单 -->
        <div v-if="isAdding || isEditing" class="maintenance-item-form">
          <div class="form-header">
            <h6 class="form-title">
              <i class="bi bi-plus-circle me-2"></i>
              {{ isEditing ? '编辑维护记录' : '新建维护记录' }}
            </h6>
            <div class="form-actions">
              <button type="button" class="btn btn-sm btn-light" @click="isEditing ? saveEditMaintenanceItem() : saveNewMaintenanceItem()" title="保存">
                <i class="bi bi-check-lg"></i>
              </button>
              <button type="button" class="btn btn-sm btn-outline-light" @click="isEditing ? cancelEditMaintenanceItem() : cancelAddMaintenanceItem()" title="取消">
                <i class="bi bi-x-lg"></i>
              </button>
            </div>
          </div>

          <div class="form-content">
            <!-- 基本信息行 -->
            <div class="row g-3 mb-3">
              <div class="col-md-3">
                <div class="form-floating">
                  <input
                    type="datetime-local"
                    class="form-control enhanced-input"
                    id="newMaintenanceTime"
                    v-model="newMaintenanceItem.time"
                  >
                  <label for="newMaintenanceTime">
                    <i class="bi bi-calendar-event me-1"></i>维护时间
                  </label>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-floating">
                  <input
                    type="text"
                    class="form-control enhanced-input"
                    id="newMaintenanceStaff"
                    v-model="newMaintenanceItem.staff"
                    placeholder="维护人员"
                  >
                  <label for="newMaintenanceStaff">
                    <i class="bi bi-person me-1"></i>维护人员
                  </label>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-floating">
                  <select
                    class="form-select enhanced-input"
                    id="newMaintenanceType"
                    v-model="newMaintenanceItem.type"
                  >
                    <option value="">-- 请选择维护类型 --</option>
                    <option value="平台部署">🚀 平台部署</option>
                    <option value="平台升级">⬆️ 平台升级</option>
                    <option value="配置变更">⚙️ 配置变更</option>
                    <option value="常规维护">🔧 常规维护</option>
                    <option value="漏洞修复">🛡️ 漏洞修复</option>
                    <option value="其他">📝 其他</option>
                  </select>
                  <label for="newMaintenanceType">
                    <i class="bi bi-gear me-1"></i>维护类型
                  </label>
                </div>
                <!-- 自定义维护类型输入框 -->
                <div v-if="newMaintenanceItem.type === '其他'" class="mt-3">
                  <div class="form-floating">
                    <input
                      type="text"
                      class="form-control enhanced-input custom-type-input"
                      id="newCustomMaintenanceType"
                      v-model="newMaintenanceItem.customType"
                      placeholder="请详细描述自定义维护类型"
                    >
                    <label for="newCustomMaintenanceType">
                      <i class="bi bi-pencil me-1"></i>自定义维护类型
                    </label>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-floating">
                  <input
                    type="url"
                    class="form-control enhanced-input"
                    id="newMaintenanceOnesLink"
                    v-model="newMaintenanceItem.onesLink"
                    placeholder="相关ONES任务链接"
                  >
                  <label for="newMaintenanceOnesLink">
                    <i class="bi bi-link-45deg me-1"></i>ONES任务链接
                  </label>
                </div>
              </div>
            </div>

            <!-- 维护内容 -->
            <div class="content-section">
              <div class="content-header mb-2">
                <h6 class="content-title">
                  <i class="bi bi-file-text me-2 text-warning"></i>
                  维护详细内容
                </h6>
              </div>
              <div class="form-floating">
                <textarea
                  class="form-control enhanced-textarea"
                  id="newMaintenanceContent"
                  v-model="newMaintenanceItem.content"
                  style="height: 120px; resize: vertical;"
                  placeholder="请详细描述本次维护的具体内容、操作步骤、影响范围等..."
                ></textarea>
                <label for="newMaintenanceContent">
                  <i class="bi bi-journal-text me-1"></i>维护详细内容
                </label>
              </div>
              <div class="form-text mt-2">
                <i class="bi bi-lightbulb me-1"></i>
                建议包含：操作步骤、影响范围、注意事项、回滚方案等信息
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加按钮 - 编辑时隐藏 -->
      <div v-if="!isEditing" class="add-button-section">
        <button type="button" class="btn btn-add-maintenance" @click="addMaintenanceItem">
          <i class="bi bi-plus-circle me-2"></i>
          {{ isAdding ? '保存并添加新记录' : '添加维护记录' }}
        </button>
      </div>
    </div>
  </collapsible-card>
</template>

<script>
/**
 * 多记录维护记录组件
 * 类似运维定制内容，支持添加多个维护记录
 */
import CollapsibleCard from './CollapsibleCard.vue'

export default {
  name: 'MultiMaintenanceRecordSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 维护记录列表
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isAdding: false, // 是否正在添加新项
      isEditing: false, // 是否正在编辑
      editingIndex: -1, // 正在编辑的项目索引
      newMaintenanceItem: {
        time: '',
        staff: '',
        type: '',
        customType: '', // 自定义维护类型
        onesLink: '',
        content: ''
      }
    }
  },
  computed: {
    /**
     * 维护记录列表
     * 用于双向绑定
     */
    maintenanceList: {
      get() {
        return this.modelValue
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue)
      }
    },
    /**
     * 已保存的维护记录列表
     */
    savedMaintenanceList() {
      return this.maintenanceList.filter(item =>
        item.time || item.staff || item.type || item.content
      )
    }
  },
  methods: {
    /**
     * 开始添加维护记录项
     */
    addMaintenanceItem() {
      // 如果正在编辑，先取消编辑
      if (this.isEditing) {
        this.cancelEditMaintenanceItem()
      }

      // 如果当前正在添加且有内容，先保存当前内容
      if (this.isAdding && this.hasMaintenanceContent()) {
        this.saveCurrentMaintenanceItem()
      } else {
        // 开始新的添加
        this.isAdding = true
        this.newMaintenanceItem = {
          time: new Date().toISOString().slice(0, 16),
          staff: '',
          type: '',
          customType: '',
          onesLink: '',
          content: ''
        }
      }

      // 滚动到表单位置
      this.$nextTick(() => {
        const formElement = document.querySelector('.maintenance-item-form')
        if (formElement) {
          formElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          })
        }
      })
    },

    /**
     * 保存新的维护项
     */
    saveNewMaintenanceItem() {
      if (this.hasMaintenanceContent()) {
        const newMaintenanceList = [...this.maintenanceList]
        // 处理维护类型：如果选择"其他"，使用自定义类型
        const finalType = this.newMaintenanceItem.type === '其他'
          ? this.newMaintenanceItem.customType
          : this.newMaintenanceItem.type

        newMaintenanceList.push({
          time: this.newMaintenanceItem.time,
          staff: this.newMaintenanceItem.staff,
          type: finalType,
          onesLink: this.newMaintenanceItem.onesLink,
          content: this.newMaintenanceItem.content
        })
        this.maintenanceList = newMaintenanceList
      }
      this.cancelAddMaintenanceItem()
    },

    /**
     * 仅保存当前内容，不关闭表单
     * 用于"保存并添加新记录"功能
     */
    saveCurrentMaintenanceItem() {
      if (this.hasMaintenanceContent()) {
        const newMaintenanceList = [...this.maintenanceList]
        // 处理维护类型：如果选择"其他"，使用自定义类型
        const finalType = this.newMaintenanceItem.type === '其他'
          ? this.newMaintenanceItem.customType
          : this.newMaintenanceItem.type

        newMaintenanceList.push({
          time: this.newMaintenanceItem.time,
          staff: this.newMaintenanceItem.staff,
          type: finalType,
          onesLink: this.newMaintenanceItem.onesLink,
          content: this.newMaintenanceItem.content
        })
        this.maintenanceList = newMaintenanceList

        // 清空当前表单但保持添加状态
        this.newMaintenanceItem = {
          time: new Date().toISOString().slice(0, 16),
          staff: '',
          type: '',
          customType: '',
          onesLink: '',
          content: ''
        }
      }
    },

    /**
     * 取消添加维护项
     */
    cancelAddMaintenanceItem() {
      this.isAdding = false
      this.newMaintenanceItem = {
        time: '',
        staff: '',
        type: '',
        customType: '',
        onesLink: '',
        content: ''
      }
    },

    /**
     * 删除维护记录项
     * @param {Number} index - 要删除的项的索引
     */
    removeMaintenanceItem(index) {
      if (confirm('确定要删除这个维护记录吗？')) {
        const newMaintenanceList = [...this.maintenanceList]
        newMaintenanceList.splice(index, 1)
        this.maintenanceList = newMaintenanceList
      }
    },

    /**
     * 编辑维护记录项
     * @param {Number} index - 要编辑的项的索引
     */
    editMaintenanceItem(index) {
      // 如果正在添加，先取消添加
      if (this.isAdding) {
        this.cancelAddMaintenanceItem()
      }

      // 设置编辑状态
      this.isEditing = true
      this.editingIndex = index

      // 加载要编辑的数据
      const itemToEdit = this.maintenanceList[index]
      this.newMaintenanceItem = {
        time: itemToEdit.time || '',
        staff: itemToEdit.staff || '',
        type: itemToEdit.type || '',
        customType: '', // 如果是自定义类型，会在下面处理
        onesLink: itemToEdit.onesLink || '',
        content: itemToEdit.content || ''
      }

      // 处理自定义维护类型
      const predefinedTypes = ['平台部署', '平台升级', '配置变更', '常规维护', '漏洞修复']
      if (itemToEdit.type && !predefinedTypes.includes(itemToEdit.type)) {
        this.newMaintenanceItem.type = '其他'
        this.newMaintenanceItem.customType = itemToEdit.type
      }

      // 滚动到表单位置
      this.$nextTick(() => {
        const formElement = document.querySelector('.maintenance-item-form')
        if (formElement) {
          formElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          })
        }
      })
    },

    /**
     * 保存编辑的维护项
     */
    saveEditMaintenanceItem() {
      if (this.hasMaintenanceContent()) {
        const newMaintenanceList = [...this.maintenanceList]
        // 处理维护类型：如果选择"其他"，使用自定义类型
        const finalType = this.newMaintenanceItem.type === '其他'
          ? this.newMaintenanceItem.customType
          : this.newMaintenanceItem.type

        newMaintenanceList[this.editingIndex] = {
          time: this.newMaintenanceItem.time,
          staff: this.newMaintenanceItem.staff,
          type: finalType,
          onesLink: this.newMaintenanceItem.onesLink,
          content: this.newMaintenanceItem.content
        }
        this.maintenanceList = newMaintenanceList
      }
      this.cancelEditMaintenanceItem()
    },

    /**
     * 取消编辑维护项
     */
    cancelEditMaintenanceItem() {
      this.isEditing = false
      this.editingIndex = -1
      this.newMaintenanceItem = {
        time: '',
        staff: '',
        type: '',
        customType: '',
        onesLink: '',
        content: ''
      }
    },

    /**
     * 检查是否有维护内容
     */
    hasMaintenanceContent() {
      return this.newMaintenanceItem.time ||
             this.newMaintenanceItem.staff ||
             this.newMaintenanceItem.type ||
             this.newMaintenanceItem.customType ||
             this.newMaintenanceItem.content
    },



    /**
     * 格式化日期时间
     */
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '未设置'
      try {
        const date = new Date(dateTimeStr)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (e) {
        return dateTimeStr
      }
    }
  },


}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

/* 维护记录分组样式 */
.maintenance-content-section {
  position: relative;
  padding: 1.5rem;
  background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #ffc107;
  transition: all 0.3s ease;
}

.maintenance-content-section:hover {
  border-color: #ffb300;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #ffc107;
}

.section-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.section-title i {
  font-size: 1.1rem;
}

/* 紧凑空状态样式 */
.empty-state-compact {
  text-align: center;
  padding: 2rem;
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  border: 1px dashed #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

/* 维护项列表样式 */
.maintenance-items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.maintenance-item-wrapper {
  transition: all 0.3s ease;
}

/* 记录显示样式 */
.maintenance-record {
  background: white;
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.maintenance-record:hover {
  border-color: #ff8f00;
  box-shadow: 0 2px 6px rgba(255, 193, 7, 0.1);
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.record-number {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  flex-shrink: 0;
}

.record-title {
  font-weight: 600;
  color: #495057;
  margin-left: 0.75rem;
  flex-grow: 1;
  font-size: 0.9rem;
}

.record-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 0.75rem;
  color: #6c757d;
  margin-right: 0.5rem;
}

.record-actions {
  display: flex;
  gap: 0.25rem;
}

.record-actions .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 4px;
}

.record-time {
  font-weight: 500;
}

.record-staff {
  margin-top: 0.1rem;
}

.record-content {
  color: #6c757d;
  font-size: 0.85rem;
  line-height: 1.4;
  margin-left: 2.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.record-link {
  margin-left: 2.25rem;
  margin-top: 0.5rem;
  font-size: 0.8rem;
}

.record-link a {
  color: #007bff;
}

/* 新建表单样式 */
.maintenance-item-form {
  background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
  border: 2px solid #ffc107;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.1);
  margin-top: 1rem;
}

.form-header {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-title {
  margin: 0;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
}

.form-content {
  padding: 1.5rem;
}

/* 内容分组样式 */
.content-section {
  background: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.content-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
}

.content-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

/* 增强的输入框样式 */
.enhanced-input,
.enhanced-textarea {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

/* 自定义维护类型输入框样式 */
.custom-type-input {
  border-color: #ffc107 !important;
  background-color: #fff8e1 !important;
}

.enhanced-input:focus,
.enhanced-textarea:focus {
  border-color: #ffc107;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.15);
  background-color: #ffffff;
}

.enhanced-input:hover:not(:focus),
.enhanced-textarea:hover:not(:focus) {
  border-color: #ced4da;
}

/* 标签样式增强 */
.form-floating > label {
  color: #6c757d;
  font-weight: 500;
}

.form-floating > .enhanced-input:focus ~ label,
.form-floating > .enhanced-input:not(:placeholder-shown) ~ label,
.form-floating > .enhanced-textarea:focus ~ label,
.form-floating > .enhanced-textarea:not(:placeholder-shown) ~ label {
  color: #ffc107;
  font-weight: 600;
}

/* 表单提示文字样式 */
.form-text {
  font-size: 0.875rem;
  color: #6c757d;
  display: flex;
  align-items: center;
}

.form-text i {
  color: #ffc107;
}

/* 添加按钮样式 */
.add-button-section {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  margin-top: 1rem;
}

.btn-add-maintenance {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
  border: none;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(255, 193, 7, 0.2);
  display: inline-flex;
  align-items: center;
}

.btn-add-maintenance:hover {
  background: linear-gradient(135deg, #ff8f00 0%, #f57c00 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 193, 7, 0.3);
}

.btn-add-maintenance:active {
  transform: translateY(0);
}

/* 聚焦状态的分组高亮 */
.maintenance-content-section:focus-within {
  border-color: #ffc107;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .maintenance-content-section {
    padding: 1rem;
  }

  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .record-title {
    margin-left: 0;
  }

  .record-meta {
    align-items: flex-start;
    margin-right: 0;
  }

  .record-content {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .record-link {
    margin-left: 0;
  }

  .form-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .form-content {
    padding: 1rem;
  }

  .content-section {
    padding: 0.75rem;
  }

  .empty-state-compact {
    padding: 1.5rem;
    font-size: 0.85rem;
  }

  .add-button-section {
    padding: 1rem;
  }

  .btn-add-maintenance {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* 动画效果 */
.maintenance-record {
  animation: slideInUp 0.3s ease-out;
}

.maintenance-item-form {
  animation: expandIn 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandIn {
  from {
    opacity: 0;
    transform: scaleY(0.8);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}


</style>
