/**
 * 表单系统类型定义
 * 为表单数据映射和升级用户配置提供完整的TypeScript类型支持
 */

// 表单类型枚举
export type FormType = '安全测评' | '安全监测' | '应用加固'

// 升级用户配置接口
export interface UpgradeUserConfig {
  username: string
  password: string
}

// 应用加固表单数据接口
export interface AppHardeningFormData {
  公司名称: string
  文档后缀: string
  客户: string
  客户标识: string
  部署的平台版本: string
  平台访问地址: string
  管理员信息: string
  升级平台地址: string
  升级用户配置: UpgradeUserConfig
  记录日期: string
  编辑人: string
  维护记录: MaintenanceRecord[]
  服务器信息: ServerInfo[]
}

// 安全监测表单数据接口
export interface SecurityMonitoringFormData {
  公司名称: string
  文档后缀: string
  客户标识: string
  日活: string
  标准或定制: string
  前端版本: string
  后端版本: string
  业务功能页面地址: string
  记录日期: string
  编辑人: string
  维护记录: MaintenanceRecord[]
  运维定制内容: CustomContent[]
  客户APP: ClientApp[]
  服务器信息: ServerInfo[]
}

// 安全测评表单数据接口
export interface SecurityTestingFormData {
  公司名称: string
  文档后缀: string
  客户标识: string
  部署包版本: string
  管理员页面IP: string
  用户页面IP: string
  升级页面IP: string
  升级用户账号: string
  升级用户密码: string
  记录日期: string
  编辑人: string
  维护记录: MaintenanceRecord[]
  服务器信息: ServerInfo[]
}

// 通用表单数据接口
export type FormData = AppHardeningFormData | SecurityMonitoringFormData | SecurityTestingFormData

// 维护记录接口
export interface MaintenanceRecord {
  id?: string
  date: string
  content: string
  operator: string
  type?: string
}

// 服务器信息接口
export interface ServerInfo {
  IP地址: string
  用途类型: string
  系统发行版: string
  内存: string
  CPU: string
  磁盘: string
  外网IP地址?: string
  SSH端口: string
  Root密码: string
  运维用户: OperationUser[]
  部署应用: string[]
  组件端口: Record<string, string>
}

// 运维用户接口
export interface OperationUser {
  username: string
  password: string
  showPassword?: boolean
}

// 客户APP接口
export interface ClientApp {
  name: string
  version: string
  description?: string
}

// 运维定制内容接口
export interface CustomContent {
  title: string
  content: string
  type?: string
}

// 数据映射验证结果接口
export interface MappingValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// 字段映射配置接口
export interface FieldMappingConfig {
  primary: string
  aliases: string[]
  description: string
}

// 表单类型特定映射配置接口
export interface FormTypeSpecificMapping {
  companyFields: string[]
  versionFields: string[]
  accessFields: string[]
  userConfigFields: string[]
}

// 数据映射配置接口
export interface DataMappingConfig {
  [key: string]: FieldMappingConfig
}

// 表单字段配置接口
export interface FormFieldConfig {
  id: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox' | 'radio'
  label: string
  placeholder?: string
  required: boolean
  columnClass?: string
  icon?: string
  options?: Array<{ label: string; value: string }>
  helpText?: string
  readonly?: boolean
  style?: Record<string, string>
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
    message?: string
  }
}

// 表单分组配置接口
export interface FormGroupConfig {
  id: string
  type: 'row' | 'group'
  formTypes: FormType[]
  fields: FormFieldConfig[]
}

// 表单提交结果接口
export interface FormSubmissionResult {
  status: 'success' | 'error'
  message: string
  submissionId?: string
  data?: any
  errors?: string[]
}

// 表单历史详情接口
export interface FormSubmissionDetail {
  id: string
  company_name: string
  form_type: FormType
  record_date: string
  server_count: number
  created_at: string
  updated_at: string
  form_data: FormData
  status: string
}

// 访问信息分组接口
export interface AccessInfoGroup {
  type: string
  isGroup: boolean
  icon: string
  color: string
  items: AccessInfoItem[]
}

// 访问信息项接口
export interface AccessInfoItem {
  label: string
  value?: string
  username?: string
  password?: string
  type: 'url' | 'auth' | 'text'
  showPassword?: boolean
}

// 组件详情接口
export interface ComponentDetail {
  name: string
  port: string
  type: string
  desc: string
}

// 组件分组接口
export interface ComponentGroup {
  [category: string]: ComponentDetail[]
}

// 表单配置接口
export interface FormConfig {
  formType: FormType
  title: string
  description?: string
  fields: FormGroupConfig[]
  validation: {
    required: string[]
    optional: string[]
  }
  defaultData: Partial<FormData>
}

// 表单状态接口
export interface FormState {
  loading: boolean
  submitting: boolean
  error: string | null
  success: boolean
  isDirty: boolean
  isValid: boolean
}

// 表单事件接口
export interface FormEvent {
  type: 'save' | 'load' | 'reset' | 'validate' | 'change'
  formType: FormType
  formData: FormData
  timestamp: Date
}

// 导出所有类型
export type {
  FormType,
  UpgradeUserConfig,
  AppHardeningFormData,
  SecurityMonitoringFormData,
  SecurityTestingFormData,
  FormData,
  MaintenanceRecord,
  ServerInfo,
  OperationUser,
  ClientApp,
  CustomContent,
  MappingValidationResult,
  FieldMappingConfig,
  FormTypeSpecificMapping,
  DataMappingConfig,
  FormFieldConfig,
  FormGroupConfig,
  FormSubmissionResult,
  FormSubmissionDetail,
  AccessInfoGroup,
  AccessInfoItem,
  ComponentDetail,
  ComponentGroup,
  FormConfig,
  FormState,
  FormEvent
}
