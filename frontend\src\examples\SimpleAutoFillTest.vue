<template>
  <div class="simple-auto-fill-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-magic me-2"></i>
                简化自动填充测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 控制面板 -->
              <div class="row mb-4">
                <div class="col-md-6">
                  <label class="form-label fw-bold">表单类型</label>
                  <select v-model="formType" class="form-select">
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">操作</label>
                  <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm" @click="addTestServer">
                      添加测试服务器
                    </button>
                    <button class="btn btn-warning btn-sm" @click="clearAll">
                      清空重置
                    </button>
                  </div>
                </div>
              </div>

              <!-- 服务器信息 -->
              <div class="row mb-4">
                <div class="col-12">
                  <h6>当前服务器信息</h6>
                  <div v-if="serverList.length === 0" class="alert alert-info">
                    暂无服务器，点击"添加测试服务器"按钮添加
                  </div>
                  <div v-else>
                    <div v-for="(server, index) in serverList" :key="index" class="card mb-2">
                      <div class="card-body py-2">
                        <strong>服务器 {{ index + 1 }}:</strong> {{ server.IP地址 }} | 
                        <strong>组件:</strong> {{ server.部署应用?.join(', ') }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 访问信息测试 -->
              <div class="row">
                <div class="col-12">
                  <h6>访问信息自动填充测试</h6>
                  <div class="card">
                    <div class="card-body">
                      <dynamic-access-info-section
                        v-model="accessData"
                        :field-config="fieldConfig"
                        :form-type="formType"
                        :server-list="serverList"
                        :key="`test-${formType}-${refreshKey}`"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 调试信息 -->
              <div class="row mt-4">
                <div class="col-12">
                  <h6>调试信息</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-6">
                          <strong>字段配置:</strong>
                          <pre class="small">{{ JSON.stringify(fieldConfig, null, 2) }}</pre>
                        </div>
                        <div class="col-md-6">
                          <strong>访问数据:</strong>
                          <pre class="small">{{ JSON.stringify(accessData, null, 2) }}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicAccessInfoSection from '@/components/forms/common/DynamicAccessInfoSection.vue'
import { getFormFieldConfig } from '@/config/formFieldConfig'

export default {
  name: 'SimpleAutoFillTest',
  components: {
    DynamicAccessInfoSection
  },
  data() {
    return {
      formType: '安全测评',
      serverList: [],
      accessData: {},
      refreshKey: 0
    }
  },
  computed: {
    fieldConfig() {
      const config = getFormFieldConfig(this.formType)
      return config.access || {}
    }
  },
  methods: {
    addTestServer() {
      const server = {
        IP地址: `192.168.1.${100 + this.serverList.length}`,
        部署应用: [],
        组件端口: {}
      }

      // 根据表单类型添加对应组件
      if (this.formType === '安全测评') {
        server.部署应用 = ['front-ssp-admin', 'front-ssp-user', 'luna', 'backend-ssp-user']
        server.组件端口 = {
          'front-ssp-admin': '8080',
          'front-ssp-user': '8081',
          'luna': '9001',
          'backend-ssp-user': '8082'
        }
      } else if (this.formType === '安全监测') {
        server.部署应用 = ['web-service-nginx', 'init', 'kibana']
        server.组件端口 = {
          'web-service-nginx': '443',
          'init': '8181',
          'kibana': '5601'
        }
      } else if (this.formType === '应用加固') {
        server.部署应用 = ['secweb', 'luna']
        server.组件端口 = {
          'secweb': '8000',
          'luna': '9001'
        }
      }

      this.serverList.push(server)
      console.log('添加测试服务器:', server)
      console.log('当前服务器列表:', this.serverList)
    },

    clearAll() {
      this.serverList = []
      this.accessData = {}
      this.refreshKey += 1
      console.log('清空所有数据')
    }
  },
  watch: {
    formType() {
      this.clearAll()
    },

    serverList: {
      handler(newList) {
        console.log('服务器列表变化:', newList)
      },
      deep: true
    },

    accessData: {
      handler(newData) {
        console.log('访问数据变化:', newData)
      },
      deep: true
    }
  },
  mounted() {
    console.log('SimpleAutoFillTest 组件已挂载')
    console.log('初始字段配置:', this.fieldConfig)
  }
}
</script>

<style scoped>
.simple-auto-fill-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
}

pre {
  max-height: 200px;
  overflow-y: auto;
  font-size: 0.75rem;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}
</style>
