import { formatDate, getNextYearDate } from '@/utils/fillSheetMethods'

// 统一的API基础URL配置
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

/**
 * 表单数据配置
 * 包含初始表单数据和新建项的配置
 */

/**
 * 获取当前用户信息
 * @returns {String} - 当前用户名
 */
const getCurrentUser = () => {
  try {
    // 方法1：从localStorage获取用户信息
    const userInfo = localStorage.getItem('user_info')
    if (userInfo) {
      const user = JSON.parse(userInfo)
      return user.real_name || user.username || user.name || 'admin'
    }

    // 方法2：如果localStorage中没有，使用默认值
    return 'admin'
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
    return 'admin'
  }
}

/**
 * 获取安全测评表单的初始数据
 * @returns {Object} - 安全测评表单的初始数据
 */
export const getSecurityTestingInitialData = () => {
  return {
    公司名称: '客户公司名称',
    文档后缀: '安全测评',
    客户标识: '',
    部署包版本: '',
    管理员页面IP: '',
    超级管理员账号: '',
    超级管理员密码: '',
    管理员账号: '',
    管理员密码: '',
    用户账号: '',
    用户密码: '',
    用户页面IP: '',
    升级页面IP: '',
    升级用户账号: '',
    升级用户密码: '',
    对外服务端口: '',
    产品功能: '',
    授权功能: '',
    授权开始日期: formatDate(new Date()),
    授权结束日期: formatDate(getNextYearDate()),
    记录日期: formatDate(new Date()),
    编辑人: getCurrentUser(), // 自动填充当前用户
    维护记录: [],
    服务器信息: []
  }
}

/**
 * 获取安全监测表单的初始数据
 * @returns {Object} - 安全监测表单的初始数据
 */
export const getSecurityMonitoringInitialData = () => {
  return {
    公司名称: '客户公司名称',
    文档后缀: '安全监测',
    客户标识: '',
    日活: '',
    标准或定制: '标准版',
    前端版本: '',
    后端版本: '',
    SDK外网流量入口: '',
    SDK流量转发到Nginx入口: '',
    业务功能页面地址: '',
    超级管理员账号: '',
    超级管理员密码: '',
    客户管理员账号: '',
    客户管理员密码: '',
    init地址: '',
    init用户名: 'prometheus_user',
    init密码: '1qaz@WSX',
    kibana地址: '',
    kibana认证信息: '账号：elastic 密码: beap123',
    记录日期: formatDate(new Date()),
    编辑人: getCurrentUser(), // 自动填充当前用户
    维护记录: [],
    运维定制内容: [],
    客户APP: [],
    服务器信息: []
  }
}

/**
 * 应用加固用户账号密码默认配置
 */
export const appHardeningUserDefaults = {
  // 平台用户默认配置
  platformUser: {
    account: '',
    password: ''
  },
  // 管理员默认配置
  admin: {
    account: '',
    password: ''
  },
  // 超级管理员默认配置
  superAdmin: {
    account: '',
    password: ''
  }
}

/**
 * 获取应用加固表单的初始数据
 * @returns {Object} - 应用加固表单的初始数据
 */
export const getAppHardeningInitialData = () => {
  return {
    公司名称: '客户公司名称',
    文档后缀: '应用加固',
    客户: '',
    客户标识: '',
    部署的平台版本: '',
    平台访问地址: '',
    平台用户账号: appHardeningUserDefaults.platformUser.account,
    平台用户密码: appHardeningUserDefaults.platformUser.password,
    管理员账号: appHardeningUserDefaults.admin.account,
    管理员密码: appHardeningUserDefaults.admin.password,
    超级管理员账号: appHardeningUserDefaults.superAdmin.account,
    超级管理员密码: appHardeningUserDefaults.superAdmin.password,
    升级平台地址: '',
    升级用户账号: '',
    升级用户密码: '',
    记录日期: formatDate(new Date()),
    编辑人: getCurrentUser(), // 自动填充当前用户
    维护记录: [],
    服务器信息: []
  }
}

/**
 * 获取通用表单的初始数据
 * @returns {Object} - 通用表单的初始数据
 */
export const getGenericFormInitialData = () => {
  return {
    公司名称: '客户公司名称',
    文档后缀: '新表单类型',
    客户标识: '',
    记录日期: formatDate(new Date()),
    编辑人: getCurrentUser(), // 自动填充当前用户
    版本信息: '',
    管理员页面IP: '',
    用户页面IP: '',
    管理员账号: '',
    管理员密码: '',
    服务器信息: [],
    维护记录: []
  }
}

/**
 * 获取初始表单数据
 * @param {String} formType - 表单类型
 * @returns {Object} - 初始表单数据
 */
export const getInitialFormData = (formType = '安全测评') => {
  switch (formType) {
    case '安全监测':
      return getSecurityMonitoringInitialData()
    case '应用加固':
      return getAppHardeningInitialData()
    case '安全测评':
      return getSecurityTestingInitialData()
    default:
      // 对于新的表单类型，使用通用表单数据
      const genericData = getGenericFormInitialData()
      genericData.文档后缀 = formType
      return genericData
  }
}

// 静态组件配置已移除，现在完全使用数据库配置
// getComponentGroups 函数已废弃，请使用 getComponentGroupsFromDatabase

/**
 * 从数据库获取组件分组数据
 * @returns {Promise<Object>} - 组件分组数据
 */
export const getComponentGroupsFromDatabase = async () => {
  try {
    // 动态导入服务，避免循环依赖
    const { getComponentsByCategory } = await import('@/services/componentService')

    // 首先获取所有可用的表单类型
    let availableFormTypes = []
    try {
      const response = await fetch(`${API_BASE_URL}/excel/form-types`)
      const result = await response.json()
      if (result.status === 'success') {
        availableFormTypes = result.data.map(formType => formType.name)
      }
    } catch (error) {
      console.warn('获取表单类型失败，使用默认类型:', error)
      availableFormTypes = ['安全测评', '安全监测', '应用加固']
    }

    console.log('🔄 可用的表单类型:', availableFormTypes)

    const result = {}

    // 为每个表单类型创建对应的键
    const formTypeKeyMapping = {
      '安全监测': 'security',
      '安全测评': 'testing',
      '应用加固': 'hardening'
    }

    // 处理所有表单类型
    for (const formType of availableFormTypes) {
      // 为预定义的表单类型使用固定键，为新表单类型使用表单名称作为键
      const resultKey = formTypeKeyMapping[formType] || formType

      try {
        console.log(`🔄 正在获取 ${formType} (${resultKey}) 的组件数据...`)
        const dbData = await getComponentsByCategory(formType)
        console.log(`📦 ${formType} 原始数据:`, dbData)

        // 缓存数据库数据到全局变量，供端口获取函数使用
        if (!window.componentCache) {
          window.componentCache = {}
        }
        window.componentCache[formType] = dbData

        // 初始化结果对象
        result[resultKey] = {}

        if (dbData && typeof dbData === 'object' && Object.keys(dbData).length > 0) {
          // 转换数据库格式为前端格式
          Object.keys(dbData).forEach(categoryKey => {
            if (dbData[categoryKey] && dbData[categoryKey].components) {
              const components = dbData[categoryKey].components.map(comp => ({
                name: comp.name,
                displayName: comp.display_name || comp.name,
                display_name: comp.display_name || comp.name, // 兼容性
                description: comp.description || '',
                port: comp.default_port,
                type: comp.description || '自研',
                desc: comp.description || ''
              }))

              console.log(`🔧 ${formType} - ${categoryKey} 转换后组件:`, components)
              result[resultKey][categoryKey] = components
            } else {
              console.warn(`⚠️ ${formType} - ${categoryKey} 数据格式错误:`, dbData[categoryKey])
            }
          })

          console.log(`✅ ${formType} 数据处理完成，分类数量: ${Object.keys(dbData).length}`)
        } else {
          console.log(`ℹ️ ${formType} 暂无组件数据，使用空对象`)
          // 保持空对象，不回退到静态配置
        }
      } catch (error) {
        console.warn(`❌ 获取 ${formType} 组件数据失败:`, error)
        // 如果某个表单类型获取失败，使用空对象，不影响其他表单类型
        result[resultKey] = {}
      }
    }

    console.log('🎯 最终组件分组结果:', result)
    return result
  } catch (error) {
    console.error('从数据库获取组件分组失败:', error)
    // 即使完全失败，也不回退到静态配置，而是返回空对象
    // 这样可以避免新表单类型获取到错误的组件数据
    console.log('🔄 返回空的组件分组对象，避免数据混乱')
    return {}
  }
}

/**
 * 创建新的服务器信息项
 * @returns {Object} - 新的服务器信息项
 */
export const createNewServerItem = () => {
  return {
    用途: '', // 服务器用途
    用途类型: 'DMZ', // 默认选择DMZ
    IP地址: '', // IP地址字段
    系统发行版: '', // 系统发行版
    系统架构: '', // 系统架构 (x86/arm)
    内存: '', // 内存
    CPU: '', // CPU
    磁盘: '', // 磁盘
    部署应用: [], // 部署组件多选
    组件端口: {}, // 组件端口配置
    折叠: false, // 默认展开服务器信息
    组件折叠: false, // 默认展开组件选择
    启用端口修改: false, // 默认不启用端口修改
    // 运维相关字段
    SSH端口: '22', // SSH端口，默认22
    运维用户: [ // 运维用户列表，支持动态添加
      {
        用户名: 'root',
        密码: '',
        isDefault: true // 标记为默认用户
      }
    ]
  }
}

/**
 * 创建新的运维定制内容项
 * @returns {Object} - 新的运维定制内容项
 */
export const createNewCustomItem = () => {
  return {
    title: '',
    content: ''
  }
}

/**
 * 创建新的客户APP项
 * @returns {Object} - 新的客户APP项
 */
export const createNewAppItem = () => {
  return {
    appid: '',
    name: '',
    packageName: '',
    platforms: [] // 支持的平台：Android、iOS、Web、小程序等
  }
}
