/**
 * 临时禁用动画 - 解决页面闪烁问题
 * 只禁用可能导致闪烁的动画，保留必要的交互效果
 */

/* 禁用加载动画 */
.loading-spinner {
  animation: none !important;
  border: 2px solid var(--gray-200) !important;
  border-top-color: var(--primary-color) !important;
}

.loading-skeleton {
  animation: none !important;
  background: var(--gray-100) !important;
}

/* 禁用淡入动画 */
.fade-in {
  animation: none !important;
  opacity: 1 !important;
  transform: none !important;
}

/* 禁用悬停提升效果 */
.hover-lift {
  transition: none !important;
}

.hover-lift:hover {
  transform: none !important;
}

/* 禁用卡片悬停动画 */
.card-enhanced {
  transition: none !important;
}

.card-enhanced:hover {
  transform: none !important;
  box-shadow: var(--shadow-md) !important;
}

/* 禁用按钮动画 */
.btn-enhanced::before {
  display: none !important;
}

.btn-enhanced:hover::before {
  display: none !important;
}

.btn-enhanced:hover {
  transform: none !important;
}

/* 禁用所有keyframes动画 */
@keyframes spin {
  to { transform: none; }
}

@keyframes loading {
  0% { background-position: 0 0; }
  100% { background-position: 0 0; }
}

@keyframes fadeIn {
  from { opacity: 1; transform: none; }
  to { opacity: 1; transform: none; }
}

/* 禁用可能导致闪烁的过渡效果 */
* {
  animation-duration: 0s !important;
  animation-delay: 0s !important;
  animation-iteration-count: 1 !important;
}

/* 保留必要的交互效果，但移除可能导致闪烁的部分 */
.btn:hover {
  transform: none !important;
}

.card:hover {
  transform: none !important;
}

/* 特别针对可能导致闪烁的元素 */
.spinner-border {
  animation: none !important;
}

.visually-hidden {
  animation: none !important;
}
