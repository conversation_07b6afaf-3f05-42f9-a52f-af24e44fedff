/**
 * 清理FloatingBall.vue中的调试日志
 * 这个脚本用于移除所有console.log调试输出
 */

import fs from 'fs'
import path from 'path'

const filePath = path.join(process.cwd(), 'frontend/src/components/forms/common/FloatingBall.vue')

// 要清理的console.log模式
const logPatterns = [
  /console\.log\('🖱️[^']*'[^)]*\)\s*\n/g,
  /console\.log\('🚫[^']*'[^)]*\)\s*\n/g,
  /console\.log\('🚀[^']*'[^)]*\)\s*\n/g,
  /console\.log\('❌[^']*'[^)]*\)\s*\n/g,
  /console\.log\('📍[^']*'[^)]*\)\s*\n/g,
  /console\.log\('⚠️[^']*'[^)]*\)\s*\n/g,
  /console\.log\('✅[^']*'[^)]*\)\s*\n/g,
  /console\.log\('🔄[^']*'[^)]*\)\s*\n/g,
  /console\.log\('✨[^']*'[^)]*\)\s*\n/g,
  /console\.log\('🎯[^']*'[^)]*\)\s*\n/g,
  /console\.log\('👆[^']*'[^)]*\)\s*\n/g,
  /console\.log\('📋[^']*'[^)]*\)\s*\n/g,
  /console\.log\('🔒[^']*'[^)]*\)\s*\n/g,
  /console\.log\('📐[^']*'[^)]*\)\s*\n/g,
  /console\.log\('⌨️[^']*'[^)]*\)\s*\n/g,
  /console\.log\('🎧[^']*'[^)]*\)\s*\n/g,
  /console\.log\('🧹[^']*'[^)]*\)\s*\n/g,
  /console\.log\('🗑️[^']*'[^)]*\)\s*\n/g,
  /console\.error\('❌[^']*'[^)]*\)\s*\n/g,
  
  // 多行console.log
  /console\.log\('🖱️[^']*',\s*\{[^}]*\}\)\s*\n/g,
  /console\.log\('📍[^']*',\s*\{[^}]*\}\)\s*\n/g,
  /console\.log\('🎯[^']*',\s*\{[^}]*\}\)\s*\n/g,
  /console\.log\('🔄[^']*',\s*\{[^}]*\}\)\s*\n/g,
  /console\.log\('📋[^']*',\s*\{[^}]*\}\)\s*\n/g,
  /console\.log\('🔒[^']*',\s*\{[^}]*\}\)\s*\n/g,
  /console\.log\('📐[^']*',\s*\{[^}]*\}\)\s*\n/g,
  
  // 复杂的多行console.log
  /console\.log\('📍[^']*',\s*\{[\s\S]*?\}\)\s*\n/g,
  /console\.log\('🎯[^']*',\s*\{[\s\S]*?\}\)\s*\n/g,
  /console\.log\('🔄[^']*',\s*\{[\s\S]*?\}\)\s*\n/g,
  /console\.log\('📋[^']*',\s*\{[\s\S]*?\}\)\s*\n/g,
  /console\.log\('🔒[^']*',\s*\{[\s\S]*?\}\)\s*\n/g,
  /console\.log\('📐[^']*',\s*\{[\s\S]*?\}\)\s*\n/g,
]

function cleanupLogs() {
  try {
    // 读取文件内容
    let content = fs.readFileSync(filePath, 'utf8')
    
    console.log('开始清理FloatingBall.vue中的调试日志...')
    
    let cleanedCount = 0
    
    // 应用所有清理模式
    logPatterns.forEach((pattern, index) => {
      const matches = content.match(pattern)
      if (matches) {
        console.log(`模式 ${index + 1}: 找到 ${matches.length} 个匹配项`)
        cleanedCount += matches.length
        content = content.replace(pattern, '')
      }
    })
    
    // 手动清理一些复杂的多行日志
    const complexPatterns = [
      // 清理多行对象日志
      /console\.log\('📍[^']*',\s*\{[\s\S]*?ballRect:[\s\S]*?\}\)\s*\n/g,
      /console\.log\('🚀[^']*',\s*\{[\s\S]*?isDragCandidate:[\s\S]*?\}\)\s*\n/g,
      /console\.log\('🎯[^']*',\s*\{[\s\S]*?constrained:[\s\S]*?\}\)\s*\n/g,
      /console\.log\('🔄[^']*',\s*\{[\s\S]*?scrollY:[\s\S]*?\}\)\s*\n/g,
      /console\.log\('📍[^']*',\s*\{[\s\S]*?initialRelativeY:[\s\S]*?\}\)\s*\n/g,
      /console\.log\('📏[^']*'[^)]*\)\s*\n/g,
      
      // 清理调试事件监听器
      /\/\/ 添加调试事件监听器[\s\S]*?}\)\s*\n/g,
    ]
    
    complexPatterns.forEach((pattern, index) => {
      const matches = content.match(pattern)
      if (matches) {
        console.log(`复杂模式 ${index + 1}: 找到 ${matches.length} 个匹配项`)
        cleanedCount += matches.length
        content = content.replace(pattern, '')
      }
    })
    
    // 清理空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n')
    
    // 写回文件
    fs.writeFileSync(filePath, content, 'utf8')
    
    console.log(`✅ 清理完成！共清理了 ${cleanedCount} 个调试日志`)
    console.log(`📁 文件路径: ${filePath}`)
    
  } catch (error) {
    console.error('❌ 清理过程中出错:', error)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanupLogs()
}

export { cleanupLogs }
