<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-file-text me-2"></i>
            表单提交详情 - {{ submission.company_name }}
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>

        <div class="modal-body">
          <div v-if="loading" class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载详细信息...</p>
          </div>

          <div v-else-if="detailData">
            <!-- 基本信息 -->
            <div class="row mb-4">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h6 class="card-title mb-0">
                      <i class="bi bi-info-circle me-2"></i>
                      基本信息
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-3">
                        <strong>公司名称:</strong><br>
                        <span class="text-primary">{{ detailData.company_name }}</span>
                      </div>
                      <div class="col-md-2">
                        <strong>表单类型:</strong><br>
                        <span class="badge" :class="getFormTypeBadgeClass(detailData.form_type)">
                          {{ detailData.form_type }}
                        </span>
                      </div>
                      <div class="col-md-2">
                        <strong>记录日期:</strong><br>
                        {{ formatDate(detailData.record_date) }}
                      </div>
                      <div class="col-md-2">
                        <strong>服务器数:</strong><br>
                        <span class="badge bg-info">{{ detailData.server_count }}</span>
                      </div>
                      <div class="col-md-2">
                        <strong>组件数:</strong><br>
                        <span class="badge bg-success">{{ detailData.component_count }}</span>
                      </div>
                      <div class="col-md-1">
                        <strong>状态:</strong><br>
                        <span class="badge" :class="getStatusBadgeClass(detailData.status)">
                          {{ getStatusText(detailData.status) }}
                        </span>
                      </div>
                    </div>
                    <div class="row mt-3">
                      <div class="col-md-3">
                        <strong>提交时间:</strong><br>
                        {{ formatDateTime(detailData.created_at) }}
                      </div>
                      <div class="col-md-3">
                        <strong>创建者:</strong><br>
                        {{ detailData.created_by || '未知' }}
                      </div>
                      <div class="col-md-3">
                        <strong>IP地址:</strong><br>
                        {{ detailData.ip_address || '未知' }}
                      </div>
                      <div class="col-md-3">
                        <strong>Excel文件:</strong><br>
                        <span class="text-success">{{ detailData.excel_filename || '未生成' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 表单数据 -->
            <div class="row">
              <div class="col-12">
                <div class="card">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                      <i class="bi bi-list-ul me-2"></i>
                      表单数据
                    </h6>
                    <div class="btn-group btn-group-sm">
                      <button class="btn btn-outline-primary" @click="toggleRawData">
                        <i class="bi bi-code"></i>
                        {{ showRawData ? '结构化视图' : '原始数据' }}
                      </button>
                      <button class="btn btn-outline-success" @click="copyFormData">
                        <i class="bi bi-clipboard"></i>
                        复制数据
                      </button>
                      <div v-if="!isShareMode" class="btn-group">
                        <button class="btn btn-outline-info" @click="createShareLink" :disabled="creatingShareLink">
                          <i class="bi bi-share"></i>
                          {{ creatingShareLink ? '生成中...' : '生成分享链接' }}
                        </button>
                        <button class="btn btn-outline-info dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" :disabled="creatingShareLink">
                          <span class="visually-hidden">切换下拉菜单</span>
                        </button>
                        <ul class="dropdown-menu">
                          <li><a class="dropdown-item" href="#" @click.prevent="createShareLinkWithExpiry(1)">1小时后过期</a></li>
                          <li><a class="dropdown-item" href="#" @click.prevent="createShareLinkWithExpiry(24)">1天后过期</a></li>
                          <li><a class="dropdown-item" href="#" @click.prevent="createShareLinkWithExpiry(72)">3天后过期</a></li>
                          <li><a class="dropdown-item" href="#" @click.prevent="createShareLinkWithExpiry(168)">1周后过期</a></li>
                          <li><a class="dropdown-item" href="#" @click.prevent="createShareLinkWithExpiry(720)">1个月后过期</a></li>
                          <li><hr class="dropdown-divider"></li>
                          <li><a class="dropdown-item text-warning" href="#" @click.prevent="createShareLinkWithExpiry(0)">
                            <i class="bi bi-infinity me-1"></i>永久有效
                          </a></li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="card-body">
                    <!-- 原始JSON数据视图 -->
                    <div v-if="showRawData">
                      <pre class="bg-light p-3 rounded"><code>{{ JSON.stringify(detailData.form_data, null, 2) }}</code></pre>
                    </div>

                    <!-- 结构化数据视图 -->
                    <div v-else>
                      <!-- 按表单分组显示数据 -->
                      <div v-for="group in getFormGroups()" :key="group.name" class="mb-4">
                        <div class="card">
                          <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">
                              <i :class="group.icon" class="me-2"></i>
                              {{ group.label }}
                            </h6>
                            <button
                              class="btn btn-sm btn-outline-secondary"
                              @click="toggleGroupCollapse(group.name)"
                              v-if="group.collapsible"
                            >
                              <i class="bi" :class="group.collapsed ? 'bi-chevron-down' : 'bi-chevron-up'"></i>
                            </button>
                          </div>
                          <div class="card-body" v-show="!group.collapsed">
                            <!-- 基本信息分组 -->
                            <div v-if="group.name === 'basic_info'">
                              <div class="row">
                                <div class="col-md-6" v-for="(value, key) in group.data" :key="key">
                                  <div class="mb-2">
                                    <strong>{{ key }}:</strong>
                                    <span class="text-primary">{{ value || '-' }}</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- 访问信息分组 -->
                            <div v-else-if="group.name === 'access_info'">
                              <div class="row">
                                <div class="col-12" v-for="user in group.data" :key="user.type">
                                  <!-- 分组类型（安全监测表单） -->
                                  <div v-if="user.isGroup" class="card mb-3 border-light access-group-card">
                                    <div class="card-header py-2" :style="{ backgroundColor: user.color + '15', borderBottom: '2px solid ' + user.color }">
                                      <div class="group-header-content">
                                        <div class="group-title">
                                          <i :class="user.icon" class="me-2"></i>
                                          {{ user.type }}
                                        </div>
                                        <!-- 显示该分组的URL地址 -->
                                        <div v-if="getGroupUrl(user)" class="group-url">
                                          <a :href="getGroupUrl(user)" target="_blank" class="group-url-link">
                                            <i class="bi bi-box-arrow-up-right me-1"></i>
                                            {{ getGroupUrl(user) }}
                                          </a>
                                          <button class="group-url-copy" @click="copyToClipboard(getGroupUrl(user))" title="复制地址">
                                            <i class="bi bi-clipboard"></i>
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="card-body py-2">
                                      <div class="row g-3">
                                        <!-- 只显示认证类型和文本类型，URL类型已在标题中显示 -->
                                        <div v-for="(item, itemIndex) in user.items.filter(item => item.type !== 'url')" :key="itemIndex"
                                             :class="getItemColumnClass(item, user.items.filter(item => item.type !== 'url').length)">
                                          <!-- 认证类型 -->
                                          <div v-if="item.type === 'auth'" class="access-item auth-item">
                                            <div class="auth-header">
                                              <div class="auth-icon-wrapper">
                                                <i class="bi bi-shield-lock auth-icon"></i>
                                              </div>
                                              <div class="auth-label">
                                                {{ item.label }}
                                              </div>
                                            </div>
                                            <div class="auth-content">
                                              <div class="auth-field">
                                                <div class="auth-field-label">
                                                  <i class="bi bi-person me-1"></i>
                                                  账号
                                                </div>
                                                <div class="auth-field-value">
                                                  {{ item.username || '-' }}
                                                </div>
                                              </div>
                                              <div class="auth-field">
                                                <div class="auth-field-label">
                                                  <i class="bi bi-key me-1"></i>
                                                  密码
                                                </div>
                                                <div class="auth-field-value">
                                                  <span class="password-text">
                                                    <span v-if="item.showPassword">{{ item.password || '-' }}</span>
                                                    <span v-else class="password-dots">{{ item.password ? '●●●●●●' : '-' }}</span>
                                                  </span>
                                                  <button v-if="item.password"
                                                    class="password-toggle-btn"
                                                    @click="toggleGroupItemPassword(item)"
                                                    :title="item.showPassword ? '隐藏密码' : '显示密码'"
                                                  >
                                                    <i :class="item.showPassword ? 'bi-eye-slash' : 'bi-eye'"></i>
                                                  </button>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                          <!-- 文本类型 -->
                                          <div v-else class="access-item text-item">
                                            <div class="text-header">
                                              <div class="text-icon-wrapper">
                                                <i class="bi bi-info-circle text-icon"></i>
                                              </div>
                                              <div class="text-label">
                                                {{ item.label }}
                                              </div>
                                            </div>
                                            <div class="text-content">
                                              <div class="text-value">
                                                <!-- URL类型字段，显示可点击链接 -->
                                                <div v-if="item.type === 'url'" class="url-field">
                                                  <a :href="formatUrl(item.value)"
                                                     target="_blank"
                                                     class="text-primary text-decoration-none"
                                                     @click="trackUrlClick(item.label, item.value)">
                                                    <i class="bi bi-link-45deg me-1"></i>
                                                    {{ item.value }}
                                                    <i class="bi bi-box-arrow-up-right ms-1" style="font-size: 0.8rem;"></i>
                                                  </a>
                                                  <button
                                                    class="btn btn-sm btn-outline-secondary ms-2"
                                                    @click="copyToClipboard(item.value, `${item.label}地址`)"
                                                    title="复制地址">
                                                    <i class="bi bi-clipboard" style="font-size: 0.7rem;"></i>
                                                  </button>
                                                </div>
                                                <!-- 普通文本字段 -->
                                                <span v-else>{{ item.value }}</span>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <!-- 单项类型（其他表单） -->
                                  <div v-else class="card mb-3 border-light">
                                    <div class="card-header py-2 bg-light">
                                      <small class="fw-bold">{{ user.type }}</small>
                                    </div>
                                    <div class="card-body py-2">
                                      <!-- 文本类型字段（如平台访问地址） -->
                                      <div v-if="user.isTextOnly">
                                        <small><strong>地址:</strong></small>
                                        <div class="mt-1">
                                          <!-- 如果是URL，显示可点击链接 -->
                                          <div v-if="isUrl(user.value)" class="url-field">
                                            <a :href="formatUrl(user.value)"
                                               target="_blank"
                                               class="text-primary text-decoration-none"
                                               @click="trackUrlClick(user.type, user.value)">
                                              <i class="bi bi-link-45deg me-1"></i>
                                              {{ user.value }}
                                              <i class="bi bi-box-arrow-up-right ms-1" style="font-size: 0.8rem;"></i>
                                            </a>
                                            <button
                                              class="btn btn-sm btn-outline-secondary ms-2"
                                              @click="copyToClipboard(user.value, `${user.type}地址`)"
                                              title="复制地址">
                                              <i class="bi bi-clipboard" style="font-size: 0.7rem;"></i>
                                            </button>
                                          </div>
                                          <!-- 普通文本 -->
                                          <span v-else class="text-muted">{{ user.value || '-' }}</span>
                                        </div>
                                      </div>
                                      <!-- 用户名密码类型字段 -->
                                      <div v-else>
                                        <div class="mb-1">
                                          <small><strong>账号:</strong> {{ user.username || '-' }}</small>
                                        </div>
                                        <div>
                                          <small><strong>密码:</strong>
                                            <span v-if="!user.showPassword" class="text-muted">{{ user.password ? '●●●●●●' : '-' }}</span>
                                            <span v-else class="text-danger">{{ user.password || '-' }}</span>
                                            <button v-if="user.password"
                                                    class="btn btn-sm btn-outline-secondary ms-1"
                                                    @click="togglePasswordVisibility(user)"
                                                    style="font-size: 0.7rem; padding: 0.1rem 0.3rem;">
                                              <i class="bi" :class="user.showPassword ? 'bi-eye-slash' : 'bi-eye'"></i>
                                            </button>
                                          </small>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- 服务器信息分组 -->
                            <div v-else-if="group.name === 'server_info'">
                              <div v-for="(server, index) in group.data" :key="index" class="mb-4">
                                <div class="card border-light">
                                  <div class="card-header py-2 bg-light">
                                    <small class="fw-bold">
                                      <i class="bi bi-server me-1"></i>
                                      服务器 #{{ index + 1 }}
                                    </small>
                                  </div>
                                    <div class="card-body py-2">
                                      <!-- 基本服务器信息 -->
                                      <div class="row">
                                        <div class="col-6">
                                          <small><strong>IP地址:</strong> {{ server.IP地址 || '-' }}</small>
                                        </div>
                                        <div class="col-6">
                                          <small><strong>用途:</strong> {{ server.用途类型 || server.服务器用途 || '-' }}</small>
                                        </div>
                                        <div class="col-6">
                                          <small><strong>系统:</strong> {{ server.系统发行版 || '-' }}</small>
                                        </div>
                                        <div class="col-6">
                                          <small><strong>架构:</strong> {{ server.系统架构 || '-' }}</small>
                                        </div>
                                        <div class="col-6">
                                          <small><strong>内存:</strong> {{ server.内存 || '-' }}</small>
                                        </div>
                                        <div class="col-6">
                                          <small><strong>CPU:</strong> {{ server.CPU || '-' }}</small>
                                        </div>
                                        <div class="col-6">
                                          <small><strong>磁盘:</strong> {{ server.磁盘 || '-' }}</small>
                                        </div>
                                        <div class="col-6">
                                          <small><strong>SSH端口:</strong> {{ server.SSH端口 || '22' }}</small>
                                        </div>
                                      </div>


                                      <!-- 运维账号信息 -->
                                      <div v-if="hasOperationAccounts(server)" class="mt-3">
                                        <small><strong>运维账号信息:</strong></small>

                                        <div class="mt-2">
                                          <!-- 运维用户数组 -->
                                          <div v-for="(user, userIndex) in server.运维用户" :key="userIndex"
                                               class="mb-2 p-2 bg-light rounded">

                                            <div class="d-flex justify-content-between align-items-center">
                                              <div>
                                                <small><strong>{{ (user && user.用户名 === 'root') ? 'Root账号' : '运维用户' }}:</strong> {{ (user && user.用户名) || '-' }}</small><br>
                                                <small><strong>密码:</strong>
                                                  <span v-if="!user || !user.showPassword" class="text-muted">●●●●●●</span>
                                                  <span v-else class="text-danger">{{ (user && user.密码) || '-' }}</span>
                                                  <button v-if="user && user.密码"
                                                          class="btn btn-sm btn-outline-secondary ms-1"
                                                          @click="toggleUserPasswordVisibility(user)"
                                                          style="font-size: 0.6rem; padding: 0.1rem 0.2rem;">
                                                    <i class="bi" :class="user && user.showPassword ? 'bi-eye-slash' : 'bi-eye'"></i>
                                                  </button>
                                                </small>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>

                                      <!-- 部署组件 -->
                                      <div v-if="server.部署应用 && server.部署应用.length > 0" class="mt-3">
                                        <small><strong>部署组件:</strong></small>
                                        <div class="mt-2">
                                          <div class="table-responsive">
                                            <table class="table table-sm table-bordered component-table" style="table-layout: fixed !important; width: 100% !important;">
                                              <thead>
                                                <tr class="table-light">
                                                  <th style="width: 33.33% !important; min-width: 33.33% !important; max-width: 33.33% !important;" class="text-center">类型</th>
                                                  <th style="width: 33.33% !important; min-width: 33.33% !important; max-width: 33.33% !important;">组件名称</th>
                                                  <th style="width: 33.33% !important; min-width: 33.33% !important; max-width: 33.33% !important;" class="text-center">分组</th>
                                                </tr>
                                              </thead>
                                              <tbody>
                                                <tr
                                                  v-for="app in getSortedComponents(server.部署应用)"
                                                  :key="app"
                                                  :class="getComponentRowClass(app)"
                                                >
                                                  <td class="text-center">
                                                    <i :class="getComponentIcon(app)" :style="{ color: getComponentColor(app) }"></i>
                                                  </td>
                                                  <td class="component-name-cell">
                                                    <span class="fw-medium">{{ app }}</span>
                                                  </td>
                                                  <td class="text-center">
                                                    <span class="component-category-badge" :class="getComponentCategoryBadgeClass(app)">
                                                      {{ getComponentCategory(app) }}
                                                    </span>
                                                  </td>
                                                </tr>
                                              </tbody>
                                            </table>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                            </div>

                            <!-- 系统架构分组 -->
                            <div v-else-if="group.name === 'component_info'">
                              <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                  <thead class="table-light">
                                    <tr>
                                      <th>组件名称</th>
                                      <th>版本</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr v-for="component in group.data" :key="component.name">
                                      <td><strong>{{ component.name }}</strong></td>
                                      <td>{{ component.version || '-' }}</td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>

                            <!-- 客户APP分组 -->
                            <div v-else-if="group.name === 'client_apps'">
                              <div class="row">
                                <div class="col-md-6" v-for="(app, index) in group.data" :key="index">
                                  <div class="card mb-3 border-light">
                                    <div class="card-header py-2 bg-light">
                                      <small class="fw-bold">
                                        <i class="bi bi-phone me-1"></i>
                                        {{ app.name || `客户APP #${index + 1}` }}
                                      </small>
                                    </div>
                                    <div class="card-body py-2">
                                      <div class="row">
                                        <div class="col-6">
                                          <small><strong>APP ID:</strong> {{ app.appid || '-' }}</small>
                                        </div>
                                        <div class="col-6">
                                          <small><strong>应用名称:</strong> {{ app.name || '-' }}</small>
                                        </div>
                                        <div class="col-12 mt-1">
                                          <small><strong>包名:</strong> {{ app.packageName || '-' }}</small>
                                        </div>
                                        <div v-if="app.platforms && app.platforms.length > 0" class="col-12 mt-1">
                                          <small><strong>平台:</strong></small>
                                          <div class="mt-1">
                                            <span v-for="platform in app.platforms" :key="platform"
                                                  class="badge me-1" :class="getPlatformBadgeClass(platform)">
                                              {{ platform }}
                                            </span>
                                          </div>
                                        </div>
                                      </div>

                                      <!-- APP描述信息（如果有的话） -->
                                      <div v-if="app.description" class="mt-2">
                                        <small><strong>描述:</strong> {{ app.description }}</small>
                                      </div>

                                      <!-- 兼容旧版本数据 -->
                                      <div v-if="app.version" class="mt-1">
                                        <small><strong>版本:</strong> {{ app.version }}</small>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- 维护记录分组 -->
                            <div v-else-if="group.name === 'maintenance_records'">
                              <div class="table-responsive">
                                <table class="table table-sm">
                                  <thead>
                                    <tr>
                                      <th>时间</th>
                                      <th>人员</th>
                                      <th>类型</th>
                                      <th>内容</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr v-for="(record, index) in group.data" :key="index">
                                      <td>{{ formatDateTime(record.time) }}</td>
                                      <td>{{ record.staff || '-' }}</td>
                                      <td>
                                        <span class="badge bg-warning text-dark">{{ record.type || '-' }}</span>
                                      </td>
                                      <td>
                                        <small>{{ record.content || '-' }}</small>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>

                            <!-- 授权信息分组 -->
                            <div v-else-if="group.name === 'authorization_info'">
                              <div class="row g-3">
                                <div class="col-md-6" v-for="(value, key) in group.data" :key="key">
                                  <div class="card border-0 bg-light">
                                    <div class="card-body p-3">
                                      <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                          <i :class="getAuthFieldIcon(key)" class="text-primary fs-5"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                          <div class="fw-bold text-dark mb-1">{{ getAuthFieldLabel(key) }}</div>
                                          <div class="text-muted small">{{ value || '-' }}</div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- 运维定制内容分组 -->
                            <div v-else-if="group.name === 'custom_content'">
                              <div class="row">
                                <div class="col-md-6" v-for="(item, index) in group.data" :key="index">
                                  <div class="card mb-3 border-light">
                                    <div class="card-header py-2 bg-light">
                                      <small class="fw-bold">
                                        <i class="bi bi-gear me-1"></i>
                                        {{ item.title || `定制内容 #${index + 1}` }}
                                      </small>
                                    </div>
                                    <div class="card-body py-2">
                                      <small>{{ item.content || '-' }}</small>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- 其他分组的通用显示 -->
                            <div v-else>
                              <div class="row">
                                <div class="col-md-6" v-for="(value, key) in group.data" :key="key">
                                  <div class="mb-2">
                                    <strong>{{ key }}:</strong>
                                    <span class="text-primary">{{ value || '-' }}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
          <button type="button" class="btn btn-outline-info me-2" @click="viewEditHistory">
            <i class="bi bi-journal-text me-1"></i>
            查看日志
          </button>
          <button type="button" class="btn btn-success" @click="showTemplateSelectionModal">
            <i class="bi bi-file-earmark-excel me-1"></i>
            选择模板重新生成
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑日志模态框 -->
    <EditHistoryModal
      v-if="showEditHistory"
      :submission-id="submission.id"
      @close="showEditHistory = false"
      @restore="handleRestore"
    />

    <!-- 模板选择模态框 -->
    <TemplateSelectionModal
      v-if="showTemplateSelection"
      :form-data="submission"
      @close="showTemplateSelection = false"
      @confirm="handleTemplateSelection"
    />
  </div>
</template>

<script>
import { defineAsyncComponent, reactive } from 'vue'
import { parseFilenameFromContentDisposition, downloadBlob } from '@/utils/fileUtils'
import TemplateSelectionModal from './TemplateSelectionModal.vue'

// 统一的API基础URL配置
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

/**
 * 表单提交详情模态框组件
 * 显示表单提交的详细信息
 */
export default {
  name: 'SubmissionDetailModal',

  components: {
    EditHistoryModal: defineAsyncComponent(() => import('./EditHistoryModal.vue')),
    TemplateSelectionModal
  },

  props: {
    submission: {
      type: Object,
      required: true
    },
    isShareMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'regenerate'],
  data() {
    return {
      loading: false,
      detailData: null,
      showRawData: false,
      showEditHistory: false,
      showTemplateSelection: false, // 是否显示模板选择模态框
      groupCollapsedState: {}, // 记录分组的折叠状态
      cachedUserInfo: null, // 缓存用户信息，避免重复创建
      cachedServerInfo: null, // 缓存服务器信息，避免重复创建
      creatingShareLink: false, // 是否正在创建分享链接
      shareLink: null // 当前的分享链接信息
    }
  },

  mounted() {
    console.log('SubmissionDetailModal mounted')
    this.loadDetailData()
  },

  methods: {
    /**
     * 获取平台徽章的CSS类
     */
    getPlatformBadgeClass(platform) {
      const platformClasses = {
        '安卓': 'bg-success',
        'iOS': 'bg-primary',
        '鸿蒙': 'bg-warning text-dark',
        '鸿蒙Next': 'bg-danger'
      }
      return platformClasses[platform] || 'bg-secondary'
    },

    /**
     * 判断字符串是否为URL
     */
    isUrl(str) {
      if (!str) return false
      // 检查是否包含常见的URL模式
      const urlPattern = /^(https?:\/\/|ftp:\/\/|www\.)/i
      const ipPattern = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(:\d+)?/
      const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/

      return urlPattern.test(str) || ipPattern.test(str) || domainPattern.test(str)
    },

    /**
     * 格式化URL，确保有协议前缀
     */
    formatUrl(url) {
      if (!url) return '#'

      // 如果已经有协议，直接返回
      if (/^https?:\/\//i.test(url)) {
        return url
      }

      // 如果是IP地址或域名，添加http://前缀
      return `http://${url}`
    },

    /**
     * 跟踪URL点击事件
     */
    trackUrlClick(label, url) {
      console.log(`🔗 用户点击了 ${label}: ${url}`)

      // 可以在这里添加统计代码
      // 例如发送到分析服务
    },

    /**
     * 复制内容到剪贴板
     */
    async copyToClipboard(text, label = '内容') {
      try {
        await navigator.clipboard.writeText(text)

        // 显示成功提示
        this.showToast(`${label}已复制到剪贴板`, 'success')

        console.log(`📋 已复制 ${label}: ${text}`)
      } catch (error) {
        console.error('复制失败:', error)

        // 降级方案：使用传统的复制方法
        try {
          const textArea = document.createElement('textarea')
          textArea.value = text
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)

          this.showToast(`${label}已复制到剪贴板`, 'success')
        } catch (fallbackError) {
          console.error('降级复制也失败:', fallbackError)
          this.showToast('复制失败，请手动复制', 'error')
        }
      }
    },

    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
      // 创建临时提示元素
      const toast = document.createElement('div')
      toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`
      toast.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 250px;
        opacity: 0;
        transition: opacity 0.3s ease;
      `
      toast.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
      `

      document.body.appendChild(toast)

      // 显示动画
      setTimeout(() => {
        toast.style.opacity = '1'
      }, 100)

      // 3秒后自动消失
      setTimeout(() => {
        toast.style.opacity = '0'
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast)
          }
        }, 300)
      }, 3000)
    },

    /**
     * 加载详细数据
     */
    async loadDetailData() {
      this.loading = true
      try {
        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${this.submission.id}`)
        const result = await response.json()

        if (result.status === 'success') {
          // 确保数据是响应式的，并为密码显示状态添加响应式属性
          this.detailData = reactive(result.data)

          // 创建并缓存用户信息和服务器信息，确保响应式
          if (this.detailData.form_data) {
            // 创建并缓存用户信息
            this.cachedUserInfo = this.createUserInfo()

            // 创建并缓存服务器信息
            this.cachedServerInfo = this.createServerInfo()
          }

          // 数据加载完成后，加载组件数据库
          await this.loadComponentDatabase()
        } else {
          console.error('加载详细数据失败:', result.message)
        }
      } catch (error) {
        console.error('加载详细数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    /**
     * 加载组件数据库数据
     */
    async loadComponentDatabase() {
      try {
        // 获取表单类型
        const formType = this.detailData?.form_type || this.submission?.form_type
        console.log('🔍 SubmissionDetailModal: 表单类型检查')
        console.log('  - detailData.form_type:', this.detailData?.form_type)
        console.log('  - submission.form_type:', this.submission?.form_type)
        console.log('  - 最终使用的表单类型:', formType)

        if (!formType) {
          console.warn('⚠️ 无法确定表单类型，跳过组件数据库加载')
          return
        }

        console.log('🔄 SubmissionDetailModal: 开始加载组件数据库数据，表单类型:', formType)

        const response = await fetch(`${API_BASE_URL}/excel/components/by-category?form_type=${encodeURIComponent(formType)}`)
        const result = await response.json()

        console.log('📡 API响应:', result)

        if (result.status === 'success') {
          // 直接使用API返回的数据格式
          this.componentDatabase = result.data || {}
          console.log('✅ SubmissionDetailModal: 组件数据库加载成功')
          console.log('📦 数据库内容:', this.componentDatabase)
          console.log('📋 分类列表:', Object.keys(this.componentDatabase))
        } else {
          console.warn('⚠️ SubmissionDetailModal: 组件数据库加载失败:', result.message)
        }
      } catch (error) {
        console.warn('⚠️ SubmissionDetailModal: 组件数据库加载异常:', error)
      }
    },

    /**
     * 获取表单分组数据
     */
    getFormGroups() {
      if (!this.detailData || !this.detailData.form_data) return []

      const formType = this.detailData.form_type
      const data = this.detailData.form_data

      // 定义分组配置
      const groupConfigs = this.getGroupConfigs(formType)

      const groups = []

      groupConfigs.forEach(config => {
        const groupData = this.extractGroupData(data, config)
        if (groupData && (Array.isArray(groupData) ? groupData.length > 0 : Object.keys(groupData).length > 0)) {
          groups.push({
            name: config.name,
            label: config.label,
            icon: config.icon,
            data: groupData,
            collapsible: config.collapsible,
            collapsed: this.groupCollapsedState[config.name] || false
          })
        }
      })

      return groups
    },

    /**
     * 获取分组配置
     */
    getGroupConfigs(formType) {
      const baseGroups = [
        {
          name: 'basic_info',
          label: '基本信息',
          icon: 'bi-info-circle',
          collapsible: true,
          fields: ['公司名称', '记录日期', '前端版本', '后端版本', '客户标识', '日活', '标准或定制', '文档后缀']
        },
        {
          name: 'access_info',
          label: '访问信息',
          icon: 'bi-person-lock',
          collapsible: true,
          fields: ['超级管理员账号', '超级管理员密码', '管理员账号', '管理员密码', '用户账号', '用户密码', '客户管理员账号', '客户管理员密码', '升级平台地址', '升级用户配置']
        },
        {
          name: 'server_info',
          label: '服务器信息',
          icon: 'bi-server',
          collapsible: true,
          fields: ['服务器信息']
        },
        {
          name: 'component_info',
          label: '系统架构',
          icon: 'bi-diagram-3',
          collapsible: true,
          fields: ['selectedComponentDetails']
        },
        {
          name: 'maintenance_records',
          label: '维护记录',
          icon: 'bi-journal-text',
          collapsible: true,
          fields: ['维护记录']
        },
        {
          name: 'custom_content',
          label: '运维定制内容',
          icon: 'bi-gear',
          collapsible: true,
          fields: ['运维定制内容']
        }
      ]

      // 添加客户APP分组（所有表单类型都可能有）
      baseGroups.splice(2, 0, {
        name: 'client_apps',
        label: '客户APP',
        icon: 'bi-phone',
        collapsible: true,
        fields: ['客户APP']
      })

      // 为安全测评表单添加授权信息分组
      if (formType === '安全测评') {
        baseGroups.push({
          name: 'authorization_info',
          label: '授权信息',
          icon: 'bi-shield-check',
          collapsible: true,
          fields: ['产品功能', '授权功能', '授权开始日期', '授权结束日期']
        })
      }

      // 安全监测表单不需要额外的网络配置分组，访问信息已包含所有相关内容

      return baseGroups
    },

    /**
     * 提取分组数据
     */
    extractGroupData(data, config) {
      if (config.name === 'server_info') {
        return this.cachedServerInfo || []
      }

      if (config.name === 'component_info') {
        return this.getComponentInfo()
      }

      if (config.name === 'maintenance_records') {
        return data['维护记录'] || []
      }

      if (config.name === 'client_apps') {
        return data['客户APP'] || []
      }

      if (config.name === 'access_info') {
        return this.cachedUserInfo || []
      }

      if (config.name === 'custom_content') {
        const customContent = data['运维定制内容'] || []
        console.log('🔍 运维定制内容数据检查:')
        console.log('  - 原始数据:', data['运维定制内容'])
        console.log('  - 处理后数据:', customContent)
        console.log('  - 数据类型:', typeof customContent)
        console.log('  - 是否为数组:', Array.isArray(customContent))
        console.log('  - 数组长度:', customContent.length)
        if (customContent.length > 0) {
          console.log('  - 第一项内容:', customContent[0])
        }
        return customContent
      }

      if (config.name === 'authorization_info') {
        return this.getAuthorizationInfo(data)
      }

      // 对于其他分组，提取相关字段
      const groupData = {}
      config.fields.forEach(field => {
        if (data[field] !== undefined && data[field] !== null && data[field] !== '') {
          groupData[field] = data[field]
        }
      })

      return groupData
    },

    /**
     * 切换分组折叠状态
     */
    toggleGroupCollapse(groupName) {
      this.groupCollapsedState[groupName] = !this.groupCollapsedState[groupName]
    },

    /**
     * 获取基本信息
     */
    getBasicInfo() {
      if (!this.detailData || !this.detailData.form_data) return {}

      const data = this.detailData.form_data
      const basicInfo = {}

      // 排除特殊字段，只显示基本信息字段
      const excludeFields = ['服务器信息', '维护记录', '部署应用', '组件端口', 'selectedComponentDetails', '客户APP']

      for (const [key, value] of Object.entries(data)) {
        if (!excludeFields.includes(key) && value !== null && value !== undefined && value !== '') {
          basicInfo[key] = value
        }
      }

      return basicInfo
    },

    /**
     * 创建用户信息（带响应式密码显示状态）
     */
    createUserInfo() {
      if (!this.detailData || !this.detailData.form_data) return []

      const data = this.detailData.form_data
      const users = []

      // 检查是否有新的分组数据结构
      if (data['访问信息'] && typeof data['访问信息'] === 'object') {
        return this.createUserInfoFromGroupedData(data['访问信息'])
      }

      // 处理应用加固表单的管理员信息字段
      if (data['管理员信息']) {
        // 应用加固表单：使用分组显示
        this.addApplicationHardeningAccessInfo(data, users)
        return users
      } else {
        // 根据表单类型使用不同的分组显示策略
        if (data['业务功能页面地址']) {
          // 安全监测表单：使用分组显示
          this.addSecurityMonitoringAccessInfo(data, users)
          return users
        } else if (data['管理员页面IP'] || data['用户页面IP'] || data['升级页面IP']) {
          // 安全测评表单：使用分组显示
          this.addSecurityTestingAccessInfo(data, users)
          return users
        } else {
          // 其他表单：使用传统显示方式
          const userTypes = [
            { type: '超级管理员', usernameKey: '超级管理员账号', passwordKey: '超级管理员密码' },
            { type: '管理员', usernameKey: '管理员账号', passwordKey: '管理员密码' },
            { type: '用户', usernameKey: '用户账号', passwordKey: '用户密码' },
            { type: '客户管理员', usernameKey: '客户管理员账号', passwordKey: '客户管理员密码' }
          ]

          userTypes.forEach(userType => {
            const username = data[userType.usernameKey]
            const password = data[userType.passwordKey]

            if (username || password) {
              users.push(reactive({
                type: userType.type,
                username: username,
                password: password,
                showPassword: false
              }))
            }
          })

          // 添加网址信息
          this.addUrlInfo(data, users)
        }
      }

      return users
    },

    /**
     * 添加网址信息到用户列表
     */
    addUrlInfo(data, users) {
      // 安全测评表单的网址字段
      const securityTestingUrls = [
        { key: '管理员页面IP', label: '管理员页面地址' },
        { key: '用户页面IP', label: '用户页面地址' },
        { key: '升级页面IP', label: '升级页面地址' }
      ]

      // 安全监测表单的网址字段
      const securityMonitoringUrls = [
        { key: '业务功能页面地址', label: '业务功能页面地址' },
        { key: 'init地址', label: 'init地址' },
        { key: 'kibana地址', label: 'kibana地址' }
      ]

      // 检查并添加安全测评的网址
      securityTestingUrls.forEach(urlInfo => {
        if (data[urlInfo.key]) {
          users.push(reactive({
            type: urlInfo.label,
            value: data[urlInfo.key],
            isTextOnly: true
          }))
        }
      })

      // 检查并添加安全监测的网址
      securityMonitoringUrls.forEach(urlInfo => {
        if (data[urlInfo.key]) {
          users.push(reactive({
            type: urlInfo.label,
            value: data[urlInfo.key],
            isTextOnly: true
          }))
        }
      })

      // 添加其他可能的网址字段
      const otherUrls = [
        { key: '对外服务端口', label: '对外服务端口' }
      ]

      otherUrls.forEach(urlInfo => {
        if (data[urlInfo.key]) {
          users.push(reactive({
            type: urlInfo.label,
            value: data[urlInfo.key],
            isTextOnly: true
          }))
        }
      })
    },

    /**
     * 添加安全监测表单的访问信息
     */
    addSecurityMonitoringAccessInfo(data, users) {
      if (!data || !users) {
        console.warn('addSecurityMonitoringAccessInfo: 无效的参数')
        return
      }
      // 业务功能页面访问组合
      const businessPageGroup = {
        type: '业务功能页面访问',
        isGroup: true,
        icon: 'bi-globe',
        color: '#007bff',
        items: []
      }

      if (data['业务功能页面地址']) {
        businessPageGroup.items.push({
          label: '页面地址',
          value: data['业务功能页面地址'],
          type: 'url'
        })
      }

      // 添加超级管理员和客户管理员到业务页面组
      if (data['超级管理员账号'] || data['超级管理员密码']) {
        businessPageGroup.items.push({
          label: '超级管理员',
          username: data['超级管理员账号'] || '',
          password: data['超级管理员密码'] || '',
          type: 'auth',
          showPassword: false
        })
      }

      if (data['客户管理员账号'] || data['客户管理员密码']) {
        businessPageGroup.items.push({
          label: '客户管理员',
          username: data['客户管理员账号'] || '',
          password: data['客户管理员密码'] || '',
          type: 'auth',
          showPassword: false
        })
      }

      if (businessPageGroup.items.length > 0) {
        users.push(reactive(businessPageGroup))
      }

      // init访问组合
      const initGroup = {
        type: 'init访问',
        isGroup: true,
        icon: 'bi-gear',
        color: '#fd7e14',
        items: []
      }

      if (data['init地址']) {
        initGroup.items.push({
          label: 'init地址',
          value: data['init地址'],
          type: 'url'
        })
      }

      if (data['init用户名'] || data['init密码']) {
        initGroup.items.push({
          label: 'init认证',
          username: data['init用户名'] || '',
          password: data['init密码'] || '',
          type: 'auth',
          showPassword: false
        })
      }

      if (initGroup.items.length > 0) {
        users.push(reactive(initGroup))
      }

      // kibana访问组合
      const kibanaGroup = {
        type: 'kibana访问',
        isGroup: true,
        icon: 'bi-bar-chart',
        color: '#17a2b8',
        items: []
      }

      if (data['kibana地址']) {
        kibanaGroup.items.push({
          label: 'kibana地址',
          value: data['kibana地址'],
          type: 'url'
        })
      }

      if (data['kibana认证信息']) {
        const kibanaAuth = data['kibana认证信息']
        if (kibanaAuth.includes(':')) {
          const [username, password] = kibanaAuth.split(':')
          kibanaGroup.items.push({
            label: 'kibana认证',
            username: username.trim(),
            password: password.trim(),
            type: 'auth',
            showPassword: false
          })
        } else {
          kibanaGroup.items.push({
            label: 'kibana认证信息',
            value: kibanaAuth,
            type: 'text'
          })
        }
      }

      if (kibanaGroup.items.length > 0) {
        users.push(reactive(kibanaGroup))
      }

      // SDK流量配置组合
      const sdkGroup = {
        type: 'SDK流量配置',
        isGroup: true,
        icon: 'bi-arrow-left-right',
        color: '#28a745',
        items: []
      }

      if (data['SDK外网流量入口']) {
        sdkGroup.items.push({
          label: 'SDK外网流量入口',
          value: data['SDK外网流量入口'],
          type: 'url'
        })
      }

      if (data['SDK流量转发到Nginx入口']) {
        sdkGroup.items.push({
          label: 'SDK流量转发到Nginx入口',
          value: data['SDK流量转发到Nginx入口'],
          type: 'url'
        })
      }

      if (sdkGroup.items.length > 0) {
        users.push(reactive(sdkGroup))
      }
    },

    /**
     * 添加安全测评表单的访问信息
     */
    addSecurityTestingAccessInfo(data, users) {
      if (!data || !users) {
        console.warn('addSecurityTestingAccessInfo: 无效的参数')
        return
      }
      // 管理员页面访问组合
      const adminPageGroup = {
        type: '管理员页面访问',
        isGroup: true,
        icon: 'bi-shield-check',
        color: '#28a745',
        items: []
      }

      if (data['管理员页面IP']) {
        adminPageGroup.items.push({
          label: '管理员页面地址',
          value: data['管理员页面IP'],
          type: 'url'
        })
      }

      if (data['管理员账号'] || data['管理员密码']) {
        adminPageGroup.items.push({
          label: '管理员认证',
          username: data['管理员账号'] || '',
          password: data['管理员密码'] || '',
          type: 'auth',
          showPassword: false
        })
      }

      if (adminPageGroup.items.length > 0) {
        users.push(reactive(adminPageGroup))
      }

      // 用户页面访问组合
      const userPageGroup = {
        type: '用户页面访问',
        isGroup: true,
        icon: 'bi-person-circle',
        color: '#007bff',
        items: []
      }

      if (data['用户页面IP']) {
        userPageGroup.items.push({
          label: '用户页面地址',
          value: data['用户页面IP'],
          type: 'url'
        })
      }

      if (data['用户账号'] || data['用户密码']) {
        userPageGroup.items.push({
          label: '用户认证',
          username: data['用户账号'] || '',
          password: data['用户密码'] || '',
          type: 'auth',
          showPassword: false
        })
      }

      if (userPageGroup.items.length > 0) {
        users.push(reactive(userPageGroup))
      }

      // 升级页面访问组合
      const upgradePageGroup = {
        type: '升级页面访问',
        isGroup: true,
        icon: 'bi-arrow-up-circle',
        color: '#fd7e14',
        items: []
      }

      if (data['升级页面IP']) {
        upgradePageGroup.items.push({
          label: '升级页面地址',
          value: data['升级页面IP'],
          type: 'url'
        })
      }

      // 升级页面通常使用超级管理员账号
      if (data['超级管理员账号'] || data['超级管理员密码']) {
        upgradePageGroup.items.push({
          label: '超级管理员认证',
          username: data['超级管理员账号'] || '',
          password: data['超级管理员密码'] || '',
          type: 'auth',
          showPassword: false
        })
      }

      if (upgradePageGroup.items.length > 0) {
        users.push(reactive(upgradePageGroup))
      }
    },

    /**
     * 添加应用加固表单的访问信息
     */
    addApplicationHardeningAccessInfo(data, users) {
      if (!data || !users) {
        console.warn('addApplicationHardeningAccessInfo: 无效的参数')
        return
      }

      // 平台访问组合
      const platformGroup = {
        type: '平台访问',
        isGroup: true,
        icon: 'bi-shield-lock',
        color: '#0d6efd',
        items: []
      }

      if (data['平台访问地址']) {
        platformGroup.items.push({
          label: '平台访问地址',
          value: data['平台访问地址'],
          type: 'url'
        })
      }

      // 解析管理员信息并添加到平台访问组
      if (data['管理员信息']) {
        const adminUsers = this.parseAdminInfoForGroup(data['管理员信息'])
        platformGroup.items.push(...adminUsers)
      }

      if (platformGroup.items.length > 0) {
        users.push(reactive(platformGroup))
      }

      // 升级平台组合（单独分组）
      const upgradeGroup = {
        type: '升级平台',
        isGroup: true,
        icon: 'bi-arrow-up-circle',
        color: '#28a745',
        items: []
      }

      if (data['升级平台地址']) {
        upgradeGroup.items.push({
          label: '升级平台地址',
          value: data['升级平台地址'],
          type: 'url'
        })
      }

      // 添加升级用户配置信息
      if (data['升级用户配置']) {
        const upgradeUserConfig = data['升级用户配置']
        if (upgradeUserConfig && typeof upgradeUserConfig === 'object') {
          upgradeGroup.items.push({
            label: '升级用户',
            username: upgradeUserConfig.username || '',
            password: upgradeUserConfig.password || '',
            type: 'auth',
            showPassword: false
          })
        }
      }

      if (upgradeGroup.items.length > 0) {
        users.push(reactive(upgradeGroup))
      }
    },

    /**
     * 解析应用加固表单的管理员信息为分组格式
     */
    parseAdminInfoForGroup(adminInfoText) {
      if (!adminInfoText) return []

      const items = []
      const lines = adminInfoText.split('\n')

      lines.forEach(line => {
        line = line.trim()
        if (!line) return

        // 解析格式：超级管理员账号：sadmin 密码：sadmin123
        if (line.includes('超级管理员账号：')) {
          const match = line.match(/超级管理员账号：([^\s]*)\s*密码：(.*)/)
          if (match) {
            items.push({
              label: '超级管理员',
              username: match[1] || '',
              password: match[2] || '',
              type: 'auth',
              showPassword: false
            })
          }
        }

        // 解析格式：平台用户账号：user 密码：user123
        if (line.includes('平台用户账号：')) {
          const match = line.match(/平台用户账号：([^\s]*)\s*密码：(.*)/)
          if (match) {
            items.push({
              label: '平台用户',
              username: match[1] || '',
              password: match[2] || '',
              type: 'auth',
              showPassword: false
            })
          }
        }

        // 解析格式：管理员账号：admin 密码：admin123
        if (line.includes('管理员账号：') && !line.includes('超级管理员账号：')) {
          const match = line.match(/管理员账号：([^\s]*)\s*密码：(.*)/)
          if (match) {
            items.push({
              label: '管理员',
              username: match[1] || '',
              password: match[2] || '',
              type: 'auth',
              showPassword: false
            })
          }
        }
      })

      return items
    },

    /**
     * 解析应用加固表单的管理员信息字段
     */
    parseAdminInfo(adminInfoText) {
      if (!adminInfoText) return []

      const users = []
      const lines = adminInfoText.split('\n')

      lines.forEach(line => {
        line = line.trim()
        if (!line) return

        // 解析格式：管理员账号：admin 密码：admin123
        if (line.includes('管理员账号：')) {
          const match = line.match(/管理员账号：([^\s]*)\s*密码：(.*)/)
          if (match) {
            users.push(reactive({
              type: '管理员',
              username: match[1] || '',
              password: match[2] || '',
              showPassword: false
            }))
          }
        }

        // 解析格式：超级管理员账号：sadmin 密码：sadmin123
        if (line.includes('超级管理员账号：')) {
          const match = line.match(/超级管理员账号：([^\s]*)\s*密码：(.*)/)
          if (match) {
            users.push(reactive({
              type: '超级管理员',
              username: match[1] || '',
              password: match[2] || '',
              showPassword: false
            }))
          }
        }

        // 解析格式：平台用户账号：user 密码：user123
        if (line.includes('平台用户账号：')) {
          const match = line.match(/平台用户账号：([^\s]*)\s*密码：(.*)/)
          if (match) {
            users.push(reactive({
              type: '平台用户',
              username: match[1] || '',
              password: match[2] || '',
              showPassword: false
            }))
          }
        }
      })

      return users
    },

    /**
     * 创建服务器信息（带响应式密码显示状态）
     */
    createServerInfo() {
      if (!this.detailData || !this.detailData.form_data || !this.detailData.form_data.服务器信息) return []

      const servers = this.detailData.form_data.服务器信息
      return servers.map(server => {
        const serverCopy = { ...server }

        // 为运维用户数组中的每个用户添加showPassword属性，并过滤掉空值
        if (serverCopy.运维用户 && Array.isArray(serverCopy.运维用户)) {
          serverCopy.运维用户 = serverCopy.运维用户
            .filter(user => user != null) // 只过滤掉null和undefined
            .map(user => ({
              ...user,
              showPassword: false
            }))
        }

        return reactive(serverCopy)
      })
    },

    /**
     * 获取用户信息
     */
    getUserInfo() {
      if (!this.detailData || !this.detailData.form_data) return []

      const data = this.detailData.form_data
      const users = []

      // 提取各种用户账号信息
      const userTypes = [
        { type: '超级管理员', usernameKey: '超级管理员账号', passwordKey: '超级管理员密码' },
        { type: '管理员', usernameKey: '管理员账号', passwordKey: '管理员密码' },
        { type: '用户', usernameKey: '用户账号', passwordKey: '用户密码' },
        { type: '客户管理员', usernameKey: '客户管理员账号', passwordKey: '客户管理员密码' }
      ]

      userTypes.forEach(userType => {
        const username = data[userType.usernameKey]
        const password = data[userType.passwordKey]

        if (username || password) {
          users.push({
            type: userType.type,
            username: username,
            password: password,
            showPassword: false
          })
        }
      })

      return users
    },

    /**
     * 获取服务器信息
     */
    getServerInfo() {
      if (!this.detailData || !this.detailData.form_data || !this.detailData.form_data.服务器信息) return []
      return this.detailData.form_data.服务器信息
    },

    /**
     * 获取组件信息
     */
    getComponentInfo() {
      if (!this.detailData || !this.detailData.form_data) return []

      const data = this.detailData.form_data
      const components = []

      // 从selectedComponentDetails获取组件详细信息
      const componentDetails = data.selectedComponentDetails || {}

      if (Object.keys(componentDetails).length > 0) {
        Object.values(componentDetails).forEach(component => {
          components.push({
            name: component.name || '未知组件',
            version: component.version || '',
            port: component.port || '',
            count: component.count || '1',
            defaultPort: component.defaultPort || ''
          })
        })
      } else {
        // 如果没有详细信息，从部署应用和组件端口构建
        const deployedApps = data.部署应用 || []
        const componentPorts = data.组件端口 || {}

        deployedApps.forEach(appName => {
          components.push({
            name: appName,
            version: '',
            port: componentPorts[appName] || '',
            count: '1',
            defaultPort: componentPorts[appName] || ''
          })
        })
      }

      return components
    },

    /**
     * 获取客户APP信息
     */
    getClientApps() {
      if (!this.detailData || !this.detailData.form_data || !this.detailData.form_data.客户APP) return []
      return this.detailData.form_data.客户APP
    },

    /**
     * 获取维护记录
     */
    getMaintenanceRecords() {
      if (!this.detailData || !this.detailData.form_data || !this.detailData.form_data.维护记录) return []
      return this.detailData.form_data.维护记录
    },

    /**
     * 检查服务器是否有运维账号信息
     */
    hasOperationAccounts(server) {
      // 只检查新的数据结构（运维用户数组）
      if (server.运维用户 && Array.isArray(server.运维用户) && server.运维用户.length > 0) {
        return server.运维用户.some(user => user != null)
      }
      return false
    },

    /**
     * 切换密码显示/隐藏
     */
    togglePasswordVisibility(user) {
      // 确保属性存在并且是响应式的
      if (!user.hasOwnProperty('showPassword')) {
        this.$set ? this.$set(user, 'showPassword', false) : (user.showPassword = false)
      }
      user.showPassword = !user.showPassword
      // 强制更新视图
      this.$forceUpdate()
    },

    /**
     * 切换服务器密码显示/隐藏
     */
    toggleServerPasswordVisibility(server, passwordField) {
      // 确保属性存在并且是响应式的
      if (!server.hasOwnProperty(passwordField)) {
        this.$set ? this.$set(server, passwordField, false) : (server[passwordField] = false)
      }
      server[passwordField] = !server[passwordField]
      // 强制更新视图
      this.$forceUpdate()
    },

    /**
     * 切换运维用户密码可见性
     */
    toggleUserPasswordVisibility(user) {
      // 确保属性存在并且是响应式的
      if (!user.hasOwnProperty('showPassword')) {
        this.$set ? this.$set(user, 'showPassword', false) : (user.showPassword = false)
      }
      user.showPassword = !user.showPassword
      // 强制更新视图
      this.$forceUpdate()
    },

    /**
     * 获取组件徽章的CSS类
     */
    getComponentBadgeClass(componentName) {
      const category = this.getComponentCategory(componentName)
      const categoryClasses = {
        '前端服务': 'component-badge-frontend',
        '后端服务': 'component-badge-backend',
        '数据库': 'component-badge-database',
        '中间件': 'component-badge-middleware',
        '监控工具': 'component-badge-monitoring',
        '安全组件': 'component-badge-security',
        '基础设施': 'component-badge-infrastructure',
        '其他': 'component-badge-other'
      }
      return categoryClasses[category] || 'component-badge-other'
    },

    /**
     * 获取组件图标
     */
    getComponentIcon(componentName) {
      const iconMap = {
        // 前端服务
        'web-service-nginx': 'bi bi-globe',
        'nginx': 'bi bi-globe',
        'apache': 'bi bi-globe',

        // 后端服务
        'bangcle-service': 'bi bi-gear-fill',
        'api-service': 'bi bi-cpu',
        'java-service': 'bi bi-cup-hot',

        // 数据库
        'mysql': 'bi bi-database',
        'postgresql': 'bi bi-database',
        'mongodb': 'bi bi-database-fill',
        'redis': 'bi bi-database-dash',

        // 中间件
        'rabbitmq': 'bi bi-arrow-left-right',
        'kafka': 'bi bi-arrow-repeat',
        'elasticsearch': 'bi bi-search',

        // 监控工具
        'kibana': 'bi bi-bar-chart',
        'grafana': 'bi bi-graph-up',
        'prometheus': 'bi bi-speedometer2',

        // 安全组件
        'init': 'bi bi-shield-check',
        'security-monitor': 'bi bi-shield-fill-check',

        // 基础设施
        'docker': 'bi bi-box',
        'kubernetes': 'bi bi-boxes'
      }

      // 模糊匹配
      for (const [key, icon] of Object.entries(iconMap)) {
        if (componentName.toLowerCase().includes(key.toLowerCase())) {
          return icon
        }
      }

      return 'bi bi-puzzle' // 默认图标
    },

    /**
     * 获取组件分类
     */
    getComponentCategory(componentName) {
      console.log('🔍 获取组件分类:', componentName, '数据库状态:', !!this.componentDatabase)

      // 首先尝试从数据库获取
      if (this.componentDatabase && Object.keys(this.componentDatabase).length > 0) {
        console.log('📦 数据库分类数据:', Object.keys(this.componentDatabase))

        for (const categoryKey in this.componentDatabase) {
          const categoryData = this.componentDatabase[categoryKey]
          if (categoryData && categoryData.components) {
            const component = categoryData.components.find(comp =>
              comp.name === componentName || comp.id === componentName
            )
            if (component) {
              const category = categoryData.category_info?.display_name || categoryKey
              console.log('✅ 从数据库找到组件分类:', componentName, '->', category)
              return category
            }
          }
        }
        console.log('⚠️ 数据库中未找到组件:', componentName)
      } else {
        console.log('⚠️ 组件数据库未加载或为空')
      }

      // 回退到硬编码映射
      const categoryMap = {
        // 前端服务
        'web-service-nginx': '前端服务',
        'nginx': '前端服务',
        'apache': '前端服务',

        // 后端服务
        'bangcle-service': '后端服务',
        'api-service': '后端服务',
        'java-service': '后端服务',
        'spring': '后端服务',

        // 数据库
        'mysql': '数据库',
        'postgresql': '数据库',
        'mongodb': '数据库',
        'redis': '数据库',

        // 中间件
        'rabbitmq': '中间件',
        'kafka': '中间件',
        'elasticsearch': '中间件',
        'zookeeper': '中间件',

        // 监控工具
        'kibana': '监控工具',
        'grafana': '监控工具',
        'prometheus': '监控工具',

        // 安全组件
        'init': '安全组件',
        'security': '安全组件',
        'auth': '安全组件',

        // 基础设施
        'docker': '基础设施',
        'kubernetes': '基础设施',
        'k8s': '基础设施'
      }

      // 精确匹配
      if (categoryMap[componentName]) {
        console.log('📋 使用硬编码分类:', componentName, '->', categoryMap[componentName])
        return categoryMap[componentName]
      }

      // 模糊匹配
      for (const [key, category] of Object.entries(categoryMap)) {
        if (componentName.toLowerCase().includes(key.toLowerCase())) {
          console.log('🔍 模糊匹配分类:', componentName, '->', category)
          return category
        }
      }

      console.log('❓ 未找到分类，使用默认:', componentName, '-> 其他')
      return '其他'
    },

    /**
     * 创建分享链接
     */
    async createShareLink() {
      if (this.creatingShareLink) return

      this.creatingShareLink = true

      try {
        const token = localStorage.getItem('access_token')
        console.log('🔑 Token from localStorage:', token)
        console.log('🔑 Token length:', token ? token.length : 0)
        console.log('🔑 Token preview:', token ? token.substring(0, 50) + '...' : 'null')

        if (!token) {
          alert('未找到登录凭证，请重新登录')
          return
        }

        const response = await fetch(`${API_BASE_URL}/api/share/create`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            submission_id: this.submission.id,
            expires_hours: 72 // 默认72小时过期
          })
        })

        console.log('📡 Response status:', response.status)
        console.log('📡 Response headers:', response.headers)

        const result = await response.json()

        if (result.status === 'success') {
          this.shareLink = result.data
          this.showShareLinkModal()
        } else {
          console.error('创建分享链接失败:', result.message)
          alert('创建分享链接失败: ' + result.message)
        }
      } catch (error) {
        console.error('创建分享链接失败:', error)
        alert('创建分享链接失败，请稍后重试')
      } finally {
        this.creatingShareLink = false
      }
    },

    /**
     * 创建指定过期时间的分享链接
     */
    async createShareLinkWithExpiry(expiryHours) {
      if (this.creatingShareLink) return

      this.creatingShareLink = true

      try {
        const token = localStorage.getItem('access_token')
        console.log('🔑 Token from localStorage:', token)

        if (!token) {
          alert('未找到登录凭证，请重新登录')
          return
        }

        const response = await fetch(`${API_BASE_URL}/api/share/create`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            submission_id: this.submission.id,
            expires_hours: expiryHours // 使用指定的过期时间
          })
        })

        const result = await response.json()

        if (result.status === 'success') {
          this.shareLink = result.data
          this.showShareLinkModal(expiryHours)
        } else {
          console.error('创建分享链接失败:', result.message)
          alert('创建分享链接失败: ' + result.message)
        }
      } catch (error) {
        console.error('创建分享链接失败:', error)
        alert('创建分享链接失败，请稍后重试')
      } finally {
        this.creatingShareLink = false
      }
    },

    /**
     * 显示分享链接模态框
     */
    showShareLinkModal(expiryHours = 72) {
      // 创建模态框内容
      const modalHtml = `
        <div class="modal fade" id="shareLinkModal" tabindex="-1" data-bs-backdrop="static" style="z-index: 1070;">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">
                  <i class="bi bi-share me-2"></i>分享链接
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <div class="alert alert-info">
                  <i class="bi bi-info-circle me-2"></i>
                  此链接允许他人无需登录即可查看表单详情，${this.getExpiryText(expiryHours)}
                </div>

                <div class="mb-3">
                  <label class="form-label">分享链接：</label>
                  <div class="input-group">
                    <input type="text" class="form-control" id="shareUrlInput" value="${this.shareLink.share_url}" readonly>
                    <button class="btn btn-outline-secondary" type="button" onclick="copyShareUrl()">
                      <i class="bi bi-clipboard"></i> 复制
                    </button>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <small class="text-muted">
                      <i class="bi bi-person me-1"></i>
                      分享人：${this.shareLink.sharer.real_name}
                    </small>
                  </div>
                  <div class="col-md-6">
                    <small class="text-muted">
                      <i class="bi bi-clock me-1"></i>
                      过期时间：${new Date(this.shareLink.expires_at).toLocaleString()}
                    </small>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="copyShareUrl()">
                  <i class="bi bi-clipboard me-1"></i>复制链接
                </button>
              </div>
            </div>
          </div>
        </div>
      `

      // 移除已存在的模态框
      const existingModal = document.getElementById('shareLinkModal')
      if (existingModal) {
        existingModal.remove()
      }

      // 添加新的模态框到页面
      document.body.insertAdjacentHTML('beforeend', modalHtml)

      // 添加复制功能
      window.copyShareUrl = () => {
        const input = document.getElementById('shareUrlInput')
        input.select()
        document.execCommand('copy')

        // 显示复制成功提示
        const button = event.target.closest('button')
        const originalText = button.innerHTML
        button.innerHTML = '<i class="bi bi-check me-1"></i>已复制'
        button.classList.remove('btn-outline-secondary', 'btn-primary')
        button.classList.add('btn-success')

        setTimeout(() => {
          button.innerHTML = originalText
          button.classList.remove('btn-success')
          button.classList.add('btn-outline-secondary')
        }, 2000)
      }

      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('shareLinkModal'))
      modal.show()

      // 模态框关闭时清理
      document.getElementById('shareLinkModal').addEventListener('hidden.bs.modal', () => {
        document.getElementById('shareLinkModal').remove()
        delete window.copyShareUrl
      })
    },

    /**
     * 获取过期时间文本
     */
    getExpiryText(expiryHours) {
      if (expiryHours === 0) {
        return '永久有效'
      } else if (expiryHours < 24) {
        return `有效期${expiryHours}小时`
      } else if (expiryHours < 168) {
        const days = Math.floor(expiryHours / 24)
        return `有效期${days}天`
      } else if (expiryHours < 720) {
        const weeks = Math.floor(expiryHours / 168)
        return `有效期${weeks}周`
      } else {
        const months = Math.floor(expiryHours / 720)
        return `有效期${months}个月`
      }
    },

    /**
     * 按分组排序组件
     */
    getSortedComponents(components) {
      if (!components || !Array.isArray(components)) {
        return []
      }

      // 定义分组优先级
      const categoryPriority = {
        '前端服务': 1,
        '后端服务': 2,
        '数据库': 3,
        '中间件': 4,
        '监控工具': 5,
        '安全组件': 6,
        '基础设施': 7,
        '其他': 8
      }

      // 为每个组件添加分组信息并排序
      return components
        .map(component => ({
          name: component,
          category: this.getComponentCategory(component),
          priority: categoryPriority[this.getComponentCategory(component)] || 8
        }))
        .sort((a, b) => {
          // 首先按分组优先级排序
          if (a.priority !== b.priority) {
            return a.priority - b.priority
          }
          // 同一分组内按名称字母顺序排序
          return a.name.localeCompare(b.name)
        })
        .map(item => item.name)
    },

    /**
     * 获取组件行的CSS类
     */
    getComponentRowClass(componentName) {
      const category = this.getComponentCategory(componentName)
      const categoryRowClasses = {
        '前端服务': 'component-row-frontend',
        '后端服务': 'component-row-backend',
        '数据库': 'component-row-database',
        '中间件': 'component-row-middleware',
        '监控工具': 'component-row-monitoring',
        '安全组件': 'component-row-security',
        '基础设施': 'component-row-infrastructure',
        '其他': 'component-row-other'
      }
      return categoryRowClasses[category] || 'component-row-other'
    },

    /**
     * 获取组件颜色
     */
    getComponentColor(componentName) {
      const category = this.getComponentCategory(componentName)
      const categoryColors = {
        '前端服务': '#007bff',
        '后端服务': '#28a745',
        '数据库': '#6f42c1',
        '中间件': '#fd7e14',
        '监控工具': '#17a2b8',
        '安全组件': '#dc3545',
        '基础设施': '#6c757d',
        '其他': '#adb5bd'
      }
      return categoryColors[category] || '#adb5bd'
    },

    /**
     * 获取组件分类徽章的CSS类
     */
    getComponentCategoryBadgeClass(componentName) {
      const category = this.getComponentCategory(componentName)
      const categoryBadgeClasses = {
        '前端服务': 'badge bg-primary',
        '后端服务': 'badge bg-success',
        '数据库': 'badge bg-purple',
        '中间件': 'badge bg-warning text-dark',
        '监控工具': 'badge bg-info',
        '安全组件': 'badge bg-danger',
        '基础设施': 'badge bg-secondary',
        '其他': 'badge bg-light text-dark'
      }
      return categoryBadgeClasses[category] || 'badge bg-light text-dark'
    },

    /**
     * 切换分组内项目的密码显示状态
     */
    toggleGroupItemPassword(item) {
      if (item) {
        item.showPassword = !item.showPassword
      }
    },

    /**
     * 切换原始数据显示
     */
    toggleRawData() {
      console.log('切换原始数据显示，当前状态:', this.showRawData)
      this.showRawData = !this.showRawData
      console.log('切换后状态:', this.showRawData)
    },

    /**
     * 复制表单数据
     */
    async copyFormData() {
      console.log('复制表单数据被点击')
      try {
        const dataText = JSON.stringify(this.detailData.form_data, null, 2)
        
        // 尝试使用现代剪贴板API
        try {
          await navigator.clipboard.writeText(dataText)
          console.log('复制成功')
          alert('数据已复制到剪贴板')
        } catch (clipboardError) {
          // 如果现代API失败，使用降级方案
          console.warn('现代剪贴板API失败，尝试降级方案:', clipboardError)
          this.fallbackCopyToClipboard(dataText)
          alert('数据已复制到剪贴板')
        }
      } catch (error) {
        console.error('复制失败:', error)
        alert('复制失败: ' + error.message)
      }
    },

    /**
     * 显示模板选择模态框
     */
    showTemplateSelectionModal() {
      console.log('🎯 显示模板选择模态框被调用')
      console.log('📋 当前submission数据:', this.submission)
      console.log('🔧 TemplateSelectionModal组件:', this.$options.components.TemplateSelectionModal)

      this.showTemplateSelection = true

      console.log('✅ showTemplateSelection已设置为:', this.showTemplateSelection)

      // 检查DOM更新
      this.$nextTick(() => {
        const modalElement = document.querySelector('.modal.show')
        console.log('🔍 模态框DOM元素:', modalElement)
        if (!modalElement) {
          console.error('❌ 模态框DOM元素未找到！')
        }
      })
    },

    /**
     * 处理模板选择确认
     * @param {Object} selection - 选择的模板信息
     */
    async handleTemplateSelection(selection) {
      console.log('模板选择确认:', selection)

      // 关闭模板选择模态框
      this.showTemplateSelection = false

      // 执行重新生成Excel
      await this.regenerateExcelWithTemplate(
        selection.templateId,
        selection.templateType,
        selection.templateInfo,
        selection.filename
      )
    },

    /**
     * 重新生成Excel
     * @param {String} templateId - 模板ID
     * @param {String} templateType - 模板类型
     * @param {Object} templateInfo - 模板信息
     * @param {String} filename - 模板文件名
     */
    async regenerateExcelWithTemplate(templateId, templateType, templateInfo, filename) {
      // 空值检查
      if (!this.submission || !this.submission.form_type || !this.submission.company_name) {
        alert('表单数据不完整，无法重新生成Excel')
        return
      }

      const confirmMessage = templateType === this.submission.form_type
        ? `确定要重新生成 ${this.submission.company_name} 的Excel文件吗？`
        : `确定要使用 "${templateType}" 模板重新生成 ${this.submission.company_name} 的Excel文件吗？\n\n注意：模板类型与原始表单类型不同，某些字段可能需要数据映射处理。`

      if (!confirm(confirmMessage)) {
        return
      }

      try {
        // 构建请求参数
        const requestBody = {
          template_id: templateId,
          template_type: templateType,
          filename: filename
        }

        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${this.submission.id}/regenerate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        })

        const contentType = response.headers.get('content-type')

        if (contentType && contentType.includes('application/json')) {
          // JSON响应，说明有错误
          const result = await response.json()
          alert(result.message || '重新生成失败')
        } else {
          // 文件响应，直接下载
          const blob = await response.blob()

          // 从响应头获取文件名
          const templateSuffix = templateType !== this.submission.form_type ? `_${templateType}模板` : ''
          const defaultFilename = `${this.submission.company_name}-运维文档-${templateType}${templateSuffix}_重新生成.xlsx`
          const contentDisposition = response.headers.get('content-disposition')
          const filename = this.parseFilenameFromContentDisposition(contentDisposition, defaultFilename)

          // 下载文件
          this.downloadBlob(blob, filename)

          const successMessage = templateType !== this.submission.form_type
            ? `Excel文件已使用 "${templateType}" 模板重新生成并下载成功`
            : 'Excel文件重新生成并下载成功'

          alert(successMessage)
          this.$emit('regenerate', {
            success: true,
            filename,
            templateType,
            templateInfo
          })
        }
      } catch (error) {
        console.error('重新生成Excel失败:', error)
        alert('重新生成Excel失败: ' + error.message)
      }
    },

    /**
     * 重新生成Excel（保留原方法作为兼容）
     * 直接使用当前表单类型重新生成
     */
    async regenerateExcel() {
      // 空值检查
      if (!this.submission || !this.submission.form_type || !this.submission.company_name) {
        alert('表单数据不完整，无法重新生成Excel')
        return
      }

      // 使用当前表单类型重新生成
      await this.regenerateExcelWithTemplate(this.submission.form_type, {
        type: this.submission.form_type,
        description: `${this.submission.form_type}项目专用模板`
      })
    },

    /**
     * 获取表单类型徽章样式
     */
    getFormTypeBadgeClass(formType) {
      const classes = {
        '安全监测': 'bg-primary',
        '安全测评': 'bg-success',
        '应用加固': 'bg-warning text-dark'
      }
      return classes[formType] || 'bg-secondary'
    },

    /**
     * 获取状态徽章样式
     */
    getStatusBadgeClass(status) {
      const classes = {
        'success': 'bg-success',
        'failed': 'bg-danger',
        'processing': 'bg-warning text-dark'
      }
      return classes[status] || 'bg-secondary'
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const texts = {
        'success': '成功',
        'failed': '失败',
        'processing': '处理中'
      }
      return texts[status] || '未知'
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString('zh-CN')
    },

    /**
     * 格式化日期时间
     */
    formatDateTime(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    },

    /**
     * 查看编辑历史
     */
    viewEditHistory() {
      console.log('查看编辑历史被点击')
      this.showEditHistory = true
    },

    /**
     * 处理数据恢复
     */
    handleRestore() {
      // 在查看页面中，我们只是显示恢复成功的消息
      // 实际的恢复操作已经在后端完成
      alert('数据已恢复成功！请刷新页面查看最新数据。')

      // 重新加载详细数据以显示最新状态
      this.loadDetailData()

      // 关闭编辑历史模态框
      this.showEditHistory = false
    },

    /**
     * 从Content-Disposition头解析文件名
     */
    parseFilenameFromContentDisposition(contentDisposition, defaultFilename) {
      return parseFilenameFromContentDisposition(contentDisposition, defaultFilename)
    },

    /**
     * 下载Blob数据为文件
     */
    downloadBlob(blob, filename) {
      return downloadBlob(blob, filename)
    },

    /**
     * 获取访问信息项目的列宽类
     */
    getItemColumnClass(item, totalItems) {
      if (!item || typeof totalItems !== 'number') {
        return 'col-md-6' // 默认列宽
      }
      // URL类型通常内容较长，给更多空间
      if (item.type === 'url') {
        return totalItems <= 2 ? 'col-md-8' : 'col-md-6'
      }
      // 认证类型需要显示账号密码，给适中空间
      if (item.type === 'auth') {
        return totalItems <= 2 ? 'col-md-4' : 'col-md-6'
      }
      // 文本类型根据项目数量自适应
      return totalItems <= 2 ? 'col-md-6' : 'col-md-4'
    },

    /**
     * 获取分组的URL地址
     */
    getGroupUrl(group) {
      if (!group || !group.items) return null

      // 查找该分组中的URL类型项目
      const urlItem = group.items.find(item => item.type === 'url')
      return urlItem ? urlItem.value : null
    },

    /**
     * 获取授权信息
     */
    getAuthorizationInfo(data) {
      const authInfo = {}
      const authFields = ['产品功能', '授权功能', '授权开始日期', '授权结束日期']

      authFields.forEach(field => {
        if (data[field] !== undefined && data[field] !== null && data[field] !== '') {
          authInfo[field] = data[field]
        }
      })

      return Object.keys(authInfo).length > 0 ? authInfo : null
    },

    /**
     * 获取授权字段的图标
     */
    getAuthFieldIcon(fieldKey) {
      const iconMap = {
        '产品功能': 'bi-box-seam',
        '授权功能': 'bi-shield-check',
        '授权开始日期': 'bi-calendar-check',
        '授权结束日期': 'bi-calendar-x'
      }
      return iconMap[fieldKey] || 'bi-info-circle'
    },

    /**
     * 获取授权字段的标签
     */
    getAuthFieldLabel(fieldKey) {
      const labelMap = {
        '产品功能': '产品功能',
        '授权功能': '授权功能',
        '授权开始日期': '授权开始日期',
        '授权结束日期': '授权结束日期'
      }
      return labelMap[fieldKey] || fieldKey
    },

    /**
     * 复制文本到剪贴板
     */
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        // 这里可以添加成功提示，比如toast通知
        console.log('地址已复制到剪贴板:', text)
      } catch (err) {
        console.error('复制失败:', err)
        // 降级方案：使用传统的复制方法
        this.fallbackCopyToClipboard(text)
      }
    },

    /**
     * 降级复制方法（兼容旧浏览器）
     */
    fallbackCopyToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        console.log('地址已复制到剪贴板:', text)
      } catch (err) {
        console.error('复制失败:', err)
      }
      document.body.removeChild(textArea)
    }
  }
}
</script>

<style scoped>
.modal {
  z-index: 1060;
}

/* URL样式简化 */
.url-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-left: 3px solid #007bff;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.url-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.url-icon-wrapper {
  width: 24px;
  height: 24px;
  background: #007bff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.url-icon {
  color: white;
  font-size: 12px;
}

.url-label {
  font-weight: 500;
  color: #495057;
  font-size: 13px;
}

.url-content {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 4px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
}

.url-link {
  flex: 1;
  color: #007bff;
  text-decoration: none;
  font-size: 13px;
  word-break: break-all;
  display: flex;
  align-items: center;
}

.url-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

.url-copy-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  cursor: pointer;
  color: #6c757d;
  font-size: 12px;
}

.url-copy-btn:hover {
  background: #e9ecef;
  color: #495057;
}

/* 认证信息样式简化 */
.auth-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-left: 3px solid #28a745;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.auth-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.auth-icon-wrapper {
  width: 24px;
  height: 24px;
  background: #28a745;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.auth-icon {
  color: white;
  font-size: 12px;
}

.auth-label {
  font-weight: 500;
  color: #495057;
  font-size: 13px;
}

.auth-content {
  background: white;
  border-radius: 4px;
  padding: 12px;
  border: 1px solid #dee2e6;
}

.auth-field {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.auth-field:last-child {
  margin-bottom: 0;
}

.auth-field-label {
  min-width: 50px;
  font-weight: 500;
  color: #6c757d;
  font-size: 12px;
  display: flex;
  align-items: center;
  margin-right: 12px;
}

.auth-field-value {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #495057;
}

.password-text {
  flex: 1;
  font-family: monospace;
}

.password-dots {
  color: #6c757d;
}

.password-toggle-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  cursor: pointer;
  color: #6c757d;
  font-size: 11px;
}

.password-toggle-btn:hover {
  background: #e9ecef;
  color: #495057;
}

/* 文本信息样式简化 */
.text-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-left: 3px solid #ffc107;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.text-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.text-icon-wrapper {
  width: 24px;
  height: 24px;
  background: #ffc107;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.text-icon {
  color: white;
  font-size: 12px;
}

.text-label {
  font-weight: 500;
  color: #495057;
  font-size: 13px;
}

.text-content {
  background: white;
  border-radius: 4px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
}

.text-value {
  color: #495057;
  font-size: 13px;
  word-break: break-all;
}

/* 访问信息分组卡片简化 */
.access-group-card {
  border: 1px solid #dee2e6 !important;
  border-radius: 8px !important;
  margin-bottom: 16px !important;
}

.access-group-card .card-header {
  border-bottom: 1px solid #dee2e6 !important;
  border-radius: 8px 8px 0 0 !important;
  padding: 12px 16px !important;
}

.access-group-card .card-header .fw-bold {
  font-size: 14px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
}

.access-group-card .card-header .fw-bold i {
  margin-right: 6px;
  font-size: 14px;
}

.access-group-card .card-body {
  padding: 16px !important;
  background: #fafbfc;
}

/* 分组标题内容布局 */
.group-header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.group-title {
  font-size: 16px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
}

.group-title i {
  margin-right: 8px;
  font-size: 18px;
}

/* 分组URL样式 */
.group-url {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  padding: 6px 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 4px;
}

.group-url-link {
  flex: 1;
  color: #495057;
  text-decoration: none;
  font-weight: 500;
  font-size: 12px;
  word-break: break-all;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
}

.group-url-link:hover {
  color: #007bff;
  text-decoration: none;
}

.group-url-link i {
  margin-right: 4px;
  font-size: 11px;
  color: #6c757d;
}

.group-url-copy {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6c757d;
  font-size: 10px;
}

.group-url-copy:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .url-item, .auth-item, .text-item {
    margin-bottom: 8px;
    padding: 10px;
  }

  .url-content, .auth-content, .text-content {
    padding: 8px;
  }

  .url-header, .auth-header, .text-header {
    margin-bottom: 6px;
  }

  .url-icon-wrapper, .auth-icon-wrapper, .text-icon-wrapper {
    width: 20px;
    height: 20px;
    margin-right: 6px;
  }

  .url-icon, .auth-icon, .text-icon {
    font-size: 10px;
  }

  /* 移动端分组标题优化 */
  .group-header-content {
    gap: 6px;
  }

  .group-title {
    font-size: 14px;
  }

  .group-title i {
    font-size: 16px;
    margin-right: 6px;
  }

  .group-url {
    padding: 4px 8px;
    flex-direction: row;
    align-items: center;
    gap: 6px;
  }

  .group-url-link {
    font-size: 11px;
    word-break: break-all;
    font-weight: 500;
  }

  .group-url-copy {
    width: 20px;
    height: 20px;
    font-size: 9px;
    margin-left: 4px;
  }
}

.card {
  border: 1px solid #dee2e6;
}

.card-header {
  background-color: #f8f9fa;
}

pre {
  max-height: 400px;
  overflow-y: auto;
  font-size: 0.85rem;
}

.badge {
  font-size: 0.75rem;
}

/* 组件表格样式 */
.component-table {
  font-size: 0.8rem;
  margin-bottom: 0;
}

.component-table th {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  font-weight: 600;
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
}

.component-table td {
  padding: 0.5rem 0.75rem;
  vertical-align: middle;
  border-color: #dee2e6;
}

.component-name-cell {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.component-category-badge {
  font-size: 0.65rem;
  padding: 0.25rem 0.5rem;
}

/* 自定义紫色徽章 */
.bg-purple {
  background-color: #6f42c1 !important;
  color: white;
}

/* 组件行悬停效果 */
.component-table tbody tr {
  transition: background-color 0.2s ease;
}

.component-table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* 不同分组的行样式 */
.component-row-frontend {
  border-left: 3px solid #007bff;
}

.component-row-backend {
  border-left: 3px solid #28a745;
}

/* URL字段样式 */
.url-field {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.url-field a {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  word-break: break-all;
  max-width: 100%;
  font-size: 0.85rem;
}

.url-field a:hover {
  background-color: #e9ecef;
  border-color: #0d6efd;
  text-decoration: none !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.url-field a:active {
  transform: translateY(0);
}

.url-field .btn {
  padding: 2px 6px;
  font-size: 0.7rem;
  border-radius: 4px;
}

.url-field .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.component-row-database {
  border-left: 3px solid #6f42c1;
}

.component-row-middleware {
  border-left: 3px solid #fd7e14;
}

.component-row-monitoring {
  border-left: 3px solid #17a2b8;
}

.component-row-security {
  border-left: 3px solid #dc3545;
}

.component-row-infrastructure {
  border-left: 3px solid #6c757d;
}

.component-row-other {
  border-left: 3px solid #adb5bd;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .component-table {
    font-size: 0.75rem;
  }

  .component-table th,
  .component-table td {
    padding: 0.375rem 0.5rem;
  }

  .component-category-badge {
    font-size: 0.6rem;
    padding: 0.125rem 0.375rem;
  }
}

/* 访问信息分组样式 */
.access-group-card {
  transition: all 0.2s ease;
  margin-bottom: 1rem;
}

.access-group-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.access-item {
  padding: 0.75rem;
  border-radius: 0.375rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.url-item {
  background-color: #f8f9fa;
  border-left: 4px solid #007bff;
}

.auth-item {
  background-color: #f0f8f0;
  border-left: 4px solid #28a745;
}

.text-item {
  background-color: #e7f3ff;
  border-left: 4px solid #17a2b8;
}

.access-label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.access-value {
  flex-grow: 1;
}

.url-item a {
  word-break: break-all;
  line-height: 1.4;
}

.url-item a:hover {
  text-decoration: underline !important;
}

.auth-item .access-value {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

/* 单行布局优化 */
.access-group-card .card-body .row {
  align-items: stretch;
}

.access-group-card .card-body .row > div {
  display: flex;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .access-item {
    padding: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .access-label {
    margin-bottom: 0.375rem;
  }

  /* 移动端改为垂直布局 */
  .access-group-card .card-body .row > div {
    flex-basis: 100% !important;
    max-width: 100% !important;
  }
}

/* 强制组件表格列宽平均分布 */
.component-table {
  table-layout: fixed !important;
  width: 100% !important;
}

.component-table th,
.component-table td {
  width: 33.33% !important;
  min-width: 33.33% !important;
  max-width: 33.33% !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}
</style>
