<template>
  <div class="component-filter-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-funnel me-2"></i>
                组件过滤测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 表单类型选择 -->
              <div class="row mb-4">
                <div class="col-md-4">
                  <label class="form-label fw-bold">选择表单类型</label>
                  <select v-model="selectedFormType" class="form-select" @change="loadFormType">
                    <option value="">请选择表单类型</option>
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">操作</label>
                  <div class="d-flex gap-2">
                    <button 
                      class="btn btn-primary btn-sm" 
                      @click="debugComponentGroups"
                      :disabled="!selectedFormType"
                    >
                      调试组件分组
                    </button>
                    <button 
                      class="btn btn-warning btn-sm" 
                      @click="forceRefresh"
                    >
                      强制刷新
                    </button>
                  </div>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">预期结果</label>
                  <div class="small">
                    <div v-if="selectedFormType === '安全测评'">应显示：testing组件</div>
                    <div v-else-if="selectedFormType === '安全监测'">应显示：security组件</div>
                    <div v-else-if="selectedFormType === '应用加固'">应显示：hardening组件</div>
                    <div v-else class="text-muted">请选择表单类型</div>
                  </div>
                </div>
              </div>

              <!-- 调试信息 -->
              <div v-if="debugInfo" class="row mb-4">
                <div class="col-12">
                  <div class="card bg-light">
                    <div class="card-header">
                      <h6 class="mb-0">调试信息</h6>
                    </div>
                    <div class="card-body">
                      <pre class="small">{{ debugInfo }}</pre>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 实际组件测试 -->
              <div v-if="selectedFormType" class="row">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">{{ selectedFormType }} - 组件过滤测试</h6>
                    </div>
                    <div class="card-body">
                      
                      <!-- 使用ServerInfoSection测试 -->
                      <div class="mb-4">
                        <h6 class="text-primary">使用ServerInfoSection组件</h6>
                        <server-info-section
                          v-model="testServerList"
                          :document-type="selectedFormType"
                          :component-groups="componentGroups"
                          :key="`server-info-${selectedFormType}-${refreshKey}`"
                        />
                      </div>

                      <!-- 直接使用ServerComponentSelector测试 -->
                      <div class="mb-4">
                        <h6 class="text-success">直接使用ServerComponentSelector组件</h6>
                        <server-component-selector
                          :server-info="testServerInfo"
                          :index="0"
                          :document-type="selectedFormType"
                          :component-groups="componentGroups"
                          :key="`component-selector-${selectedFormType}-${refreshKey}`"
                        />
                      </div>

                      <!-- 组件分组对比 -->
                      <div class="row">
                        <div class="col-md-6">
                          <h6 class="text-info">原始组件分组</h6>
                          <div class="small">
                            <div v-for="(groups, formType) in componentGroups" :key="formType" class="mb-3">
                              <strong>{{ formType }}:</strong>
                              <div class="ms-2">
                                <div v-for="(components, groupKey) in groups" :key="groupKey" class="mb-1">
                                  <span class="badge bg-secondary me-1">{{ groupKey }}</span>
                                  <span class="text-muted">({{ components.length }} 个组件)</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <h6 class="text-warning">当前表单类型的组件</h6>
                          <div class="small">
                            <div v-if="currentFormTypeComponents">
                              <div v-for="(components, groupKey) in currentFormTypeComponents" :key="groupKey" class="mb-2">
                                <strong>{{ groupKey }}</strong>
                                <div class="ms-2">
                                  <div v-for="component in components" :key="component.name" class="mb-1">
                                    <span class="badge bg-light text-dark">{{ component.name }}</span>
                                    <small class="text-muted ms-1">{{ component.description || '无描述' }}</small>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div v-else class="text-danger">
                              ❌ 当前表单类型没有组件数据！
                            </div>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ServerInfoSection from '@/components/forms/common/ServerInfoSection.vue'
import ServerComponentSelector from '@/components/forms/common/ServerComponentSelector.vue'
import { createNewServerItem } from '@/config/formDataConfig'

export default {
  name: 'ComponentFilterTest',
  components: {
    ServerInfoSection,
    ServerComponentSelector
  },
  data() {
    return {
      selectedFormType: '',
      refreshKey: 0,
      debugInfo: '',
      testServerList: [],
      testServerInfo: {},
      componentGroups: {},
      currentFormTypeComponents: null
    }
  },
  methods: {
    /**
     * 加载表单类型
     */
    loadFormType() {
      if (!this.selectedFormType) return

      console.log(`加载表单类型: ${this.selectedFormType}`)

      // 创建测试服务器数据
      this.testServerList = [createNewServerItem()]
      this.testServerInfo = createNewServerItem()

      // 获取组件分组数据（这里需要从实际的数据源获取）
      this.loadComponentGroups()

      // 强制刷新组件
      this.refreshKey += 1

      console.log('表单类型加载完成:', {
        formType: this.selectedFormType,
        serverList: this.testServerList,
        componentGroups: this.componentGroups
      })
    },

    /**
     * 加载组件分组数据
     */
    async loadComponentGroups() {
      try {
        // 尝试从实际的数据源加载组件分组
        const { getComponentGroupsFromDatabase } = await import('@/config/formDataConfig')
        this.componentGroups = await getComponentGroupsFromDatabase()
        console.log('从数据库加载的组件分组:', this.componentGroups)
      } catch (error) {
        console.warn('从数据库加载组件分组失败，使用模拟数据:', error)

        // 使用模拟数据作为后备
        this.componentGroups = {
          testing: {
            'aimrsk-dependence': [
              { name: 'adbd', description: 'Android Debug Bridge' },
              { name: 'tp-mongo', description: 'MongoDB数据库' },
              { name: 'tp-redis', description: 'Redis缓存' }
            ],
            'aimrsk-engine': [
              { name: 'luna', description: 'Luna升级组件' },
              { name: 'tp-android-static-engine', description: 'Android静态检测引擎' },
              { name: 'tp-ios-engine', description: 'iOS检测引擎' }
            ],
            'aimrsk-web': [
              { name: 'backend-ssp-admin', description: '后端管理服务' },
              { name: 'front-ssp-admin', description: '前端管理界面' }
            ]
          },
          security: {
            'security-monitor': [
              { name: 'monitor-agent', description: '监控代理' },
              { name: 'log-collector', description: '日志收集器' }
            ],
            'security-analysis': [
              { name: 'threat-detector', description: '威胁检测器' },
              { name: 'behavior-analyzer', description: '行为分析器' }
            ]
          },
          hardening: {
            'hardening-platform': [
              { name: 'secweb', description: '安全Web平台' },
              { name: 'hardening-engine', description: '加固引擎' }
            ],
            'hardening-tools': [
              { name: 'code-protector', description: '代码保护工具' },
              { name: 'resource-encryptor', description: '资源加密工具' }
            ]
          }
        }
      }

      // 获取当前表单类型的组件
      this.updateCurrentFormTypeComponents()
    },

    /**
     * 更新当前表单类型的组件
     */
    updateCurrentFormTypeComponents() {
      const formTypeMapping = {
        '安全监测': 'security',
        '安全测评': 'testing',
        '应用加固': 'hardening'
      }

      const formTypeKey = formTypeMapping[this.selectedFormType]
      this.currentFormTypeComponents = this.componentGroups[formTypeKey] || null

      console.log('当前表单类型组件:', {
        selectedFormType: this.selectedFormType,
        formTypeKey,
        components: this.currentFormTypeComponents
      })
    },

    /**
     * 调试组件分组
     */
    debugComponentGroups() {
      console.log('开始调试组件分组...')
      
      const formTypeMapping = {
        '安全监测': 'security',
        '安全测评': 'testing',
        '应用加固': 'hardening'
      }

      const currentDebug = {
        selectedFormType: this.selectedFormType,
        formTypeKey: formTypeMapping[this.selectedFormType],
        allComponentGroups: Object.keys(this.componentGroups),
        currentFormTypeComponents: this.currentFormTypeComponents,
        hasCurrentFormTypeComponents: !!this.currentFormTypeComponents,
        componentGroupsStructure: Object.keys(this.componentGroups).reduce((acc, key) => {
          acc[key] = Object.keys(this.componentGroups[key])
          return acc
        }, {})
      }

      this.debugInfo = JSON.stringify(currentDebug, null, 2)
      
      console.log('调试信息:', currentDebug)
    },

    /**
     * 强制刷新
     */
    forceRefresh() {
      this.refreshKey += 1
      this.loadFormType()
      console.log('强制刷新完成, refreshKey:', this.refreshKey)
    }
  },
  mounted() {
    console.log('ComponentFilterTest 组件已挂载')
  }
}
</script>

<style scoped>
.component-filter-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 2px solid #e9ecef;
}

pre {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #0dcaf0 !important; }
.text-danger { color: #dc3545 !important; }
</style>
