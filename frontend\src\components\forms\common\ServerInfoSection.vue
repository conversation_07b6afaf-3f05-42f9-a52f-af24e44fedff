<template>
  <collapsible-card card-class="border-success" storage-key="server-info-section">
    <template #header>
      <span>
        <i class="bi bi-server me-2"></i>服务器信息
      </span>
      <!-- 右上角切换按钮 -->
      <div class="server-view-controls-header" @click.stop>
        <div class="btn-group btn-group-sm" role="group">
          <input
            id="server-view-card-header"
            :checked="viewMode === 'card'"
            type="radio"
            value="card"
            class="btn-check"
            @change.stop="$emit('update:view-mode', 'card')"
          />
          <label for="server-view-card-header" class="btn btn-outline-primary" @click.stop>
            <i class="bi bi-card-list me-1"></i>卡片
          </label>

          <input
            id="server-view-table-header"
            :checked="viewMode === 'table'"
            type="radio"
            value="table"
            class="btn-check"
            @change.stop="$emit('update:view-mode', 'table')"
          />
          <label for="server-view-table-header" class="btn btn-outline-primary" @click.stop>
            <i class="bi bi-table me-1"></i>表格
          </label>
        </div>
      </div>
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-success">服务器数量: {{ serverList.length }}</span>
        <span v-for="(item, index) in serverList" :key="'server-summary-' + index" class="badge bg-light text-dark border">
          {{ item.用途类型 === '自定义' ? item.用途 : item.用途类型 }}
          {{ item.IP地址 ? '(内网:' + item.IP地址 + (item.外网IP地址 ? ', 外网:' + item.外网IP地址 : '') + ')' : '' }}
        </span>
      </div>
    </template>

    <div v-if="serverList.length === 0" class="text-center py-3 text-muted">
      <i class="bi bi-info-circle me-2"></i>暂无服务器信息
    </div>

    <server-info-item
      v-for="(item, index) in serverList"
      :key="'server-' + index"
      :server-info="item"
      :index="index"
      :document-type="documentType"
      :component-groups="componentGroups"
      @remove="removeServerItem(index)"
      @architecture-changed="onArchitectureChanged"
      @refresh-components="$emit('refresh-components')"
      @show-toast="$emit('show-toast', $event.message, $event.title, $event.type)"
    />

    <!-- 添加按钮 -->
    <div class="add-button-section">
      <button type="button" class="btn btn-add-server" @click="addServerItem">
        <i class="bi bi-plus-circle me-2"></i>
        <span>添加服务器</span>
      </button>
    </div>
  </collapsible-card>
</template>

<script>
import ServerInfoItem from './ServerInfoItem.vue'
import CollapsibleCard from './CollapsibleCard.vue'
import { saveFormDataToLocalStorage, loadFormDataFromLocalStorage } from '@/utils/fillSheetData'
import { createNewServerItem } from '@/config/formDataConfig'

/**
 * 服务器信息部分组件
 * 用于管理多个服务器信息项
 */
export default {
  name: 'ServerInfoSection',
  components: {
    ServerInfoItem,
    CollapsibleCard
  },
  props: {
    // 服务器信息列表
    modelValue: {
      type: Array,
      default: () => []
    },
    // 文档类型
    documentType: {
      type: String,
      default: '安全测评'
    },
    // 组件分组
    componentGroups: {
      type: Object,
      required: true
    },
    // 显示模式
    viewMode: {
      type: String,
      default: 'card'
    }
  },
  emits: ['update:modelValue', 'architecture-changed', 'refresh-components', 'show-toast', 'update:view-mode'],
  computed: {
    /**
     * 服务器列表
     * 用于双向绑定
     */
    serverList: {
      get() {
        return this.modelValue
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue)
      }
    }
  },
  methods: {
    /**
     * 添加服务器信息项
     * 向服务器信息数组添加新项，并自动折叠之前的服务器
     */
    addServerItem() {
      const newServerList = [...this.serverList]

      // 折叠所有现有的服务器信息，让用户专注于新添加的服务器
      newServerList.forEach(server => {
        server.折叠 = true
        server.组件折叠 = true
      })

      // 使用配置文件中的函数创建新的服务器信息项
      const newServer = createNewServerItem()
      // 新添加的服务器默认展开，方便用户立即编辑
      newServer.折叠 = false
      // 组件选择器使用配置文件中的默认设置（现在是展开状态）

      // 如果是安全监测表单，添加bangcle用户
      if (this.documentType === '安全监测') {
        const hasBangcle = newServer.运维用户.some(user => user.用户名 === 'bangcle')
        if (!hasBangcle) {
          newServer.运维用户.push({
            用户名: 'bangcle',
            密码: 'beap123',
            showPassword: false,
            isDefault: true
          })
        }
      }

      newServerList.push(newServer)
      this.serverList = newServerList

      // 可选：滚动到新添加的服务器位置
      this.$nextTick(() => {
        const newServerIndex = newServerList.length - 1
        const newServerElement = document.querySelector(`[data-server-index="${newServerIndex}"]`)
        if (newServerElement) {
          newServerElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          })
        }
      })
    },

    /**
     * 删除服务器信息项
     * @param {Number} index - 要删除的项的索引
     */
    removeServerItem(index) {
      const newServerList = [...this.serverList]
      newServerList.splice(index, 1)
      this.serverList = newServerList
    },

    /**
     * 保存服务器信息配置到本地存储
     */
    saveServerInfo() {
      if (this.serverList.length === 0) {
        alert('没有服务器信息可保存')
        return
      }

      // 保存服务器信息到本地存储
      const saved = saveFormDataToLocalStorage(this.serverList, 'serverInfoConfig')

      if (saved) {
        alert('服务器配置已保存')

        // 折叠所有服务器信息，包括组件选择区域
        const newServerList = this.serverList.map(server => ({
          ...server,
          折叠: true,
          组件折叠: true  // 折叠组件选择区域，显示摘要便于点击修改
        }))
        this.serverList = newServerList
      } else {
        alert('保存失败，请重试')
      }
    },

    /**
     * 从本地存储加载服务器信息配置
     */
    loadServerInfo() {
      // 从本地存储加载服务器信息
      const savedData = loadFormDataFromLocalStorage('serverInfoConfig')

      if (savedData && Array.isArray(savedData) && savedData.length > 0) {
        // 确认是否加载
        if (confirm('确定要加载保存的服务器配置吗？当前配置将被覆盖。')) {
          this.serverList = savedData
          alert('服务器配置已加载')
        }
      } else {
        alert('没有找到保存的服务器配置')
      }
    },

    /**
     * 处理服务器架构变化
     * 向父组件传递架构变化事件
     * @param {Object} event - 架构变化事件对象
     */
    onArchitectureChanged(event) {
      this.$emit('architecture-changed', event)
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

button.btn {
  transition: all 0.2s ease;
}

button.btn:hover {
  transform: translateY(-1px);
}

/* 添加按钮样式 */
.add-button-section {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  margin-top: 1rem;
}

.btn-add-server {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
  display: inline-flex;
  align-items: center;
}

.btn-add-server:hover {
  background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
}

.btn-add-server:active {
  transform: translateY(0);
}
</style>
