<template>
  <span :class="statusClasses" class="status-indicator">
    <i v-if="icon" :class="icon" class="me-1"></i>
    <span>{{ text }}</span>
    <i v-if="pulse" class="pulse-dot ms-2"></i>
  </span>
</template>

<script>
export default {
  name: 'StatusIndicator',
  props: {
    status: {
      type: String,
      required: true,
      validator: value => ['success', 'warning', 'danger', 'info', 'pending', 'processing'].includes(value)
    },
    text: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      default: ''
    },
    pulse: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'md',
      validator: value => ['sm', 'md', 'lg'].includes(value)
    }
  },
  computed: {
    statusClasses() {
      return [
        `status-${this.status}`,
        `status-${this.size}`,
        {
          'status-pulse': this.pulse
        }
      ]
    }
  }
}
</script>

<style scoped>
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  transition: all var(--transition-base);
}

/* 尺寸变体 */
.status-sm {
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
}

.status-lg {
  padding: 0.375rem 1rem;
  font-size: 1rem;
}

/* 状态颜色 */
.status-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-warning {
  background-color: rgba(255, 193, 7, 0.1);
  color: #b45309;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-info {
  background-color: rgba(23, 162, 184, 0.1);
  color: var(--info-color);
  border: 1px solid rgba(23, 162, 184, 0.2);
}

.status-pending {
  background-color: rgba(108, 117, 125, 0.1);
  color: var(--gray-600);
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.status-processing {
  background-color: rgba(0, 123, 255, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(0, 123, 255, 0.2);
}

/* 脉冲效果 */
.pulse-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 currentColor;
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px transparent;
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 transparent;
  }
}

/* 悬停效果 */
.status-indicator:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-sm);
}

/* 特殊状态动画 */
.status-processing {
  position: relative;
  overflow: hidden;
}

.status-processing::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>
