<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-xl">
      <div class="modal-content shadow-lg border-0">
        <div class="modal-header bg-gradient-info text-white border-0">
          <h5 class="modal-title fw-bold">
            <i class="bi bi-people-fill me-2"></i>
            管理用户组成员 - {{ group.name }}
          </h5>
          <button type="button" class="btn-close btn-close-white" @click="$emit('close')"></button>
        </div>
        <div class="modal-body p-4">
          <!-- 用户组信息 -->
          <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
              <div class="row">
                <div class="col-md-8">
                  <h6 class="text-primary mb-2">
                    <i class="bi bi-info-circle me-1"></i>用户组信息
                  </h6>
                  <p class="mb-1"><strong>名称：</strong>{{ group.name }}</p>
                  <p class="mb-1"><strong>代码：</strong><code>{{ group.code }}</code></p>
                  <p class="mb-0"><strong>描述：</strong>{{ group.description || '无描述' }}</p>
                </div>
                <div class="col-md-4 text-end">
                  <div class="d-flex flex-column align-items-end">
                    <span class="badge bg-primary fs-6 mb-2">{{ currentMembers.length }} 个成员</span>
                    <span :class="group.is_active ? 'badge bg-success' : 'badge bg-secondary'">
                      {{ group.is_active ? '已启用' : '已禁用' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 成员管理 -->
          <div class="member-transfer-container">
            <div class="row g-4">
              <!-- 可添加用户 -->
              <div class="col-5">
                <div class="transfer-panel">
                  <div class="panel-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                      <span class="fw-semibold text-muted">可添加用户</span>
                      <span class="badge bg-secondary">{{ availableUsers.length }}</span>
                    </div>
                  </div>
                  <div class="panel-body">
                    <!-- 搜索框 -->
                    <div class="mb-3">
                      <div class="input-group input-group-sm">
                        <span class="input-group-text bg-light border-end-0">
                          <i class="bi bi-search text-muted"></i>
                        </span>
                        <input 
                          type="text" 
                          class="form-control border-start-0 ps-0" 
                          placeholder="搜索用户..."
                          v-model="searchQuery"
                        >
                      </div>
                    </div>
                    
                    <div v-if="filteredAvailableUsers.length === 0" class="text-muted text-center py-4">
                      <i class="bi bi-person-x fs-1 d-block mb-2"></i>
                      <div>{{ searchQuery ? '未找到匹配用户' : '暂无可添加用户' }}</div>
                    </div>
                    <div v-else class="user-list">
                      <div 
                        v-for="user in filteredAvailableUsers" 
                        :key="user.id" 
                        class="user-item"
                        @click="addUser(user)"
                      >
                        <div class="d-flex align-items-center">
                          <div class="user-avatar me-3">
                            <i class="bi bi-person-circle fs-4 text-primary"></i>
                          </div>
                          <div class="flex-grow-1">
                            <div class="fw-semibold">{{ user.real_name || user.username }}</div>
                            <small class="text-muted">{{ user.email }}</small>
                            <div class="mt-1">
                              <span v-if="user.department" class="badge bg-light text-dark me-1">{{ user.department }}</span>
                              <span v-if="user.position" class="badge bg-light text-dark">{{ user.position }}</span>
                            </div>
                          </div>
                          <div class="user-actions">
                            <span :class="user.is_active ? 'badge bg-success' : 'badge bg-secondary'">
                              {{ user.is_active ? '活跃' : '禁用' }}
                            </span>
                            <i class="bi bi-plus-circle text-primary ms-2"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="col-2 d-flex flex-column justify-content-center align-items-center">
                <button 
                  type="button" 
                  class="btn btn-outline-primary btn-sm mb-3"
                  @click="addAllUsers"
                  :disabled="filteredAvailableUsers.length === 0"
                  title="添加全部"
                >
                  <i class="bi bi-chevron-double-right"></i>
                </button>
                <button 
                  type="button" 
                  class="btn btn-outline-secondary btn-sm"
                  @click="removeAllUsers"
                  :disabled="currentMembers.length === 0"
                  title="移除全部"
                >
                  <i class="bi bi-chevron-double-left"></i>
                </button>
              </div>
              
              <!-- 当前成员 -->
              <div class="col-5">
                <div class="transfer-panel">
                  <div class="panel-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                      <span class="fw-semibold text-muted">当前成员</span>
                      <span class="badge bg-primary">{{ currentMembers.length }}</span>
                    </div>
                  </div>
                  <div class="panel-body">
                    <div v-if="currentMembers.length === 0" class="text-muted text-center py-4">
                      <i class="bi bi-people fs-1 d-block mb-2"></i>
                      <div>暂无成员</div>
                    </div>
                    <div v-else class="user-list">
                      <div 
                        v-for="user in currentMembers" 
                        :key="user.id" 
                        class="user-item member"
                        @click="removeUser(user)"
                      >
                        <div class="d-flex align-items-center">
                          <div class="user-avatar me-3">
                            <i class="bi bi-person-check-fill fs-4 text-success"></i>
                          </div>
                          <div class="flex-grow-1">
                            <div class="fw-semibold">{{ user.real_name || user.username }}</div>
                            <small class="text-muted">{{ user.email }}</small>
                            <div class="mt-1">
                              <span v-if="user.department" class="badge bg-light text-dark me-1">{{ user.department }}</span>
                              <span v-if="user.position" class="badge bg-light text-dark">{{ user.position }}</span>
                            </div>
                          </div>
                          <div class="user-actions">
                            <span v-if="user.is_admin" class="badge bg-warning text-dark me-2">管理员</span>
                            <span :class="user.is_active ? 'badge bg-success' : 'badge bg-secondary'">
                              {{ user.is_active ? '活跃' : '禁用' }}
                            </span>
                            <i class="bi bi-dash-circle text-danger ms-2"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer bg-light border-0 p-4">
          <button type="button" class="btn btn-outline-secondary px-4" @click="$emit('close')">
            <i class="bi bi-x-circle me-1"></i>关闭
          </button>
          <button type="button" class="btn btn-primary px-4" @click="saveMembers" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-2"></span>
            <i v-else class="bi bi-check-circle me-1"></i>
            {{ saving ? '保存中...' : '保存更改' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GroupMembersModal',
  props: {
    group: {
      type: Object,
      required: true
    },
    allUsers: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'save'],
  data() {
    return {
      currentMembers: [],
      searchQuery: '',
      saving: false
    }
  },
  computed: {
    availableUsers() {
      return this.allUsers.filter(user => 
        !this.currentMembers.some(member => member.id === user.id)
      )
    },
    
    filteredAvailableUsers() {
      if (!this.searchQuery) {
        return this.availableUsers
      }
      
      const query = this.searchQuery.toLowerCase()
      return this.availableUsers.filter(user => 
        user.username.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query) ||
        (user.real_name && user.real_name.toLowerCase().includes(query)) ||
        (user.department && user.department.toLowerCase().includes(query)) ||
        (user.position && user.position.toLowerCase().includes(query))
      )
    }
  },
  mounted() {
    this.initMembers()
  },
  methods: {
    initMembers() {
      // 初始化当前成员列表
      this.currentMembers = this.group.users ? [...this.group.users] : []
    },
    
    addUser(user) {
      if (!this.currentMembers.some(member => member.id === user.id)) {
        this.currentMembers.push(user)
      }
    },
    
    removeUser(user) {
      const index = this.currentMembers.findIndex(member => member.id === user.id)
      if (index > -1) {
        this.currentMembers.splice(index, 1)
      }
    },
    
    addAllUsers() {
      this.filteredAvailableUsers.forEach(user => {
        if (!this.currentMembers.some(member => member.id === user.id)) {
          this.currentMembers.push(user)
        }
      })
    },
    
    removeAllUsers() {
      this.currentMembers = []
    },
    
    async saveMembers() {
      this.saving = true
      try {
        const memberIds = this.currentMembers.map(user => user.id)
        this.$emit('save', {
          groupId: this.group.id,
          memberIds: memberIds,
          members: this.currentMembers
        })
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.bg-gradient-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.transfer-panel {
  border: 1px solid #dee2e6;
  border-radius: 12px;
  background: white;
  height: 500px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.panel-header {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 12px 12px 0 0;
}

.panel-body {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.user-list {
  max-height: 100%;
}

.user-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-item:hover {
  border-color: #007bff;
  background: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.user-item.member {
  background: #e7f3ff;
  border-color: #007bff;
}

.user-item.member:hover {
  background: #d1ecf1;
  border-color: #0056b3;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-actions {
  display: flex;
  align-items: center;
}

.user-actions i {
  font-size: 1.1em;
  transition: all 0.2s ease;
}

.user-item:hover .user-actions i {
  transform: scale(1.1);
}

.panel-body::-webkit-scrollbar {
  width: 6px;
}

.panel-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-content {
  border-radius: 15px;
  overflow: hidden;
}

@media (max-width: 768px) {
  .modal-dialog {
    margin: 0.5rem;
  }
  
  .transfer-panel {
    height: 400px;
  }
}
</style>
