<template>
  <div class="date-field-component">
    <input 
      :id="id"
      :type="inputType"
      :value="value"
      :readonly="field.is_readonly"
      :required="field.is_required"
      :class="fieldClasses"
      @input="handleInput"
      @change="handleChange"
    >
  </div>
</template>

<script>
export default {
  name: 'DateFieldComponent',
  props: {
    id: String,
    field: {
      type: Object,
      required: true
    },
    value: {
      type: String,
      default: ''
    }
  },
  emits: ['update:value', 'field-change'],
  computed: {
    inputType() {
      return this.field.field_type === 'datetime' ? 'datetime-local' : 'date'
    },
    fieldClasses() {
      let classes = ['form-control']
      
      if (this.field.css_classes) {
        classes.push(this.field.css_classes)
      }
      
      if (this.field.is_readonly) {
        classes.push('readonly-field')
      }
      
      return classes.join(' ')
    }
  },
  methods: {
    handleInput(event) {
      this.$emit('update:value', event.target.value)
    },
    
    handleChange(event) {
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: event.target.value,
        event: 'change'
      })
    }
  }
}
</script>

<style scoped>
.readonly-field {
  background-color: #f8f9fa;
  cursor: not-allowed;
}
</style>
