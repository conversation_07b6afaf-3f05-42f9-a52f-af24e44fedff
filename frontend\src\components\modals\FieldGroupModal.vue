<template>
  <!-- 字段分组编辑模态框 -->
  <div class="modal fade" id="fieldGroupModal" tabindex="-1" aria-labelledby="fieldGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="fieldGroupModalLabel">
            <i class="bi bi-folder me-2"></i>
            {{ isEditing ? '编辑字段分组' : '新建字段分组' }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveGroup">
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="groupName" class="form-label">分组名称 <span class="text-danger">*</span></label>
                  <input
                    type="text"
                    class="form-control"
                    id="groupName"
                    v-model="form.group_name"
                    :disabled="isEditing"
                    placeholder="例如: custom_info"
                    required
                  >
                  <div class="form-text">英文名称，用于系统内部标识</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="groupLabel" class="form-label">显示名称 <span class="text-danger">*</span></label>
                  <input
                    type="text"
                    class="form-control"
                    id="groupLabel"
                    v-model="form.group_label"
                    placeholder="例如: 自定义信息"
                    required
                  >
                  <div class="form-text">用户界面显示的名称</div>
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label for="groupDescription" class="form-label">分组描述</label>
              <textarea
                class="form-control"
                id="groupDescription"
                v-model="form.group_description"
                rows="3"
                placeholder="描述这个分组的用途..."
              ></textarea>
            </div>

            <div class="row">
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="displayOrder" class="form-label">显示顺序</label>
                  <input
                    type="number"
                    class="form-control"
                    id="displayOrder"
                    v-model.number="form.display_order"
                    min="0"
                    placeholder="自动分配"
                  >
                  <div class="form-text">数字越小越靠前，留空自动分配到最后</div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="mb-3">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="isCollapsible"
                      v-model="form.is_collapsible"
                    >
                    <label class="form-check-label" for="isCollapsible">
                      可折叠
                    </label>
                  </div>
                  <div class="form-text">是否支持折叠展开</div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="mb-3">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="isExpandedByDefault"
                      v-model="form.is_expanded_by_default"
                      :disabled="!form.is_collapsible"
                    >
                    <label class="form-check-label" for="isExpandedByDefault">
                      默认展开
                    </label>
                  </div>
                  <div class="form-text">初始状态是否展开</div>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button type="button" class="btn btn-primary" @click="saveGroup" :disabled="saving">
            <i class="bi bi-check-circle me-1"></i>
            <span v-if="saving">保存中...</span>
            <span v-else>{{ isEditing ? '更新' : '创建' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'FieldGroupModal',
  props: {
    formType: {
      type: String,
      required: true
    }
  },
  emits: ['saved'],
  data() {
    return {
      isEditing: false,
      saving: false,
      form: {
        group_name: '',
        group_label: '',
        group_description: '',
        display_order: 0,
        is_collapsible: true,
        is_expanded_by_default: true
      }
    }
  },
  methods: {
    show(group = null) {
      this.isEditing = !!group

      if (group) {
        // 编辑模式
        this.form = {
          id: group.id,
          group_name: group.group_name,
          group_label: group.group_label,
          group_description: group.group_description || '',
          display_order: group.display_order || 0,
          is_collapsible: group.is_collapsible,
          is_expanded_by_default: group.is_expanded_by_default
        }
      } else {
        // 新建模式
        this.form = {
          group_name: '',
          group_label: '',
          group_description: '',
          display_order: 0,
          is_collapsible: true,
          is_expanded_by_default: true
        }
      }

      // 使用强力遮罩清理器
      if (window.backdropKiller) {
        window.backdropKiller.kill()
        setTimeout(() => {
          window.backdropKiller.safeShow('fieldGroupModal', {
            backdrop: false,
            keyboard: true
          })
        }, 100)
      } else {
        // 备用方案
        this.cleanupModal()
        const modalElement = document.getElementById('fieldGroupModal')
        if (modalElement) {
          setTimeout(() => {
            modalElement.style.display = 'block'
            modalElement.classList.add('show')
            modalElement.setAttribute('aria-modal', 'true')
            modalElement.removeAttribute('aria-hidden')
            document.body.classList.add('modal-open')
          }, 100)
        }
      }
    },

    cleanupModal() {
      try {
        // 清理可能存在的模态框实例
        const modalElement = document.getElementById('fieldGroupModal')
        if (modalElement) {
          const existingModal = window.bootstrap.Modal.getInstance(modalElement)
          if (existingModal) {
            existingModal.dispose()
          }
        }

        // 清理遮罩层
        const backdrops = document.querySelectorAll('.modal-backdrop')
        backdrops.forEach(backdrop => {
          if (backdrop.parentNode) {
            backdrop.parentNode.removeChild(backdrop)
          }
        })

        // 重置body样式
        document.body.classList.remove('modal-open')
        document.body.style.overflow = ''
        document.body.style.paddingRight = ''
      } catch (error) {
        console.warn('清理字段分组模态框时出错:', error)
      }
    },

    async saveGroup() {
      if (!this.form.group_name || !this.form.group_label) {
        alert('请填写必填字段')
        return
      }

      this.saving = true
      
      try {
        const data = {
          ...this.form,
          form_type: this.formType
        }

        let response
        if (this.isEditing) {
          response = await axios.put(`/excel/form-field-groups/${this.form.id}`, data)
        } else {
          response = await axios.post('/excel/form-field-groups', data)
        }

        if (response.data.status === 'success') {
          // 关闭模态框
          const modal = window.bootstrap.Modal.getInstance(document.getElementById('fieldGroupModal'))
          modal.hide()
          
          // 触发保存事件
          this.$emit('saved', response.data.data)
          
          // 显示成功消息
          this.$emit('show-toast', 
            this.isEditing ? '字段分组更新成功' : '字段分组创建成功', 
            '成功', 
            'success'
          )
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        console.error('保存字段分组失败:', error)
        this.$emit('show-toast', 
          `保存失败: ${error.response && error.response.data && error.response.data.message || error.message}`,
          '错误', 
          'error'
        )
      } finally {
        this.saving = false
      }
    }
  },

  beforeUnmount() {
    // 组件销毁前清理模态框
    this.cleanupModal()
  }
}
</script>

<style scoped>
/* 确保模态框层级正确 */
:deep(.modal) {
  z-index: 1060 !important;
}

:deep(.modal-backdrop) {
  z-index: 1055 !important;
}

/* 修复模态框显示问题 */
:deep(.modal.show) {
  display: block !important;
}

:deep(.modal-dialog) {
  margin: 1.75rem auto;
  pointer-events: auto;
}

:deep(.modal-content) {
  pointer-events: auto;
}
</style>

<style scoped>
.form-check-input:disabled + .form-check-label {
  opacity: 0.5;
}

.form-text {
  font-size: 0.875rem;
  color: #6c757d;
}

.text-danger {
  color: #dc3545 !important;
}
</style>
