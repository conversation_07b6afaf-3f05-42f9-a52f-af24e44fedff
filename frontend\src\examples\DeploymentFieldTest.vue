<template>
  <div class="deployment-field-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-gear me-2"></i>
                部署字段显示测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 表单类型选择 -->
              <div class="row mb-4">
                <div class="col-md-4">
                  <label class="form-label fw-bold">选择表单类型</label>
                  <select v-model="selectedFormType" class="form-select" @change="loadFormType">
                    <option value="">请选择表单类型</option>
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">操作</label>
                  <div class="d-flex gap-2">
                    <button 
                      class="btn btn-primary btn-sm" 
                      @click="debugDeploymentFields"
                      :disabled="!selectedFormType"
                    >
                      调试部署字段
                    </button>
                    <button 
                      class="btn btn-warning btn-sm" 
                      @click="forceRefresh"
                    >
                      强制刷新
                    </button>
                  </div>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">预期结果</label>
                  <div class="small">
                    <div v-if="selectedFormType === '安全测评'">应显示：部署包版本</div>
                    <div v-else-if="selectedFormType === '安全监测'">应显示：前端版本、后端版本、标准/定制</div>
                    <div v-else-if="selectedFormType === '应用加固'">应显示：部署的平台版本</div>
                    <div v-else class="text-muted">请选择表单类型</div>
                  </div>
                </div>
              </div>

              <!-- 调试信息 -->
              <div v-if="debugInfo" class="row mb-4">
                <div class="col-12">
                  <div class="card bg-light">
                    <div class="card-header">
                      <h6 class="mb-0">调试信息</h6>
                    </div>
                    <div class="card-body">
                      <pre class="small">{{ debugInfo }}</pre>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 实际表单测试 -->
              <div v-if="selectedFormType" class="row">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">{{ selectedFormType }} - 实际渲染测试</h6>
                    </div>
                    <div class="card-body">
                      
                      <!-- 使用BaseForm测试 -->
                      <div class="mb-4">
                        <h6 class="text-primary">使用BaseForm组件</h6>
                        <base-form
                          v-model="testFormData"
                          :form-type="selectedFormType"
                          :component-groups="componentGroups"
                          :key="`base-form-${selectedFormType}-${refreshKey}`"
                        />
                      </div>

                      <!-- 直接使用CommonBasicInfo测试 -->
                      <div class="mb-4">
                        <h6 class="text-success">直接使用CommonBasicInfo组件</h6>
                        <common-basic-info
                          v-model:company="testFormData.公司名称"
                          v-model:date="testFormData.记录日期"
                          v-model:editor="testFormData.编辑人"
                          v-model:field-values="basicInfoValues"
                          :basic-field-config="basicFieldConfig"
                          :customer-field-config="customerFieldConfig"
                          :deployment-field-config="deploymentFieldConfig"
                          :form-type="selectedFormType"
                          :key="`common-basic-${selectedFormType}-${refreshKey}`"
                        />
                      </div>

                      <!-- 字段配置对比 -->
                      <div class="row">
                        <div class="col-md-6">
                          <h6 class="text-info">原始部署字段配置</h6>
                          <div class="small">
                            <div v-for="(field, index) in rawDeploymentFields" :key="field.id" class="mb-2">
                              <strong>{{ field.id }}</strong>
                              <div class="text-muted">
                                formTypes: {{ field.formTypes ? field.formTypes.join(', ') : '无限制' }}
                              </div>
                              <div class="text-muted">
                                字段: {{ field.fields?.map(f => f.label).join(', ') }}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <h6 class="text-warning">过滤后的部署字段</h6>
                          <div class="small">
                            <div v-for="(field, index) in deploymentFieldConfig" :key="field.id" class="mb-2">
                              <strong>{{ field.id }}</strong>
                              <div class="text-success">
                                字段: {{ field.fields?.map(f => f.label).join(', ') }}
                              </div>
                            </div>
                            <div v-if="deploymentFieldConfig.length === 0" class="text-danger">
                              ❌ 没有匹配的部署字段！
                            </div>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseForm from '@/components/forms/common/BaseForm.vue'
import CommonBasicInfo from '@/components/forms/common/CommonBasicInfo.vue'
import {
  getBasicInfoFieldsByFormType,
  getCustomerInfoFieldsByFormType,
  getDeploymentInfoFieldsByFormType,
  deploymentInfoFields
} from '@/config/formFields'
import { getInitialFormData } from '@/config/formDataConfig'
import { quickDebugAllFormTypes } from '@/utils/formFieldDebug'

export default {
  name: 'DeploymentFieldTest',
  components: {
    BaseForm,
    CommonBasicInfo
  },
  data() {
    return {
      selectedFormType: '',
      refreshKey: 0,
      debugInfo: '',
      testFormData: {},
      basicInfoValues: {},
      basicFieldConfig: [],
      customerFieldConfig: [],
      deploymentFieldConfig: [],
      rawDeploymentFields: deploymentInfoFields,
      componentGroups: {}
    }
  },
  methods: {
    /**
     * 加载表单类型
     */
    loadFormType() {
      if (!this.selectedFormType) return

      console.log(`加载表单类型: ${this.selectedFormType}`)

      // 获取字段配置
      this.basicFieldConfig = getBasicInfoFieldsByFormType(this.selectedFormType)
      this.customerFieldConfig = getCustomerInfoFieldsByFormType(this.selectedFormType)
      this.deploymentFieldConfig = getDeploymentInfoFieldsByFormType(this.selectedFormType)

      // 获取初始表单数据
      this.testFormData = getInitialFormData(this.selectedFormType)

      // 设置基本信息值
      this.updateBasicInfoValues()

      // 强制刷新组件
      this.refreshKey += 1

      console.log('字段配置加载完成:', {
        basicFields: this.basicFieldConfig.length,
        customerFields: this.customerFieldConfig.length,
        deploymentFields: this.deploymentFieldConfig.length
      })
    },

    /**
     * 更新基本信息值
     */
    updateBasicInfoValues() {
      const values = {}

      if (this.selectedFormType === '安全测评') {
        values.customerId = this.testFormData.客户标识 || ''
        values.deploymentVersion = this.testFormData.部署包版本 || ''
      } else if (this.selectedFormType === '安全监测') {
        values.customerId = this.testFormData.客户标识 || ''
        values.dailyActive = this.testFormData.日活 || ''
        values.frontendVersion = this.testFormData.前端版本 || ''
        values.backendVersion = this.testFormData.后端版本 || ''
        values.standardOrCustom = this.testFormData.标准或定制 || '标准版'
      } else if (this.selectedFormType === '应用加固') {
        values.customerId = this.testFormData.客户标识 || ''
        values.customer = this.testFormData.客户 || ''
        values.platformVersion = this.testFormData.部署的平台版本 || ''
      }

      this.basicInfoValues = values
    },

    /**
     * 调试部署字段
     */
    debugDeploymentFields() {
      console.log('开始调试部署字段...')
      
      const debugReport = quickDebugAllFormTypes()
      
      const currentDebug = {
        selectedFormType: this.selectedFormType,
        rawDeploymentFieldsCount: this.rawDeploymentFields.length,
        filteredDeploymentFieldsCount: this.deploymentFieldConfig.length,
        rawFields: this.rawDeploymentFields.map(field => ({
          id: field.id,
          formTypes: field.formTypes,
          shouldShow: !field.formTypes || field.formTypes.includes(this.selectedFormType),
          fields: field.fields?.map(f => f.label)
        })),
        filteredFields: this.deploymentFieldConfig.map(field => ({
          id: field.id,
          fields: field.fields?.map(f => f.label)
        }))
      }

      this.debugInfo = JSON.stringify(currentDebug, null, 2)
      
      console.log('调试信息:', currentDebug)
      console.log('完整调试报告:', debugReport)
    },

    /**
     * 强制刷新
     */
    forceRefresh() {
      this.refreshKey += 1
      this.loadFormType()
      console.log('强制刷新完成, refreshKey:', this.refreshKey)
    }
  },
  mounted() {
    console.log('DeploymentFieldTest 组件已挂载')
    console.log('原始部署字段配置:', this.rawDeploymentFields)
  }
}
</script>

<style scoped>
.deployment-field-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 2px solid #e9ecef;
}

pre {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #0dcaf0 !important; }
.text-danger { color: #dc3545 !important; }
</style>
