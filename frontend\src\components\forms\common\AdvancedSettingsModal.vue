<template>
  <div class="modal-backdrop fade show" @click="$emit('close')"></div>
  <div class="modal-container" :style="modalStyle">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-gear-fill me-2"></i>
            服务器 #{{ serverIndex + 1 }} - 高级设置
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <!-- SSH配置 -->
          <div class="mb-4">
            <h6 class="mb-3">
              <i class="bi bi-terminal me-2"></i>SSH配置
            </h6>
            <div class="row">
              <div class="col-md-6">
                <label class="form-label">SSH端口</label>
                <input 
                  v-model="localData.SSH端口" 
                  type="text" 
                  class="form-control" 
                  placeholder="22"
                />
              </div>
            </div>
          </div>

          <!-- 运维用户配置 -->
          <div class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h6 class="mb-0">
                <i class="bi bi-people me-2"></i>运维用户
              </h6>
              <button type="button" class="btn btn-success btn-sm" @click="addUser">
                <i class="bi bi-plus-circle me-1"></i>添加用户
              </button>
            </div>
            
            <div class="user-list">
              <div v-for="(user, index) in localData.运维用户" :key="'user-' + index" class="user-item">
                <div class="row align-items-center">
                  <div class="col-md-4">
                    <label class="form-label">用户名</label>
                    <input 
                      v-model="user.用户名" 
                      type="text" 
                      class="form-control form-control-sm" 
                      placeholder="用户名"
                      :disabled="user.isDefault"
                    />
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">密码</label>
                    <div class="input-group input-group-sm">
                      <input 
                        v-model="user.密码" 
                        :type="user.showPassword ? 'text' : 'password'"
                        class="form-control" 
                        placeholder="密码"
                      />
                      <button 
                        type="button" 
                        class="btn btn-outline-secondary"
                        @click="togglePasswordVisibility(user)"
                      >
                        <i :class="user.showPassword ? 'bi-eye-slash' : 'bi-eye'"></i>
                      </button>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                      <button 
                        v-if="!user.isDefault"
                        type="button" 
                        class="btn btn-outline-danger btn-sm w-100"
                        @click="removeUser(index)"
                      >
                        <i class="bi bi-trash"></i>
                      </button>
                      <span v-else class="badge bg-primary w-100">默认</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 其他配置 -->
          <div class="mb-4">
            <h6 class="mb-3">
              <i class="bi bi-sliders me-2"></i>其他配置
            </h6>
            <div class="row">
              <div class="col-md-6">
                <label class="form-label">备注信息</label>
                <textarea 
                  v-model="localData.备注" 
                  class="form-control" 
                  rows="3" 
                  placeholder="可选的备注信息"
                ></textarea>
              </div>
              <div class="col-md-6">
                <label class="form-label">标签</label>
                <input 
                  v-model="localData.标签" 
                  type="text" 
                  class="form-control" 
                  placeholder="用逗号分隔多个标签"
                />
                <small class="form-text text-muted">例如：生产环境,核心服务器</small>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            取消
          </button>
          <button type="button" class="btn btn-primary" @click="saveSettings">
            <i class="bi bi-check-lg me-1"></i>
            保存设置
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdvancedSettingsModal',
  props: {
    serverIndex: {
      type: Number,
      required: true
    },
    serverInfo: {
      type: Object,
      required: true
    },
    triggerElement: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'update'],
  data() {
    return {
      localData: {
        SSH端口: '22',
        运维用户: [
          {
            用户名: 'root',
            密码: '',
            isDefault: true,
            showPassword: false
          }
        ],
        备注: '',
        标签: ''
      },
      modalStyle: {}
    }
  },
  mounted() {
    this.initializeData()
    this.calculateModalPosition()

    // 监听窗口大小变化
    this.resizeHandler = () => {
      this.calculateModalPosition()
    }
    window.addEventListener('resize', this.resizeHandler)
  },

  beforeUnmount() {
    // 清理事件监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }
  },
  methods: {
    initializeData() {
      // 复制服务器信息到本地数据
      this.localData = {
        SSH端口: this.serverInfo.SSH端口 || '22',
        运维用户: this.serverInfo.运维用户 ? JSON.parse(JSON.stringify(this.serverInfo.运维用户)) : [
          {
            用户名: 'root',
            密码: '',
            isDefault: true,
            showPassword: false
          }
        ],
        备注: this.serverInfo.备注 || '',
        标签: this.serverInfo.标签 || ''
      }
      
      // 确保每个用户都有showPassword属性
      this.localData.运维用户.forEach(user => {
        if (user.showPassword === undefined) {
          user.showPassword = false
        }
      })
    },
    
    addUser() {
      this.localData.运维用户.push({
        用户名: '',
        密码: '',
        isDefault: false,
        showPassword: false
      })
    },
    
    removeUser(index) {
      if (confirm('确定要删除这个用户吗？')) {
        this.localData.运维用户.splice(index, 1)
      }
    },
    
    togglePasswordVisibility(user) {
      user.showPassword = !user.showPassword
    },

    calculateModalPosition() {
      // 自适应尺寸：根据视窗大小动态调整
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      // 动态计算宽度（高级设置相对较小）
      let modalWidth
      if (viewportWidth >= 1200) {
        modalWidth = Math.min(700, viewportWidth * 0.6) // 大屏幕：最大700px或60%视窗宽度
      } else if (viewportWidth >= 768) {
        modalWidth = Math.min(600, viewportWidth * 0.8) // 中等屏幕：最大600px或80%视窗宽度
      } else {
        modalWidth = viewportWidth - 20 // 小屏幕：几乎全宽，留20px边距
      }

      // 动态计算最大高度
      const maxHeight = viewportHeight - 40 // 留40px上下边距

      this.modalStyle = {
        position: 'fixed',
        top: '40%', // 从50%改为40%，让模态框往上移动
        left: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 1055,
        width: `${modalWidth}px`,
        maxWidth: '95vw', // 确保不超出视窗
        maxHeight: `${maxHeight}px`,
        overflow: 'auto'
      }
    },

    saveSettings() {
      // 清理数据，移除showPassword属性
      const cleanedUsers = this.localData.运维用户.map(user => ({
        用户名: user.用户名,
        密码: user.密码,
        isDefault: user.isDefault
      }))

      const updatedData = {
        SSH端口: this.localData.SSH端口,
        运维用户: cleanedUsers,
        备注: this.localData.备注,
        标签: this.localData.标签
      }

      this.$emit('update', updatedData)
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  animation: backdropFadeIn 0.3s ease-out;
}

.modal-container {
  position: fixed;
  z-index: var(--z-modal);
  pointer-events: auto;
}

.modal-dialog {
  margin: 0;
  width: 100%;
  min-width: 300px;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-200);
}

.modal-content {
  border-radius: var(--radius-lg);
  background: white;
  animation: modalSlideIn 0.3s ease-out;
  border: none;
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 模态框头部样式 */
.modal-header {
  background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
  color: var(--gray-700);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  position: relative;
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  color: var(--warning-color);
}

.modal-title i {
  color: var(--warning-color);
  margin-right: var(--spacing-sm);
}

.btn-close {
  background: var(--gray-100);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  color: var(--gray-600);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all var(--transition-base);
}

.btn-close:hover {
  background: var(--gray-200);
  border-color: var(--gray-400);
  color: var(--gray-700);
}

/* 模态框内容区域 */
.modal-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--spacing-xs);
  display: block;
}

.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  padding: 0.5rem 0.75rem;
  font-size: var(--font-size-sm);
  transition: all var(--transition-base);
  background: white;
  width: 100%;
}

.form-control:focus, .form-select:focus {
  border-color: var(--warning-color);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.25);
  outline: none;
}

/* 模态框底部 */
.modal-footer {
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: all var(--transition-base);
  border: 1px solid transparent;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background: var(--gray-200);
  color: var(--gray-800);
}

.btn-primary {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn-primary:hover {
  background: var(--warning-dark);
  border-color: var(--warning-dark);
}

.user-item {
  padding: var(--spacing-lg);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  background: white;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.user-item:hover {
  border-color: var(--warning-color);
  box-shadow: var(--shadow-md);
}

.user-item:last-child {
  margin-bottom: 0;
}

.user-list {
  max-height: 320px;
  overflow-y: auto;
}

.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .modal-dialog {
    min-width: 260px;
  }
}

@media (max-width: 768px) {
  .modal-header {
    padding: 20px 24px;
  }

  .modal-title {
    font-size: 1.25rem;
  }

  .modal-body {
    padding: 24px;
  }

  .modal-footer {
    padding: 20px 24px;
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .user-item {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .modal-dialog {
    width: calc(100vw - 10px);
    max-width: calc(100vw - 10px);
    margin: 5px;
    border-radius: 12px;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-footer {
    padding: 16px 20px;
  }
}
</style>
