<template>
  <div class="excel-import">
    <div class="container-fluid">
      <!-- 页面标题 -->
      <div class="row mb-4">
        <div class="col-12">
          <h2 class="page-title">
            <i class="bi bi-upload me-2"></i>Excel文件导入
          </h2>
          <p class="text-muted">将现有的Excel文件导入系统，自动生成填写记录</p>
        </div>
      </div>

      <!-- 导入步骤指示器 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="import-steps">
            <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
              <div class="step-number">1</div>
              <div class="step-title">选择文件</div>
            </div>
            <div class="step-line" :class="{ active: currentStep > 1 }"></div>
            <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
              <div class="step-number">2</div>
              <div class="step-title">填写表单</div>
            </div>
            <div class="step-line" :class="{ active: currentStep > 2 }"></div>
            <div class="step" :class="{ active: currentStep >= 3 }">
              <div class="step-number">3</div>
              <div class="step-title">确认提交</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤1: 文件上传 -->
      <div v-if="currentStep === 1" class="row">
        <div class="col-md-8 mx-auto">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-file-earmark-excel me-2"></i>选择Excel文件
              </h5>
            </div>
            <div class="card-body">
              <!-- 文件上传区域 -->
              <div class="upload-area" 
                   @drop="handleDrop" 
                   @dragover.prevent 
                   @dragenter.prevent
                   :class="{ 'drag-over': isDragOver }"
                   @dragenter="isDragOver = true"
                   @dragleave="isDragOver = false">
                <div class="upload-content">
                  <i class="bi bi-cloud-upload upload-icon"></i>
                  <h5>拖拽Excel文件到此处</h5>
                  <p class="text-muted">或者点击下方按钮选择文件</p>
                  <input type="file"
                         ref="fileInput"
                         @change="handleFileSelect"
                         accept=".xlsx,.xls"
                         style="display: none;">
                  <button type="button"
                          class="btn btn-primary"
                          @click="clickFileInput">
                    <i class="bi bi-folder2-open me-2"></i>选择文件
                  </button>

                </div>
              </div>

              <!-- 表单类型选择 -->
              <div class="mt-4">
                <label class="form-label">表单类型（可选）</label>
                <select v-model="selectedFormType" class="form-select">
                  <option value="">自动检测</option>
                  <option value="安全测评">安全测评</option>
                  <option value="安全监测">安全监测</option>
                  <option value="应用加固">应用加固</option>
                </select>
                <div class="form-text">
                  如果不选择，系统将自动检测Excel文件的表单类型
                </div>
              </div>

              <!-- 已选择的文件信息 -->
              <div v-if="selectedFile" class="mt-4">
                <div class="selected-file">
                  <div class="file-info">
                    <i class="bi bi-file-earmark-excel text-success me-2"></i>
                    <span class="file-name">{{ selectedFile.name }}</span>
                    <span class="file-size text-muted">({{ formatFileSize(selectedFile.size) }})</span>
                  </div>
                  <button type="button" 
                          class="btn btn-sm btn-outline-danger" 
                          @click="clearFile">
                    <i class="bi bi-x"></i>
                  </button>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="mt-4 text-end">
                <button type="button"
                        class="btn btn-success"
                        @click="parseAndFillForm"
                        :disabled="!selectedFile || loading">
                  <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                  <i v-else class="bi bi-file-earmark-text me-2"></i>
                  {{ loading ? '解析中...' : '解析并填写表单' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2: 填写表单 -->
      <div v-if="currentStep === 2" class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-eye me-2"></i>数据预览与编辑 - {{ previewData.form_type }}
              </h5>
              <small class="text-muted">
                已从Excel文件解析数据，您可以直接在下方预览和编辑数据
              </small>
            </div>
            <div class="card-body">
              <!-- 解析摘要 -->
              <div class="alert alert-info mb-4">
                <div class="row">
                  <div class="col-md-3">
                    <strong>公司名称:</strong> {{ previewData.company_name || '未识别' }}
                  </div>
                  <div class="col-md-3">
                    <strong>记录日期:</strong> {{ previewData.record_date || '未识别' }}
                  </div>
                  <div class="col-md-3">
                    <strong>服务器数量:</strong> {{ previewData.server_count || 0 }}
                  </div>
                  <div class="col-md-3">
                    <strong>组件数量:</strong> {{ previewData.component_count || 0 }}
                  </div>
                </div>
              </div>

              <!-- 错误和警告信息 -->
              <div v-if="previewData.errors && previewData.errors.length > 0" class="alert alert-danger">
                <h6><i class="bi bi-exclamation-triangle me-2"></i>发现错误</h6>
                <ul class="mb-0">
                  <li v-for="error in previewData.errors" :key="error">{{ error }}</li>
                </ul>
              </div>

              <div v-if="previewData.warnings && previewData.warnings.length > 0" class="alert alert-warning">
                <h6><i class="bi bi-exclamation-circle me-2"></i>警告信息</h6>
                <ul class="mb-0">
                  <li v-for="warning in previewData.warnings" :key="warning">{{ warning }}</li>
                </ul>
              </div>

              <!-- 数据统计信息 -->
              <div class="row mb-4">
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-primary">{{ Object.keys(jsonFormData).length }}</h5>
                      <p class="card-text small">基本字段</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-success">{{ (jsonFormData['服务器信息'] && jsonFormData['服务器信息'].length) || 0 }}</h5>
                      <p class="card-text small">服务器</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-info">{{ (jsonFormData['维护记录'] && jsonFormData['维护记录'].length) || 0 }}</h5>
                      <p class="card-text small">维护记录</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-warning">{{ (jsonFormData['部署应用'] && jsonFormData['部署应用'].length) || 0 }}</h5>
                      <p class="card-text small">组件</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 动态表单组件进行预览和编辑 -->
              <div class="border rounded p-3 mb-4" style="background-color: #f8f9fa;">
                <h6 class="mb-3">
                  <i class="bi bi-pencil-square me-2"></i>数据编辑
                  <small class="text-muted ms-2">您可以直接在下方修改数据</small>
                </h6>

                <DynamicFormRenderer
                  v-if="jsonFormData && previewData.form_type"
                  :form-type="previewData.form_type"
                  :initial-data="jsonFormData"
                  @form-submit="handlePreviewFormSubmit"
                  @form-change="handlePreviewFormChange"
                  @show-toast="showToast"
                  ref="previewForm"
                  :show-submit-button="false"
                />
              </div>

              <!-- 操作按钮 -->
              <div class="text-end mt-4">
                <button type="button"
                        class="btn btn-secondary me-2"
                        @click="goBack">
                  <i class="bi bi-arrow-left me-2"></i>返回
                </button>
                <button type="button"
                        class="btn btn-info me-2"
                        @click="exportToJson"
                        :disabled="importing">
                  <i class="bi bi-download me-2"></i>导出JSON
                </button>
                <button type="button"
                        class="btn btn-primary"
                        @click="confirmImportWithPreview"
                        :disabled="importing">
                  <span v-if="importing" class="spinner-border spinner-border-sm me-2"></span>
                  <i v-else class="bi bi-check-circle me-2"></i>
                  {{ importing ? '导入中...' : '确认导入' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 3" class="row">
        <div class="col-md-8 mx-auto">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-check-circle text-success me-2"></i>导入完成
              </h5>
            </div>
            <div class="card-body text-center">
              <div class="import-success">
                <i class="bi bi-check-circle-fill text-success display-1"></i>
                <h4 class="mt-3">导入成功！</h4>
                <p class="text-muted">Excel文件已成功导入系统</p>
                
                <div class="import-result-info mt-4">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="result-item">
                        <div class="result-label">公司名称</div>
                        <div class="result-value">{{ importResult.company_name }}</div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="result-item">
                        <div class="result-label">表单类型</div>
                        <div class="result-value">{{ importResult.form_type }}</div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="result-item">
                        <div class="result-label">记录日期</div>
                        <div class="result-value">{{ importResult.record_date }}</div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="result-item">
                        <div class="result-label">服务器数量</div>
                        <div class="result-value">{{ importResult.server_count }} 台</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mt-4">
                  <button type="button"
                          class="btn btn-success me-2"
                          @click="editInForm">
                    <i class="bi bi-pencil-square me-2"></i>编辑表单
                  </button>
                  <button type="button"
                          class="btn btn-primary me-2"
                          @click="viewRecord">
                    <i class="bi bi-eye me-2"></i>查看记录
                  </button>
                  <button type="button"
                          class="btn btn-outline-secondary"
                          @click="importAnother">
                    <i class="bi bi-plus-circle me-2"></i>导入其他文件
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicFormRenderer from '@/components/forms/DynamicFormRenderer.vue'

// 统一的API基础URL配置
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

export default {
  name: 'ExcelImport',
  components: {
    DynamicFormRenderer
  },
  data() {
    return {
      currentStep: 1,
      selectedFile: null,
      selectedFormType: '',
      isDragOver: false,
      loading: false,
      importing: false,
      submitting: false,
      previewData: {},
      importResult: {},
      formData: {},
      jsonFormData: {} // 新增：用于存储转换后的JSON格式数据
    }
  },
  computed: {
    hasErrors() {
      return this.previewData.errors && this.previewData.errors.length > 0
    }
  },
  methods: {
    // 验证并设置文件
    validateAndSetFile(file) {
      // 验证文件类型
      if (!file.name.match(/\.(xlsx|xls)$/i)) {
        alert('请选择Excel文件(.xlsx或.xls格式)')
        return
      }

      // 验证文件大小 (10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('文件大小不能超过10MB')
        return
      }

      this.selectedFile = file
    },

    // 文件选择处理
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file) {
        this.validateAndSetFile(file)
      }
    },

    // 拖拽处理
    handleDrop(event) {
      event.preventDefault()
      this.isDragOver = false

      const files = event.dataTransfer.files
      if (files.length > 0) {
        this.validateAndSetFile(files[0])
      }
    },

    // 点击选择文件
    clickFileInput() {
      // 优先使用ref，如果不可用则使用DOM查询作为备选
      const fileInput = this.$refs.fileInput
      if (fileInput) {
        fileInput.click()
      } else {
        const fileInputDom = document.querySelector('input[type="file"]')
        if (fileInputDom) {
          fileInputDom.click()
        }
      }
    },

    // 清除文件
    clearFile() {
      this.selectedFile = null
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = ''
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 拖拽事件处理
    handleDragOver(event) {
      event.preventDefault()
      this.isDragOver = true
    },

    handleDragLeave(event) {
      event.preventDefault()
      this.isDragOver = false
    },

    // 将Excel解析结果转换为JSON格式
    convertToJsonFormat() {
      try {
        console.log('🔄 开始将Excel解析结果转换为JSON格式...')

        // 合并基本信息
        const jsonData = {
          ...(this.previewData.basic_info || {}),
          文档后缀: this.previewData.form_type,
          服务器信息: this.previewData.servers || [],
          维护记录: this.previewData.maintenance_records || []
        }

        // 确保必要字段存在
        if (!jsonData.公司名称 && this.previewData.company_name) {
          jsonData.公司名称 = this.previewData.company_name
        }

        if (!jsonData.记录日期 && this.previewData.record_date) {
          jsonData.记录日期 = this.previewData.record_date
        }

        this.jsonFormData = jsonData

        console.log('✅ JSON格式转换完成:', {
          fieldCount: Object.keys(jsonData).length,
          companyName: jsonData.公司名称,
          formType: jsonData.文档后缀,
          serverCount: (jsonData.服务器信息 && jsonData.服务器信息.length) || 0,
          maintenanceCount: (jsonData.维护记录 && jsonData.维护记录.length) || 0
        })

      } catch (error) {
        console.error('❌ JSON格式转换失败:', error)
        this.jsonFormData = {}
      }
    },

    // 解析并填写表单
    async parseAndFillForm() {
      if (!this.selectedFile) {
        alert('请先选择文件')
        return
      }

      this.loading = true
      try {
        const formData = new FormData()
        formData.append('file', this.selectedFile)
        if (this.selectedFormType) {
          formData.append('form_type', this.selectedFormType)
        }

        // 直接使用fetch调用API
        const response = await fetch('/excel/import_excel/preview', {
          method: 'POST',
          body: formData
        })

        const result = await response.json()

        if (result.status === 'success') {
          this.previewData = result.data
          // 将Excel解析结果转换为JSON格式
          this.convertToJsonFormat()
          this.currentStep = 2
        } else {
          alert(result.message || '解析失败')
        }
      } catch (error) {
        console.error('解析Excel失败:', error)
        alert('解析Excel失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 确认导入
    async confirmImport() {
      if (!this.previewData || !this.previewData.temp_file_id) {
        alert('缺少预览数据，请重新预览')
        return
      }

      this.importing = true
      try {
        // 发送JSON数据，包含temp_file_id
        const requestData = {
          temp_file_id: this.previewData.temp_file_id,
          form_type: this.previewData.form_type || this.selectedFormType
        }

        // 使用fetch发送JSON数据
        const response = await fetch('/excel/import_excel/confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestData)
        })

        const result = await response.json()

        if (result.status === 'success') {
          this.importResult = result.data
          this.currentStep = 3
        } else {
          alert(result.message || '导入失败')
        }
      } catch (error) {
        console.error('确认导入失败:', error)
        alert('确认导入失败: ' + error.message)
      } finally {
        this.importing = false
      }
    },

    // 返回上一步
    goBack() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    // 查看记录
    viewRecord() {
      this.$router.push('/history-data')
    },

    // 填充到表单
    async fillToForm() {
      if (!this.previewData || !this.previewData.temp_file_id) {
        alert('缺少预览数据，请重新解析')
        return
      }

      this.importing = true
      try {
        // 将解析的数据直接传递给填写页面
        const formData = {
          // 合并基本字段数据
          ...(this.previewData.basic_info || {}),
          文档后缀: this.previewData.form_type,
          服务器信息: this.previewData.servers || [],
          维护记录: this.previewData.maintenance_records || []
        }

        // 将数据存储到sessionStorage，供填写页面使用
        sessionStorage.setItem('importedFormData', JSON.stringify(formData))
        sessionStorage.setItem('importedFromExcel', 'true')
        // 清除之前的使用标记
        sessionStorage.removeItem('importDataUsed')

        console.log('📦 数据已存储到sessionStorage:', {
          dataSize: JSON.stringify(formData).length,
          fieldCount: Object.keys(formData).length,
          formType: this.previewData.form_type,
          companyName: formData['公司名称'],
          serverCount: (formData['服务器信息'] && formData['服务器信息'].length) || 0
        })

        // 跳转到填写页面
        this.$router.push({
          path: '/fill-sheet',
          query: {
            from_import: 'true',
            form_type: this.previewData.form_type
          }
        })
      } catch (error) {
        console.error('跳转到填写页面失败:', error)
        alert('跳转到填写页面失败: ' + error.message)
      } finally {
        this.importing = false
      }
    },

    // 编辑表单
    editInForm() {
      if (this.importResult && this.importResult.submission_id) {
        // 跳转到填写页面，并传递submission_id作为初始数据
        this.$router.push({
          path: '/fill-sheet',
          query: {
            import_id: this.importResult.submission_id,
            form_type: this.importResult.form_type
          }
        })
      } else {
        alert('无法获取导入记录信息')
      }
    },

    // 处理表单提交
    async handleFormSubmit(data) {
      console.log('表单提交数据:', data)
      this.formData = data.formData
      await this.submitFormData()
    },

    // 处理表单变化
    handleFormChange(data) {
      this.formData = data
    },

    // 处理预览表单的变化
    handlePreviewFormChange(data) {
      console.log('📝 预览表单数据变化:', data)
      this.jsonFormData = { ...this.jsonFormData, ...data }
    },

    // 处理预览表单的提交
    handlePreviewFormSubmit(data) {
      console.log('📤 预览表单提交:', data)
      this.jsonFormData = data.formData
      // 自动确认导入
      this.confirmImportWithPreview()
    },

    // 导出JSON数据
    exportToJson() {
      try {
        const jsonStr = JSON.stringify(this.jsonFormData, null, 2)
        const blob = new Blob([jsonStr], { type: 'application/json' })
        const url = URL.createObjectURL(blob)

        const link = document.createElement('a')
        link.href = url
        link.download = `${this.jsonFormData.公司名称 || 'excel_import'}_${this.jsonFormData.文档后缀 || 'data'}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        console.log('✅ JSON文件导出成功')
      } catch (error) {
        console.error('❌ JSON导出失败:', error)
        alert('JSON导出失败: ' + error.message)
      }
    },

    // 使用预览数据确认导入
    async confirmImportWithPreview() {
      if (!this.jsonFormData || Object.keys(this.jsonFormData).length === 0) {
        alert('没有可导入的数据')
        return
      }

      this.importing = true
      try {
        console.log('📤 开始导入预览后的数据:', this.jsonFormData)

        // 直接使用JSON导入API
        const response = await fetch(`${API_BASE_URL}/excel/import_json/preview`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            form_data: this.jsonFormData,
            form_type: this.jsonFormData.文档后缀 || this.previewData.form_type
          })
        })

        const result = await response.json()

        if (result.status === 'success') {
          // 确认导入
          const confirmResponse = await fetch(`${API_BASE_URL}/excel/import_json/confirm`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              temp_data_id: result.data.temp_data_id,
              form_type: this.jsonFormData.文档后缀 || this.previewData.form_type
            })
          })

          const confirmResult = await confirmResponse.json()

          if (confirmResult.status === 'success') {
            this.importResult = {
              company_name: this.jsonFormData.公司名称,
              form_type: this.jsonFormData.文档后缀,
              record_date: this.jsonFormData.记录日期,
              server_count: (this.jsonFormData.服务器信息 && this.jsonFormData.服务器信息.length) || 0,
              submission_id: confirmResult.data.submission_id
            }
            this.currentStep = 3
          } else {
            alert(confirmResult.message || '确认导入失败')
          }
        } else {
          alert(result.message || '预览导入失败')
        }
      } catch (error) {
        console.error('❌ 导入失败:', error)
        alert('导入失败: ' + error.message)
      } finally {
        this.importing = false
      }
    },

    // 显示提示信息
    showToast(message, title = '提示', type = 'info') {
      // 这里可以集成toast组件
      console.log(`${title}: ${message}`)
      if (type === 'error') {
        alert(`${title}: ${message}`)
      }
    },

    // 提交表单数据
    async submitFormData() {
      if (!this.formData || Object.keys(this.formData).length === 0) {
        alert('请填写表单数据')
        return
      }

      this.submitting = true
      try {
        // 直接提交表单数据到后端
        const response = await fetch('/submit_form', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.formData)
        })

        const result = await response.json()

        if (result.status === 'success') {
          this.importResult = {
            company_name: this.formData.公司名称 || this.formData.company_name,
            form_type: this.previewData.form_type,
            record_date: this.formData.记录日期 || this.formData.record_date,
            server_count: this.formData.服务器信息 ? this.formData.服务器信息.length : 0,
            submission_id: result.submission_id
          }
          this.currentStep = 3
        } else {
          alert(result.message || '提交失败')
        }
      } catch (error) {
        console.error('提交表单失败:', error)
        alert('提交表单失败: ' + error.message)
      } finally {
        this.submitting = false
      }
    },

    // 导入其他文件
    importAnother() {
      // 重置所有状态
      this.currentStep = 1
      this.selectedFile = null
      this.selectedFormType = ''
      this.previewData = {}
      this.importResult = {}
      this.formData = {}
      this.jsonFormData = {}

      // 清除文件输入
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = ''
      }

      console.log('🔄 已重置，可以导入其他文件')
    },

    // 强制导入（当有错误时）
    async forceImport() {
      if (!this.previewData || !this.previewData.temp_file_id) {
        alert('缺少预览数据，请重新解析')
        return
      }

      this.importing = true
      try {
        // 发送JSON数据，包含temp_file_id
        const requestData = {
          temp_file_id: this.previewData.temp_file_id,
          form_type: this.previewData.form_type || this.selectedFormType,
          force: true
        }

        // 使用fetch发送JSON数据
        const response = await fetch('/excel/import_excel/confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestData)
        })

        const result = await response.json()

        if (result.status === 'success') {
          this.importResult = result.data
          this.currentStep = 3
        } else {
          alert(result.message || '强制导入失败')
        }
      } catch (error) {
        console.error('强制导入失败:', error)
        alert('强制导入失败: ' + error.message)
      } finally {
        this.importing = false
      }
    },

    // 导入其他文件
    importAnother() {
      // 重置所有状态
      this.currentStep = 1
      this.selectedFile = null
      this.selectedFormType = ''
      this.previewData = {}
      this.importResult = {}
      this.formData = {}
      this.clearFile()
    }
  }
}
</script>

<style scoped>
.excel-import {
  padding: 20px;
}

.page-title {
  color: #2c3e50;
  font-weight: 600;
}

/* 步骤指示器样式 */
.import-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2rem 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background-color: #007bff;
  color: white;
}

.step.completed .step-number {
  background-color: #28a745;
  color: white;
}

.step-title {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.step.active .step-title {
  color: #007bff;
  font-weight: 600;
}

.step.completed .step-title {
  color: #28a745;
}

.step-line {
  width: 100px;
  height: 2px;
  background-color: #e9ecef;
  margin: 0 20px;
  margin-top: -20px;
  transition: all 0.3s ease;
}

.step-line.active {
  background-color: #007bff;
}

/* 文件上传区域样式 */
.upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 3rem 2rem;
  text-align: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #007bff;
  background-color: #e3f2fd;
}

.upload-area.drag-over {
  border-color: #007bff;
  background-color: #e3f2fd;
  transform: scale(1.02);
}

.upload-content {
  /* pointer-events: none; 注释掉这行，因为它会阻止按钮点击 */
}

.upload-content > *:not(button) {
  pointer-events: none;
}

.upload-icon {
  font-size: 3rem;
  color: #007bff;
  margin-bottom: 1rem;
}

.upload-area h5 {
  color: #495057;
  margin-bottom: 0.5rem;
}

/* 已选择文件样式 */
.selected-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 6px;
}

.file-info {
  display: flex;
  align-items: center;
}

.file-name {
  font-weight: 500;
  margin-right: 0.5rem;
}

.file-size {
  font-size: 0.9rem;
}

/* 预览摘要样式 */
.preview-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.summary-item {
  text-align: center;
  margin-bottom: 1rem;
}

.summary-label {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.summary-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
}

/* 导入结果样式 */
.import-success {
  padding: 2rem;
}

.import-result-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.result-item {
  text-align: center;
  margin-bottom: 1rem;
}

.result-label {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.result-value {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
}

/* 表格样式优化 */
.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  border-top: none;
}

.table td {
  vertical-align: middle;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .import-steps {
    flex-direction: column;
  }

  .step-line {
    width: 2px;
    height: 50px;
    margin: 10px 0;
  }

  .upload-area {
    padding: 2rem 1rem;
  }

  .upload-icon {
    font-size: 2rem;
  }

  .preview-summary .row > div {
    margin-bottom: 1rem;
  }
}

/* 动画效果 */
.card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* 按钮样式优化 */
.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn:disabled {
  transform: none;
  box-shadow: none;
}
</style>
