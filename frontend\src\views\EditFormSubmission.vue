<template>
  <div class="edit-form-submission">
    <div class="container-fluid">
      <!-- 页面头部 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h2 class="mb-1">
                <i class="bi bi-pencil-square me-2"></i>
                编辑表单提交记录
              </h2>
              <p class="text-muted mb-0" v-if="submissionData">
                <span>{{ getBaseCompanyName(submissionData.company_name) }}</span>
                <span v-if="hasAlias(submissionData.company_name)"
                      class="badge bg-secondary ms-2">
                  {{ getAlias(submissionData.company_name) }}
                </span>
                - {{ submissionData.form_type }}
                ({{ formatDate(submissionData.record_date) }})
              </p>
            </div>
            <div>
              <button class="btn btn-secondary" @click="goBack">
                <i class="bi bi-arrow-left me-1"></i>
                返回
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载表单数据...</p>
      </div>

      <!-- 错误信息 -->
      <div v-else-if="error" class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ error }}
      </div>

      <!-- 编辑表单 -->
      <div v-else-if="submissionData" class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-form-check me-2"></i>
                表单数据编辑
              </h5>
            </div>
            <div class="card-body">
              <!-- 编辑原因 -->
              <div class="row mb-4">
                <div class="col-md-12">
                  <label class="form-label">编辑原因 <span class="text-danger">*</span></label>
                  <input
                    type="text"
                    class="form-control"
                    v-model="editReason"
                    placeholder="请输入编辑原因"
                    required
                  >
                </div>
              </div>

              <!-- 动态表单组件 -->
              <div class="form-content">
                <!-- 根据表单类型动态加载对应的表单组件 -->
                <component
                  :is="formComponent"
                  v-if="formComponent && formData && componentGroups"
                  v-model="formData"
                  :component-groups="componentGroups"
                  :is-edit-mode="true"
                />

                <!-- 如果组件没有加载，显示提示信息 -->
                <div v-else class="alert alert-warning">
                  <i class="bi bi-exclamation-triangle me-2"></i>
                  正在加载表单内容，请稍候...
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="row mt-4">
                <div class="col-12">
                  <div class="d-flex justify-content-end gap-2">
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      @click="resetForm"
                    >
                      <i class="bi bi-arrow-clockwise me-1"></i>
                      重置
                    </button>
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="saveChanges"
                      :disabled="saving || !canSave"
                      :title="saveButtonTooltip"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                    >
                      <span v-if="saving" class="spinner-border spinner-border-sm me-1"></span>
                      <i v-else class="bi bi-check-lg me-1"></i>
                      保存修改
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <ConfirmModal
      v-if="showConfirm"
      :title="confirmTitle"
      :message="confirmMessage"
      @confirm="confirmAction"
      @cancel="showConfirm = false"
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from 'vue'
import { getComponentGroupsFromDatabase } from '@/config/formDataConfig'
import axios from 'axios'

// 统一的API基础URL配置
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

/**
 * 编辑表单提交记录页面
 * 支持编辑不同类型的表单数据并记录编辑历史
 */
export default {
  name: 'EditFormSubmission',

  components: {
    ConfirmModal: defineAsyncComponent(() => import('@/components/common/ConfirmModal.vue')),

    // 动态导入表单组件
    SecurityMonitoringForm: defineAsyncComponent(() => import('@/components/forms/securityMonitoring/SecurityMonitoringForm.vue')),
    SecurityTestingForm: defineAsyncComponent(() => import('@/components/forms/securityTesting/SecurityTestingForm.vue')),
    AppHardeningForm: defineAsyncComponent(() => import('@/components/forms/appHardening/AppHardeningForm.vue'))
  },

  props: {
    id: {
      type: [String, Number],
      required: true
    }
  },

  data() {
    return {
      loading: false,
      saving: false,
      error: null,

      // 表单数据
      submissionData: null,
      formData: null,
      originalFormData: null,

      // 组件分组数据
      componentGroups: {},

      // 编辑信息
      editReason: '',

      // 模态框状态
      showConfirm: false,
      confirmTitle: '',
      confirmMessage: '',
      confirmAction: null
    }
  },

  computed: {
    submissionId() {
      return parseInt(this.id)
    },

    /**
     * 根据表单类型返回对应的组件
     */
    formComponent() {
      if (!this.submissionData) return null

      const componentMap = {
        '安全监测': 'SecurityMonitoringForm',
        '安全测评': 'SecurityTestingForm',
        '应用加固': 'AppHardeningForm'
      }

      return componentMap[this.submissionData.form_type] || null
    },

    /**
     * 获取必填字段列表
     */
    requiredFields() {
      if (!this.submissionData) return []

      const formType = this.submissionData.form_type

      if (formType === '安全监测') {
        return [
          { key: '公司名称', label: '公司名称' },
          { key: '前端版本', label: '前端版本' },
          { key: '后端版本', label: '后端版本' },
          { key: '记录日期', label: '记录日期' }
        ]
      } else if (formType === '应用加固') {
        return [
          { key: '公司名称', label: '公司名称' },
          { key: '客户', label: '客户' },
          { key: '记录日期', label: '记录日期' }
        ]
      } else {
        return [
          { key: '公司名称', label: '公司名称' },
          { key: '部署包版本', label: '部署包版本' },
          { key: '管理员页面IP', label: '管理员页面IP' },
          { key: '用户页面IP', label: '用户页面IP' },
          { key: '记录日期', label: '记录日期' }
        ]
      }
    },

    /**
     * 获取缺失的必填字段
     */
    missingFields() {
      if (!this.formData) return []

      return this.requiredFields.filter(field => {
        const value = this.formData[field.key]
        return !value || (typeof value === 'string' && !value.trim())
      })
    },

    /**
     * 检查是否可以保存
     */
    canSave() {
      return this.editReason.trim() && this.missingFields.length === 0
    },

    /**
     * 保存按钮的悬停提示
     */
    saveButtonTooltip() {
      const issues = []

      if (!this.editReason.trim()) {
        issues.push('请填写编辑原因')
      }

      if (this.missingFields.length > 0) {
        const fieldNames = this.missingFields.map(f => f.label).join('、')
        issues.push(`请填写必填字段：${fieldNames}`)
      }

      if (issues.length === 0) {
        return '保存修改'
      }

      return issues.join('；')
    }
  },

  async mounted() {
    // 并行加载组件数据和表单数据
    await Promise.all([
      this.loadComponentGroups(),
      this.loadSubmissionData()
    ])

    // 初始化Bootstrap tooltips
    this.$nextTick(() => {
      this.initTooltips()
    })
  },

  updated() {
    // 每次更新后重新初始化tooltips
    this.$nextTick(() => {
      this.initTooltips()
    })
  },

  methods: {
    /**
     * 初始化Bootstrap tooltips
     */
    initTooltips() {
      try {
        // 销毁现有的tooltips
        const existingTooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]')
        existingTooltips.forEach(element => {
          const tooltip = bootstrap.Tooltip.getInstance(element)
          if (tooltip) {
            tooltip.dispose()
          }
        })

        // 重新初始化tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        tooltipTriggerList.map(function (tooltipTriggerEl) {
          return new bootstrap.Tooltip(tooltipTriggerEl)
        })
      } catch (error) {
        console.warn('初始化tooltips失败:', error)
      }
    },

    /**
     * 加载表单提交数据
     */
    async loadSubmissionData() {
      this.loading = true
      this.error = null

      try {
        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${this.submissionId}`)
        const result = await response.json()

        if (result.status === 'success') {
          this.submissionData = result.data
          this.formData = { ...result.data.form_data }
          this.originalFormData = { ...result.data.form_data }
        } else {
          this.error = result.message || '加载数据失败'
        }
      } catch (error) {
        this.error = '网络错误，请稍后重试'
        console.error('加载表单数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.confirmTitle = '确认重置'
      this.confirmMessage = '确定要重置表单数据吗？所有未保存的修改将丢失。'
      this.confirmAction = () => {
        this.formData = { ...this.originalFormData }
        this.editReason = ''
        this.showConfirm = false
      }
      this.showConfirm = true
    },

    /**
     * 保存修改
     */
    async saveChanges() {
      // 验证编辑原因
      if (!this.editReason.trim()) {
        alert('请填写编辑原因')
        return
      }

      // 验证必填字段
      if (this.missingFields.length > 0) {
        const fieldNames = this.missingFields.map(f => f.label).join('、')
        alert(`请填写以下必填字段：${fieldNames}`)
        return
      }

      this.saving = true

      try {
        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${this.submissionId}/edit`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            form_data: this.formData,
            edit_reason: this.editReason,
            edited_by: this.formData.编辑人 || '未知用户',
            company_name: this.submissionData.company_name,
            form_type: this.submissionData.form_type,
            record_date: this.submissionData.record_date
          })
        })

        const result = await response.json()

        if (result.status === 'success') {
          // 更新本地数据
          this.submissionData = result.data.submission
          this.originalFormData = { ...this.formData }
          this.editReason = ''

          // 显示成功消息
          this.$emit('show-toast', {
            type: 'success',
            message: '表单数据保存成功'
          })

          // 返回历史页面
          setTimeout(() => {
            this.goBack()
          }, 1500)
        } else {
          alert('保存失败: ' + result.message)
        }
      } catch (error) {
        console.error('保存失败:', error)
        alert('保存失败，请稍后重试')
      } finally {
        this.saving = false
      }
    },

    /**
     * 返回上一页
     */
    goBack() {
      this.$router.push('/history-data')
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    /**
     * 检查公司名称是否包含别名
     */
    hasAlias(companyName) {
      return companyName && companyName.includes('-') && companyName.split('-').length > 1
    },

    /**
     * 获取基础公司名称（去除别名部分）
     */
    getBaseCompanyName(companyName) {
      if (!companyName) return ''
      if (this.hasAlias(companyName)) {
        return companyName.split('-')[0]
      }
      return companyName
    },

    /**
     * 获取别名部分
     */
    getAlias(companyName) {
      if (!companyName || !this.hasAlias(companyName)) return ''
      const parts = companyName.split('-')
      return parts.slice(1).join('-')
    },

    /**
     * 加载组件分组数据
     */
    async loadComponentGroups() {
      try {
        console.log('🔄 编辑页面：开始从数据库加载组件数据...')
        // 优先使用数据库数据
        this.componentGroups = await getComponentGroupsFromDatabase()
        console.log('✅ 编辑页面：成功从数据库加载组件数据:', this.componentGroups)
      } catch (error) {
        console.warn('⚠️ 编辑页面：从数据库加载组件数据失败:', error)
        // 如果数据库加载失败，设置空的组件分组
        this.componentGroups = {}
        console.log('📋 编辑页面：数据库加载失败，使用空的组件配置')
      }
    }
  }
}
</script>

<style scoped>
.edit-form-submission {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
}

.form-content {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.btn {
  border-radius: 6px;
}

.text-danger {
  color: #dc3545 !important;
}

.gap-2 {
  gap: 0.5rem;
}

/* 禁用按钮的样式 */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 悬停提示的样式 */
.btn[data-bs-toggle="tooltip"]:disabled {
  pointer-events: auto; /* 允许禁用按钮显示tooltip */
}

/* 必填字段的视觉指示 */
.form-label.required::after {
  content: " *";
  color: #dc3545;
  font-weight: bold;
}

/* 错误状态的输入框 */
.form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* 成功状态的输入框 */
.form-control.is-valid {
  border-color: #198754;
  box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}
</style>
