"""
Redis缓存工具类
提供统一的缓存操作接口，支持用户权限、组件配置、模板配置等缓存功能
"""

import json
import logging
from functools import wraps
from typing import Any, Optional, Dict, List
from flask import current_app
from flask_caching import Cache

# 全局缓存实例
cache = Cache()

logger = logging.getLogger(__name__)


class CacheManager:
    """缓存管理器"""
    
    @staticmethod
    def get_cache_key(prefix: str, *args) -> str:
        """生成缓存键"""
        key_parts = [prefix] + [str(arg) for arg in args if arg is not None]
        return ':'.join(key_parts)
    
    @staticmethod
    def get_timeout(cache_type: str) -> int:
        """获取缓存超时时间"""
        cache_config = current_app.config.get('CACHE_CONFIG', {})
        return cache_config.get(cache_type, current_app.config.get('CACHE_DEFAULT_TIMEOUT', 300))


class UserCacheManager:
    """用户相关缓存管理"""
    
    @staticmethod
    def get_user_permissions_key(user_id: int) -> str:
        """获取用户权限缓存键"""
        return CacheManager.get_cache_key('user_permissions', user_id)
    
    @staticmethod
    def get_user_info_key(user_id: int) -> str:
        """获取用户信息缓存键"""
        return CacheManager.get_cache_key('user_info', user_id)
    
    @staticmethod
    def cache_user_permissions(user_id: int, permissions: List[str]) -> bool:
        """缓存用户权限"""
        try:
            key = UserCacheManager.get_user_permissions_key(user_id)
            timeout = CacheManager.get_timeout('user_permissions')
            cache.set(key, permissions, timeout=timeout)
            logger.debug(f"缓存用户权限: user_id={user_id}, permissions_count={len(permissions)}")
            return True
        except Exception as e:
            logger.error(f"缓存用户权限失败: user_id={user_id}, error={str(e)}")
            logger.debug(f"Redis连接配置: host={current_app.config.get('REDIS_HOST')}, port={current_app.config.get('REDIS_PORT')}")
            return False
    
    @staticmethod
    def get_user_permissions(user_id: int) -> Optional[List[str]]:
        """获取用户权限缓存"""
        try:
            key = UserCacheManager.get_user_permissions_key(user_id)
            permissions = cache.get(key)

            # 记录缓存统计
            from app.utils.performance_monitor import performance_monitor
            hit = permissions is not None
            performance_monitor.record_cache_hit('user_permissions', hit)

            if permissions is not None:
                logger.debug(f"命中用户权限缓存: user_id={user_id}, permissions_count={len(permissions)}")
            return permissions
        except Exception as e:
            logger.error(f"获取用户权限缓存失败: user_id={user_id}, error={str(e)}")
            return None
    
    @staticmethod
    def cache_user_info(user_id: int, user_info: Dict) -> bool:
        """缓存用户信息"""
        try:
            key = UserCacheManager.get_user_info_key(user_id)
            timeout = CacheManager.get_timeout('user_info')
            cache.set(key, user_info, timeout=timeout)
            logger.debug(f"缓存用户信息: user_id={user_id}")
            return True
        except Exception as e:
            logger.error(f"缓存用户信息失败: user_id={user_id}, error={str(e)}")
            return False
    
    @staticmethod
    def get_user_info(user_id: int) -> Optional[Dict]:
        """获取用户信息缓存"""
        try:
            key = UserCacheManager.get_user_info_key(user_id)
            user_info = cache.get(key)

            # 记录缓存统计
            from app.utils.performance_monitor import performance_monitor
            hit = user_info is not None
            performance_monitor.record_cache_hit('user_info', hit)

            if user_info is not None:
                logger.debug(f"命中用户信息缓存: user_id={user_id}")
            return user_info
        except Exception as e:
            logger.error(f"获取用户信息缓存失败: user_id={user_id}, error={str(e)}")
            return None
    
    @staticmethod
    def clear_user_cache(user_id: int) -> bool:
        """清除用户相关缓存"""
        try:
            permissions_key = UserCacheManager.get_user_permissions_key(user_id)
            info_key = UserCacheManager.get_user_info_key(user_id)
            cache.delete(permissions_key)
            cache.delete(info_key)
            logger.debug(f"清除用户缓存: user_id={user_id}")
            return True
        except Exception as e:
            logger.error(f"清除用户缓存失败: user_id={user_id}, error={str(e)}")
            return False


class ComponentCacheManager:
    """组件配置缓存管理"""
    
    @staticmethod
    def get_components_key(form_type: str) -> str:
        """获取组件配置缓存键"""
        return CacheManager.get_cache_key('components', form_type)
    
    @staticmethod
    def get_categories_key(form_type: str = None) -> str:
        """获取组件分类缓存键"""
        if form_type:
            return CacheManager.get_cache_key('categories', form_type)
        return CacheManager.get_cache_key('categories', 'all')
    
    @staticmethod
    def cache_components(form_type: str, components_data: Dict) -> bool:
        """缓存组件配置"""
        try:
            key = ComponentCacheManager.get_components_key(form_type)
            timeout = CacheManager.get_timeout('components')
            cache.set(key, components_data, timeout=timeout)
            logger.debug(f"缓存组件配置: form_type={form_type}, categories_count={len(components_data)}")
            return True
        except Exception as e:
            logger.error(f"缓存组件配置失败: form_type={form_type}, error={str(e)}")
            return False
    
    @staticmethod
    def get_components(form_type: str) -> Optional[Dict]:
        """获取组件配置缓存"""
        try:
            key = ComponentCacheManager.get_components_key(form_type)
            components_data = cache.get(key)

            # 记录缓存统计
            from app.utils.performance_monitor import performance_monitor
            hit = components_data is not None
            performance_monitor.record_cache_hit('components', hit)

            if components_data is not None:
                logger.debug(f"命中组件配置缓存: form_type={form_type}")
            return components_data
        except Exception as e:
            logger.error(f"获取组件配置缓存失败: form_type={form_type}, error={str(e)}")
            return None
    
    @staticmethod
    def cache_categories(categories_data: List[Dict], form_type: str = None) -> bool:
        """缓存组件分类"""
        try:
            key = ComponentCacheManager.get_categories_key(form_type)
            timeout = CacheManager.get_timeout('components')
            cache.set(key, categories_data, timeout=timeout)
            logger.debug(f"缓存组件分类: form_type={form_type}, count={len(categories_data)}")
            return True
        except Exception as e:
            logger.error(f"缓存组件分类失败: form_type={form_type}, error={str(e)}")
            return False
    
    @staticmethod
    def get_categories(form_type: str = None) -> Optional[List[Dict]]:
        """获取组件分类缓存"""
        try:
            key = ComponentCacheManager.get_categories_key(form_type)
            categories_data = cache.get(key)
            if categories_data is not None:
                logger.debug(f"命中组件分类缓存: form_type={form_type}")
            return categories_data
        except Exception as e:
            logger.error(f"获取组件分类缓存失败: form_type={form_type}, error={str(e)}")
            return None
    
    @staticmethod
    def clear_component_cache(form_type: str = None) -> bool:
        """清除组件相关缓存"""
        try:
            if form_type:
                # 清除指定表单类型的缓存
                components_key = ComponentCacheManager.get_components_key(form_type)
                categories_key = ComponentCacheManager.get_categories_key(form_type)
                cache.delete(components_key)
                cache.delete(categories_key)
                logger.debug(f"清除组件缓存: form_type={form_type}")
            else:
                # 清除所有组件缓存
                # TODO: 实现通配符删除
                logger.debug("清除所有组件缓存")
            return True
        except Exception as e:
            logger.error(f"清除组件缓存失败: form_type={form_type}, error={str(e)}")
            return False


class TemplateCacheManager:
    """模板配置缓存管理"""
    
    @staticmethod
    def get_template_key(form_type: str) -> str:
        """获取模板配置缓存键"""
        return CacheManager.get_cache_key('template', form_type)
    
    @staticmethod
    def cache_template_path(form_type: str, template_path: str) -> bool:
        """缓存模板路径"""
        try:
            key = TemplateCacheManager.get_template_key(form_type)
            timeout = CacheManager.get_timeout('templates')
            cache.set(key, template_path, timeout=timeout)
            logger.debug(f"缓存模板路径: form_type={form_type}, path={template_path}")
            return True
        except Exception as e:
            logger.error(f"缓存模板路径失败: form_type={form_type}, error={str(e)}")
            return False
    
    @staticmethod
    def get_template_path(form_type: str) -> Optional[str]:
        """获取模板路径缓存"""
        try:
            key = TemplateCacheManager.get_template_key(form_type)
            template_path = cache.get(key)

            # 记录缓存统计
            from app.utils.performance_monitor import performance_monitor
            hit = template_path is not None
            performance_monitor.record_cache_hit('templates', hit)

            if template_path is not None:
                logger.debug(f"命中模板路径缓存: form_type={form_type}")
            return template_path
        except Exception as e:
            logger.error(f"获取模板路径缓存失败: form_type={form_type}, error={str(e)}")
            return None
    
    @staticmethod
    def clear_template_cache(form_type: str = None) -> bool:
        """清除模板缓存"""
        try:
            if form_type:
                key = TemplateCacheManager.get_template_key(form_type)
                cache.delete(key)
                logger.debug(f"清除模板缓存: form_type={form_type}")
            else:
                # TODO: 清除所有模板缓存
                logger.debug("清除所有模板缓存")
            return True
        except Exception as e:
            logger.error(f"清除模板缓存失败: form_type={form_type}, error={str(e)}")
            return False


class FormConfigCacheManager:
    """表单字段配置缓存管理"""
    
    @staticmethod
    def get_form_config_key(form_type: str) -> str:
        """获取表单配置缓存键"""
        return CacheManager.get_cache_key('form_config', form_type)
    
    @staticmethod
    def cache_form_config(form_type: str, config_data: Dict) -> bool:
        """缓存表单配置"""
        try:
            key = FormConfigCacheManager.get_form_config_key(form_type)
            timeout = CacheManager.get_timeout('form_config')
            cache.set(key, config_data, timeout=timeout)
            logger.debug(f"缓存表单配置: form_type={form_type}")
            return True
        except Exception as e:
            logger.error(f"缓存表单配置失败: form_type={form_type}, error={str(e)}")
            return False
    
    @staticmethod
    def get_form_config(form_type: str) -> Optional[Dict]:
        """获取表单配置缓存"""
        try:
            key = FormConfigCacheManager.get_form_config_key(form_type)
            config_data = cache.get(key)

            # 记录缓存统计
            from app.utils.performance_monitor import performance_monitor
            hit = config_data is not None
            performance_monitor.record_cache_hit('form_config', hit)

            if config_data is not None:
                logger.debug(f"命中表单配置缓存: form_type={form_type}")
            return config_data
        except Exception as e:
            logger.error(f"获取表单配置缓存失败: form_type={form_type}, error={str(e)}")
            return None
    
    @staticmethod
    def clear_form_config_cache(form_type: str = None) -> bool:
        """清除表单配置缓存"""
        try:
            if form_type:
                key = FormConfigCacheManager.get_form_config_key(form_type)
                cache.delete(key)
                logger.debug(f"清除表单配置缓存: form_type={form_type}")
            else:
                # TODO: 清除所有表单配置缓存
                logger.debug("清除所有表单配置缓存")
            return True
        except Exception as e:
            logger.error(f"清除表单配置缓存失败: form_type={form_type}, error={str(e)}")
            return False


def cache_result(cache_key_func, timeout_type='default'):
    """
    缓存装饰器

    Args:
        cache_key_func: 生成缓存键的函数
        timeout_type: 缓存超时类型
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 生成缓存键
                cache_key = cache_key_func(*args, **kwargs)

                # 尝试从缓存获取
                result = cache.get(cache_key)

                # 记录缓存统计
                from app.utils.performance_monitor import performance_monitor
                hit = result is not None
                performance_monitor.record_cache_hit(timeout_type, hit)

                if result is not None:
                    logger.debug(f"缓存命中: {cache_key}")
                    return result

                # 执行原函数
                result = func(*args, **kwargs)

                # 缓存结果
                if result is not None:
                    timeout = CacheManager.get_timeout(timeout_type)
                    cache.set(cache_key, result, timeout=timeout)
                    logger.debug(f"缓存结果: {cache_key}")

                return result

            except Exception as e:
                logger.error(f"缓存装饰器错误: {str(e)}")
                # 缓存失败时直接执行原函数
                return func(*args, **kwargs)

        return wrapper
    return decorator


class FormSnapshotCacheManager:
    """表单快照缓存管理 - 支持多个快照"""

    @staticmethod
    def get_user_snapshots_key(user_id: int) -> str:
        """获取用户所有快照的缓存键"""
        return CacheManager.get_cache_key('user_snapshots', user_id)

    @staticmethod
    def get_snapshot_key(user_id: int, form_type: str, snapshot_id: str) -> str:
        """获取单个快照的缓存键"""
        return CacheManager.get_cache_key('form_snapshot', user_id, form_type, snapshot_id)

    @staticmethod
    def cache_form_snapshot(user_id: int, form_type: str, snapshot_data: Dict) -> bool:
        """缓存表单快照"""
        try:
            import hashlib
            import time

            # 生成唯一的快照ID
            snapshot_name = snapshot_data.get('snapshot_name', f'快照_{int(time.time())}')
            snapshot_id = hashlib.md5(f"{user_id}_{form_type}_{snapshot_name}_{time.time()}".encode()).hexdigest()[:12]

            # 保存单个快照
            snapshot_key = FormSnapshotCacheManager.get_snapshot_key(user_id, form_type, snapshot_id)
            timeout = CacheManager.get_timeout('form_snapshot')

            # 添加快照ID到数据中
            snapshot_data_with_id = {
                **snapshot_data,
                'snapshot_id': snapshot_id
            }

            cache.set(snapshot_key, snapshot_data_with_id, timeout=timeout)

            # 更新用户快照索引
            user_snapshots_key = FormSnapshotCacheManager.get_user_snapshots_key(user_id)
            user_snapshots = cache.get(user_snapshots_key) or {}

            if form_type not in user_snapshots:
                user_snapshots[form_type] = []

            # 添加快照信息到索引
            snapshot_info = {
                'snapshot_id': snapshot_id,
                'snapshot_name': snapshot_name,
                'form_type': form_type,
                'created_at': snapshot_data.get('created_at'),
                'user_id': user_id,
                'user_name': snapshot_data.get('user_name')
            }

            # 检查是否已存在同名快照，如果存在则替换
            existing_index = None
            for i, existing_snapshot in enumerate(user_snapshots[form_type]):
                if existing_snapshot['snapshot_name'] == snapshot_name:
                    existing_index = i
                    # 删除旧的快照数据
                    old_snapshot_key = FormSnapshotCacheManager.get_snapshot_key(
                        user_id, form_type, existing_snapshot['snapshot_id']
                    )
                    cache.delete(old_snapshot_key)
                    break

            if existing_index is not None:
                user_snapshots[form_type][existing_index] = snapshot_info
            else:
                user_snapshots[form_type].append(snapshot_info)

            # 保存更新后的索引
            cache.set(user_snapshots_key, user_snapshots, timeout=timeout)

            logger.debug(f"缓存表单快照: user_id={user_id}, form_type={form_type}, snapshot_id={snapshot_id}, name={snapshot_name}")
            return True

        except Exception as e:
            logger.error(f"缓存表单快照失败: user_id={user_id}, form_type={form_type}, error={str(e)}")
            return False

    @staticmethod
    def get_form_snapshot(user_id: int, form_type: str) -> Optional[Dict]:
        """获取表单类型的最新快照"""
        try:
            user_snapshots_key = FormSnapshotCacheManager.get_user_snapshots_key(user_id)
            user_snapshots = cache.get(user_snapshots_key) or {}

            if form_type not in user_snapshots or not user_snapshots[form_type]:
                return None

            # 获取最新的快照（按创建时间排序）
            snapshots = user_snapshots[form_type]
            if not snapshots:
                return None

            latest_snapshot_info = max(snapshots, key=lambda x: x.get('created_at', ''))
            snapshot_id = latest_snapshot_info['snapshot_id']

            # 获取快照详细数据
            snapshot_key = FormSnapshotCacheManager.get_snapshot_key(user_id, form_type, snapshot_id)
            snapshot_data = cache.get(snapshot_key)

            if snapshot_data is not None:
                logger.debug(f"命中表单快照缓存: user_id={user_id}, form_type={form_type}, snapshot_id={snapshot_id}")

            return snapshot_data

        except Exception as e:
            logger.error(f"获取表单快照缓存失败: user_id={user_id}, form_type={form_type}, error={str(e)}")
            return None

    @staticmethod
    def get_all_user_snapshots(user_id: int) -> List[Dict]:
        """获取用户所有快照列表"""
        try:
            user_snapshots_key = FormSnapshotCacheManager.get_user_snapshots_key(user_id)
            user_snapshots = cache.get(user_snapshots_key) or {}

            all_snapshots = []
            for form_type, snapshots in user_snapshots.items():
                for snapshot_info in snapshots:
                    all_snapshots.append({
                        'form_type': form_type,
                        'snapshot_name': snapshot_info['snapshot_name'],
                        'created_at': snapshot_info['created_at'],
                        'snapshot_id': snapshot_info['snapshot_id'],
                        'user_name': snapshot_info.get('user_name'),
                        'has_data': True
                    })

            # 按创建时间倒序排列
            all_snapshots.sort(key=lambda x: x.get('created_at', ''), reverse=True)

            logger.debug(f"获取用户所有快照: user_id={user_id}, count={len(all_snapshots)}")
            return all_snapshots

        except Exception as e:
            logger.error(f"获取用户所有快照失败: user_id={user_id}, error={str(e)}")
            return []

    @staticmethod
    def clear_form_snapshot(user_id: int, form_type: str = None) -> bool:
        """清除表单快照缓存"""
        try:
            if form_type:
                # 清除指定表单类型的所有快照
                user_snapshots_key = FormSnapshotCacheManager.get_user_snapshots_key(user_id)
                user_snapshots = cache.get(user_snapshots_key) or {}

                if form_type in user_snapshots:
                    # 删除所有快照数据
                    for snapshot_info in user_snapshots[form_type]:
                        snapshot_key = FormSnapshotCacheManager.get_snapshot_key(
                            user_id, form_type, snapshot_info['snapshot_id']
                        )
                        cache.delete(snapshot_key)

                    # 从索引中移除
                    del user_snapshots[form_type]
                    cache.set(user_snapshots_key, user_snapshots, timeout=CacheManager.get_timeout('form_snapshot'))

                logger.debug(f"清除表单快照缓存: user_id={user_id}, form_type={form_type}")
            else:
                # 清除用户所有快照
                user_snapshots_key = FormSnapshotCacheManager.get_user_snapshots_key(user_id)
                user_snapshots = cache.get(user_snapshots_key) or {}

                # 删除所有快照数据
                for form_type, snapshots in user_snapshots.items():
                    for snapshot_info in snapshots:
                        snapshot_key = FormSnapshotCacheManager.get_snapshot_key(
                            user_id, form_type, snapshot_info['snapshot_id']
                        )
                        cache.delete(snapshot_key)

                # 清除索引
                cache.delete(user_snapshots_key)
                logger.debug(f"清除用户所有表单快照: user_id={user_id}")

            return True
        except Exception as e:
            logger.error(f"清除表单快照缓存失败: user_id={user_id}, form_type={form_type}, error={str(e)}")
            return False


class HistoryRecordsCacheManager:
    """历史记录缓存管理"""

    @staticmethod
    def get_history_key(user_id: int = None, page: int = 1, per_page: int = 10, **filters) -> str:
        """获取历史记录缓存键"""
        # 构建过滤器字符串
        filter_str = '_'.join([f"{k}:{v}" for k, v in sorted(filters.items()) if v is not None])
        if user_id:
            return CacheManager.get_cache_key('history_records', user_id, page, per_page, filter_str)
        return CacheManager.get_cache_key('history_records', 'all', page, per_page, filter_str)

    @staticmethod
    def cache_history_records(records_data: Dict, user_id: int = None, page: int = 1, per_page: int = 10, **filters) -> bool:
        """缓存历史记录"""
        try:
            key = HistoryRecordsCacheManager.get_history_key(user_id, page, per_page, **filters)
            timeout = CacheManager.get_timeout('history_records')
            cache.set(key, records_data, timeout=timeout)
            logger.debug(f"缓存历史记录: user_id={user_id}, page={page}, per_page={per_page}")
            return True
        except Exception as e:
            logger.error(f"缓存历史记录失败: user_id={user_id}, error={str(e)}")
            return False

    @staticmethod
    def get_history_records(user_id: int = None, page: int = 1, per_page: int = 10, **filters) -> Optional[Dict]:
        """获取历史记录缓存"""
        try:
            key = HistoryRecordsCacheManager.get_history_key(user_id, page, per_page, **filters)
            records_data = cache.get(key)

            # 记录缓存统计
            from app.utils.performance_monitor import performance_monitor
            hit = records_data is not None
            performance_monitor.record_cache_hit('history_records', hit)

            if records_data is not None:
                logger.debug(f"命中历史记录缓存: user_id={user_id}, page={page}")
            return records_data
        except Exception as e:
            logger.error(f"获取历史记录缓存失败: user_id={user_id}, error={str(e)}")
            return None

    @staticmethod
    def clear_history_cache(user_id: int = None) -> bool:
        """清除历史记录缓存"""
        try:
            if user_id:
                # TODO: 清除指定用户的历史记录缓存
                logger.debug(f"清除用户历史记录缓存: user_id={user_id}")
            else:
                # TODO: 清除所有历史记录缓存
                logger.debug("清除所有历史记录缓存")
            return True
        except Exception as e:
            logger.error(f"清除历史记录缓存失败: user_id={user_id}, error={str(e)}")
            return False


class DuplicateCheckCacheManager:
    """重复提交检测缓存管理"""

    @staticmethod
    def get_duplicate_key(company_name: str, form_type: str) -> str:
        """获取重复检测缓存键"""
        # 使用公司名称和表单类型的哈希值作为键
        import hashlib
        key_data = f"{company_name}:{form_type}".encode('utf-8')
        hash_key = hashlib.md5(key_data).hexdigest()
        return CacheManager.get_cache_key('duplicate_check', hash_key)

    @staticmethod
    def mark_submission(company_name: str, form_type: str, submission_id: int) -> bool:
        """标记提交记录"""
        try:
            key = DuplicateCheckCacheManager.get_duplicate_key(company_name, form_type)
            timeout = CacheManager.get_timeout('duplicate_check')
            cache.set(key, submission_id, timeout=timeout)
            logger.debug(f"标记提交记录: company={company_name}, form_type={form_type}, id={submission_id}")
            return True
        except Exception as e:
            logger.error(f"标记提交记录失败: company={company_name}, form_type={form_type}, error={str(e)}")
            return False

    @staticmethod
    def check_duplicate(company_name: str, form_type: str) -> Optional[int]:
        """检查重复提交"""
        try:
            key = DuplicateCheckCacheManager.get_duplicate_key(company_name, form_type)
            submission_id = cache.get(key)
            if submission_id is not None:
                logger.debug(f"检测到重复提交: company={company_name}, form_type={form_type}, existing_id={submission_id}")
            return submission_id
        except Exception as e:
            logger.error(f"检查重复提交失败: company={company_name}, form_type={form_type}, error={str(e)}")
            return None

    @staticmethod
    def clear_duplicate_check(company_name: str, form_type: str) -> bool:
        """清除重复检测记录"""
        try:
            key = DuplicateCheckCacheManager.get_duplicate_key(company_name, form_type)
            cache.delete(key)
            logger.debug(f"清除重复检测记录: company={company_name}, form_type={form_type}")
            return True
        except Exception as e:
            logger.error(f"清除重复检测记录失败: company={company_name}, form_type={form_type}, error={str(e)}")
            return False


class RateLimitCacheManager:
    """限流缓存管理"""

    @staticmethod
    def get_rate_limit_key(identifier: str, action: str) -> str:
        """获取限流缓存键"""
        return CacheManager.get_cache_key('rate_limit', identifier, action)

    @staticmethod
    def check_rate_limit(identifier: str, action: str, limit: int, window: int) -> Dict:
        """检查限流状态"""
        try:
            key = RateLimitCacheManager.get_rate_limit_key(identifier, action)
            current_count = cache.get(key) or 0

            # 记录缓存统计
            from app.utils.performance_monitor import performance_monitor
            hit = current_count > 0  # 如果有计数记录，说明缓存命中
            performance_monitor.record_cache_hit('rate_limit', hit)

            if current_count >= limit:
                logger.warning(f"触发限流: identifier={identifier}, action={action}, count={current_count}, limit={limit}")
                return {
                    'allowed': False,
                    'current_count': current_count,
                    'limit': limit,
                    'reset_time': window
                }

            # 增加计数
            cache.set(key, current_count + 1, timeout=window)
            logger.debug(f"限流检查通过: identifier={identifier}, action={action}, count={current_count + 1}")

            return {
                'allowed': True,
                'current_count': current_count + 1,
                'limit': limit,
                'reset_time': window
            }
        except Exception as e:
            logger.error(f"限流检查失败: identifier={identifier}, action={action}, error={str(e)}")
            # 限流检查失败时允许通过
            return {'allowed': True, 'current_count': 0, 'limit': limit, 'reset_time': window}

    @staticmethod
    def reset_rate_limit(identifier: str, action: str) -> bool:
        """重置限流计数"""
        try:
            key = RateLimitCacheManager.get_rate_limit_key(identifier, action)
            cache.delete(key)
            logger.debug(f"重置限流计数: identifier={identifier}, action={action}")
            return True
        except Exception as e:
            logger.error(f"重置限流计数失败: identifier={identifier}, action={action}, error={str(e)}")
            return False


class FormTypeCacheManager:
    """表单类型缓存管理"""

    @staticmethod
    def get_form_types_list_key() -> str:
        """获取表单类型列表缓存键"""
        return CacheManager.get_cache_key('form_types', 'list')

    @staticmethod
    def cache_form_types_list(form_types_data: List[Dict]) -> bool:
        """缓存表单类型列表"""
        try:
            key = FormTypeCacheManager.get_form_types_list_key()
            timeout = CacheManager.get_timeout('form_types')
            cache.set(key, form_types_data, timeout=timeout)
            logger.debug(f"缓存表单类型列表: count={len(form_types_data)}")
            return True
        except Exception as e:
            logger.error(f"缓存表单类型列表失败: error={str(e)}")
            return False

    @staticmethod
    def get_form_types_list() -> Optional[List[Dict]]:
        """获取表单类型列表缓存"""
        try:
            key = FormTypeCacheManager.get_form_types_list_key()
            form_types_data = cache.get(key)

            # 记录缓存统计
            from app.utils.performance_monitor import performance_monitor
            hit = form_types_data is not None
            performance_monitor.record_cache_hit('form_types', hit)

            if form_types_data is not None:
                logger.debug(f"命中表单类型列表缓存: count={len(form_types_data)}")
            return form_types_data
        except Exception as e:
            logger.error(f"获取表单类型列表缓存失败: error={str(e)}")
            return None

    @staticmethod
    def clear_form_types_cache() -> bool:
        """清除表单类型相关缓存"""
        try:
            # 清除表单类型列表缓存
            list_key = FormTypeCacheManager.get_form_types_list_key()
            result1 = cache.delete(list_key)

            # 也尝试清除旧的缓存键（兼容性）
            result2 = cache.delete('form_types_list')

            # 直接使用Redis客户端清除（确保清除成功）
            if hasattr(cache.cache, '_write_client'):
                redis_client = cache.cache._write_client
                result3 = redis_client.delete(list_key)
                result4 = redis_client.delete('form_types_list')
                logger.debug(f"Redis直接删除结果: {list_key}={result3}, form_types_list={result4}")

            logger.info(f"清除表单类型缓存: Flask-Cache结果={result1},{result2}")
            return True
        except Exception as e:
            logger.error(f"清除表单类型缓存失败: error={str(e)}")
            return False


def clear_all_cache():
    """清除所有缓存"""
    try:
        cache.clear()
        logger.info("已清除所有缓存")
        return True
    except Exception as e:
        logger.error(f"清除所有缓存失败: {str(e)}")
        return False
