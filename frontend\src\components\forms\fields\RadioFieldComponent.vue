<template>
  <div class="radio-field-component">
    <div 
      v-for="(label, optionValue) in options" 
      :key="optionValue"
      class="form-check"
    >
      <input 
        :id="`${id}_${optionValue}`"
        type="radio" 
        :name="id"
        :value="optionValue"
        :checked="value === optionValue"
        :disabled="field.is_readonly"
        class="form-check-input"
        @change="handleChange"
      >
      <label :for="`${id}_${optionValue}`" class="form-check-label">
        {{ label }}
      </label>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RadioFieldComponent',
  props: {
    id: String,
    field: {
      type: Object,
      required: true
    },
    value: {
      type: [String, Number],
      default: ''
    }
  },
  emits: ['update:value', 'field-change'],
  computed: {
    options() {
      return this.field.field_options || {}
    }
  },
  methods: {
    handleChange(event) {
      const newValue = event.target.value
      this.$emit('update:value', newValue)
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: newValue,
        event: 'change'
      })
    }
  }
}
</script>

<style scoped>
.form-check {
  margin-bottom: 0.5rem;
}

.form-check:last-child {
  margin-bottom: 0;
}
</style>
