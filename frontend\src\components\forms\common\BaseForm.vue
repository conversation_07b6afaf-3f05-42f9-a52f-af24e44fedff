<template>
  <div :class="formClass">
    <!-- 悬浮球导航 - 统一处理 -->
    <floating-ball
      ref="floatingBall"
      :form-type="formType"
      @expand-all="expandAllSections"
      @collapse-all="collapseAllSections"
      @save-form="$emit('save-form')"
      @load-form="$emit('load-form')"
      @sections-updated="handleSectionsUpdated"
    />
    
    <!-- 基本信息部分 - 所有表单共用 -->
    <div id="basic-info-section">
      <common-basic-info
        v-model:company="formData.公司名称"
        v-model:date="formData.记录日期"
        v-model:editor="formData.编辑人"
        v-model:field-values="basicInfoValues"
        :basic-field-config="basicFieldConfig"
        :customer-field-config="customerFieldConfig"
        :deployment-field-config="deploymentFieldConfig"
        :form-type="formType"
        :key="refreshKey"
      />
    </div>

    <!-- 服务器信息部分 - 所有表单共用 -->
    <div id="server-info-section">
      <!-- 使用transition添加切换动画 -->
      <transition name="server-view-fade" mode="out-in">
        <!-- 卡片模式 -->
        <server-info-section
          v-if="serverViewMode === 'card'"
          v-model="formData.服务器信息"
          :component-groups="componentGroups"
          :document-type="formType"
          :view-mode="serverViewMode"
          :key="refreshKey"
          @architecture-changed="handleArchitectureChanged"
          @refresh-components="$emit('refresh-components')"
          @show-toast="showToast"
          @update:view-mode="serverViewMode = $event"
        />

        <!-- 表格模式 -->
        <server-info-table
          v-else
          v-model="formData.服务器信息"
          :component-groups="componentGroups"
          :document-type="formType"
          :view-mode="serverViewMode"
          :key="refreshKey + '-table'"
          @refresh-components="$emit('refresh-components')"
          @show-toast="showToast"
          @update:view-mode="serverViewMode = $event"
        />
      </transition>
    </div>

    <!-- 访问信息部分 - 通过配置动态渲染 -->
    <div id="access-info-section">
      <dynamic-access-info-section
        v-model="accessInfoData"
        :field-config="accessFieldConfig"
        :form-type="formType"
        :server-list="formData.服务器信息"
        :key="refreshKey"
      >
        <!-- 自动填充提示信息插槽 -->
        <template #autoFillHints>
          <slot name="access-info-hints"></slot>
        </template>
      </dynamic-access-info-section>
    </div>

    <!-- 自定义部分插槽 - 允许子组件添加特殊部分 -->
    <slot name="custom-sections"></slot>

    <!-- 维护记录部分 - 所有表单共用 -->
    <div id="maintenance-record-section">
      <multi-maintenance-record-section
        v-model="formData.维护记录"
        :key="refreshKey"
      />
    </div>

    <!-- Toast通知组件 -->
    <toast-notification ref="toast" />
  </div>
</template>

<script>
import { computed, ref, onMounted, watch, nextTick } from 'vue'
import { useFormData } from '@/composables/useFormData'
import { useFormMethods } from '@/composables/useFormMethods'
import {
  getBasicInfoFieldsByFormType,
  getCustomerInfoFieldsByFormType,
  getDeploymentInfoFieldsByFormType
} from '@/config/formFields'

// 组件导入
import CommonBasicInfo from './CommonBasicInfo.vue'
import ServerInfoSection from './ServerInfoSection.vue'
import ServerInfoTable from './ServerInfoTable.vue'
import DynamicAccessInfoSection from './DynamicAccessInfoSection.vue'
import MultiMaintenanceRecordSection from './MultiMaintenanceRecordSection.vue'
import FloatingBall from './FloatingBall.vue'
import ToastNotification from '../../common/ToastNotification.vue'

/**
 * 基础表单组件
 * 提供所有表单类型的通用功能和结构
 * 通过插槽机制支持特殊需求的定制
 */
export default {
  name: 'BaseForm',
  components: {
    CommonBasicInfo,
    ServerInfoSection,
    ServerInfoTable,
    DynamicAccessInfoSection,
    MultiMaintenanceRecordSection,
    FloatingBall,
    ToastNotification
  },
  props: {
    modelValue: { 
      type: Object, 
      required: true 
    },
    formType: { 
      type: String, 
      required: true 
    },
    componentGroups: { 
      type: Object, 
      required: true 
    }
  },
  emits: ['update:modelValue', 'save-form', 'load-form', 'refresh-components'],
  setup(props, { emit }) {
    const toast = ref(null)
    const floatingBall = ref(null)

    // 服务器信息显示模式
    const serverViewMode = ref(localStorage.getItem('server-view-mode') || 'table')

    // 防止循环更新的标志
    const isUpdatingFromParent = ref(false)

    // 使用统一的表单数据处理
    const {
      formData,
      fieldConfig,
      basicInfoValues,
      accessInfoData,
      validateForm
    } = useFormData(props.formType, props.modelValue)

    // 使用统一的表单方法
    const {
      refreshKey,
      expandAllSections,
      collapseAllSections,
      refreshSections,
      showToast,
      handleSectionsUpdated,
      onArchitectureChanged,
      setToastRef
    } = useFormMethods(props.formType)

    // 计算表单样式类
    const formClass = computed(() => {
      const baseClass = 'base-form'
      const typeClass = props.formType.toLowerCase().replace(/\s+/g, '-') + '-form'
      return `${baseClass} ${typeClass}`
    })

    // 获取基本信息字段配置
    const basicFieldConfig = computed(() =>
      getBasicInfoFieldsByFormType(props.formType)
    )

    const customerFieldConfig = computed(() =>
      getCustomerInfoFieldsByFormType(props.formType)
    )

    const deploymentFieldConfig = computed(() =>
      getDeploymentInfoFieldsByFormType(props.formType)
    )

    // 获取访问信息字段配置
    const accessFieldConfig = computed(() => 
      fieldConfig.value.access || {}
    )

    // 监听表单数据变化，向上传递
    const handleFormDataChange = (newData) => {
      emit('update:modelValue', newData)
    }

    // 处理特殊的架构变化逻辑（主要用于安全监测）
    const handleArchitectureChanged = (event) => {
      onArchitectureChanged(event)
      
      // 如果是安全监测表单，处理kibana认证信息
      if (props.formType === '安全监测') {
        const { serverIndex, architecture } = event
        
        // 检查是否有ARM架构的服务器
        const hasArmServer = formData.value.服务器信息?.some(server => 
          server.系统架构 === 'arm'
        )

        // 根据是否有ARM架构服务器来设置kibana认证信息
        if (hasArmServer) {
          formData.value.kibana认证信息 = ''
        } else {
          formData.value.kibana认证信息 = 'elastic:beap123'
        }

        console.log(`Kibana认证信息已更新为: ${formData.value.kibana认证信息 || '(空)'}`)
      }
    }

    // 手动刷新悬浮球sections
    const refreshSidebarSections = () => {
      if (floatingBall.value && typeof floatingBall.value.refreshSections === 'function') {
        floatingBall.value.refreshSections()
      }
    }

    // 监听表单类型变化，强制刷新组件
    watch(() => props.formType, (newFormType, oldFormType) => {
      if (newFormType !== oldFormType) {
        console.log(`BaseForm: 表单类型从 ${oldFormType} 变更为 ${newFormType}`)
        refreshSections()
      }
    })

    // 监听组件分组数据变化
    watch(() => props.componentGroups, (newGroups, oldGroups) => {
      console.log('BaseForm: 组件分组数据变化', {
        newGroups,
        oldGroups,
        formType: props.formType
      })

      // 检查当前表单类型的组件数据
      const formTypeMapping = {
        '安全监测': 'security',
        '安全测评': 'testing',
        '应用加固': 'hardening'
      }

      const formTypeKey = formTypeMapping[props.formType]
      if (formTypeKey && newGroups && newGroups[formTypeKey]) {
        console.log(`BaseForm: ${props.formType} 的组件数据已加载:`, newGroups[formTypeKey])
        refreshSections()
      }
    }, { deep: true })

    // 监听modelValue变化
    watch(() => props.modelValue, (newData) => {
      if (newData && typeof newData === 'object') {
        console.log('BaseForm: 接收到新的表单数据', newData)
        // 标记正在从父组件更新，避免循环
        isUpdatingFromParent.value = true
        // 使用 nextTick 确保更新完成后重置标志
        nextTick(() => {
          isUpdatingFromParent.value = false
        })
      }
    }, { deep: true })

    // 监听内部formData变化，向上传递
    watch(formData, (newFormData) => {
      // 只有在不是从父组件更新时才向上传递
      if (!isUpdatingFromParent.value) {
        console.log('BaseForm: 内部表单数据变化，向上传递', newFormData)
        emit('update:modelValue', newFormData)
      } else {
        console.log('BaseForm: 跳过向上传递，因为正在从父组件更新')
      }
    }, { deep: true })

    // 监听服务器显示模式变化，保存到本地存储
    watch(serverViewMode, (newMode) => {
      localStorage.setItem('server-view-mode', newMode)
      console.log('BaseForm: 服务器显示模式切换为:', newMode)
    })

    // 组件挂载后设置Toast引用
    onMounted(() => {
      if (toast.value) {
        setToastRef(toast.value)
      }

      console.log('BaseForm 挂载完成:', {
        formType: props.formType,
        basicFieldConfig: basicFieldConfig.value,
        customerFieldConfig: customerFieldConfig.value,
        deploymentFieldConfig: deploymentFieldConfig.value
      })
    })

    return {
      // 数据
      formData,
      basicInfoValues,
      accessInfoData,
      refreshKey,
      serverViewMode,

      // 配置
      basicFieldConfig,
      customerFieldConfig,
      deploymentFieldConfig,
      accessFieldConfig,
      formClass,

      // 方法
      expandAllSections,
      collapseAllSections,
      refreshSections,
      showToast,
      handleSectionsUpdated,
      handleArchitectureChanged,
      refreshSidebarSections,
      validateForm,
      handleFormDataChange,

      // 引用
      toast,
      floatingBall
    }
  }
}
</script>

<style scoped>
.base-form {
  position: relative;
}

/* 不同表单类型的主题色 */
.安全测评-form .card {
  border-left: 4px solid #28a745; /* 绿色边框 */
}

.安全测评-form .card-header {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.安全监测-form .card {
  border-left: 4px solid #007bff; /* 蓝色边框 */
}

.安全监测-form .card-header {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.应用加固-form .card {
  border-left: 4px solid #ff9800; /* 橙色边框 */
}

.应用加固-form .card-header {
  background-color: #fff3e0;
  color: #e65100;
}

/* 通用卡片样式 */
.base-form .card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.base-form .card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.base-form .card-header {
  font-weight: bold;
}

/* 响应式布局调整 */
@media (min-width: 768px) {
  .base-form {
    padding-right: 15px;
  }
}

/* 服务器视图切换动画 */
.server-view-fade-enter-active,
.server-view-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.server-view-fade-enter-from {
  opacity: 0;
  transform: translateX(30px) scale(0.95);
}

.server-view-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px) scale(0.95);
}

.server-view-fade-enter-to,
.server-view-fade-leave-from {
  opacity: 1;
  transform: translateX(0) scale(1);
}

/* 标题栏中的切换按钮样式 */
.server-view-controls-header {
  margin-left: auto;
  margin-right: 10px; /* 为折叠箭头留出空间 */
}

.server-view-controls-header .btn-group {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.server-view-controls-header .btn-outline-primary {
  border-color: #007bff;
  color: #007bff;
  transition: all 0.3s ease;
  font-size: 0.75rem;
  padding: 0.375rem 0.5rem;
  border-radius: 0;
  border-width: 1px;
  white-space: nowrap;
  min-width: 60px;
  background: white;
}

.server-view-controls-header .btn-outline-primary:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

.server-view-controls-header .btn-outline-primary:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

.server-view-controls-header .btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

.server-view-controls-header .btn-check:checked + .btn-outline-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.server-view-controls-header .btn-outline-primary i {
  transition: transform 0.2s ease;
}

.server-view-controls-header .btn-outline-primary:hover i {
  transform: scale(1.1);
}

/* 确保卡片头部有相对定位 */
.card-header {
  position: relative;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .server-view-controls-header .btn-outline-primary {
    min-width: 50px;
    padding: 0.25rem 0.4rem;
    font-size: 0.7rem;
  }

  .server-view-controls-header .btn-outline-primary i {
    margin-right: 0.25rem !important;
  }
}
</style>
