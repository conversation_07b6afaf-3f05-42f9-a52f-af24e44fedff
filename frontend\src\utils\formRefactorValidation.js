/**
 * 表单重构验证工具
 * 用于验证重构后的表单组件是否正常工作
 */

/**
 * 验证表单组件的基本功能
 * @param {Object} formComponent - 表单组件实例
 * @param {string} formType - 表单类型
 * @returns {Object} 验证结果
 */
export function validateFormComponent(formComponent, formType) {
  const results = {
    formType,
    passed: 0,
    failed: 0,
    errors: [],
    warnings: [],
    details: []
  }

  try {
    // 1. 验证组件是否正确挂载
    if (!formComponent) {
      results.errors.push('表单组件未正确挂载')
      results.failed++
    } else {
      results.details.push('✓ 表单组件挂载成功')
      results.passed++
    }

    // 2. 验证是否使用了BaseForm
    const hasBaseForm = formComponent.$children?.some(child => 
      child.$options.name === 'BaseForm'
    ) || formComponent.$refs?.baseForm

    if (hasBaseForm) {
      results.details.push('✓ 使用了BaseForm基础组件')
      results.passed++
    } else {
      results.warnings.push('未检测到BaseForm组件，可能仍在使用旧的实现')
    }

    // 3. 验证表单数据绑定
    if (formComponent.formData) {
      results.details.push('✓ 表单数据绑定正常')
      results.passed++
    } else {
      results.errors.push('表单数据绑定异常')
      results.failed++
    }

    // 4. 验证必要的方法是否存在
    const requiredMethods = ['expandAllSections', 'collapseAllSections']
    const missingMethods = requiredMethods.filter(method => 
      typeof formComponent[method] !== 'function'
    )

    if (missingMethods.length === 0) {
      results.details.push('✓ 必要方法都存在')
      results.passed++
    } else {
      results.errors.push(`缺少必要方法: ${missingMethods.join(', ')}`)
      results.failed++
    }

    // 5. 验证事件发射器
    const requiredEmits = ['update:modelValue', 'save-form', 'load-form']
    const hasEmits = requiredEmits.every(emit => 
      formComponent.$options.emits?.includes(emit)
    )

    if (hasEmits) {
      results.details.push('✓ 事件发射器配置正确')
      results.passed++
    } else {
      results.warnings.push('事件发射器配置可能不完整')
    }

  } catch (error) {
    results.errors.push(`验证过程中发生错误: ${error.message}`)
    results.failed++
  }

  return results
}

/**
 * 验证表单字段配置
 * @param {string} formType - 表单类型
 * @returns {Object} 验证结果
 */
export function validateFormFieldConfig(formType) {
  const results = {
    formType,
    passed: 0,
    failed: 0,
    errors: [],
    warnings: [],
    details: []
  }

  try {
    // 动态导入配置
    import('@/config/formFieldConfig').then(({ FORM_FIELD_CONFIGS, getFormFieldConfig }) => {
      const config = getFormFieldConfig(formType)
      
      if (config && Object.keys(config).length > 0) {
        results.details.push('✓ 表单字段配置存在')
        results.passed++

        // 验证基本信息配置
        if (config.basic) {
          results.details.push('✓ 基本信息字段配置存在')
          results.passed++
        } else {
          results.warnings.push('基本信息字段配置缺失')
        }

        // 验证访问信息配置
        if (config.access) {
          results.details.push('✓ 访问信息字段配置存在')
          results.passed++
        } else {
          results.warnings.push('访问信息字段配置缺失')
        }

        // 验证验证规则配置
        if (config.validation) {
          results.details.push('✓ 验证规则配置存在')
          results.passed++
        } else {
          results.warnings.push('验证规则配置缺失')
        }

      } else {
        results.errors.push(`表单类型 "${formType}" 的配置不存在`)
        results.failed++
      }
    }).catch(error => {
      results.errors.push(`加载配置文件失败: ${error.message}`)
      results.failed++
    })

  } catch (error) {
    results.errors.push(`验证配置过程中发生错误: ${error.message}`)
    results.failed++
  }

  return results
}

/**
 * 生成重构验证报告
 * @param {Array} validationResults - 验证结果数组
 * @returns {string} 格式化的报告
 */
export function generateValidationReport(validationResults) {
  let report = '\n=== 表单重构验证报告 ===\n\n'
  
  let totalPassed = 0
  let totalFailed = 0
  let totalWarnings = 0

  validationResults.forEach(result => {
    report += `📋 ${result.formType}\n`
    report += `   通过: ${result.passed} | 失败: ${result.failed} | 警告: ${result.warnings?.length || 0}\n`
    
    if (result.details?.length > 0) {
      report += '   详情:\n'
      result.details.forEach(detail => {
        report += `     ${detail}\n`
      })
    }

    if (result.errors?.length > 0) {
      report += '   ❌ 错误:\n'
      result.errors.forEach(error => {
        report += `     ${error}\n`
      })
    }

    if (result.warnings?.length > 0) {
      report += '   ⚠️ 警告:\n'
      result.warnings.forEach(warning => {
        report += `     ${warning}\n`
      })
    }

    report += '\n'
    
    totalPassed += result.passed
    totalFailed += result.failed
    totalWarnings += (result.warnings?.length || 0)
  })

  report += `📊 总计: 通过 ${totalPassed} | 失败 ${totalFailed} | 警告 ${totalWarnings}\n`
  
  const successRate = totalPassed / (totalPassed + totalFailed) * 100
  report += `✅ 成功率: ${successRate.toFixed(1)}%\n`

  if (totalFailed === 0) {
    report += '\n🎉 重构验证通过！所有表单组件都正常工作。\n'
  } else {
    report += '\n⚠️ 发现问题，请检查上述错误并修复。\n'
  }

  return report
}

/**
 * 快速验证所有表单类型
 * @returns {Promise<string>} 验证报告
 */
export async function quickValidateAllForms() {
  const formTypes = ['安全测评', '安全监测', '应用加固']
  const results = []

  for (const formType of formTypes) {
    const configResult = validateFormFieldConfig(formType)
    results.push(configResult)
  }

  return generateValidationReport(results)
}

/**
 * 代码重复检测
 * @param {Array} formComponents - 表单组件数组
 * @returns {Object} 重复检测结果
 */
export function detectCodeDuplication(formComponents) {
  const duplications = {
    methods: {},
    computed: {},
    data: {},
    total: 0
  }

  // 检测方法重复
  const methodNames = new Set()
  formComponents.forEach(component => {
    if (component.$options.methods) {
      Object.keys(component.$options.methods).forEach(methodName => {
        if (methodNames.has(methodName)) {
          if (!duplications.methods[methodName]) {
            duplications.methods[methodName] = 0
          }
          duplications.methods[methodName]++
          duplications.total++
        } else {
          methodNames.add(methodName)
        }
      })
    }
  })

  return duplications
}
