<template>
  <div class="access-info-style-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-person-lock me-2"></i>
                访问信息样式一致性测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 表单类型选择 -->
              <div class="row mb-4">
                <div class="col-md-4">
                  <label class="form-label fw-bold">选择表单类型</label>
                  <select v-model="selectedFormType" class="form-select" @change="loadFormType">
                    <option value="">请选择表单类型</option>
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">预期结果</label>
                  <div class="small">
                    <div v-if="selectedFormType === '安全测评'">独立的用户账号密码输入框</div>
                    <div v-else-if="selectedFormType === '安全监测'">独立的用户账号密码输入框</div>
                    <div v-else-if="selectedFormType === '应用加固'" class="text-success">修改后：独立的用户账号密码输入框</div>
                    <div v-else class="text-muted">请选择表单类型</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">操作</label>
                  <div class="d-flex gap-2">
                    <button 
                      class="btn btn-warning btn-sm" 
                      @click="forceRefresh"
                    >
                      强制刷新
                    </button>
                    <button 
                      class="btn btn-info btn-sm" 
                      @click="showDebugInfo = !showDebugInfo"
                    >
                      {{ showDebugInfo ? '隐藏' : '显示' }}调试
                    </button>
                  </div>
                </div>
              </div>

              <!-- 测试说明 -->
              <div class="alert alert-info mb-4">
                <h6 class="alert-heading">测试说明</h6>
                <p class="mb-2">此测试用于验证应用加固表单的访问信息样式是否与其他表单保持一致：</p>
                <ul class="mb-0">
                  <li><strong>修改前</strong>：应用加固使用textarea和object类型的复合字段</li>
                  <li><strong>修改后</strong>：应用加固使用独立的账号密码输入框，与安全测评、安全监测保持一致</li>
                  <li><strong>验证点</strong>：所有表单类型的访问信息都应该使用相同的输入框样式</li>
                </ul>
              </div>

              <!-- 调试信息 -->
              <div v-if="showDebugInfo && selectedFormType" class="row mb-4">
                <div class="col-12">
                  <div class="card bg-light">
                    <div class="card-header">
                      <h6 class="mb-0">调试信息 - {{ selectedFormType }}</h6>
                    </div>
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-6">
                          <h6 class="text-primary">访问信息字段配置</h6>
                          <pre class="small">{{ JSON.stringify(accessFieldConfig, null, 2) }}</pre>
                        </div>
                        <div class="col-md-6">
                          <h6 class="text-success">表单数据</h6>
                          <pre class="small">{{ JSON.stringify(testFormData, null, 2) }}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 实际表单测试 -->
              <div v-if="selectedFormType" class="row">
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">{{ selectedFormType }} - 访问信息样式测试</h6>
                    </div>
                    <div class="card-body">
                      
                      <!-- 使用DynamicAccessInfoSection测试 -->
                      <div class="mb-4">
                        <h6 class="text-primary">使用DynamicAccessInfoSection组件</h6>
                        <dynamic-access-info-section
                          v-model="accessInfoData"
                          :form-type="selectedFormType"
                          :key="`access-info-${selectedFormType}-${refreshKey}`"
                        />
                      </div>

                      <!-- 字段对比分析 -->
                      <div class="row">
                        <div class="col-md-6">
                          <h6 class="text-info">字段类型分析</h6>
                          <div class="small">
                            <div v-for="(field, key) in accessFieldConfig" :key="key" class="mb-2">
                              <div class="d-flex justify-content-between align-items-center">
                                <strong>{{ field.field }}</strong>
                                <span class="badge" :class="getFieldTypeBadgeClass(field.type)">
                                  {{ field.type }}
                                </span>
                              </div>
                              <div class="text-muted small">
                                {{ field.placeholder || '无占位符' }}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <h6 class="text-warning">样式一致性检查</h6>
                          <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                              <span>使用独立输入框</span>
                              <span v-if="hasIndependentFields" class="badge bg-success">✓ 通过</span>
                              <span v-else class="badge bg-danger">✗ 失败</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                              <span>密码字段有切换按钮</span>
                              <span v-if="hasPasswordToggle" class="badge bg-success">✓ 通过</span>
                              <span v-else class="badge bg-warning">- 部分</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                              <span>避免复合字段</span>
                              <span v-if="!hasComplexFields" class="badge bg-success">✓ 通过</span>
                              <span v-else class="badge bg-danger">✗ 失败</span>
                            </div>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicAccessInfoSection from '@/components/forms/common/DynamicAccessInfoSection.vue'
import { getFormFieldConfig } from '@/config/formFieldConfig'
import { getInitialFormData } from '@/config/formDataConfig'

export default {
  name: 'AccessInfoStyleTest',
  components: {
    DynamicAccessInfoSection
  },
  data() {
    return {
      selectedFormType: '',
      refreshKey: 0,
      showDebugInfo: false,
      testFormData: {},
      accessInfoData: {},
      accessFieldConfig: {}
    }
  },
  computed: {
    /**
     * 检查是否有独立的字段
     */
    hasIndependentFields() {
      const fields = Object.values(this.accessFieldConfig)
      return fields.some(field => 
        field.type === 'text' || field.type === 'password'
      )
    },

    /**
     * 检查是否有密码切换功能
     */
    hasPasswordToggle() {
      const fields = Object.values(this.accessFieldConfig)
      return fields.some(field => 
        field.type === 'password' && field.showToggle
      )
    },

    /**
     * 检查是否有复合字段
     */
    hasComplexFields() {
      const fields = Object.values(this.accessFieldConfig)
      return fields.some(field => 
        field.type === 'textarea' || field.type === 'object'
      )
    }
  },
  methods: {
    /**
     * 加载表单类型
     */
    loadFormType() {
      if (!this.selectedFormType) return

      console.log(`加载表单类型: ${this.selectedFormType}`)

      // 获取字段配置
      const config = getFormFieldConfig(this.selectedFormType)
      this.accessFieldConfig = config.access || {}

      // 获取初始表单数据
      this.testFormData = getInitialFormData(this.selectedFormType)

      // 设置访问信息数据
      this.updateAccessInfoData()

      // 强制刷新组件
      this.refreshKey += 1

      console.log('表单类型加载完成:', {
        formType: this.selectedFormType,
        accessFieldConfig: this.accessFieldConfig,
        testFormData: this.testFormData
      })
    },

    /**
     * 更新访问信息数据
     */
    updateAccessInfoData() {
      const data = {}

      Object.keys(this.accessFieldConfig).forEach(key => {
        const field = this.accessFieldConfig[key]
        const fieldName = field.field
        data[key] = this.testFormData[fieldName] || field.default || ''
      })

      this.accessInfoData = data
    },

    /**
     * 获取字段类型的徽章样式
     */
    getFieldTypeBadgeClass(type) {
      const classes = {
        'text': 'bg-primary',
        'password': 'bg-success',
        'textarea': 'bg-warning',
        'object': 'bg-danger'
      }
      return classes[type] || 'bg-secondary'
    },

    /**
     * 强制刷新
     */
    forceRefresh() {
      this.refreshKey += 1
      this.loadFormType()
      console.log('强制刷新完成, refreshKey:', this.refreshKey)
    }
  },
  watch: {
    // 监听访问信息数据变化
    accessInfoData: {
      handler(newData) {
        console.log('访问信息数据变化:', newData)
        
        // 同步回表单数据
        Object.keys(this.accessFieldConfig).forEach(key => {
          const field = this.accessFieldConfig[key]
          const fieldName = field.field
          if (fieldName && newData[key] !== undefined) {
            this.testFormData[fieldName] = newData[key]
          }
        })
      },
      deep: true
    }
  },
  mounted() {
    console.log('AccessInfoStyleTest 组件已挂载')
  }
}
</script>

<style scoped>
.access-info-style-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 2px solid #e9ecef;
}

pre {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #0dcaf0 !important; }
.text-danger { color: #dc3545 !important; }

.list-group-item {
  border: 1px solid #dee2e6;
}

.badge {
  font-size: 0.75rem;
}
</style>
