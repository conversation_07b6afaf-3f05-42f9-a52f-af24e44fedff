<template>
  <div class="form-template-manager">
    <div class="container-fluid">
      <!-- 页面标题 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="page-title mb-1">
                    <i class="bi bi-file-earmark-text me-2"></i>
                    表单模板管理
                  </h2>
                  <p class="text-muted mb-0">统一管理表单类型、组件配置、Excel模板和字段结构</p>
                </div>
                <div>
                  <button class="btn btn-outline-info me-2" @click="clearCache">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    刷新缓存
                  </button>
                  <button class="btn btn-success me-2" @click="showOneClickInitModal">
                    <i class="bi bi-magic me-1"></i>
                    一键初始化表单
                  </button>
                  <button class="btn btn-primary" @click="showAddFormTypeModal">
                    <i class="bi bi-plus-circle me-1"></i>
                    添加表单类型
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单类型管理 -->
      <div class="row">
        <div class="col-12">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul me-2"></i>
                    表单类型列表
                  </h5>
                </div>
                <div class="card-body">
                  <div v-if="loading" class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">加载中...</span>
                    </div>
                  </div>
                  
                  <div v-else-if="formTypes.length === 0" class="text-center py-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                    <h5 class="text-muted mt-3">暂无表单类型</h5>
                    <p class="text-muted">点击"添加表单类型"来创建第一个表单类型</p>
                  </div>
                  
                  <div v-else class="table-responsive">
                    <table class="table table-hover">
                      <thead>
                        <tr>
                          <th>表单类型名称</th>
                          <th>描述</th>
                          <th>组件数量</th>
                          <th>模板状态</th>
                          <th>创建时间</th>
                          <th>操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="formType in formTypes" :key="formType.id">
                          <td>
                            <strong>{{ formType.name }}</strong>
                            <span v-if="isBuiltInFormType(formType.name)" class="badge bg-info ms-2">内置</span>
                          </td>
                          <td>{{ formType.description || '无描述' }}</td>
                          <td>
                            <span class="badge bg-primary">{{ getComponentCount(formType.name) }}</span>
                          </td>
                          <td>
                            <span :class="getTemplateStatusBadge(formType.name)">
                              {{ getTemplateStatus(formType.name) }}
                            </span>
                          </td>
                          <td>{{ formatDate(formType.created_at) }}</td>
                          <td>
                            <div class="btn-group btn-group-sm">
                              <button class="btn btn-outline-primary" @click="switchToComponentsTab(formType.name)" title="组件配置">
                                <i class="bi bi-puzzle"></i>
                              </button>
                              <button class="btn btn-outline-success" @click="switchToTemplatesTab(formType.name)" title="Excel模板">
                                <i class="bi bi-file-earmark-excel"></i>
                              </button>
                              <button class="btn btn-outline-info" @click="goToDetailedFieldConfig(formType.name)" title="字段配置">
                                <i class="bi bi-input-cursor"></i>
                              </button>
                              <button class="btn btn-outline-warning" @click="editFormType(formType)" title="编辑">
                                <i class="bi bi-pencil"></i>
                              </button>
                              <button 
                                class="btn btn-outline-danger" 
                                @click="deleteFormType(formType)" 
                                title="删除"
                                :disabled="isBuiltInFormType(formType.name) || !formType.id"
                              >
                                <i class="bi bi-trash"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
        </div>
      </div>
    </div>

    <!-- 添加表单类型模态框 -->
    <div class="modal fade" id="addFormTypeModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ editingFormType ? '编辑表单类型' : '添加表单类型' }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveFormType">
              <div class="mb-3">
                <label for="formTypeName" class="form-label">表单类型名称 *</label>
                <input
                  type="text"
                  class="form-control"
                  id="formTypeName"
                  v-model="formTypeForm.name"
                  :disabled="editingFormType && isBuiltInFormType(formTypeForm.name)"
                  required
                >
                <div class="form-text">
                  表单类型的唯一标识，建议使用中文名称
                </div>
              </div>
              <div class="mb-3">
                <label for="formTypeDescription" class="form-label">描述</label>
                <textarea
                  class="form-control"
                  id="formTypeDescription"
                  v-model="formTypeForm.description"
                  rows="3"
                  placeholder="描述该表单类型的用途和特点"
                ></textarea>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" @click="saveFormType">
              {{ editingFormType ? '更新' : '添加' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 一键初始化表单模态框 -->
    <div class="modal fade" id="oneClickInitModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header bg-success text-white">
            <h5 class="modal-title">
              <i class="bi bi-magic me-2"></i>
              一键初始化表单
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              <strong>一键初始化功能说明：</strong>
              <ul class="mb-0 mt-2">
                <li>自动创建新的表单类型</li>
                <li>初始化完整的基础字段配置（基本信息、客户信息、访问信息、服务器信息、维护记录）</li>
                <li>创建默认的字段分组结构</li>
                <li>包含所有常用字段，可根据需要删除或修改</li>
              </ul>
            </div>

            <form @submit.prevent="oneClickInitForm">
              <div class="mb-3">
                <label for="newFormTypeName" class="form-label">新表单类型名称 *</label>
                <input
                  type="text"
                  class="form-control"
                  id="newFormTypeName"
                  v-model="oneClickForm.name"
                  placeholder="请输入新表单类型名称，如：网络安全评估"
                  required
                >
                <div class="form-text">
                  将自动创建该表单类型并初始化完整的字段配置
                </div>
              </div>
              <div class="mb-3">
                <label for="newFormTypeDescription" class="form-label">描述（可选）</label>
                <textarea
                  class="form-control"
                  id="newFormTypeDescription"
                  v-model="oneClickForm.description"
                  rows="2"
                  placeholder="描述该表单类型的用途和特点"
                ></textarea>
              </div>
            </form>

            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle me-2"></i>
              <strong>注意：</strong>不能使用内置表单类型的名称（安全测评、安全监测、应用加固）
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-success" @click="oneClickInitForm" :disabled="oneClickInitializing">
              <span v-if="oneClickInitializing">
                <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                初始化中...
              </span>
              <span v-else>
                <i class="bi bi-magic me-1"></i>
                开始初始化
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { Modal } from 'bootstrap'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'FormTemplateManager',
  setup() {
    const router = useRouter()

    // 响应式数据
    const formTypes = ref([])
    const componentStats = ref({})
    const templateStats = ref({})
    const loading = ref(false)
    const editingFormType = ref(null)

    // 表单类型表单数据
    const formTypeForm = reactive({
      name: '',
      description: ''
    })

    // 一键初始化表单数据
    const oneClickForm = reactive({
      name: '',
      description: ''
    })
    const oneClickInitializing = ref(false)

    // 内置表单类型（不可删除）
    const builtInFormTypes = ['安全测评', '安全监测', '应用加固']

    // 计算属性
    const isBuiltInFormType = (name) => {
      return builtInFormTypes.includes(name)
    }

    // 智能获取组件分类的方法
    const getComponentCategory = (component) => {
      return component.category_key ||
             component.component_type ||
             component.category ||
             component.type ||
             component.group_name ||
             component.group_key ||
             'other'
    }

    // 方法
    const loadFormTypes = async () => {
      try {
        loading.value = true
        const response = await axios.get('/excel/form-types')

        if (response.data.status === 'success') {
          formTypes.value = response.data.data
          await loadStatistics()
        } else {
          console.error('表单类型API返回错误:', response.data.message)
          alert('加载表单类型失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('加载表单类型失败:', error)
        alert('加载表单类型失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }

    // 加载统计数据
    const loadStatistics = async () => {
      try {
        const [componentResponse, templateResponse] = await Promise.all([
          axios.get('/excel/components/statistics'),
          axios.get('/excel/templates/statistics')
        ])

        if (componentResponse.data.status === 'success') {
          componentStats.value = componentResponse.data.data
        }

        if (templateResponse.data.status === 'success') {
          templateStats.value = templateResponse.data.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    }

    const getComponentCount = (formTypeName) => {
      return componentStats.value[formTypeName] || 0
    }

    const getTemplateStatus = (formTypeName) => {
      const stats = templateStats.value[formTypeName]
      if (!stats) return '未配置'
      return stats.hasActiveTemplate ? '已配置' : '未配置'
    }

    const getTemplateStatusBadge = (formTypeName) => {
      const status = getTemplateStatus(formTypeName)
      return status === '已配置' ? 'badge bg-success' : 'badge bg-secondary'
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleString('zh-CN')
    }

    const formatTemplateDate = (timestamp) => {
      if (!timestamp) return '未知'
      // 如果是时间戳（秒），转换为毫秒
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN')
    }

    // 表单类型管理方法
    const showAddFormTypeModal = () => {
      editingFormType.value = null
      formTypeForm.name = ''
      formTypeForm.description = ''

      const modal = new Modal(document.getElementById('addFormTypeModal'))
      modal.show()
    }

    const editFormType = (formType) => {
      editingFormType.value = formType
      formTypeForm.name = formType.name
      formTypeForm.description = formType.description || ''

      const modal = new Modal(document.getElementById('addFormTypeModal'))
      modal.show()
    }

    const saveFormType = async () => {
      try {
        const url = editingFormType.value
          ? `/excel/form-types/${editingFormType.value.id}`
          : '/excel/form-types'

        const method = editingFormType.value ? 'put' : 'post'

        const response = await axios[method](url, {
          name: formTypeForm.name,
          description: formTypeForm.description
        })

        if (response.data.status === 'success') {
          const modal = Modal.getInstance(document.getElementById('addFormTypeModal'))
          modal.hide()

          await loadFormTypes()
          alert(editingFormType.value ? '表单类型更新成功' : '表单类型添加成功')
        } else {
          alert('操作失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('保存表单类型失败:', error)
        alert('操作失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const deleteFormType = async (formType) => {
      if (isBuiltInFormType(formType.name)) {
        alert('内置表单类型不能删除')
        return
      }

      if (!confirm(`确定要删除表单类型"${formType.name}"吗？此操作不可恢复。`)) {
        return
      }

      try {
        const response = await axios.delete(`/excel/form-types/${formType.id}`)

        if (response.data.status === 'success') {
          await loadFormTypes()
          alert('表单类型删除成功')
        } else {
          alert('删除失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('删除表单类型失败:', error)
        alert('删除失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const clearCache = async () => {
      try {
        await loadFormTypes()
        alert('缓存刷新成功')
      } catch (error) {
        console.error('刷新缓存失败:', error)
        alert('刷新缓存失败')
      }
    }

    // 标签页切换方法
    const switchToComponentsTab = (formTypeName) => {
      router.push(`/component-manager?formType=${encodeURIComponent(formTypeName)}`)
    }

    const switchToTemplatesTab = (formTypeName) => {
      router.push(`/template-manager?formType=${encodeURIComponent(formTypeName)}`)
    }

    const switchToFieldsTab = (formTypeName) => {
      router.push(`/form-field-config?formType=${encodeURIComponent(formTypeName)}`)
    }



    // 一键初始化表单
    const showOneClickInitModal = () => {
      // 重置表单
      oneClickForm.name = ''
      oneClickForm.description = ''

      // 显示模态框
      const modal = new Modal(document.getElementById('oneClickInitModal'))
      modal.show()
    }

    const oneClickInitForm = async () => {
      const formTypeName = oneClickForm.name.trim()
      if (!formTypeName) {
        alert('请输入表单类型名称')
        return
      }

      // 检查名称是否已存在
      const existingFormType = formTypes.value.find(ft => ft.name === formTypeName)
      if (existingFormType) {
        alert('该表单类型名称已存在，请使用其他名称')
        return
      }

      // 检查是否为内置类型名称
      const builtInTypes = ['安全测评', '安全监测', '应用加固']
      if (builtInTypes.includes(formTypeName)) {
        alert('不能使用内置表单类型的名称')
        return
      }

      try {
        oneClickInitializing.value = true

        // 调用一键初始化API
        console.log('🚀 开始一键初始化表单...')
        const response = await axios.post('/excel/form-types/one-click-init', {
          name: formTypeName,
          description: oneClickForm.description || `${formTypeName}表单配置`
        })

        if (response.data.status !== 'success') {
          throw new Error(response.data.message)
        }

        const data = response.data.data

        // 更新界面
        await loadFormTypes()

        // 关闭模态框
        const modal = Modal.getInstance(document.getElementById('oneClickInitModal'))
        if (modal) modal.hide()

        // 显示成功消息
        alert(`🎉 表单"${formTypeName}"一键初始化成功！
✅ 创建了 ${data.groups_created} 个分组
✅ 创建了 ${data.fields_created} 个字段

包含与"应用加固"表单完全相同的字段配置：
• 基本信息字段（4个）：公司名称、记录日期、编辑人、文档后缀
• 客户信息字段（3个）：客户标识、客户、部署的平台版本
• 访问信息字段（3个）：平台访问地址、管理员信息、升级平台地址
• 服务器信息字段（4个）：IP地址、主机名、操作系统、部署应用
• 维护记录字段（3个）：维护日期、维护内容、维护人员

现在您可以像使用"应用加固"表单一样填写和使用这个新表单！`)

      } catch (error) {
        console.error('一键初始化表单失败:', error)
        alert(`一键初始化失败: ${error.response?.data?.message || error.message}`)
      } finally {
        oneClickInitializing.value = false
      }
    }

    const goToDetailedFieldConfig = (formTypeName) => {
      router.push(`/form-field-config?formType=${encodeURIComponent(formTypeName)}`)
    }

    // 生命周期
    onMounted(() => {
      loadFormTypes()
    })

    return {
      formTypes,
      componentStats,
      templateStats,
      loading,
      editingFormType,
      formTypeForm,
      oneClickForm,
      oneClickInitializing,
      isBuiltInFormType,
      getComponentCount,
      getTemplateStatus,
      getTemplateStatusBadge,
      formatDate,
      showAddFormTypeModal,
      showOneClickInitModal,
      oneClickInitForm,
      editFormType,
      saveFormType,
      deleteFormType,
      clearCache,
      switchToComponentsTab,
      switchToTemplatesTab,
      goToDetailedFieldConfig
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}



.table th {
  font-weight: 600;
  background-color: #f8f9fa;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.page-title {
  color: #2c3e50;
}

/* 空状态样式 */
.empty-state {
  padding: 3rem 2rem;
}

.empty-state i {
  opacity: 0.5;
}

/* 徽章样式优化 */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .btn-group-sm .btn {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
  }
}
</style>
