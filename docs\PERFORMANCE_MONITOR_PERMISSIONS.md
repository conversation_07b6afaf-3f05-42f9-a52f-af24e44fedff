# 🔐 性能监控权限配置指南

## 📋 权限问题说明

如果您在访问性能监控页面时看到 **"权限受限"** 提示，这意味着您的账户缺少查看完整性能数据的权限。

## 🎯 所需权限

性能监控功能需要以下权限：

### 基础权限 (必需)
- **登录权限**: 能够正常登录系统
- **页面访问**: 能够访问性能监控页面

### 完整功能权限
- **`system.view`**: 查看系统性能统计
- **`system.config`**: 清理性能数据 (管理功能)

## 🔧 权限配置方法

### 方法一：管理员分配权限

1. **联系系统管理员**
   - 请求 `system.view` 权限
   - 说明需要查看性能监控的业务需求

2. **管理员操作步骤**：
   ```
   1. 登录管理员账户
   2. 进入 "用户管理" 页面
   3. 找到需要授权的用户
   4. 点击 "编辑" 按钮
   5. 在权限设置中勾选 "system.view"
   6. 保存设置
   ```

### 方法二：角色权限配置

1. **创建性能监控角色**：
   ```
   角色名称: 性能监控员
   权限包含: system.view
   ```

2. **将用户分配到角色**：
   ```
   1. 进入 "角色管理" 页面
   2. 找到 "性能监控员" 角色
   3. 添加需要权限的用户
   ```

### 方法三：用户组权限配置

1. **创建性能监控用户组**
2. **为用户组分配权限**
3. **将用户加入用户组**

## 📊 权限级别说明

### 🟢 完整权限 (`system.view`)
可以查看：
- ✅ 系统资源使用情况 (CPU、内存、磁盘)
- ✅ 缓存性能统计
- ✅ 请求性能统计
- ✅ 慢查询监控
- ✅ 性能优化建议
- ✅ 系统健康检查

### 🟡 基础权限 (仅登录)
可以查看：
- ✅ 基础系统资源信息
- ✅ 简单健康检查
- ❌ 详细缓存统计
- ❌ 请求性能分析
- ❌ 慢查询详情
- ❌ 优化建议

### 🔴 无权限 (未登录)
- ❌ 无法访问任何性能监控功能

## 🛠️ 管理员权限配置

### 1. 通过数据库直接配置

```sql
-- 查看现有权限
SELECT * FROM permission WHERE code LIKE 'system%';

-- 为用户添加权限 (通过角色)
INSERT INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM user u, role r 
WHERE u.username = '用户名' AND r.code = 'admin';

-- 为角色添加权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM role r, permission p
WHERE r.code = '角色代码' AND p.code = 'system.view';
```

### 2. 通过管理界面配置

1. **权限管理页面**：
   - 创建或编辑权限
   - 设置权限代码：`system.view`
   - 设置权限名称：系统监控查看

2. **角色管理页面**：
   - 创建性能监控相关角色
   - 分配相应权限

3. **用户管理页面**：
   - 为用户分配角色
   - 或直接分配权限

## 🔍 权限验证流程

```
用户访问性能监控页面
    ↓
检查是否登录
    ↓
检查是否有 system.view 权限
    ↓
有权限：显示完整功能
无权限：显示基础功能 + 权限提示
```

## 📞 获取帮助

### 用户求助
如果您需要性能监控权限，请联系：
- **直接上级**: 申请业务需求说明
- **系统管理员**: 技术权限配置
- **IT部门**: 系统访问权限

### 管理员支持
如果您是管理员需要配置权限，请参考：
- [用户权限管理指南](user-permissions.md)
- [角色管理指南](role-management.md)
- [系统管理功能](system-administration.md)

## ⚠️ 安全注意事项

### 权限分配原则
1. **最小权限原则**: 只分配必要的权限
2. **职责分离**: 不同角色分配不同权限
3. **定期审查**: 定期检查权限分配情况

### 敏感信息保护
- 性能监控数据可能包含系统敏感信息
- 只向需要的人员分配相关权限
- 定期审查权限使用情况

## 🎉 配置完成验证

权限配置完成后，用户应该能够：

1. **正常访问性能监控页面**
2. **看到完整的性能数据**
3. **不再显示权限受限提示**
4. **能够使用所有监控功能**

如果仍有问题，请检查：
- 用户是否重新登录
- 权限配置是否正确
- 缓存是否已清理

---

> 📝 **创建时间**: 2024-12-19  
> 🔄 **更新时间**: 2024-12-19  
> 👤 **开发者**: Augment Agent  
> 📋 **状态**: 可使用
