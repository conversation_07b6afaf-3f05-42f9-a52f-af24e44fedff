<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5)">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-journal-text me-2"></i>
            表单编辑日志
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')"></button>
        </div>

        <div class="modal-body">
          <div v-if="loading" class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载编辑日志...</p>
          </div>

          <div v-else-if="error" class="alert alert-danger">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ error }}
          </div>

          <div v-else>
            <!-- 基本信息 -->
            <div class="row mb-4" v-if="historyData">
              <div class="col-12">
                <div class="card bg-light">
                  <div class="card-body">
                    <h6 class="card-title mb-2">表单信息</h6>
                    <div class="row">
                      <div class="col-md-4">
                        <strong>公司名称:</strong> {{ historyData.company_name }}
                      </div>
                      <div class="col-md-4">
                        <strong>表单类型:</strong> {{ historyData.form_type }}
                      </div>
                      <div class="col-md-4">
                        <strong>修改次数:</strong> {{ historyData.total_edits }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 编辑历史列表 -->
            <div v-if="historyData && historyData.edit_history.length > 0">
              <h6 class="mb-3">
                <i class="bi bi-list-ul me-2"></i>
                修改日志 ({{ historyData.edit_history.length }})
              </h6>

              <div class="timeline">
                <div
                  v-for="(edit, index) in historyData.edit_history"
                  :key="edit.id"
                  class="timeline-item"
                  :class="{ 'timeline-item-latest': index === 0 }"
                >
                  <div class="timeline-marker">
                    <i class="bi" :class="getEditTypeIcon(edit.edit_type)"></i>
                  </div>

                  <div class="timeline-content">
                    <div class="card">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                          <span class="badge" :class="getEditTypeBadgeClass(edit.edit_type)">
                            {{ getEditTypeText(edit.edit_type) }}
                          </span>
                          <span class="ms-2 text-muted">
                            {{ formatDateTime(edit.created_at) }}
                          </span>
                        </div>
                        <div class="btn-group btn-group-sm">
                          <button
                            class="btn btn-outline-primary"
                            @click="toggleDetails(edit.id)"
                            :title="expandedItems.includes(edit.id) ? '收起详情' : '展开详情'"
                          >
                            <i class="bi" :class="expandedItems.includes(edit.id) ? 'bi-chevron-up' : 'bi-chevron-down'"></i>
                          </button>
                          <button
                            class="btn btn-outline-success"
                            @click="restoreFromEdit(edit, 'old')"
                            :disabled="!edit.old_data"
                            title="恢复到编辑前状态"
                          >
                            <i class="bi bi-arrow-counterclockwise"></i>
                          </button>
                          <button
                            class="btn btn-outline-info"
                            @click="restoreFromEdit(edit, 'new')"
                            :disabled="!edit.new_data"
                            title="恢复到编辑后状态"
                          >
                            <i class="bi bi-arrow-clockwise"></i>
                          </button>
                        </div>
                      </div>

                      <div class="card-body">
                        <!-- 基本信息 -->
                        <div class="row mb-2">
                          <div class="col-md-6">
                            <strong>操作人员:</strong> {{ edit.edited_by || '未知' }}
                          </div>
                          <div class="col-md-6">
                            <strong>修改原因:</strong> {{ edit.edit_reason || '无' }}
                          </div>
                        </div>

                        <div class="mb-2">
                          <strong>操作描述:</strong> {{ edit.edit_description }}
                        </div>

                        <!-- 变更摘要 -->
                        <div v-if="edit.changes_detail && edit.changes_detail.length > 0" class="mb-3">
                          <div class="d-flex align-items-center mb-2">
                            <strong class="me-2">修改内容:</strong>
                            <span class="badge bg-info">{{ edit.changes_detail.length }} 个字段</span>
                          </div>
                          <div class="change-summary">
                            <div
                              v-for="change in edit.changes_detail.slice(0, 3)"
                              :key="change.field"
                              class="change-item mb-1"
                            >
                              <span class="field-name">{{ change.field }}</span>
                              <span class="change-arrow">→</span>
                              <span class="new-value">{{ formatValue(change.new_value) }}</span>
                            </div>
                            <div v-if="edit.changes_detail.length > 3" class="text-muted small">
                              还有 {{ edit.changes_detail.length - 3 }} 个字段被修改...
                            </div>
                          </div>
                        </div>

                        <!-- 详细变更信息 -->
                        <div v-if="expandedItems.includes(edit.id)" class="border-top pt-3">
                          <div v-if="edit.changes_detail && edit.changes_detail.length > 0">
                            <h6>详细变更:</h6>
                            <div class="table-responsive">
                              <table class="table table-sm">
                                <thead>
                                  <tr>
                                    <th width="30%">字段</th>
                                    <th width="35%">修改前</th>
                                    <th width="35%">修改后</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr v-for="change in edit.changes_detail" :key="change.field">
                                    <td><code>{{ change.field }}</code></td>
                                    <td class="change-value-cell">
                                      <div class="change-value old-value">
                                        <pre v-if="isComplexValue(change.old_value)" class="value-pre">{{ formatComplexValue(change.old_value) }}</pre>
                                        <span v-else>{{ formatValue(change.old_value) }}</span>
                                      </div>
                                    </td>
                                    <td class="change-value-cell">
                                      <div class="change-value new-value">
                                        <pre v-if="isComplexValue(change.new_value)" class="value-pre">{{ formatComplexValue(change.new_value) }}</pre>
                                        <span v-else>{{ formatValue(change.new_value) }}</span>
                                      </div>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                          <div v-else class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            无详细变更信息
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-4">
              <i class="bi bi-journal-text display-4 text-muted"></i>
              <p class="mt-2 text-muted">暂无修改日志记录</p>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            关闭
          </button>
          <button type="button" class="btn btn-primary" @click="refreshHistory">
            <i class="bi bi-arrow-clockwise me-1"></i>
            刷新
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

// 统一的API基础URL配置
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

/**
 * 表单编辑日志模态框组件
 * 显示表单的修改日志，包括操作人员、修改内容、修改时间等详细信息
 */
export default {
  name: 'EditHistoryModal',

  props: {
    submissionId: {
      type: Number,
      required: true
    }
  },

  emits: ['close', 'restore'],

  data() {
    return {
      loading: false,
      error: null,
      historyData: null,
      expandedItems: []
    }
  },

  mounted() {
    this.loadEditHistory()
  },

  methods: {
    /**
     * 加载编辑日志
     */
    async loadEditHistory() {
      this.loading = true
      this.error = null

      try {
        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${this.submissionId}/edit_history`)
        const result = await response.json()

        if (result.status === 'success') {
          this.historyData = result.data
        } else {
          this.error = result.message || '加载编辑日志失败'
        }
      } catch (error) {
        this.error = '网络错误，请稍后重试'
        console.error('加载编辑日志失败:', error)
      } finally {
        this.loading = false
      }
    },

    /**
     * 刷新日志记录
     */
    refreshHistory() {
      this.loadEditHistory()
    },

    /**
     * 切换详情展开状态
     */
    toggleDetails(editId) {
      const index = this.expandedItems.indexOf(editId)
      if (index > -1) {
        this.expandedItems.splice(index, 1)
      } else {
        this.expandedItems.push(editId)
      }
    },

    /**
     * 从编辑记录恢复数据
     */
    async restoreFromEdit(edit, restoreTo) {
      try {
        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${this.submissionId}/restore/${edit.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            restore_to: restoreTo,
            edit_reason: `从编辑历史恢复 (${edit.edit_type})`,
            edited_by: '系统恢复'
          })
        })

        const result = await response.json()

        if (result.status === 'success') {
          // 发送恢复事件给父组件
          this.$emit('restore', {
            edit_type: edit.edit_type,
            restore_data: result.data.submission.form_data
          })
        } else {
          alert('恢复失败: ' + result.message)
        }
      } catch (error) {
        console.error('恢复数据失败:', error)
        alert('恢复失败，请稍后重试')
      }
    },

    /**
     * 获取编辑类型图标
     */
    getEditTypeIcon(editType) {
      const iconMap = {
        'update': 'bi-pencil-square',
        'restore': 'bi-arrow-counterclockwise',
        'regenerate': 'bi-arrow-repeat',
        'overwrite': 'bi-exclamation-triangle'
      }
      return iconMap[editType] || 'bi-pencil'
    },

    /**
     * 获取编辑类型徽章样式
     */
    getEditTypeBadgeClass(editType) {
      const classMap = {
        'update': 'bg-primary',
        'restore': 'bg-warning',
        'regenerate': 'bg-info',
        'overwrite': 'bg-danger'
      }
      return classMap[editType] || 'bg-secondary'
    },

    /**
     * 获取编辑类型文本
     */
    getEditTypeText(editType) {
      const textMap = {
        'update': '修改',
        'restore': '恢复',
        'regenerate': '重新生成',
        'overwrite': '强制覆盖'
      }
      return textMap[editType] || editType
    },

    /**
     * 格式化日期时间
     */
    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },

    /**
     * 格式化值显示
     */
    formatValue(value) {
      if (value === null || value === undefined) {
        return '(空)'
      }

      // 处理数组
      if (Array.isArray(value)) {
        if (value.length === 0) {
          return '(空数组)'
        }

        // 如果是维护记录数组，特殊处理
        if (value.length > 0 && typeof value[0] === 'object' && value[0].维护时间) {
          const summaries = value.map((record, index) => {
            const time = record.维护时间 || '未知时间'
            const person = record.维护人员 || '未知人员'
            const type = record.维护类型 || '未知类型'
            return `${index + 1}. ${time} - ${person} (${type})`
          })
          return summaries.join('\n')
        }

        // 其他数组类型
        if (value.length <= 3) {
          return value.map(item => this.formatSingleValue(item)).join(', ')
        } else {
          return `${value.slice(0, 3).map(item => this.formatSingleValue(item)).join(', ')} 等${value.length}项`
        }
      }

      // 处理对象
      if (typeof value === 'object') {
        const keys = Object.keys(value)
        if (keys.length === 0) {
          return '(空对象)'
        }

        // 如果是维护记录对象
        if (value.维护时间 || value.维护人员 || value.维护类型) {
          const parts = []
          if (value.维护时间) parts.push(`时间: ${value.维护时间}`)
          if (value.维护人员) parts.push(`人员: ${value.维护人员}`)
          if (value.维护类型) parts.push(`类型: ${value.维护类型}`)
          if (value.维护内容) parts.push(`内容: ${value.维护内容.substring(0, 20)}...`)
          return parts.join(', ')
        }

        // 其他对象类型，显示主要字段
        const mainKeys = keys.slice(0, 3)
        const summary = mainKeys.map(key => `${key}: ${this.formatSingleValue(value[key])}`).join(', ')
        return keys.length > 3 ? `${summary} 等${keys.length}个字段` : summary
      }

      // 处理字符串
      if (typeof value === 'string') {
        if (value.length > 100) {
          return value.substring(0, 100) + '...'
        }
        return value
      }

      // 其他类型直接转换
      return String(value)
    },

    /**
     * 格式化单个值（用于数组和对象内部）
     */
    formatSingleValue(value) {
      if (value === null || value === undefined) {
        return '(空)'
      }
      if (typeof value === 'string') {
        return value.length > 20 ? value.substring(0, 20) + '...' : value
      }
      if (typeof value === 'object') {
        return '[对象]'
      }
      return String(value)
    },

    /**
     * 判断是否为复杂值（需要特殊显示）
     */
    isComplexValue(value) {
      return Array.isArray(value) || (typeof value === 'object' && value !== null)
    },

    /**
     * 格式化复杂值（用于详细显示）
     */
    formatComplexValue(value) {
      if (value === null || value === undefined) {
        return '(空)'
      }

      try {
        // 如果是维护记录数组，特殊格式化
        if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && value[0].维护时间) {
          return value.map((record, index) => {
            const parts = []
            parts.push(`记录 ${index + 1}:`)
            if (record.维护时间) parts.push(`  时间: ${record.维护时间}`)
            if (record.维护人员) parts.push(`  人员: ${record.维护人员}`)
            if (record.维护类型) parts.push(`  类型: ${record.维护类型}`)
            if (record.任务链接) parts.push(`  链接: ${record.任务链接}`)
            if (record.维护内容) parts.push(`  内容: ${record.维护内容}`)
            return parts.join('\n')
          }).join('\n\n')
        }

        // 其他复杂对象，使用JSON格式化
        return JSON.stringify(value, null, 2)
      } catch (error) {
        return String(value)
      }
    }
  }
}
</script>

<style scoped>
.modal {
  z-index: 1060;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-marker {
  position: absolute;
  left: -22px;
  top: 10px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #6c757d;
}

.timeline-item-latest .timeline-marker {
  border-color: #007bff;
  background: #007bff;
  color: white;
}

.timeline-content {
  margin-left: 15px;
}

.card {
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.badge {
  font-size: 0.75rem;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.change-value-cell {
  max-width: 300px;
  word-wrap: break-word;
}

.change-value {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.old-value {
  background-color: #f8d7da;
  border-left: 3px solid #dc3545;
}

.new-value {
  background-color: #d1e7dd;
  border-left: 3px solid #198754;
}

.value-pre {
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
  font-size: 0.8rem;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}

code {
  background-color: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.875rem;
}

.change-summary {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 10px;
  border-left: 3px solid #007bff;
}

.change-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.field-name {
  background-color: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
  color: #495057;
  min-width: 120px;
  text-align: center;
}

.change-arrow {
  margin: 0 8px;
  color: #007bff;
  font-weight: bold;
}

.new-value {
  background-color: #d1e7dd;
  padding: 2px 6px;
  border-radius: 3px;
  color: #0f5132;
  flex: 1;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
