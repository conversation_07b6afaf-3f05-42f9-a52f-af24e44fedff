/**
 * 表单快照API接口
 * 使用Redis缓存存储，支持跨会话访问
 */

import axios from 'axios'

// 配置API基础URL，参考componentService.js的模式
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

/**
 * 保存表单快照到Redis
 * @param {string} formType - 表单类型
 * @param {Object} snapshotData - 快照数据
 * @param {string} snapshotName - 快照名称
 * @returns {Promise} API响应
 */
export const saveFormSnapshot = async (formType, snapshotData, snapshotName) => {
  try {
    console.log('保存表单快照到Redis:', { formType, snapshotName })
    
    const response = await axios.post(`${API_BASE_URL}/api/form-snapshot/save`, {
      form_type: formType,
      snapshot_data: snapshotData,
      snapshot_name: snapshotName
    })
    
    console.log('表单快照保存成功:', response.data)
    return response.data
  } catch (error) {
    console.error('保存表单快照失败:', error)
    throw error
  }
}

/**
 * 从Redis加载表单快照
 * @param {string} formType - 表单类型
 * @returns {Promise} 快照数据
 */
export const loadFormSnapshot = async (formType) => {
  try {
    console.log('从Redis加载表单快照:', formType)
    
    const response = await axios.get(`${API_BASE_URL}/api/form-snapshot/load/${formType}`)
    
    console.log('表单快照加载结果:', response.data)
    return response.data
  } catch (error) {
    console.error('加载表单快照失败:', error)
    throw error
  }
}

/**
 * 删除表单快照
 * @param {string} formType - 表单类型
 * @returns {Promise} API响应
 */
export const deleteFormSnapshot = async (formType) => {
  try {
    console.log('删除表单快照:', formType)

    const response = await axios.delete(`${API_BASE_URL}/api/form-snapshot/delete/${formType}`)

    console.log('表单快照删除成功:', response.data)
    return response.data
  } catch (error) {
    console.error('删除表单快照失败:', error)
    throw error
  }
}

/**
 * 根据快照ID删除特定快照
 * @param {string} snapshotId - 快照ID
 * @returns {Promise} API响应
 */
export const deleteSnapshotById = async (snapshotId) => {
  try {
    console.log('根据ID删除快照:', snapshotId)

    const response = await axios.delete(`${API_BASE_URL}/api/form-snapshot/delete-by-id/${snapshotId}`)

    console.log('快照删除成功:', response.data)
    return response.data
  } catch (error) {
    console.error('根据ID删除快照失败:', error)
    throw error
  }
}

/**
 * 根据快照ID加载特定快照
 * @param {string} snapshotId - 快照ID
 * @returns {Promise} 快照数据
 */
export const loadSnapshotById = async (snapshotId) => {
  try {
    console.log('根据ID加载快照:', snapshotId)

    const response = await axios.get(`${API_BASE_URL}/api/form-snapshot/load-by-id/${snapshotId}`)

    console.log('快照加载结果:', response.data)
    return response.data
  } catch (error) {
    console.error('根据ID加载快照失败:', error)
    throw error
  }
}

/**
 * 获取用户的所有表单快照列表
 * @returns {Promise} 快照列表
 */
export const listFormSnapshots = async () => {
  try {
    console.log('获取表单快照列表')

    const response = await axios.get(`${API_BASE_URL}/api/form-snapshot/list`)
    
    console.log('快照列表获取成功:', response.data)
    return response.data
  } catch (error) {
    console.error('获取快照列表失败:', error)
    throw error
  }
}

/**
 * 检查表单快照系统健康状态
 * @returns {Promise} 健康状态
 */
export const checkSnapshotHealth = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/form-snapshot/health`)
    return response.data
  } catch (error) {
    console.error('检查快照系统健康状态失败:', error)
    throw error
  }
}

/**
 * 批量删除快照
 * @param {Array} snapshotIds - 快照ID数组
 * @returns {Promise} API响应
 */
export const batchDeleteSnapshots = async (snapshotIds) => {
  try {
    console.log('批量删除快照:', snapshotIds)

    const response = await axios.post(`${API_BASE_URL}/api/form-snapshot/batch-delete`, {
      snapshot_ids: snapshotIds
    })

    console.log('批量删除快照成功:', response.data)
    return response.data
  } catch (error) {
    console.error('批量删除快照失败:', error)
    throw error
  }
}

/**
 * 按条件清理快照
 * @param {Object} conditions - 清理条件
 * @param {string} conditions.form_type - 表单类型
 * @param {number} conditions.days_old - 清理N天前的快照
 * @param {string} conditions.name_pattern - 名称匹配模式（正则表达式）
 * @returns {Promise} API响应
 */
export const clearSnapshotsByCondition = async (conditions) => {
  try {
    console.log('按条件清理快照:', conditions)

    const response = await axios.post(`${API_BASE_URL}/api/form-snapshot/clear-by-condition`, conditions)

    console.log('按条件清理快照成功:', response.data)
    return response.data
  } catch (error) {
    console.error('按条件清理快照失败:', error)
    throw error
  }
}

/**
 * 清除所有快照
 * @returns {Promise} API响应
 */
export const clearAllSnapshots = async () => {
  try {
    console.log('清除所有快照')

    const response = await axios.delete(`${API_BASE_URL}/api/form-snapshot/clear-all`)

    console.log('清除所有快照成功:', response.data)
    return response.data
  } catch (error) {
    console.error('清除所有快照失败:', error)
    throw error
  }
}

/**
 * 迁移localStorage快照到Redis
 * 用于从旧版本升级
 * @param {string} formType - 表单类型
 * @returns {Promise} 迁移结果
 */
export const migrateLocalStorageSnapshots = async (formType) => {
  try {
    console.log('开始迁移localStorage快照到Redis')

    // 从localStorage获取旧快照
    const savedSnapshots = localStorage.getItem('formSnapshots')
    if (!savedSnapshots) {
      console.log('没有找到localStorage快照数据')
      return { migrated: 0, message: '没有需要迁移的数据' }
    }

    const snapshots = JSON.parse(savedSnapshots)
    let migratedCount = 0

    for (const snapshot of snapshots) {
      // 检查快照是否匹配当前表单类型
      const snapshotFormType = snapshot.data?.文档后缀 || formType
      if (snapshotFormType === formType) {
        try {
          await saveFormSnapshot(snapshotFormType, snapshot.data, snapshot.name)
          migratedCount++
          console.log(`已迁移快照: ${snapshot.name}`)
        } catch (error) {
          console.error(`迁移快照失败: ${snapshot.name}`, error)
        }
      }
    }

    if (migratedCount > 0) {
      console.log(`成功迁移 ${migratedCount} 个快照到Redis`)

      // 询问是否清除localStorage数据
      if (confirm(`已成功迁移 ${migratedCount} 个快照到服务器。是否清除本地存储的旧快照数据？`)) {
        localStorage.removeItem('formSnapshots')
        console.log('已清除localStorage快照数据')
      }
    }

    return {
      migrated: migratedCount,
      message: `成功迁移 ${migratedCount} 个快照`
    }
  } catch (error) {
    console.error('迁移快照失败:', error)
    throw error
  }
}
