<template>
  <div class="container mt-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-3">
      <ol class="breadcrumb">
        <li class="breadcrumb-item">
          <router-link to="/form-template-manager" class="text-decoration-none">
            <i class="bi bi-house me-1"></i>表单模板管理
          </router-link>
        </li>
        <li class="breadcrumb-item active" aria-current="page">
          <i class="bi bi-file-earmark-excel me-1"></i>模板管理
        </li>
      </ol>
    </nav>

    <!-- 页面头部 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="mb-0">
          <i class="bi bi-file-earmark-excel me-2"></i>
          模板管理
        </h2>
        <small class="text-muted" v-if="selectedType">
          当前管理：<strong>{{ selectedType }}</strong> 表单类型的模板
        </small>
      </div>
      <div>
        <button class="btn btn-outline-secondary me-2" @click="goBack">
          <i class="bi bi-arrow-left me-1"></i>
          返回管理页面
        </button>
      </div>
    </div>

    <div class="alert alert-info">
      <i class="bi bi-info-circle me-2"></i>
      在这里您可以导出和导入Excel模板文件。修改模板后导入可以自定义生成的Excel文件格式。
      <br>
      <i class="bi bi-lightbulb me-2"></i>
      **占位符使用提示：** 在Excel模板中，您可以使用特定的占位符来自动填充数据。将鼠标悬停在右上角的 <i class="bi bi-question-circle-fill"></i> 图标上查看详细列表。
    </div>

    <!-- 模板列表 -->
    <div class="card mb-4">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0">模板管理</h5>
      </div>
      <div class="card-body">
        <div v-if="loading" class="text-center py-3">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2">正在加载模板列表...</p>
        </div>

        <div v-else-if="error" class="alert alert-danger">
          <i class="bi bi-exclamation-triangle me-2"></i>
          {{ error }}
        </div>

        <div v-else-if="templates.length === 0" class="alert alert-warning">
          <i class="bi bi-exclamation-circle me-2"></i>
          没有找到可用的模板文件。
        </div>

        <div v-else>
          <!-- 模板类型过滤和版本选择 -->
          <div class="row g-3 mb-4">
            <!-- 模板类型过滤 -->
            <div class="col-md-4">
              <label for="templateType" class="form-label">
                <i class="bi bi-funnel me-1"></i>
                过滤模板类型
              </label>
              <select class="form-select" id="templateType" v-model="selectedType">
                <option v-for="formType in formTypes" :key="formType.id" :value="formType.name">
                  {{ formType.name }}
                </option>
              </select>
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>
                选择表单类型来查看对应的模板版本
              </div>
            </div>

            <!-- 模板版本选择 -->
            <div class="col-md-8">
              <label for="templateVersion" class="form-label">模板版本</label>
              <div class="input-group">
                <select class="form-select" id="templateVersion" v-model="selectedTemplate" :disabled="!typeTemplates.length">
                  <option v-for="template in typeTemplates" :key="template.filename" :value="template">
                    {{ template.alias || (template.isActive ? '当前版本' : formatDate(template.lastModified) + ' 的备份') }}
                  </option>
                </select>
                <button class="btn btn-primary" @click="exportSelectedTemplate" :disabled="!selectedTemplate">
                  <i class="bi bi-download me-1"></i> 导出
                </button>
                <button
                  class="btn btn-success"
                  @click="activateSelectedTemplate"
                  :disabled="!selectedTemplate || selectedTemplate.isActive || activating"
                >
                  <span v-if="activating">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    激活中...
                  </span>
                  <span v-else>
                    <i class="bi bi-check-circle me-1"></i> 使用此版本
                  </span>
                </button>
                <button
                  class="btn btn-danger"
                  @click="confirmDeleteTemplate"
                  :disabled="!selectedTemplate || selectedTemplate.isActive || deleting"
                >
                  <span v-if="deleting">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    删除中...
                  </span>
                  <span v-else>
                    <i class="bi bi-trash me-1"></i> 删除
                  </span>
                </button>
              </div>
              <div class="form-text" v-if="selectedTemplate">
                <div class="d-flex align-items-center justify-content-between">
                  <div>
                    最后修改时间: {{ formatDate(selectedTemplate.lastModified) }}
                    <span v-if="selectedTemplate.isActive" class="badge bg-success ms-2">当前使用</span>
                  </div>
                  <button class="btn btn-sm btn-outline-secondary" @click="showSetAliasModal">
                    <i class="bi bi-pencil-square me-1"></i> 设置别名
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 当前使用的模板信息 -->
          <div class="alert alert-info" v-if="currentTemplate">
            <div class="d-flex align-items-center">
              <div class="flex-grow-1">
                <h6 class="alert-heading mb-1">当前使用的模板</h6>
                <p class="mb-0">
                  <span class="badge" :class="getTemplateBadgeClass(currentTemplate.type)">{{ currentTemplate.type }}</span>
                  {{ currentTemplate.displayName }}
                </p>
                <small>最后修改时间: {{ formatDate(currentTemplate.lastModified) }}</small>
              </div>
              <button class="btn btn-sm btn-outline-info" @click="exportCurrentTemplate">
                <i class="bi bi-download me-1"></i> 导出
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导入模板 -->
    <div class="card">
      <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">导入新模板</h5>
        <div class="template-help" data-bs-toggle="tooltip" data-bs-html="true" data-bs-placement="left" :title="templateHelpText">
          <i class="bi bi-question-circle-fill text-white"></i>
        </div>
      </div>
      <div class="card-body">
        <form @submit.prevent="uploadTemplate(false)">
          <div class="row g-3">
            <div class="col-md-4">
              <label for="uploadTemplateType" class="form-label">模板类型</label>
              <select class="form-select" id="uploadTemplateType" v-model="uploadForm.type" required>
                <option value="">-- 请选择模板类型 --</option>
                <option v-for="formType in formTypes" :key="formType.id" :value="formType.name">
                  {{ formType.name }}
                </option>
              </select>
            </div>

            <div class="col-md-8">
              <label for="templateFile" class="form-label">选择Excel模板文件</label>
              <input type="file" class="form-control" id="templateFile" ref="fileInput"
                    @change="handleFileChange" accept=".xlsx" required>
            </div>
          </div>

          <div class="mt-3">
            <div class="form-group">
              <label for="templateAlias" class="form-label">模板别名（可选）</label>
              <input type="text" class="form-control" id="templateAlias" v-model="uploadForm.alias"
                     placeholder="例如：客户A专用、V2.0版本、测试版等">
              <div class="form-text">
                <strong>💡 别名功能：</strong>别名会添加到文件名后面，帮助区分不同版本的模板。
                <br>例如：<code>安全监测-运维信息登记模板-客户A专用.xlsx</code>
              </div>
            </div>
          </div>

          <div class="alert alert-success mt-3">
            <i class="bi bi-lightbulb me-2"></i>
            <strong>智能文件名管理：</strong>
            <ul class="mb-0 mt-2">
              <li>📝 <strong>自动命名</strong>：文件名格式为 <code>模板类型-原文件名-别名.xlsx</code></li>
              <li>🔄 <strong>别名区分</strong>：通过别名创建同一模板的不同版本</li>
              <li>⚠️ <strong>精确冲突</strong>：只有文件名完全相同才提示覆盖</li>
              <li>✅ <strong>安全确认</strong>：覆盖前会显示详细信息供您确认</li>
            </ul>
            <div class="mt-2">
              <small class="text-muted">
                💡 <strong>示例</strong>：上传 <code>运维模板.xlsx</code> + 别名 <code>客户A专用</code>
                → 生成 <code>安全监测-运维信息登记模板-客户A专用.xlsx</code>
              </small>
            </div>
          </div>

          <div class="alert alert-secondary mt-3">
            <div class="d-flex align-items-start">
              <i class="bi bi-lightbulb-fill me-2 mt-1 text-warning"></i>
              <div>
                <strong>模板提示：</strong>
                <p class="mb-1 mt-1">模板中可以使用占位符来自动填充数据。将鼠标悬停在右上角的<i class="bi bi-question-circle-fill text-white"></i>图标上，查看所有可用的占位符。</p>
                <p class="mb-0">例如，使用<code>\{\{公司名称\}\}</code>将自动替换为公司名称，<code>\{\{ item.IP地址 | range:服务器信息 \}\}</code>将生成服务器IP地址列表。此外，您还可以使用 <code>\{\{组件名.字段\}\}</code> (如 <code>\{\{docker.port\}\}</code> 获取端口、<code>\{\{mongodb.version\}\}</code> 获取版本) 和 <code>\{\{组件名.count\}\}</code> (如 <code>\{\{docker.count\}\}</code> 获取数量) 格式来获取组件信息。请注意占位符区分大小写。</p>
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-end mt-3">
            <button type="submit" class="btn btn-success" :disabled="uploading">
              <span v-if="uploading">
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                上传中...
              </span>
              <span v-else>
                <i class="bi bi-upload me-1"></i> 上传模板
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 设置别名模态框 -->
    <div class="modal fade" id="setAliasModal" tabindex="-1" aria-labelledby="setAliasModalLabel" aria-hidden="true" ref="setAliasModal">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="setAliasModalLabel">设置模板别名</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="templateAlias" class="form-label">模板别名</label>
              <input type="text" class="form-control" id="templateAlias" v-model="aliasForm.alias" placeholder="请输入模板别名，如：初始版本、正式版等">
              <div class="form-text">
                设置一个易于识别的别名，方便在选择模板时快速找到需要的版本。
              </div>
            </div>
            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              当前设置别名的模板: {{ selectedTemplate ? (selectedTemplate.alias || '未设置别名') : '' }}
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" @click="setTemplateAlias" :disabled="settingAlias">
              <span v-if="settingAlias">
                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                保存中...
              </span>
              <span v-else>
                <i class="bi bi-check-lg me-1"></i> 保存别名
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true" ref="deleteConfirmModal">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除模板</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              <strong>警告：此操作不可撤销！</strong>
            </div>
            <p>您确定要删除以下模板吗？</p>
            <div class="card mb-3" v-if="selectedTemplate">
              <div class="card-body">
                <h6 class="card-title">{{ selectedTemplate.alias || selectedTemplate.filename }}</h6>
                <p class="card-text mb-1">
                  <small>文件名: {{ selectedTemplate.filename }}</small>
                </p>
                <p class="card-text mb-1">
                  <small>类型: {{ selectedTemplate.type }}</small>
                </p>
                <p class="card-text">
                  <small>最后修改: {{ formatDate(selectedTemplate.lastModified) }}</small>
                </p>
              </div>
            </div>
            <p class="text-danger">删除后，该模板文件将从服务器上永久移除，且无法恢复。</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-danger" @click="deleteTemplate" :disabled="deleting">
              <span v-if="deleting">
                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                删除中...
              </span>
              <span v-else>
                <i class="bi bi-trash me-1"></i> 确认删除
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'
import excelApi from '@/api/excel'
import { Modal, Tooltip } from 'bootstrap'

export default {
  name: 'TemplateManager',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const templates = ref([])
    const formTypes = ref([])
    const loading = ref(true)
    const error = ref(null)
    const uploading = ref(false)
    const fileInput = ref(null)
    const activating = ref(false) // 是否正在激活模板
    const deleting = ref(false) // 是否正在删除模板
    const selectedType = ref('') // 当前选择的模板类型
    const selectedTemplate = ref(null) // 当前选择的模板版本
    const setAliasModal = ref(null) // 设置别名的模态框引用
    const deleteConfirmModal = ref(null) // 删除确认的模态框引用
    const settingAlias = ref(false) // 是否正在设置别名

    const uploadForm = reactive({
      type: '',
      file: null,
      alias: ''
    })

    const aliasForm = reactive({
      alias: ''
    })

    // 模板帮助信息
    const templateHelpText = `
      <div class="text-start">
        <h6 class="mb-2">模板占位符使用说明</h6>

        <p class="mb-1">**Excel模板中可使用以下占位符自动填充数据：**</p>

        <p class="mb-1"><strong>1. 通用信息占位符：</strong></p>
        <ul class="mb-2">
          <li><code>\{\{公司名称\}\}</code> - 从表单获取</li>
          <li><code>\{\{记录日期\}\}</code> - 从表单获取</li>
          <li><code>\{\{部署包版本\}\}</code> - 从表单获取</li>
          <li><code>\{\{客户\}\}</code> - 从表单获取</li>
          <li><code>\{\{客户标识\}\}</code> - 从表单获取</li>
          <li><code>\{\{修改时间\}\}</code> - Excel 文件本身的最后修改时间</li>
          <li><code>\{\{维护记录_文本\}\}</code> - 所有维护记录合并的格式化文本</li>
        </ul>

        <p class="mb-1"><strong>2. 动态列表占位符 (Range/Split)：</strong></p>
        <ul class="mb-2">
          <li>用于根据列表数据动态生成行或列。通常与 <code>item.</code> 结合使用。</li>
          <li><code>\{\{ item.字段名 | range:列表名称 \}\}</code> - 为列表中的每个项目生成一行，并填充字段名对应的值。</li>
          <li>示例：<code>\{\{ item.IP地址 | range:服务器信息 \}\}</code> 将为每个服务器生成一行，显示其IP地址。</li>
          <li><code>\{\{ 字段名 | split:col,分隔符 | range:列表名称 \}\}</code> - 将列表字段内容按分隔符分割，横向生成多列。</li>
          <li>示例：<code>\{\{ 部署应用 | split:col,; | range:服务器信息 \}\}</code> 将服务器的"部署应用"字段按分号分割，生成多列显示各应用名称。</li>
          <li>其他常用 range 列表：服务器信息, 运维定制内容, 客户APP, 维护记录等 (具体可用列表取决于表单类型)。</li>
        </ul>

        <p class="mb-1"><strong>3. 组件信息占位符 (推荐)：</strong></p>
        <ul class="mb-2">
          <li>用于获取特定组件的详细信息或部署数量。格式为 <code>\{\{组件名.字段或count\}\}</code>。</li>
          <li>**获取字段值：** <code>\{\{组件名.字段名\}\}</code></li>
          <li>示例：<code>\{\{docker.port\}\}</code> 获取 Docker 组件的端口，<code>\{\{mongodb.version\}\}</code> 获取 MongoDB 组件的版本，<code>\{\{nginx.description\}\}</code> 获取 Nginx 组件的描述。</li>
          <li>**获取部署数量：** <code>\{\{组件名.count\}\}</code></li>
          <li>示例：<code>\{\{docker.count\}\}</code> 获取 Docker 组件的部署数量。</li>
          <li>**支持获取的字段包括：** <code>id</code>, <code>name</code>, <code>display_name</code>, <code>version</code>, <code>port</code>, <code>default_port</code>, <code>description</code>, <code>protocol</code>, <code>alias</code>, <code>category_key</code>, <code>form_type</code>, <code>is_active</code>, <code>created_at</code>, <code>updated_at</code>, <code>category_display_name</code>, <code>category_color</code>, <code>category_icon</code>, <code>category_order</code>, <code>count</code>。</li>
          <li>**请注意占位符区分大小写。** 组件名称需与数据库中的 <code>name</code> 字段一致。</li>
        </ul>

        <p class="mb-1"><strong>4. 特殊表头占位符：</strong></p>
        <ul class="mb-2">
          <li><code>#COMPONENT_PORTS#</code> - 在表头单元格中使用，自动横向展开所有组件的端口列表。</li>
          <li><code>#SERVER_COMPONENTS#</code> - 在表头单元格中使用，自动生成服务器与组件的部署矩阵（✓表示部署）。</li>
        </ul>

        <p class="mb-1">**提示：** 具体可用的占位符可能因模板类型而异。建议先导出模板查看原始占位符。</p>

      </div>
    `

    // 根据选择的类型过滤模板
    const typeTemplates = computed(() => {
      return templates.value.filter(template => template.type === selectedType.value)
    })

    // 获取当前使用的模板
    const currentTemplate = computed(() => {
      return templates.value.find(template => template.isActive && template.type === selectedType.value)
    })

    // 当模板类型变化时，重置选择的模板
    watch(selectedType, (newType, oldType) => {
      console.log(`🔄 表单类型变化: ${oldType} -> ${newType}`)

      // 默认选择当前活动的模板
      const active = typeTemplates.value.find(t => t.isActive)
      if (active) {
        selectedTemplate.value = active
        console.log(`✅ 选择激活的模板: ${active.alias || active.filename}`)
      } else if (typeTemplates.value.length > 0) {
        selectedTemplate.value = typeTemplates.value[0]
        console.log(`✅ 选择第一个模板: ${typeTemplates.value[0].alias || typeTemplates.value[0].filename}`)
      } else {
        selectedTemplate.value = null
        console.log('❌ 没有可用的模板')
      }
    })

    // 获取表单类型列表
    const fetchFormTypes = async () => {
      try {
        console.log('🔍 获取表单类型列表')
        const response = await excelApi.getFormTypes()
        formTypes.value = response.data.data
        console.log(`📋 获取到 ${formTypes.value.length} 个表单类型:`, formTypes.value)

        // 检查URL参数中是否指定了表单类型
        const urlFormType = route.query.formType
        if (urlFormType && formTypes.value.some(ft => ft.name === urlFormType)) {
          selectedType.value = urlFormType
          console.log(`🎯 从URL参数设置表单类型: ${urlFormType}`)
        } else if (formTypes.value.length > 0 && !selectedType.value) {
          // 设置默认选择的表单类型（但不在这里设置，让fetchTemplates来处理）
          console.log('⏳ 等待模板加载后设置默认表单类型')
        }
      } catch (err) {
        console.error('获取表单类型失败:', err)
        error.value = '获取表单类型失败: ' + (err.response && err.response.data && err.response.data.message ? err.response.data.message : err.message)
      }
    }

    // 获取模板列表
    const fetchTemplates = async () => {
      loading.value = true
      error.value = null

      try {
        console.log('🔍 获取所有模板')
        const response = await excelApi.getTemplates()

        templates.value = response.data.data
        console.log(`📋 获取到 ${templates.value.length} 个模板:`, templates.value)

        // 🔧 设置默认选择的模板类型和版本
        if (templates.value.length > 0) {
          // 检查URL参数中是否指定了表单类型
          const urlFormType = route.query.formType

          if (urlFormType) {
            console.log(`🎯 从URL参数设置表单类型: ${urlFormType}`)
            selectedType.value = urlFormType
          } else if (!selectedType.value) {
            // 如果没有选择的类型，选择第一个可用的类型
            const availableTypes = [...new Set(templates.value.map(t => t.type || t.formType))]
            if (availableTypes.length > 0) {
              selectedType.value = availableTypes[0]
              console.log(`🎯 自动选择第一个表单类型: ${selectedType.value}`)
            }
          }

          // 触发watch以设置默认选择的模板版本
          if (selectedType.value) {
            // 手动触发模板选择逻辑
            const typeTemplatesForSelection = templates.value.filter(template =>
              (template.type || template.formType) === selectedType.value
            )

            if (typeTemplatesForSelection.length > 0) {
              // 优先选择激活的模板
              const activeTemplate = typeTemplatesForSelection.find(t => t.isActive)
              if (activeTemplate) {
                selectedTemplate.value = activeTemplate
                console.log(`✅ 选择激活的模板: ${activeTemplate.alias || activeTemplate.filename}`)
              } else {
                selectedTemplate.value = typeTemplatesForSelection[0]
                console.log(`✅ 选择第一个模板: ${typeTemplatesForSelection[0].alias || typeTemplatesForSelection[0].filename}`)
              }
            }
          }
        }
      } catch (err) {
        console.error('获取模板列表失败:', err)
        error.value = '获取模板列表失败: ' + (err.response && err.response.data && err.response.data.message ? err.response.data.message : err.message)
      } finally {
        loading.value = false
      }
    }

    // 导出模板
    const exportTemplate = async (filename) => {
      try {
        const response = await excelApi.downloadTemplate(filename)

        // 使用更安全的下载方式，避免HTTPS警告
        try {
          // 尝试使用现代的下载API
          if (window.showSaveFilePicker) {
            const fileHandle = await window.showSaveFilePicker({
              suggestedName: filename,
              types: [{
                description: 'Excel files',
                accept: { 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'] }
              }]
            })
            const writable = await fileHandle.createWritable()
            await writable.write(new Blob([response.data]))
            await writable.close()
          } else {
            // 回退到传统方式
            const url = window.URL.createObjectURL(new Blob([response.data]))
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', filename)
            link.style.display = 'none'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
          }
        } catch (downloadError) {
          // 如果现代API失败，使用传统方式
          const url = window.URL.createObjectURL(new Blob([response.data]))
          const link = document.createElement('a')
          link.href = url
          link.setAttribute('download', filename)
          link.style.display = 'none'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }
      } catch (err) {
        console.error('导出模板失败:', err)
        alert('导出模板失败: ' + (err.response && err.response.data && err.response.data.message ? err.response.data.message : err.message))
      }
    }

    // 处理文件选择
    const handleFileChange = (event) => {
      uploadForm.file = event.target.files[0] || null
    }

    // 创建FormData的辅助函数
    const createFormData = (confirmOverwrite = false) => {
      const formData = new FormData()
      formData.append('file', uploadForm.file)
      formData.append('type', uploadForm.type)
      if (uploadForm.alias) {
        formData.append('alias', uploadForm.alias)
      }
      if (confirmOverwrite) {
        formData.append('confirm', 'true')
      }
      return formData
    }

    // 上传模板
    const uploadTemplate = async (confirmOverwrite = false) => {
      // 确保confirmOverwrite是布尔值（防止事件对象被传入）
      const isConfirmOverwrite = confirmOverwrite === true

      if (!uploadForm.file || !uploadForm.type) {
        alert('请选择模板类型和文件')
        return
      }

      // 防止重复上传
      if (uploading.value && !isConfirmOverwrite) {
        console.log('⚠️ 上传正在进行中，忽略重复请求')
        return
      }

      uploading.value = true

      try {
        // 每次都创建新的FormData
        const formData = createFormData(isConfirmOverwrite)

        if (isConfirmOverwrite) {
          console.log('📤 发送确认覆盖请求，confirm=true')
        } else {
          console.log('📤 发送普通上传请求')
        }

        console.log('📋 上传参数:', {
          filename: uploadForm.file.name,
          type: uploadForm.type,
          alias: uploadForm.alias,
          confirmOverwrite: isConfirmOverwrite
        })

        const response = await excelApi.uploadTemplate(formData)

        console.log('📥 收到上传响应:', response.status, response.data)

        // 显示成功信息
        if (response.data && response.data.data && response.data.data.conflict_info) {
          const conflictInfo = response.data.data.conflict_info
          let message = response.data.message || '模板上传成功!'

          if (conflictInfo.has_conflict) {
            console.log('⚠️ 响应中包含冲突信息，但状态码是200（成功）')
            console.log('这意味着用户之前已经确认了覆盖操作')

            message += '\n\n📋 处理详情:'
            message += `\n✅ ${conflictInfo.action_taken === 'replaced' ? '已替换现有模板' :
                              conflictInfo.action_taken === 'updated' ? '已更新数据库记录' :
                              conflictInfo.action_taken === 'overwritten' ? '已覆盖现有文件' : '已创建新模板'}`
          }

          alert(message)
        } else {
          alert(response.data.message || '模板上传成功!')
        }

        // 重置表单
        uploadForm.type = ''
        uploadForm.file = null
        uploadForm.alias = ''
        if (fileInput.value) {
          fileInput.value.value = ''
        }

        // 保存当前选择的模板类型
        const currentType = uploadForm.type

        // 刷新模板列表
        await fetchTemplates()

        // 确保选择了上传的模板类型
        if (currentType) {
          selectedType.value = currentType
        }
      } catch (err) {
        console.log('❌ 上传请求出现错误:', err)
        console.log('错误状态码:', err.response?.status)
        console.log('错误响应数据:', err.response?.data)

        // 处理409冲突状态码
        if (err.response && err.response.status === 409 && err.response.data.status === 'conflict') {
          // 显示冲突确认对话框
          const conflictInfo = err.response.data.data.conflict_info
          let message = '⚠️ 检测到模板文件冲突！\n\n'
          message += `📁 将要上传的文件: ${err.response.data.data.filename}\n`
          message += `📋 模板类型: ${err.response.data.data.type}\n\n`

          if (conflictInfo.existing_info) {
            message += '📄 现有模板信息:\n'
            message += `• 文件名: ${conflictInfo.existing_info.filename}\n`
            message += `• 模板类型: ${conflictInfo.existing_info.template_type}\n`
            message += `• 表单类型: ${conflictInfo.existing_info.form_type}\n`
            message += `• 别名: ${conflictInfo.existing_info.alias}\n`
            message += `• 创建时间: ${conflictInfo.existing_info.created_at}\n`
            message += `• 最后修改: ${conflictInfo.existing_info.last_modified}\n`

            if (conflictInfo.existing_info.is_active) {
              message += '• ⚠️ 状态: 正在使用中\n'
            }
          }

          message += `\n🔄 操作影响: ${conflictInfo.action_taken === 'replaced' ? '将替换现有模板文件和数据库记录' :
                                    conflictInfo.action_taken === 'updated' ? '将更新数据库记录' :
                                    conflictInfo.action_taken === 'overwritten' ? '将覆盖现有文件' : '将创建新模板'}\n\n`
          message += '⚠️ 警告：此操作将永久覆盖现有模板！\n\n'
          message += '请确认是否要继续上传并覆盖现有模板？'

          // 使用更明显的确认对话框
          console.log('🚨 检测到文件冲突，显示确认对话框')
          console.log('冲突信息:', conflictInfo)

          // 延迟显示确认对话框，确保用户能看到
          setTimeout(async () => {
            const userConfirmed = window.confirm(message)
            console.log('用户选择:', userConfirmed ? '确认覆盖' : '取消上传')

            if (userConfirmed) {
              // 用户确认，重新上传并确认覆盖
              console.log('🔄 用户确认覆盖，重新上传...')

              // 重置上传状态，允许重新上传
              uploading.value = false

              // 延迟一下再调用，确保状态重置
              setTimeout(() => {
                uploadTemplate(true)
              }, 50)
            } else {
              // 用户取消，不执行任何操作
              console.log('❌ 用户取消上传')
              uploading.value = false  // 重置上传状态
              alert('上传已取消')
            }
          }, 100)
        } else {
          console.error('上传模板失败:', err)
          alert('上传模板失败: ' + (err.response && err.response.data && err.response.data.message ? err.response.data.message : err.message))
        }
      } finally {
        uploading.value = false
      }
    }

    // 导出选中的模板
    const exportSelectedTemplate = () => {
      if (selectedTemplate.value && selectedTemplate.value.filename) {
        exportTemplate(selectedTemplate.value.filename)
      }
    }

    // 导出当前使用的模板
    const exportCurrentTemplate = () => {
      if (currentTemplate.value && currentTemplate.value.filename) {
        exportTemplate(currentTemplate.value.filename)
      }
    }

    // 激活选中的模板
    const activateSelectedTemplate = () => {
      if (selectedTemplate.value && selectedTemplate.value.id) {
        activateTemplate(selectedTemplate.value.id)
      }
    }

    // 激活模板版本
    const activateTemplate = async (id, formType) => {
      if (!id) return

      activating.value = true

      // 保存当前选中的模板ID，用于刷新后重新选择
      const activatedId = id

      try {
        const response = await excelApi.activateTemplate(id, formType)

        if (response.data.status === 'success') {
          alert('模板激活成功！')
          // 刷新模板列表
          await fetchTemplates()

          // 在刷新后，重新选择刚才激活的模板
          if (activatedId) {
            const activatedTemplate = templates.value.find(t => t.id === activatedId)
            if (activatedTemplate) {
              selectedTemplate.value = activatedTemplate
            }
          }
        } else {
          alert('激活模板失败: ' + response.data.message)
        }
      } catch (err) {
        console.error('激活模板失败:', err)
        alert('激活模板失败: ' + (err.response && err.response.data && err.response.data.message ? err.response.data.message : err.message))
      } finally {
        activating.value = false
      }
    }

    // 格式化日期
    const formatDate = (timestamp) => {
      if (!timestamp) return '未知'
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN')
    }

    // 获取模板类型的徽章样式
    const getTemplateBadgeClass = (type) => {
      switch (type) {
        case '安全测评':
          return 'bg-primary'
        case '安全监测':
          return 'bg-info'
        case '应用加固':
          return 'bg-success'
        default:
          return 'bg-secondary'
      }
    }

    // 显示设置别名模态框
    const showSetAliasModal = () => {
      if (!selectedTemplate.value) return

      // 设置初始别名为当前别名
      aliasForm.alias = selectedTemplate.value.alias || ''

      // 显示模态框
      if (setAliasModal.value) {
        const modal = new Modal(setAliasModal.value)
        modal.show()
      }
    }

    // 设置模板别名
    const setTemplateAlias = async () => {
      if (!selectedTemplate.value || !selectedTemplate.value.filename) return

      settingAlias.value = true

      // 保存当前选中的模板ID和类型，用于刷新后重新选择
      const currentId = selectedTemplate.value.id
      const currentType = selectedTemplate.value.type

      try {
        const response = await excelApi.setTemplateAlias(
          selectedTemplate.value.filename,
          aliasForm.alias
        )

        if (response.data.status === 'success') {
          // 更新本地模板数据
          selectedTemplate.value.alias = aliasForm.alias

          // 关闭模态框
          if (setAliasModal.value) {
            try {
              const modal = Modal.getInstance(setAliasModal.value)
              if (modal && modal._element && modal._element.parentNode) {
                modal.hide()
              } else {
                // 手动清理模态框
                setAliasModal.value.style.display = 'none'
                setAliasModal.value.classList.remove('show')
                document.body.classList.remove('modal-open')
                const backdrops = document.querySelectorAll('.modal-backdrop')
                backdrops.forEach(backdrop => backdrop.remove())
              }
            } catch (error) {
              console.warn('关闭模态框时出错:', error)
            }
          }

          // 刷新模板列表
          await fetchTemplates()

          // 在刷新后，重新选择刚才设置别名的模板
          if (currentId) {
            // 确保选择了同类型的模板
            if (currentType) {
              selectedType.value = currentType
            }

            // 查找并选择刚才设置别名的模板
            const updatedTemplate = templates.value.find(t => t.id === currentId)
            if (updatedTemplate) {
              selectedTemplate.value = updatedTemplate
            }
          }
        } else {
          alert('设置别名失败: ' + response.data.message)
        }
      } catch (err) {
        console.error('设置别名失败:', err)
        alert('设置别名失败: ' + (err.response && err.response.data && err.response.data.message ? err.response.data.message : err.message))
      } finally {
        settingAlias.value = false
      }
    }

    // 显示删除确认模态框
    const confirmDeleteTemplate = () => {
      if (!selectedTemplate.value) return

      // 显示模态框
      if (deleteConfirmModal.value) {
        const modal = new Modal(deleteConfirmModal.value)
        modal.show()
      }
    }

    // 删除模板
    const deleteTemplate = async () => {
      if (!selectedTemplate.value || !selectedTemplate.value.id) return

      deleting.value = true

      // 保存当前模板类型，用于刷新后选择同类型的模板
      const currentType = selectedTemplate.value.type

      try {
        const response = await excelApi.deleteTemplate(selectedTemplate.value.id)

        if (response.data.status === 'success') {
          // 关闭模态框
          if (deleteConfirmModal.value) {
            try {
              const modal = Modal.getInstance(deleteConfirmModal.value)
              if (modal && modal._element && modal._element.parentNode) {
                modal.hide()
              } else {
                // 手动清理模态框
                deleteConfirmModal.value.style.display = 'none'
                deleteConfirmModal.value.classList.remove('show')
                document.body.classList.remove('modal-open')
                const backdrops = document.querySelectorAll('.modal-backdrop')
                backdrops.forEach(backdrop => backdrop.remove())
              }
            } catch (error) {
              console.warn('关闭模态框时出错:', error)
            }
          }

          // 刷新模板列表
          await fetchTemplates()

          // 确保选择了同类型的模板
          if (currentType) {
            selectedType.value = currentType
          }

          // 提示成功
          alert('模板删除成功！')
        } else {
          alert('删除模板失败: ' + response.data.message)
        }
      } catch (err) {
        console.error('删除模板失败:', err)
        alert('删除模板失败: ' + (err.response && err.response.data && err.response.data.message ? err.response.data.message : err.message))
      } finally {
        deleting.value = false
      }
    }

    // 返回表单模板管理页面
    const goBack = () => {
      router.push('/form-template-manager')
    }

    onMounted(async () => {
      console.log('🚀 页面加载开始')

      // 先获取表单类型
      await fetchFormTypes()

      // 再获取模板（这样可以根据表单类型设置默认选择）
      await fetchTemplates()

      console.log('✅ 页面加载完成')

      // 初始化tooltip
      nextTick(() => {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        tooltipTriggerList.map(function (tooltipTriggerEl) {
          return new Tooltip(tooltipTriggerEl, {
            html: true,
            container: 'body',
            trigger: 'hover focus',
            boundary: 'window'
          })
        })
      })
    })

    return {
      route,
      templates,
      formTypes,
      typeTemplates,
      currentTemplate,
      loading,
      error,
      uploading,
      activating,
      deleting,
      selectedType,
      selectedTemplate,
      uploadForm,
      aliasForm,
      fileInput,
      setAliasModal,
      deleteConfirmModal,
      settingAlias,
      templateHelpText,
      exportTemplate,
      exportSelectedTemplate,
      exportCurrentTemplate,
      handleFileChange,
      uploadTemplate,
      activateTemplate,
      activateSelectedTemplate,
      showSetAliasModal,
      setTemplateAlias,
      confirmDeleteTemplate,
      deleteTemplate,
      formatDate,
      getTemplateBadgeClass,
      goBack
    }
  }
}
</script>

<style scoped>
/* 面包屑导航样式 */
.breadcrumb {
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: #6c757d;
}

.breadcrumb-item.active {
  color: #495057;
  font-weight: 500;
}

.breadcrumb-item a {
  color: #007bff;
  transition: color 0.15s ease-in-out;
}

.breadcrumb-item a:hover {
  color: #0056b3;
}

/* 页面标题样式 */
h2 {
  color: #2c3e50;
  font-weight: 600;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
  font-weight: 500;
}

.table th {
  font-weight: 600;
}

.template-help {
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s ease;
}

.template-help:hover {
  transform: scale(1.1);
  opacity: 0.9;
}

/* 自定义tooltip样式 */
.tooltip-inner {
  max-width: 400px;
  text-align: left;
  padding: 15px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.tooltip-inner code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 90%;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .breadcrumb {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }
}
</style>
