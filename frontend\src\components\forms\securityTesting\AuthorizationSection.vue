<template>
  <collapsible-card card-class="border-warning" storage-key="authorization-section">
    <template #header>
      <i class="bi bi-shield-check me-2"></i>授权信息
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-warning text-dark">功能: {{ productFeatures || '未填写' }}</span>
        <span class="badge bg-warning text-dark">授权期限: {{ authStartDate ? authStartDate + ' 至 ' + (authEndDate || '未设置') : '未设置' }}</span>
      </div>
    </template>

    <div class="form-floating mb-3">
      <input
        type="text"
        class="form-control"
        id="productFeatures"
        v-model="productFeatures"
        placeholder="例如：Android测评、iOS测评"
        @input="updateProductFeatures"
      >
      <label for="productFeatures">产品功能</label>
    </div>

    <div class="form-floating mb-3">
      <textarea
        class="form-control"
        id="authorizationFeatures"
        v-model="authorizationFeatures"
        style="height: 100px"
        placeholder="例如：安卓测评不限制个数及次数，ios测评不限制个数及次数。"
        @input="updateAuthorizationFeatures"
      ></textarea>
      <label for="authorizationFeatures">授权功能</label>
    </div>

    <div class="row mb-3">
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="date"
            class="form-control"
            id="authStartDate"
            v-model="authStartDate"
            @input="updateAuthStartDate"
          >
          <label for="authStartDate">授权开始日期</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="date"
            class="form-control"
            id="authEndDate"
            v-model="authEndDate"
            @input="updateAuthEndDate"
          >
          <label for="authEndDate">授权结束日期</label>
        </div>
      </div>
    </div>
  </collapsible-card>
</template>

<script>
/**
 * 授权信息部分组件
 * 用于填写产品功能和授权信息
 */
import CollapsibleCard from '../common/CollapsibleCard.vue'

export default {
  name: 'AuthorizationSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 产品功能
    productFunc: {
      type: String,
      default: ''
    },
    // 授权功能
    authFunc: {
      type: String,
      default: ''
    },
    // 授权开始日期
    authStart: {
      type: String,
      default: ''
    },
    // 授权结束日期
    authEnd: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      productFeatures: this.productFunc,
      authorizationFeatures: this.authFunc,
      authStartDate: this.authStart,
      authEndDate: this.authEnd
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    productFunc(newVal) {
      this.productFeatures = newVal
    },
    authFunc(newVal) {
      this.authorizationFeatures = newVal
    },
    authStart(newVal) {
      this.authStartDate = newVal
    },
    authEnd(newVal) {
      this.authEndDate = newVal
    }
  },
  methods: {
    /**
     * 更新产品功能
     * 向父组件发送更新事件
     */
    updateProductFeatures() {
      this.$emit('update:productFunc', this.productFeatures)
    },

    /**
     * 更新授权功能
     * 向父组件发送更新事件
     */
    updateAuthorizationFeatures() {
      this.$emit('update:authFunc', this.authorizationFeatures)
    },

    /**
     * 更新授权开始日期
     * 向父组件发送更新事件
     */
    updateAuthStartDate() {
      this.$emit('update:authStart', this.authStartDate)
    },

    /**
     * 更新授权结束日期
     * 向父组件发送更新事件
     */
    updateAuthEndDate() {
      this.$emit('update:authEnd', this.authEndDate)
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
</style>
