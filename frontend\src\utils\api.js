import axios from 'axios'

// 根据环境设置基础URL
const baseURL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')  // 生产环境使用相对路径，通过nginx代理
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')  // 开发环境直连后端

// 创建axios实例
const api = axios.create({
  baseURL: baseURL,
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  config => {
    // 优先使用access_token，兼容旧的token
    const token = localStorage.getItem('access_token') || localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理通用错误
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          // 但在调试页面中不自动跳转，让用户看到错误信息
          if (window.location.pathname !== '/auth-debug') {
            localStorage.removeItem('access_token')
            localStorage.removeItem('token')
            localStorage.removeItem('user_info')
            if (window.location.pathname !== '/login') {
              window.location.href = '/login'
            }
          } else {
            // 在调试页面中，只记录错误，不跳转
            console.error('401错误 (调试模式):', data.message)
          }
          break
        case 403:
          // 权限不足
          console.error('权限不足:', data.message)
          break
        case 404:
          // 资源不存在
          console.error('资源不存在:', data.message)
          break
        case 500:
          // 服务器内部错误
          console.error('服务器错误:', data.message)
          break
        default:
          console.error('请求错误:', data.message || error.message)
      }
    } else if (error.request) {
      // 网络错误
      console.error('网络错误:', error.message)
    } else {
      // 其他错误
      console.error('请求配置错误:', error.message)
    }
    
    return Promise.reject(error)
  }
)

console.log('API baseURL:', baseURL, 'Environment:', process.env.NODE_ENV)

export default api
