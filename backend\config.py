import os
from datetime import timedelta
from urllib.parse import quote_plus

basedir = os.path.abspath(os.path.dirname(__file__))

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-please-change-in-production'

    # MySQL数据库配置
    MYSQL_HOST = '************'
    MYSQL_PORT = 3306
    MYSQL_USER = 'junguangchen'
    MYSQL_PASSWORD = '1qaz@WSX'
    MYSQL_DATABASE = 'export_excel_prod'  # 生产环境数据库

    # 构建MySQL连接字符串
    encoded_password = quote_plus(MYSQL_PASSWORD)
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{encoded_password}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4'

    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # 数据库连接池优化配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 20,                    # 连接池大小
        'pool_timeout': 30,                 # 获取连接超时时间(秒)
        'pool_recycle': 3600,              # 连接回收时间(秒)，1小时
        'pool_pre_ping': True,             # 连接预检查，确保连接有效
        'max_overflow': 30,                # 最大溢出连接数
        'pool_reset_on_return': 'commit',  # 连接返回时重置方式
        'echo': False,                     # 关闭SQL日志输出
        'echo_pool': False,                # 关闭连接池日志
    }

    UPLOAD_FOLDER = os.path.join(basedir, 'uploads')
    EXCEL_FOLDER = os.path.join(basedir, 'excel_files')
    # Excel子目录配置
    EXCEL_TEMPLATES_FOLDER = os.path.join(basedir, 'excel_files', 'templates')
    EXCEL_GENERATED_FOLDER = os.path.join(basedir, 'excel_files', 'generated')
    # 添加CORS配置
    CORS_HEADERS = 'Content-Type'

    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-string-please-change'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)  # Token 24小时过期
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)  # 刷新Token 30天过期

    # Bcrypt配置
    BCRYPT_LOG_ROUNDS = 12

    # Redis缓存配置
    REDIS_HOST = '************'
    REDIS_PORT = 6379
    REDIS_DB = 0
    REDIS_PASSWORD = '1qaz@WSX'  # Redis密码
    REDIS_URL = f'redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}'

    # Flask-Caching配置
    CACHE_TYPE = 'redis'
    CACHE_REDIS_URL = REDIS_URL
    CACHE_DEFAULT_TIMEOUT = 300  # 默认缓存5分钟

    # 缓存配置
    CACHE_CONFIG = {
        'user_permissions': 1800,    # 用户权限缓存30分钟
        'components': 3600,          # 组件配置缓存1小时
        'templates': 7200,           # 模板配置缓存2小时
        'form_config': 3600,         # 表单字段配置缓存1小时
        'user_info': 1800,           # 用户信息缓存30分钟
        'form_snapshot': 3600,       # 表单快照缓存1小时
        'history_records': 1800,     # 历史记录缓存30分钟
        'duplicate_check': 300,      # 重复提交检测缓存5分钟
        'rate_limit': 3600,          # 限流缓存1小时
        'static_resources': 86400,   # 静态资源缓存24小时
    }


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False

    # 开发环境使用专用数据库
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://junguangchen:1qaz%40WSX@************:3306/export_excel_dev'

    # 开发环境使用Redis缓存 - 使用不同的DB
    REDIS_DB = 1  # 开发环境使用DB 1，避免与生产环境冲突
    REDIS_URL = f'redis://:{Config.REDIS_PASSWORD}@{Config.REDIS_HOST}:{Config.REDIS_PORT}/{REDIS_DB}'
    CACHE_REDIS_URL = REDIS_URL

    # 开发环境可以使用更详细的日志
    LOG_LEVEL = 'DEBUG'

    # 开发环境可以使用较短的缓存时间进行测试
    CACHE_CONFIG = {
        'user_permissions': 300,     # 开发环境5分钟
        'components': 600,           # 开发环境10分钟
        'templates': 900,            # 开发环境15分钟
        'form_config': 600,          # 开发环境10分钟
        'user_info': 300,            # 开发环境5分钟
        'form_snapshot': 600,        # 开发环境10分钟
        'history_records': 300,      # 开发环境5分钟
        'duplicate_check': 60,       # 开发环境1分钟
        'rate_limit': 600,           # 开发环境10分钟
        'static_resources': 3600,    # 开发环境1小时
    }


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False

    # 生产环境使用生产数据库
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://junguangchen:1qaz%40WSX@************:3306/export_excel_prod'

    # 生产环境使用Redis缓存 - 使用默认DB 0
    REDIS_DB = 0  # 生产环境使用DB 0
    REDIS_URL = f'redis://:{Config.REDIS_PASSWORD}@{Config.REDIS_HOST}:{Config.REDIS_PORT}/{REDIS_DB}'
    CACHE_REDIS_URL = REDIS_URL

    # 生产环境使用更安全的密钥
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-secret-key-change-me'
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'production-jwt-secret-change-me'

    # 生产环境日志级别
    LOG_LEVEL = 'INFO'

    # 生产环境使用更长的缓存时间 - 性能优化
    CACHE_CONFIG = {
        'user_permissions': 7200,    # 生产环境2小时 (优化)
        'components': 14400,         # 生产环境4小时 (优化)
        'templates': 28800,          # 生产环境8小时 (优化)
        'form_config': 14400,        # 生产环境4小时 (优化)
        'user_info': 7200,           # 生产环境2小时 (优化)
        'form_snapshot': 14400,      # 生产环境4小时 (优化)
        'history_records': 7200,     # 生产环境2小时 (优化)
        'duplicate_check': 300,      # 生产环境5分钟
        'rate_limit': 3600,          # 生产环境1小时
        'static_resources': 86400,   # 生产环境24小时
    }

    # 生产环境数据库连接池优化
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 30,                    # 生产环境更大的连接池
        'pool_timeout': 60,                 # 生产环境更长的超时时间
        'pool_recycle': 7200,              # 生产环境2小时回收
        'pool_pre_ping': True,             # 连接预检查
        'max_overflow': 50,                # 生产环境更多溢出连接
        'pool_reset_on_return': 'commit',  # 连接返回时重置
        'echo': False,                     # 关闭SQL日志
        'echo_pool': False,                # 关闭连接池日志
    }


class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    TESTING = True

    # 测试环境使用内存数据库
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

    # 测试环境禁用缓存或使用简单缓存
    CACHE_TYPE = 'simple'

    # 测试环境使用很短的缓存时间
    CACHE_CONFIG = {
        'user_permissions': 60,      # 测试环境1分钟
        'components': 120,           # 测试环境2分钟
        'templates': 180,            # 测试环境3分钟
        'form_config': 120,          # 测试环境2分钟
        'user_info': 60,             # 测试环境1分钟
    }


# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
