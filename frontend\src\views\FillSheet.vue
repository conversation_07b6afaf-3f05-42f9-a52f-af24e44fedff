<template>
  <div class="container-fluid mt-4 px-md-3 px-lg-4">
    <div class="row justify-content-center">
      <div class="col-12">
        <div class="modern-card main-form-card">
          <div class="modern-card-header d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
              <i class="bi bi-file-earmark-plus me-2 text-primary"></i>
              <h5 class="mb-0 text-primary">运维信息登记表单</h5>
              <span class="text-muted ms-3">{{ getCurrentFormTypeDescription() }}</span>
            </div>
            <div class="form-actions">
              <button
                type="button"
                class="btn-modern btn-modern-primary btn-modern-sm me-2"
                @click="showSaveModal = true"
              >
                <i class="bi bi-camera me-1"></i>保存快照
              </button>
              <button
                type="button"
                class="btn-modern btn-modern-secondary btn-modern-sm"
                @click="loadForm"
              >
                <i class="bi bi-folder-open me-1"></i>加载快照
              </button>
            </div>
          </div>
          <div class="modern-card-body">

          <!-- 使用表单头部组件 -->
          <form-header
            v-model="formData.公司名称"
            :doc-type="formData.文档后缀"
            :available-form-types="availableFormTypes"
            :selected-template="selectedTemplate"
            @document-type-change="onTemplateTypeChange"
            @template-change="onTemplateChange"
          />

          <div class="alert alert-info border-0 mb-4" style="background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);">
            <div class="d-flex align-items-start">
              <i class="bi bi-info-circle-fill text-info me-3 mt-1"></i>
              <div>
                <strong>维护提醒：</strong>为了后期对项目维护，请保持项目运维文档内容为最新，维护后请及时记录维护内容，并更新整个项目运维文档中变化的基本信息。
              </div>
            </div>
          </div>

            <form @submit.prevent="submitForm(false)">


              <!-- 使用动态组件加载当前选择的表单 -->
              <component
                :is="currentFormComponent"
                :key="formType"
                v-model="formData"
                :component-groups="componentGroups"
                :form-type="formType"
                @save-form="saveForm"
                @load-form="loadForm"
                @refresh-components="handleRefreshComponents"
                @form-submit="handleDynamicFormSubmit"
                @form-change="handleDynamicFormChange"
                @show-toast="showToast"
              >
                <!-- 加载中的占位内容 -->
                <template #fallback>
                  <div class="loading-container">
                    <div class="loading-spinner-modern"></div>
                    <p>正在加载表单，请稍候...</p>
                  </div>
                </template>
              </component>

              <!-- 使用表单底部组件 -->
              <form-footer
                :loading="loading"
                @reset="resetForm"
              />
            </form>
          </div>
        </div>
        </div>
      </div>
    </div>

    <!-- 快照模态框 -->
    <snapshot-modal
      ref="snapshotModal"
      :mode="snapshotModalMode"
      :snapshots="snapshots"
      :default-name="snapshotName"
      @save-snapshot="handleSaveSnapshot"
      @load-snapshot="handleLoadSnapshot"
      @delete-snapshot="deleteSnapshot"
    />

    <!-- 简单模态框 - 保存快照 -->
    <simple-modal
      :title="'保存表单快照'"
      :show="showSaveModal"
      @update:show="showSaveModal = $event"
      :modal-size="'md'"
      :safe-modal="true"
      :responsive="true"
      :responsive-options="{
        minWidth: 400,
        minHeight: 300,
        mobileFullscreen: false
      }"
    >
      <div class="mb-3">
        <label for="simpleSnapshotName" class="form-label">快照名称</label>
        <input
          type="text"
          class="modern-input"
          id="simpleSnapshotName"
          v-model="snapshotName"
          placeholder="请输入快照名称"
        >
      </div>
      <template #footer-buttons>
        <button
          type="button"
          class="btn-modern btn-modern-primary"
          @click="handleSimpleSaveSnapshot"
          :disabled="!snapshotName"
        >
          保存
        </button>
      </template>
    </simple-modal>

    <!-- 简单模态框 - 快照管理 -->
    <simple-modal
      :title="'快照管理'"
      :show="showLoadModal"
      @update:show="showLoadModal = $event"
      :modal-size="'xl'"
      :safe-modal="true"
      :responsive="true"
      :responsive-options="{
        minWidth: 600,
        minHeight: 400,
        mobileFullscreen: true
      }"
    >
      <div v-if="snapshots.length === 0" class="empty-state-container">
        <div class="empty-state-content">
          <div class="empty-state-icon">
            <i class="bi bi-camera2"></i>
          </div>
          <h4 class="empty-state-title">暂无快照</h4>
          <p class="empty-state-description">
            您还没有保存任何快照<br>
            填写表单后，点击"保存快照"按钮来保存当前状态<br>
            <small class="text-muted">保存的快照可以在这里进行管理和加载</small>
          </p>
          <div class="empty-state-actions">
            <button
              type="button"
              class="btn-modern btn-modern-primary btn-modern-sm"
              @click="showLoadModal = false; showSaveModal = true"
            >
              <i class="bi bi-plus-circle me-1"></i>创建第一个快照
            </button>
          </div>
        </div>
      </div>
      <div v-else>
        <!-- 快照过滤和搜索栏 -->
        <div class="snapshot-filter-bar">
          <div class="filter-left">
            <div class="input-group input-group-sm">
              <span class="input-group-text">
                <i class="bi bi-search"></i>
              </span>
              <input
                type="text"
                class="modern-input"
                placeholder="搜索快照名称..."
                v-model="snapshotFilter.searchText"
                @input="filterSnapshots"
              >
            </div>
          </div>
          <div class="filter-center">
            <select
              class="modern-select"
              style="font-size: 0.875rem;"
              v-model="snapshotFilter.formType"
              @change="filterSnapshots"
            >
              <option value="">所有类型</option>
              <option value="安全测评">安全测评</option>
              <option value="安全监测">安全监测</option>
              <option value="应用加固">应用加固</option>
            </select>
          </div>
          <div class="filter-right">
            <select
              class="modern-select"
              style="font-size: 0.875rem;"
              v-model="snapshotFilter.sortBy"
              @change="filterSnapshots"
            >
              <option value="date_desc">最新创建</option>
              <option value="date_asc">最早创建</option>
              <option value="name_asc">名称 A-Z</option>
              <option value="name_desc">名称 Z-A</option>
            </select>
          </div>
        </div>

        <!-- 快照统计和操作栏 -->
        <div class="snapshot-header">
          <div class="snapshot-stats">
            <span class="status-badge status-badge-info me-2">
              <i class="bi bi-camera2 me-1"></i>{{ filteredSnapshots.length }}/{{ snapshots.length }} 个快照
            </span>
            <span v-if="selectedSnapshots.length > 0" class="status-badge status-badge-warning">
              <i class="bi bi-check-square me-1"></i>已选择 {{ selectedSnapshots.length }} 个
            </span>
            <span v-if="snapshotFilter.searchText || snapshotFilter.formType" class="status-badge status-badge-info">
              <i class="bi bi-funnel me-1"></i>已过滤
            </span>
          </div>
          <div class="snapshot-actions">
            <div class="form-check me-3">
              <input
                class="form-check-input"
                type="checkbox"
                id="selectAllSnapshots"
                :checked="selectedSnapshots.length === filteredSnapshots.length && filteredSnapshots.length > 0"
                :indeterminate="selectedSnapshots.length > 0 && selectedSnapshots.length < filteredSnapshots.length"
                @change="toggleSelectAll"
              >
              <label class="form-check-label fw-medium" for="selectAllSnapshots">
                全选
              </label>
            </div>
            <button
              v-if="selectedSnapshots.length > 0"
              type="button"
              class="btn btn-sm btn-outline-danger"
              @click="batchDeleteSelected"
            >
              <i class="bi bi-trash me-1"></i>删除选中
            </button>
          </div>
        </div>

        <!-- 快照列表 -->
        <div class="snapshot-grid">
          <div
            v-for="(snapshot, index) in filteredSnapshots"
            :key="snapshot.id"
            class="snapshot-card"
            :class="{ 'selected': selectedSnapshotIndex === getOriginalIndex(snapshot) }"
            @click="selectedSnapshotIndex = getOriginalIndex(snapshot)"
          >
            <div class="snapshot-card-header">
              <div class="snapshot-checkbox">
                <input
                  class="form-check-input"
                  type="checkbox"
                  :value="snapshot.snapshotId || snapshot.id"
                  v-model="selectedSnapshots"
                  @click.stop
                >
              </div>
              <div class="snapshot-type-badge">
                <span class="status-badge" :class="getFormTypeBadgeClass(snapshot.formType)">
                  {{ snapshot.formType }}
                </span>
              </div>
              <div class="snapshot-actions">
                <button
                  type="button"
                  class="btn btn-sm btn-outline-danger"
                  @click.stop="deleteSnapshot(getOriginalIndex(snapshot))"
                  title="删除快照"
                >
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </div>
            <div class="snapshot-card-body">
              <h6 class="snapshot-title">{{ snapshot.name }}</h6>
              <div class="snapshot-meta">
                <small class="text-muted">
                  <i class="bi bi-clock me-1"></i>{{ formatSnapshotDate(snapshot.date) }}
                </small>
              </div>
            </div>
            <div class="snapshot-card-footer">
              <button
                type="button"
                class="btn btn-sm btn-primary w-100"
                @click.stop="handleLoadSnapshot(getOriginalIndex(snapshot))"
              >
                <i class="bi bi-download me-1"></i>加载此快照
              </button>
            </div>
          </div>
        </div>
      </div>
      <template #footer-buttons>
        <div class="snapshot-footer">
          <div class="snapshot-footer-left">
            <button
              type="button"
              class="btn btn-outline-primary btn-sm me-2"
              @click="showLoadModal = false; showSaveModal = true"
            >
              <i class="bi bi-plus-circle me-1"></i>新建快照
            </button>
            <button
              v-if="snapshots.length > 0 && selectedSnapshotIndex !== null"
              type="button"
              class="btn btn-success btn-sm"
              @click="handleSimpleLoadSnapshot"
              :title="getLoadButtonTooltip()"
            >
              <i class="bi bi-download me-1"></i>
              {{ getLoadButtonText() }}
            </button>
          </div>
          <div class="snapshot-footer-right">
            <button
              v-if="snapshotFilter.searchText || snapshotFilter.formType"
              type="button"
              class="btn btn-outline-info btn-sm me-2"
              @click="resetSnapshotFilter"
              title="清除所有过滤条件"
            >
              <i class="bi bi-x-circle me-1"></i>清除过滤
            </button>
            <button
              type="button"
              class="btn btn-outline-secondary btn-sm me-2"
              @click="manualMigrationCheck"
              title="重新检查并迁移localStorage快照"
            >
              <i class="bi bi-arrow-up-circle me-1"></i>迁移本地快照
            </button>
            <div class="btn-group me-2" role="group">
              <button
                type="button"
                class="btn btn-outline-warning btn-sm dropdown-toggle"
                data-bs-toggle="dropdown"
                aria-expanded="false"
                title="批量清理选项"
              >
                <i class="bi bi-broom me-1"></i>批量清理
              </button>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" @click.prevent="showClearByTypeModal">
                  <i class="bi bi-funnel me-2"></i>按表单类型清理
                </a></li>
                <li><a class="dropdown-item" href="#" @click.prevent="showClearByTimeModal">
                  <i class="bi bi-calendar-x me-2"></i>按时间清理
                </a></li>
                <li><a class="dropdown-item" href="#" @click.prevent="showClearByNameModal">
                  <i class="bi bi-search me-2"></i>按名称模式清理
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" @click.prevent="clearAllSnapshotsConfirm">
                  <i class="bi bi-trash3 me-2"></i>清除所有快照
                </a></li>
              </ul>
            </div>
            <button
              type="button"
              class="btn btn-outline-secondary btn-sm"
              @click="showLoadModal = false"
              title="关闭快照管理"
            >
              <i class="bi bi-x-lg me-1"></i>取消
            </button>
          </div>
        </div>
      </template>
    </simple-modal>

    <!-- 重复提交处理模态框 -->
    <duplicate-submission-modal
      v-if="showDuplicateModal"
      :duplicate-data="duplicateData"
      @close="showDuplicateModal = false"
      @force-overwrite="handleForceOverwrite"
      @submit-with-alias="handleSubmitWithAlias"
      @edit-existing="handleEditExisting"
      @view-existing="handleViewExisting"
      @go-to-history="handleGoToHistory"
    />
</template>

<script>
import excelApi from '@/api/excel'
import { defineAsyncComponent } from 'vue'
import FormHeader from '@/components/forms/common/FormHeader.vue'
import FormFooter from '@/components/forms/common/FormFooter.vue'
import SnapshotModal from '@/components/forms/common/SnapshotModal.vue'
import SimpleModal from '@/components/forms/common/SimpleModal.vue'
import DuplicateSubmissionModal from '@/components/modals/DuplicateSubmissionModal.vue'
// 移除增强组件导入，回退到原始状态

// 统一的API基础URL配置
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

// 使用异步组件加载表单组件，并添加加载状态
const SecurityTestingForm = defineAsyncComponent({
  loader: () => import('@/components/forms/securityTesting/SecurityTestingForm.vue'),
  loadingComponent: null, // 使用默认的加载组件
  delay: 200, // 延迟显示加载组件的时间
  timeout: 5000 // 超时时间
})

const SecurityMonitoringForm = defineAsyncComponent({
  loader: () => import('@/components/forms/securityMonitoring/SecurityMonitoringForm.vue'),
  loadingComponent: null,
  delay: 200,
  timeout: 5000
})

const AppHardeningForm = defineAsyncComponent({
  loader: () => import('@/components/forms/appHardening/AppHardeningForm.vue'),
  loadingComponent: null,
  delay: 200,
  timeout: 5000
})

const GenericForm = defineAsyncComponent({
  loader: () => import('@/components/forms/GenericForm.vue'),
  loadingComponent: null,
  delay: 200,
  timeout: 5000
})

const DynamicFormRenderer = defineAsyncComponent({
  loader: () => import('@/components/forms/DynamicFormRenderer.vue'),
  loadingComponent: null,
  delay: 200,
  timeout: 5000
})

// 导入拆分出来的方法和数据
import {
  submitForm,
  validateForm,
  downloadFile,
  formatDate,
  getNextYearDate,
  initServiceTypes,
  onTemplateTypeChange
} from '@/utils/fillSheetMethods'
// 从配置文件导入表单数据配置
import {
  getInitialFormData,
  getComponentGroupsFromDatabase
} from '@/config/formDataConfig'
// 从工具文件导入本地存储相关函数
import {
  saveFormDataToLocalStorage,
  loadFormDataFromLocalStorage
} from '@/utils/fillSheetData'
// 导入表单快照API
import {
  saveFormSnapshot,
  loadFormSnapshot,
  deleteFormSnapshot,
  listFormSnapshots,
  migrateLocalStorageSnapshots,
  deleteSnapshotById,
  loadSnapshotById,
  batchDeleteSnapshots,
  clearSnapshotsByCondition,
  clearAllSnapshots
} from '@/api/formSnapshot'


/**
 * 填写表单组件
 * 用于填写运维信息并生成Excel文件
 */
export default {
  name: 'FillSheet',
  components: {
    FormHeader,
    FormFooter,
    SnapshotModal,
    SimpleModal,
    DuplicateSubmissionModal,
    'security-testing-form': SecurityTestingForm,
    'security-monitoring-form': SecurityMonitoringForm,
    'app-hardening-form': AppHardeningForm,
    'generic-form': GenericForm,
    'dynamic-form-renderer': DynamicFormRenderer
  },
  data() {
    return {
      // 使用数据库数据，如果失败则回退到静态配置
      componentGroups: {},
      serviceTypes: [],
      formData: getInitialFormData(),
      loading: false,
      error: null,
      isChangingTemplate: false,
      placeholderTimeout: null,
      // 表单类型列表
      availableFormTypes: ['安全测评', '安全监测', '应用加固'], // 默认值，会从数据库动态加载
      // 选中的模板
      selectedTemplate: null,
      // 多版本快照相关数据
      snapshots: [],
      filteredSnapshots: [], // 过滤后的快照列表
      snapshotModalMode: 'save',
      snapshotName: '',
      selectedSnapshotIndex: null,
      selectedSnapshots: [], // 批量选择的快照ID列表
      showSaveModal: false,
      showLoadModal: false,

      // 快照过滤条件
      snapshotFilter: {
        searchText: '',
        formType: '',
        sortBy: 'date_desc'
      },
      // 重复提交处理相关
      showDuplicateModal: false,
      duplicateData: null
    }
  },
  async mounted() {
    // 检查是否从Excel导入页面跳转过来
    const importId = this.$route.query.import_id
    const formType = this.$route.query.form_type
    const fromImport = this.$route.query.from_import

    if (importId) {
      console.log('从Excel导入页面跳转，导入ID:', importId, '表单类型:', formType)
      // 从导入数据初始化表单
      await this.loadFromImportData(importId, formType)
    } else if (fromImport === 'true') {
      console.log('从Excel导入页面直接跳转，表单类型:', formType)
      // 从sessionStorage加载导入的数据
      await this.loadFromSessionStorage(formType)
    } else {
      // 从本地存储中读取上次选择的表单类型
      const savedFormType = localStorage.getItem('selectedFormType')
      if (savedFormType) {
        this.formData.文档后缀 = savedFormType
      }
    }

    // 自动填充当前用户信息
    this.fillCurrentUserInfo()

    // 加载表单类型
    await this.loadFormTypes()
    // 加载组件数据
    await this.loadComponentGroups()
    // 初始化服务类型
    this.initServiceTypes()

    // 加载已保存的快照列表
    this.loadSnapshots()

    // 页面加载后初始化表单字段
    this.$nextTick(() => {
      this.onTemplateTypeChange()
    })
  },
  computed: {
    // 添加计算属性，用于跟踪表单类型的变化
    formType() {
      return this.formData.文档后缀
    },
    // 根据表单类型返回对应的组件
    currentFormComponent() {
      // 预定义的表单组件映射
      const componentMap = {
        '安全监测': 'security-monitoring-form',
        '应用加固': 'app-hardening-form',
        '安全测评': 'security-testing-form'
      }

      // 如果是预定义的表单类型，返回对应组件
      if (componentMap[this.formType]) {
        return componentMap[this.formType]
      }

      // 对于新的表单类型，使用动态表单渲染器
      return 'dynamic-form-renderer'
    }
  },
  watch: {
    // 监听表单类型的变化
    formType(newVal, oldVal) {
      console.log(`FillSheet: 表单类型从 ${oldVal} 变为 ${newVal}`)
      // 强制重新渲染组件
      this.$forceUpdate()

      // 延迟一段时间后再次强制重新渲染，确保所有子组件都能正确更新
      this.$nextTick(() => {
        setTimeout(() => {
          console.log('FillSheet: 延迟强制重新渲染，当前表单类型:', newVal)
          this.$forceUpdate()
        }, 100)
      })
    }
  },
  methods: {
    // 移除增强组件相关方法，回退到原始状态

    // 获取当前表单类型描述
    getCurrentFormTypeDescription() {
      const descriptions = {
        '安全监测': '安全监测运维信息登记',
        '安全测评': '安全测评运维信息登记',
        '应用加固': '应用加固运维信息登记'
      }
      return descriptions[this.formType] || '运维信息登记'
    },

    /**
     * 自动填充当前用户信息
     */
    fillCurrentUserInfo() {
      try {
        let editorName = ''

        // 方法1：从Vuex store获取当前用户信息
        const currentUser = this.$store.getters.currentUser
        if (currentUser) {
          editorName = currentUser.real_name || currentUser.username || currentUser.name
          console.log('从Vuex store获取编辑人:', editorName)
        }

        // 方法2：如果Vuex中没有，尝试从localStorage获取
        if (!editorName) {
          const userInfo = localStorage.getItem('user_info')
          if (userInfo) {
            try {
              const user = JSON.parse(userInfo)
              editorName = user.real_name || user.username || user.name
              console.log('从localStorage获取编辑人:', editorName)
            } catch (e) {
              console.error('解析localStorage用户信息失败:', e)
            }
          }
        }

        // 方法3：如果都没有，使用默认值
        if (!editorName) {
          editorName = 'admin' // 默认编辑人
          console.warn('使用默认编辑人:', editorName)
        }

        // 更新编辑人信息
        this.formData.编辑人 = editorName
        console.log('FillSheet 最终设置的编辑人:', editorName)
      } catch (error) {
        console.error('填充当前用户信息失败:', error)
        // 出错时使用默认值
        this.formData.编辑人 = 'admin'
      }
    },

    /**
     * 从sessionStorage加载导入的数据
     */
    async loadFromSessionStorage(formType) {
      try {
        console.log('正在从sessionStorage加载导入数据...')

        // 检查是否有导入的数据
        const importedDataStr = sessionStorage.getItem('importedFormData')
        const isFromExcel = sessionStorage.getItem('importedFromExcel')

        if (importedDataStr && isFromExcel === 'true') {
          // 检查是否已经使用过这些数据
          const dataUsed = sessionStorage.getItem('importDataUsed')
          if (dataUsed === 'true') {
            console.log('导入数据已经被使用过，跳过重复加载')
            return
          }

          const importedData = JSON.parse(importedDataStr)
          console.log('成功获取导入数据:', importedData)

          // 设置表单类型
          if (formType) {
            this.formData.文档后缀 = formType
            importedData.文档后缀 = formType
          }

          // 合并导入的数据到表单数据
          this.formData = {
            ...this.formData,
            ...importedData
          }

          // 保存表单类型到本地存储
          localStorage.setItem('selectedFormType', this.formData.文档后缀)

          console.log('表单数据已从Excel导入数据初始化:', this.formData)

          // 重新初始化服务类型
          this.initServiceTypes()

          // 强制重新渲染界面
          this.$nextTick(() => {
            this.$forceUpdate()
            console.log('Excel导入数据加载完成，强制重新渲染界面')

            // 延迟再次渲染，确保所有组件都能正确更新
            setTimeout(() => {
              this.$forceUpdate()
              console.log('二次强制重新渲染，确保组件完全更新')
            }, 300)
          })

          // 显示成功提示
          this.showToast('表单数据已从Excel文件中加载，您可以编辑后重新提交', '成功', 'success')

          // 标记数据已经被使用，但不立即清理
          sessionStorage.setItem('importDataUsed', 'true')
          console.log('已标记导入数据为已使用')

          // 延迟清理sessionStorage，确保数据已经被使用且页面稳定
          setTimeout(() => {
            // 只有在数据被标记为已使用的情况下才清理
            if (sessionStorage.getItem('importDataUsed') === 'true') {
              sessionStorage.removeItem('importedFormData')
              sessionStorage.removeItem('importedFromExcel')
              sessionStorage.removeItem('importDataUsed')
              console.log('已清理sessionStorage中的导入数据')
            }
          }, 3000) // 延长到3秒，确保页面稳定

        } else {
          console.log('没有找到导入的数据')
          this.showToast('没有找到导入的数据', '提示', 'info')
        }
      } catch (error) {
        console.error('加载导入数据时发生错误:', error)
        this.showToast('加载导入数据时发生错误: ' + error.message, '错误', 'error')
      }
    },

    /**
     * 从导入数据加载表单
     */
    async loadFromImportData(importId, formType) {
      try {
        console.log('正在加载导入数据，ID:', importId)

        // 获取导入的表单数据
        const response = await fetch(`${API_BASE_URL}/excel/form_submissions/${importId}`)
        const result = await response.json()

        if (result.status === 'success') {
          const importedData = result.data.form_data
          console.log('成功获取导入数据:', importedData)

          // 设置表单类型
          if (formType) {
            this.formData.文档后缀 = formType
            importedData.文档后缀 = formType
          }

          // 合并导入的数据到表单数据
          this.formData = {
            ...this.formData,
            ...importedData
          }

          // 保存表单类型到本地存储
          localStorage.setItem('selectedFormType', this.formData.文档后缀)

          console.log('表单数据已从导入记录初始化:', this.formData)

          // 显示成功提示
          this.showToast('表单数据已从Excel导入记录中加载', '成功', 'success')

        } else {
          console.error('获取导入数据失败:', result.message)
          this.showToast('无法加载导入数据: ' + result.message, '错误', 'error')
        }
      } catch (error) {
        console.error('加载导入数据时发生错误:', error)
        this.showToast('加载导入数据时发生错误: ' + error.message, '错误', 'error')
      }
    },

    /**
     * 加载表单类型列表
     */
    async loadFormTypes() {
      try {
        const response = await fetch(`${API_BASE_URL}/excel/form-types`)
        const result = await response.json()

        if (result.status === 'success') {
          this.availableFormTypes = result.data.map(formType => formType.name)
          console.log('✅ 表单类型加载成功:', this.availableFormTypes)
        } else {
          throw new Error(result.message || '获取表单类型失败')
        }
      } catch (error) {
        console.error('❌ 加载表单类型失败:', error)
        // 使用默认表单类型作为后备
        this.availableFormTypes = ['安全测评', '安全监测', '应用加固']
        console.log('🔄 使用默认表单类型:', this.availableFormTypes)
      }
    },

    // 使用拆分出来的方法
    async submitForm(forceOverwrite = false, customAlias = null) {
      // 调试：检查调用时的参数
      console.log('FillSheet.submitForm 被调用，参数:', { forceOverwrite, customAlias })
      console.log('调用堆栈:', new Error().stack)

      // 创建一个状态对象的引用
      const self = this;
      const state = {
        get loading() { return self.loading },
        set loading(value) { self.loading = value },
        get error() { return self.error },
        set error(value) { self.error = value },
        // 添加重复检查处理回调
        onDuplicateFound: (duplicateData) => {
          self.duplicateData = duplicateData
          self.showDuplicateModal = true
        }
      };

      // 如果有自定义别名，添加到表单数据中
      const formDataToSubmit = { ...this.formData }
      if (customAlias) {
        formDataToSubmit.custom_alias = customAlias
      }

      await submitForm(formDataToSubmit, forceOverwrite, this.resetForm, this.downloadFile, state)
    },
    validateForm() {
      return validateForm(this.formData)
    },
    async downloadFile(fileId, state, expectedFilename) {
      // 创建一个状态对象的引用
      const self = this;
      const stateObj = state || {
        get loading() { return self.loading },
        set loading(value) { self.loading = value }
      };
      await downloadFile(fileId, stateObj, expectedFilename)
    },
    formatDate(date) {
      return formatDate(date)
    },
    getNextYearDate() {
      return getNextYearDate()
    },
    initServiceTypes() {
      this.serviceTypes = initServiceTypes(this.componentGroups, this.formData.文档后缀)
    },
    async onTemplateTypeChange(newType) {
      console.log('FillSheet 接收到表单类型变更事件:', newType || this.formData.文档后缀)

      // 如果提供了新的类型，则更新表单数据
      if (newType) {
        this.formData.文档后缀 = newType

        // 表单类型切换时，重置模板选择
        console.log('表单类型切换，重置模板选择')
        this.selectedTemplate = null
        this.formData.selectedTemplateId = null
        this.formData.selectedTemplateName = null
      }

      // 创建一个状态对象的引用
      const self = this;
      const state = {
        get isChangingTemplate() { return self.isChangingTemplate },
        set isChangingTemplate(value) { self.isChangingTemplate = value },
        get placeholderTimeout() { return self.placeholderTimeout },
        set placeholderTimeout(value) { self.placeholderTimeout = value }
      };

      await onTemplateTypeChange(this.formData, state, this.initServiceTypes)

      // 强制重新渲染组件
      this.$forceUpdate()

      // 延迟一段时间后再次强制重新渲染，确保组件能够正确显示
      setTimeout(() => {
        console.log('延迟强制重新渲染，当前表单类型:', this.formData.文档后缀)
        this.$forceUpdate()

        // 再次延迟，确保导航栏也能正确更新
        setTimeout(() => {
          console.log('二次延迟强制重新渲染，确保导航栏更新')
          this.$forceUpdate()
        }, 200)
      }, 200)
    },

    /**
     * 处理动态表单提交
     */
    handleDynamicFormSubmit(event) {
      console.log('动态表单提交:', event)
      // 更新表单数据
      this.formData = { ...this.formData, ...event.formData }
      // 调用原有的提交方法
      this.submitForm()
    },

    /**
     * 处理动态表单变化
     */
    handleDynamicFormChange(event) {
      console.log('动态表单字段变化:', event)
      // 可以在这里处理字段变化的逻辑
    },

    /**
     * 显示Toast通知
     */
    showToast(message, title = '提示', type = 'info') {
      // 这里可以集成Toast通知组件
      console.log(`Toast [${type}] ${title}: ${message}`)
      // 临时使用alert作为替代
      if (type === 'error') {
        alert(`错误: ${message}`)
      } else if (type === 'warning') {
        alert(`警告: ${message}`)
      } else if (type === 'success') {
        alert(`成功: ${message}`)
      } else {
        console.log(`${title}: ${message}`)
      }
    },

    /**
     * 处理模板变更
     */
    onTemplateChange(template) {
      console.log('模板变更为:', template)
      this.selectedTemplate = template

      // 将选中的模板信息添加到表单数据中，用于后端生成Excel时使用
      if (template) {
        this.formData.selectedTemplateId = template.id
        this.formData.selectedTemplateName = template.alias || template.filename

        // 移除模板变更提示，避免频繁打扰用户
        // this.showToast(
        //   `已选择模板: ${template.alias || template.filename}`,
        //   '模板选择',
        //   'success'
        // )
      } else {
        // 清空模板信息
        this.formData.selectedTemplateId = null
        this.formData.selectedTemplateName = null
        console.log('模板选择已清空')
      }
    },


    /**
     * 重置表单
     * 将表单恢复到初始状态
     */
    resetForm() {
      // 保存当前的文档类型
      const currentDocType = this.formData.文档后缀

      // 根据当前表单类型重置表单数据
      this.formData = getInitialFormData(currentDocType)

      // 重新初始化部署应用数组
      this.initServiceTypes()

      // 清除错误信息
      this.error = null

      // 打印当前表单类型，用于调试
      console.log('重置后的表单类型:', this.formData.文档后缀)
    },

    /**
     * 加载已保存的快照列表（从Redis）
     */
    async loadSnapshots() {
      try {
        console.log('从Redis加载快照列表...')

        // 首先尝试从Redis加载
        const response = await listFormSnapshots()
        if (response.status === 'success') {
          // 转换Redis快照格式为前端格式
          this.snapshots = response.data.map(snapshot => ({
            id: snapshot.snapshot_id || `${snapshot.form_type}_${Date.now()}`,
            name: snapshot.snapshot_name,
            date: new Date(snapshot.created_at).toLocaleString(),
            formType: snapshot.form_type,
            hasData: snapshot.has_data,
            snapshotId: snapshot.snapshot_id // 添加快照ID用于精确操作
          }))
          console.log(`从Redis加载了 ${this.snapshots.length} 个快照:`, this.snapshots.map(s => s.name))

          // 应用过滤
          this.filterSnapshots()
        } else {
          this.snapshots = []
          this.filteredSnapshots = []
          console.log('Redis中没有找到快照数据')
        }

        // 检查是否需要迁移localStorage数据
        await this.checkAndMigrateLocalSnapshots()

      } catch (error) {
        console.error('从Redis加载快照列表失败:', error)

        // 回退到localStorage
        try {
          const savedSnapshots = localStorage.getItem('formSnapshots')
          if (savedSnapshots) {
            this.snapshots = JSON.parse(savedSnapshots)
            console.log(`回退到localStorage，加载了 ${this.snapshots.length} 个快照`)

            // 提示用户迁移数据
            if (this.snapshots.length > 0) {
              console.log('检测到localStorage中的快照数据，建议迁移到服务器')
            }
          } else {
            this.snapshots = []
          }
        } catch (localError) {
          console.error('从localStorage加载快照也失败:', localError)
          this.snapshots = []
        }
      }
    },

    /**
     * 检查并迁移localStorage快照到Redis
     */
    async checkAndMigrateLocalSnapshots() {
      try {
        // 检查是否已经提示过迁移
        const migrationAsked = localStorage.getItem('snapshot_migration_asked')
        if (migrationAsked === 'true') {
          console.log('已经询问过迁移，跳过提示')
          return
        }

        const savedSnapshots = localStorage.getItem('formSnapshots')
        if (savedSnapshots && savedSnapshots !== '[]') {
          const localSnapshots = JSON.parse(savedSnapshots)
          if (localSnapshots.length > 0) {
            console.log(`检测到 ${localSnapshots.length} 个localStorage快照，询问是否迁移`)

            // 标记已经询问过迁移
            localStorage.setItem('snapshot_migration_asked', 'true')

            if (confirm(`检测到 ${localSnapshots.length} 个本地保存的快照。是否迁移到服务器以便在不同设备间同步？`)) {
              let migratedCount = 0

              for (const snapshot of localSnapshots) {
                try {
                  const formType = snapshot.data?.文档后缀 || '安全测评'
                  await saveFormSnapshot(formType, snapshot.data, snapshot.name)
                  migratedCount++
                  console.log(`已迁移快照: ${snapshot.name}`)
                } catch (error) {
                  console.error(`迁移快照失败: ${snapshot.name}`, error)
                }
              }

              if (migratedCount > 0) {
                alert(`成功迁移 ${migratedCount} 个快照到服务器`)

                // 重新加载快照列表（但不再检查迁移）
                const response = await listFormSnapshots()
                if (response.status === 'success') {
                  this.snapshots = response.data.map(snapshot => ({
                    id: `${snapshot.form_type}_${Date.now()}`,
                    name: snapshot.snapshot_name,
                    date: new Date(snapshot.created_at).toLocaleString(),
                    formType: snapshot.form_type,
                    hasData: snapshot.has_data
                  }))
                }

                // 询问是否清除本地数据
                if (confirm('迁移完成！是否清除本地存储的旧快照数据？')) {
                  localStorage.removeItem('formSnapshots')
                  console.log('已清除localStorage快照数据')
                }
              }
            } else {
              console.log('用户选择不迁移快照')
            }
          }
        } else {
          // 如果没有localStorage快照，也标记为已询问过
          localStorage.setItem('snapshot_migration_asked', 'true')
        }
      } catch (error) {
        console.error('检查迁移localStorage快照失败:', error)
      }
    },

    /**
     * 保存整个表单数据到本地存储
     */
    saveForm(event) {
      // 阻止事件冒泡，防止触发表单提交
      if (event) {
        event.preventDefault()
        event.stopPropagation()
      }

      console.log('FillSheet: 保存表单被触发')

      // 设置默认快照名称
      this.snapshotName = `${this.formData.公司名称}-${this.formData.文档后缀}-${new Date().toLocaleString()}`

      // 直接使用简单模态框
      this.showSaveModal = true

      // 阻止默认行为，防止表单提交
      return false
    },

    /**
     * 回退到旧的保存方法（当模态框不可用时）
     */
    fallbackSaveForm() {
      // 尝试使用简单模态框
      this.showSaveModal = true

      // 如果简单模态框也不可用，回退到 prompt
      if (!this.showSaveModal) {
        const snapshotName = prompt('请输入快照名称:', this.snapshotName)
        if (snapshotName) {
          this.handleSaveSnapshot(snapshotName)
        }
      }
    },

    /**
     * 处理简单模态框保存快照
     */
    handleSimpleSaveSnapshot() {
      // 检查快照名称
      if (!this.snapshotName || this.snapshotName.trim() === '') {
        alert('请输入快照名称')
        return
      }

      // 检查表单数据是否有效
      if (!this.formData || Object.keys(this.formData).length === 0) {
        alert('表单数据为空，无法保存快照')
        return
      }

      // 检查是否有必要的字段
      if (!this.formData.文档后缀) {
        alert('表单类型未设置，请先选择表单类型')
        return
      }

      const trimmedName = this.snapshotName.trim()

      // 确认保存
      const confirmMessage = `确定要保存快照 "${trimmedName}" 吗？\n\n` +
                           `表单类型: ${this.formData.文档后缀}\n` +
                           `公司名称: ${this.formData.公司名称 || '未填写'}\n\n` +
                           `快照将保存到服务器。`

      if (confirm(confirmMessage)) {
        this.handleSaveSnapshot(trimmedName)
        this.showSaveModal = false
        this.snapshotName = '' // 清空输入框
      }
    },

    /**
     * 处理保存快照（保存到Redis）
     */
    async handleSaveSnapshot(snapshotName) {
      if (snapshotName) {
        // 检查是否正在切换模板
        if (this.isChangingTemplate) {
          // 显示友好提示并自动重试
          const shouldRetry = confirm('表单正在切换类型中，是否等待切换完成后自动保存快照？')
          if (shouldRetry) {
            // 等待切换完成后自动重试
            const retryInterval = setInterval(() => {
              if (!this.isChangingTemplate) {
                clearInterval(retryInterval)
                this.handleSaveSnapshot(snapshotName)
              }
            }, 100)

            // 设置超时，避免无限等待
            setTimeout(() => {
              clearInterval(retryInterval)
            }, 5000)
          }
          return
        }

        // 确保文档后缀字段存在
        if (!this.formData.文档后缀) {
          console.warn('表单数据中缺少文档后缀字段，使用默认值')
          this.formData.文档后缀 = '安全测评'
        }

        try {
          // 保存到Redis
          const response = await saveFormSnapshot(
            this.formData.文档后缀,
            JSON.parse(JSON.stringify(this.formData)),
            snapshotName
          )

          if (response.status === 'success') {
            console.log('快照保存到Redis成功:', response.data)

            // 重新加载快照列表
            await this.loadSnapshots()

            // 同时保存为默认表单数据（向后兼容）
            saveFormDataToLocalStorage(this.formData, 'completeFormData')

            // 保存当前选择的表单类型
            localStorage.setItem('selectedFormType', this.formData.文档后缀)

            alert(`快照 "${snapshotName}" 已保存到服务器`)
          } else {
            throw new Error(response.message || '保存失败')
          }
        } catch (error) {
          console.error('保存快照到Redis失败:', error)

          // 回退到localStorage保存
          const snapshot = {
            id: Date.now().toString(),
            name: snapshotName,
            date: new Date().toLocaleString(),
            data: JSON.parse(JSON.stringify(this.formData))
          }

          this.snapshots.push(snapshot)
          localStorage.setItem('formSnapshots', JSON.stringify(this.snapshots))

          alert(`快照 "${snapshotName}" 已保存到本地（服务器保存失败）`)
        }
      }
    },

    /**
     * 打开快照管理界面
     */
    loadForm(event) {
      // 阻止事件冒泡，防止触发表单提交
      if (event) {
        event.preventDefault()
        event.stopPropagation()
      }

      console.log('🔄 FillSheet: 快照管理被触发')
      console.log(`📊 当前快照数量: ${this.snapshots.length}`)
      console.log('📋 当前快照列表:', this.snapshots.map(s => ({ name: s.name, id: s.id, snapshotId: s.snapshotId })))

      // 重置选中的快照索引和批量选择
      this.selectedSnapshotIndex = null
      this.selectedSnapshots = []

      // 刷新快照列表
      console.log('🔄 开始刷新快照列表...')
      this.loadSnapshots().then(() => {
        console.log('✅ 快照列表刷新完成')
        console.log(`📊 刷新后快照数量: ${this.snapshots.length}`)
      }).catch(error => {
        console.error('❌ 快照列表刷新失败:', error)
      })

      // 打开快照管理模态框
      console.log('🔓 打开快照管理模态框')
      this.showLoadModal = true

      // 添加调试：检查模态框是否真的显示
      this.$nextTick(() => {
        const modalElement = document.querySelector('.simple-modal-overlay')
        if (modalElement) {
          console.log('✅ 模态框元素已找到:', modalElement)
          console.log('📏 模态框样式:', window.getComputedStyle(modalElement))
          console.log('🎯 模态框位置:', modalElement.getBoundingClientRect())
        } else {
          console.error('❌ 模态框元素未找到')
        }
      })

      // 阻止默认行为，防止表单提交
      return false
    },

    /**
     * 回退到旧的加载方法（当模态框不可用时）
     */
    fallbackLoadForm() {
      // 尝试使用简单模态框
      this.selectedSnapshotIndex = null
      this.showLoadModal = true

      // 如果简单模态框也不可用，回退到 prompt
      if (!this.showLoadModal) {
        // 构建快照选择列表
        const options = this.snapshots.map((snapshot, index) =>
          `${index + 1}. ${snapshot.name} (${snapshot.date})`
        ).join('\n')

        // 显示快照选择对话框
        const selection = prompt(`请选择要加载的快照 (1-${this.snapshots.length}):\n${options}`)

        if (selection) {
          const index = parseInt(selection) - 1
          if (index >= 0 && index < this.snapshots.length) {
            this.handleLoadSnapshot(index)
          } else {
            alert('无效的选择')
          }
        }
      }
    },

    /**
     * 处理简单模态框加载快照
     */
    handleSimpleLoadSnapshot() {
      // 检查是否有快照
      if (this.snapshots.length === 0) {
        alert('没有可加载的快照。请先保存一个快照。')
        return
      }

      // 检查是否选择了快照
      if (this.selectedSnapshotIndex === null) {
        alert('请先选择一个要加载的快照。')
        return
      }

      // 检查选择的索引是否有效
      if (this.selectedSnapshotIndex < 0 || this.selectedSnapshotIndex >= this.snapshots.length) {
        alert('选择的快照不存在，请重新选择。')
        this.selectedSnapshotIndex = null
        return
      }

      // 获取选中的快照信息
      const selectedSnapshot = this.snapshots[this.selectedSnapshotIndex]

      // 确认加载
      const confirmMessage = `确定要加载快照 "${selectedSnapshot.name}" 吗？\n\n` +
                           `表单类型: ${selectedSnapshot.formType || '未知'}\n` +
                           `创建时间: ${selectedSnapshot.date}\n\n` +
                           `当前表单数据将被覆盖。`

      if (confirm(confirmMessage)) {
        this.handleLoadSnapshot(this.selectedSnapshotIndex)
        this.showLoadModal = false
      }
    },

    /**
     * 处理加载快照（从Redis）
     */
    async handleLoadSnapshot(index) {
      console.log('🔄 开始加载快照，索引:', index)
      console.log('📊 快照总数:', this.snapshots.length)

      if (index >= 0 && index < this.snapshots.length) {
        const snapshot = this.snapshots[index]
        console.log('📋 选中的快照:', snapshot)

        // 确认是否加载
        if (confirm(`确定要加载快照 "${snapshot.name}" 吗？当前表单数据将被覆盖。`)) {
          try {
            let snapshotData = null

            // 如果快照有snapshotId，说明是从Redis加载的新格式
            if (snapshot.snapshotId) {
              console.log('🔄 从Redis加载快照 (按ID):', snapshot.snapshotId)
              const response = await loadSnapshotById(snapshot.snapshotId)
              console.log('📡 API响应:', response)

              if (response.status === 'success' && response.data) {
                snapshotData = response.data.snapshot_data
                console.log('✅ 从Redis加载快照数据成功:', snapshotData)
              } else {
                console.error('❌ Redis响应错误:', response)
                throw new Error('Redis中未找到快照数据')
              }
            } else if (snapshot.formType) {
              // 兼容旧的Redis格式
              console.log('🔄 从Redis加载快照 (按表单类型):', snapshot.formType)
              const response = await loadFormSnapshot(snapshot.formType)
              console.log('📡 API响应:', response)

              if (response.status === 'success' && response.data) {
                snapshotData = response.data.snapshot_data
                console.log('✅ 从Redis加载快照数据成功:', snapshotData)
              } else {
                console.error('❌ Redis响应错误:', response)
                throw new Error('Redis中未找到快照数据')
              }
            } else {
              // 兼容localStorage格式的快照
              snapshotData = snapshot.data
              console.log('📁 使用localStorage格式的快照数据:', snapshotData)
            }

            if (!snapshotData) {
              console.error('❌ 快照数据为空')
              throw new Error('快照数据为空')
            }

            // 调试：检查快照数据
            console.log('📋 加载的快照数据:', snapshotData)
            console.log('📄 快照中的文档后缀:', snapshotData.文档后缀)
            console.log('📊 快照数据字段数量:', Object.keys(snapshotData).length)

            // 保存当前的文档类型
            const currentDocType = snapshotData.文档后缀 || this.formData.文档后缀 || '安全测评'
            console.log('📝 确定的文档类型:', currentDocType)

            // 备份当前表单数据（用于调试）
            const oldFormData = { ...this.formData }
            console.log('💾 备份当前表单数据，字段数量:', Object.keys(oldFormData).length)

            // 使用Vue 3的方式更新数据：直接替换整个对象
            this.formData = { ...snapshotData }
            console.log('🔄 表单数据已替换')

            // 确保文档类型正确
            this.formData.文档后缀 = currentDocType
            console.log('✅ 文档类型已确保正确:', this.formData.文档后缀)

            // 调试：检查恢复后的表单数据
            console.log('📋 恢复后的表单数据文档后缀:', this.formData.文档后缀)
            console.log('📊 恢复后的表单数据字段数量:', Object.keys(this.formData).length)
            console.log('📄 恢复后的完整表单数据:', this.formData)

            // 保存当前选择的表单类型到本地存储
            localStorage.setItem('selectedFormType', this.formData.文档后缀)
            console.log('💾 已保存表单类型到本地存储')

            // 重新初始化部署应用数组
            console.log('🔄 重新初始化部署应用数组')
            this.initServiceTypes()

            // 强制重新渲染界面
            this.$nextTick(() => {
              this.$forceUpdate()
              console.log('🎨 快照加载完成，强制重新渲染界面')
            })

            // 关闭模态框
            this.showLoadModal = false
            console.log('🔒 已关闭快照管理模态框')

            alert(`快照 "${snapshot.name}" 已加载`)

          } catch (error) {
            console.error('❌ 加载快照失败:', error)
            console.error('❌ 错误堆栈:', error.stack)
            alert(`加载快照失败: ${error.message}`)
          }
        } else {
          console.log('❌ 用户取消了快照加载')
        }
      } else {
        console.error('❌ 无效的快照索引:', index, '快照总数:', this.snapshots.length)
        alert('无效的快照选择')
      }
    },

    /**
     * 处理强制覆盖
     */
    async handleForceOverwrite() {
      this.showDuplicateModal = false
      await this.submitForm(true)
    },

    /**
     * 处理使用别名提交
     */
    async handleSubmitWithAlias(aliasData) {
      this.showDuplicateModal = false
      await this.submitForm(false, aliasData.alias)
    },

    /**
     * 处理编辑现有记录
     */
    handleEditExisting(recordId) {
      this.showDuplicateModal = false
      this.$router.push(`/edit-form-submission/${recordId}`)
    },

    /**
     * 处理查看现有记录
     */
    handleViewExisting(recordId) {
      this.showDuplicateModal = false
      // 这里可以打开查看详情模态框，或者跳转到历史页面并高亮显示该记录
      this.$router.push({
        path: '/history-data',
        query: { highlight: recordId }
      })
    },

    /**
     * 处理跳转到历史记录页面
     */
    handleGoToHistory() {
      this.showDuplicateModal = false
      this.$router.push('/history-data')
    },

    /**
     * 获取加载按钮的文本
     */
    getLoadButtonText() {
      if (this.snapshots.length === 0) {
        return '无快照可加载'
      } else if (this.selectedSnapshotIndex === null) {
        return '请选择快照'
      } else {
        return '加载选中快照'
      }
    },

    /**
     * 获取加载按钮的提示文本
     */
    getLoadButtonTooltip() {
      if (this.snapshots.length === 0) {
        return '当前没有保存的快照，请先保存一个快照'
      } else if (this.selectedSnapshotIndex === null) {
        return '请先选择一个要加载的快照'
      } else {
        const snapshot = this.snapshots[this.selectedSnapshotIndex]
        return `加载选中的快照: ${snapshot.name} (${snapshot.formType || '未知类型'})`
      }
    },

    /**
     * 获取表单类型对应的徽章样式类
     */
    getFormTypeBadgeClass(formType) {
      const badgeClasses = {
        '安全测评': 'bg-success',
        '安全监测': 'bg-info',
        '应用加固': 'bg-warning text-dark'
      }
      return badgeClasses[formType] || 'bg-secondary'
    },

    /**
     * 格式化快照日期显示
     */
    formatSnapshotDate(dateString) {
      try {
        const date = new Date(dateString)
        const now = new Date()
        const diffTime = Math.abs(now - date)
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

        if (diffDays === 1) {
          return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
        } else if (diffDays === 2) {
          return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
        } else if (diffDays <= 7) {
          return `${diffDays - 1}天前`
        } else {
          return date.toLocaleDateString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })
        }
      } catch (error) {
        return dateString
      }
    },

    /**
     * 过滤快照列表
     */
    filterSnapshots() {
      let filtered = [...this.snapshots]

      // 按搜索文本过滤
      if (this.snapshotFilter.searchText) {
        const searchText = this.snapshotFilter.searchText.toLowerCase()
        filtered = filtered.filter(snapshot =>
          snapshot.name.toLowerCase().includes(searchText)
        )
      }

      // 按表单类型过滤
      if (this.snapshotFilter.formType) {
        filtered = filtered.filter(snapshot =>
          snapshot.formType === this.snapshotFilter.formType
        )
      }

      // 排序
      switch (this.snapshotFilter.sortBy) {
        case 'date_desc':
          filtered.sort((a, b) => new Date(b.date) - new Date(a.date))
          break
        case 'date_asc':
          filtered.sort((a, b) => new Date(a.date) - new Date(b.date))
          break
        case 'name_asc':
          filtered.sort((a, b) => a.name.localeCompare(b.name))
          break
        case 'name_desc':
          filtered.sort((a, b) => b.name.localeCompare(a.name))
          break
      }

      this.filteredSnapshots = filtered
    },

    /**
     * 获取快照在原始列表中的索引
     */
    getOriginalIndex(snapshot) {
      return this.snapshots.findIndex(s => s.id === snapshot.id)
    },

    /**
     * 重置过滤条件
     */
    resetSnapshotFilter() {
      this.snapshotFilter = {
        searchText: '',
        formType: '',
        sortBy: 'date_desc'
      }
      this.filterSnapshots()
    },

    /**
     * 切换全选状态
     */
    toggleSelectAll() {
      if (this.selectedSnapshots.length === this.filteredSnapshots.length) {
        // 当前全选，取消全选
        this.selectedSnapshots = []
      } else {
        // 当前非全选，全选所有过滤后的快照
        this.selectedSnapshots = this.filteredSnapshots.map(snapshot => snapshot.snapshotId || snapshot.id)
      }
    },

    /**
     * 批量删除选中的快照
     */
    async batchDeleteSelected() {
      if (this.selectedSnapshots.length === 0) {
        alert('请先选择要删除的快照')
        return
      }

      const confirmMessage = `确定要删除选中的 ${this.selectedSnapshots.length} 个快照吗？此操作不可恢复！`
      if (!confirm(confirmMessage)) {
        return
      }

      try {
        console.log('批量删除快照:', this.selectedSnapshots)

        const response = await batchDeleteSnapshots(this.selectedSnapshots)

        if (response.status === 'success') {
          const { deleted_count, not_found_count } = response.data
          alert(`批量删除完成！成功删除 ${deleted_count} 个快照${not_found_count > 0 ? `，${not_found_count} 个快照未找到` : ''}`)

          // 清空选择并重新加载列表
          this.selectedSnapshots = []
          await this.loadSnapshots()
        } else {
          throw new Error(response.message || '批量删除失败')
        }
      } catch (error) {
        console.error('批量删除快照失败:', error)
        alert(`批量删除失败: ${error.message}`)
      }
    },

    /**
     * 显示按表单类型清理的对话框
     */
    showClearByTypeModal() {
      const formTypes = [...new Set(this.snapshots.map(s => s.formType))].filter(Boolean)
      if (formTypes.length === 0) {
        alert('没有可清理的快照')
        return
      }

      const typeOptions = formTypes.map((type, index) => `${index + 1}. ${type}`).join('\n')
      const selection = prompt(`选择要清理的表单类型 (1-${formTypes.length}):\n${typeOptions}`)

      if (selection) {
        const index = parseInt(selection) - 1
        if (index >= 0 && index < formTypes.length) {
          this.clearSnapshotsByType(formTypes[index])
        } else {
          alert('无效的选择')
        }
      }
    },

    /**
     * 按表单类型清理快照
     */
    async clearSnapshotsByType(formType) {
      const count = this.snapshots.filter(s => s.formType === formType).length
      if (!confirm(`确定要删除所有 "${formType}" 类型的快照吗？共 ${count} 个快照将被删除！`)) {
        return
      }

      try {
        const response = await clearSnapshotsByCondition({ form_type: formType })

        if (response.status === 'success') {
          alert(`成功清理了 ${response.data.deleted_count} 个 "${formType}" 类型的快照`)
          await this.loadSnapshots()
        } else {
          throw new Error(response.message || '清理失败')
        }
      } catch (error) {
        console.error('按类型清理快照失败:', error)
        alert(`清理失败: ${error.message}`)
      }
    },

    /**
     * 显示按时间清理的对话框
     */
    showClearByTimeModal() {
      const days = prompt('请输入要清理多少天前的快照（例如：7 表示清理7天前的快照）:')
      if (days && !isNaN(days) && parseInt(days) > 0) {
        this.clearSnapshotsByTime(parseInt(days))
      } else if (days !== null) {
        alert('请输入有效的天数')
      }
    },

    /**
     * 按时间清理快照
     */
    async clearSnapshotsByTime(daysOld) {
      if (!confirm(`确定要删除 ${daysOld} 天前的所有快照吗？此操作不可恢复！`)) {
        return
      }

      try {
        const response = await clearSnapshotsByCondition({ days_old: daysOld })

        if (response.status === 'success') {
          alert(`成功清理了 ${response.data.deleted_count} 个 ${daysOld} 天前的快照`)
          await this.loadSnapshots()
        } else {
          throw new Error(response.message || '清理失败')
        }
      } catch (error) {
        console.error('按时间清理快照失败:', error)
        alert(`清理失败: ${error.message}`)
      }
    },

    /**
     * 显示按名称模式清理的对话框
     */
    showClearByNameModal() {
      const pattern = prompt('请输入要清理的快照名称模式（支持正则表达式）:\n例如：\n- "测试" 清理包含"测试"的快照\n- "^临时" 清理以"临时"开头的快照\n- "\\d{4}-\\d{2}-\\d{2}" 清理包含日期格式的快照')
      if (pattern) {
        this.clearSnapshotsByName(pattern)
      }
    },

    /**
     * 按名称模式清理快照
     */
    async clearSnapshotsByName(namePattern) {
      try {
        // 先预览匹配的快照
        const matchingSnapshots = this.snapshots.filter(snapshot => {
          try {
            return new RegExp(namePattern).test(snapshot.name)
          } catch {
            return snapshot.name.includes(namePattern)
          }
        })

        if (matchingSnapshots.length === 0) {
          alert('没有找到匹配的快照')
          return
        }

        const previewMessage = `找到 ${matchingSnapshots.length} 个匹配的快照:\n${matchingSnapshots.map(s => `- ${s.name}`).slice(0, 10).join('\n')}${matchingSnapshots.length > 10 ? '\n...' : ''}\n\n确定要删除这些快照吗？`

        if (!confirm(previewMessage)) {
          return
        }

        const response = await clearSnapshotsByCondition({ name_pattern: namePattern })

        if (response.status === 'success') {
          alert(`成功清理了 ${response.data.deleted_count} 个匹配的快照`)
          await this.loadSnapshots()
        } else {
          throw new Error(response.message || '清理失败')
        }
      } catch (error) {
        console.error('按名称模式清理快照失败:', error)
        alert(`清理失败: ${error.message}`)
      }
    },

    /**
     * 清除所有快照（需要确认）
     */
    async clearAllSnapshotsConfirm() {
      const totalCount = this.snapshots.length
      if (totalCount === 0) {
        alert('没有快照需要清理')
        return
      }

      const confirmMessage = `⚠️ 危险操作 ⚠️\n\n确定要删除所有 ${totalCount} 个快照吗？\n此操作不可恢复！\n\n请输入 "确认删除" 来继续:`
      const confirmation = prompt(confirmMessage)

      if (confirmation !== '确认删除') {
        alert('操作已取消')
        return
      }

      try {
        const response = await clearAllSnapshots()

        if (response.status === 'success') {
          alert(`成功清除了所有快照！`)
          this.selectedSnapshots = []
          await this.loadSnapshots()
        } else {
          throw new Error(response.message || '清除失败')
        }
      } catch (error) {
        console.error('清除所有快照失败:', error)
        alert(`清除失败: ${error.message}`)
      }
    },

    /**
     * 重置迁移状态（用于重新提示迁移）
     */
    resetMigrationStatus() {
      localStorage.removeItem('snapshot_migration_asked')
      console.log('已重置迁移状态，下次加载时会重新提示迁移')
      alert('迁移状态已重置，刷新页面后会重新提示迁移')
    },

    /**
     * 手动触发迁移检查
     */
    async manualMigrationCheck() {
      // 临时移除迁移标记
      const originalFlag = localStorage.getItem('snapshot_migration_asked')
      localStorage.removeItem('snapshot_migration_asked')

      try {
        await this.checkAndMigrateLocalSnapshots()
      } catch (error) {
        console.error('手动迁移检查失败:', error)
        // 恢复原始标记
        if (originalFlag) {
          localStorage.setItem('snapshot_migration_asked', originalFlag)
        }
      }
    },

    /**
     * 删除快照（从Redis）
     */
    async deleteSnapshot(index) {
      if (index >= 0 && index < this.snapshots.length) {
        const snapshot = this.snapshots[index]

        if (confirm(`确定要删除快照 "${snapshot.name}" 吗？`)) {
          try {
            // 如果快照有snapshotId，说明是Redis快照（新格式）
            if (snapshot.snapshotId) {
              console.log('从Redis删除快照 (按ID):', snapshot.snapshotId)
              const response = await deleteSnapshotById(snapshot.snapshotId)

              if (response.status === 'success') {
                console.log('Redis快照删除成功')
                // 重新加载快照列表
                await this.loadSnapshots()
                alert('快照已删除')
              } else {
                throw new Error(response.message || '删除失败')
              }
            } else if (snapshot.formType) {
              // 兼容旧的Redis格式
              console.log('从Redis删除快照 (按表单类型):', snapshot.formType)
              const response = await deleteFormSnapshot(snapshot.formType)

              if (response.status === 'success') {
                console.log('Redis快照删除成功')
                // 重新加载快照列表
                await this.loadSnapshots()
                alert('快照已删除')
              } else {
                throw new Error(response.message || '删除失败')
              }
            } else {
              // 兼容localStorage格式的快照
              this.snapshots.splice(index, 1)
              localStorage.setItem('formSnapshots', JSON.stringify(this.snapshots))
              alert('快照已删除')
            }
          } catch (error) {
            console.error('删除快照失败:', error)
            alert(`删除快照失败: ${error.message}`)
          }
        }
      }
    },

    /**
     * 加载组件分组数据
     */
    async loadComponentGroups() {
      try {
        console.log('🔄 开始从数据库加载组件数据...')
        // 优先使用数据库数据
        this.componentGroups = await getComponentGroupsFromDatabase()
        console.log('✅ 成功从数据库加载组件数据:', this.componentGroups)

        // 检查所有表单类型的数据
        Object.keys(this.componentGroups).forEach(formTypeKey => {
          const formTypeData = this.componentGroups[formTypeKey]
          console.log(`🔍 ${formTypeKey} 组件分类:`, Object.keys(formTypeData))

          Object.keys(formTypeData).forEach(categoryKey => {
            const components = formTypeData[categoryKey]
            console.log(`📦 ${formTypeKey} - ${categoryKey}:`, components.map(c => c.name))
          })
        })

        // 强制更新组件
        this.$forceUpdate()
        console.log('🔄 强制更新组件完成')

      } catch (error) {
        console.error('❌ 从数据库加载组件数据失败:', error)
        this.$toast?.error('加载组件数据失败，请检查网络连接或联系管理员', '错误')
        this.componentGroups = {}
      }
    },

    /**
     * 处理组件刷新事件
     */
    async handleRefreshComponents() {
      console.log('🔄 FillSheet: 收到刷新组件事件')
      await this.loadComponentGroups()
      // 移除 $forceUpdate() 调用，避免表单数据被重置
      // Vue的响应式系统会自动处理数据更新
      console.log('✅ FillSheet: 组件数据已刷新，表单数据保持不变')
    }
  }
}
</script>

<style scoped>
/* 主表单卡片样式 - 现代化优化 */
.main-form-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  margin-bottom: 2rem;
  max-width: 1800px;
  margin-left: auto;
  margin-right: auto;
  transition: all 0.3s ease;
  overflow: hidden;
}

.main-form-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 卡片头部优化 */
.main-form-card .card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem 2rem;
  border-radius: 12px 12px 0 0;
}

.main-form-card .card-header h5 {
  color: #2c3e50;
  font-weight: 700;
  margin: 0;
  font-size: 1.25rem;
}

.main-form-card .card-header .text-muted {
  color: #6c757d !important;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 表单操作按钮优化 */
.form-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.form-actions .btn {
  border-radius: 25px;
  font-weight: 500;
  padding: 0.5rem 1.25rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.form-actions .btn-outline-primary {
  border-color: #007bff;
  color: #007bff;
  background: transparent;
}

.form-actions .btn-outline-primary:hover {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-color: #007bff;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

.form-actions .btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
  background: transparent;
}

.form-actions .btn-outline-secondary:hover {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  border-color: #6c757d;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

/* 表单内容区域样式 - 统一样式 */
/* 表单通用样式 - 与其他页面保持一致 */
.security-testing-form .card,
.security-monitoring-form .card,
.app-hardening-form .card,
.generic-form .card {
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  background-color: #ffffff;
}

.card-body {
  padding: 1.5rem;
}

.security-testing-form .card-header,
.security-monitoring-form .card-header,
.app-hardening-form .card-header,
.generic-form .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  padding: 1rem 1.5rem;
  color: #495057;
}

.form-floating {
  width: 100%;
  margin-bottom: 1rem;
}

.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  padding: 1rem 0.75rem;
  width: 100%;
}

.form-floating > label {
  padding: 1rem 0.75rem;
}

.security-testing-form .form-floating > .form-control:focus,
.security-testing-form .form-floating > .form-control:not(:placeholder-shown),
.security-monitoring-form .form-floating > .form-control:focus,
.security-monitoring-form .form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.security-testing-form .form-floating > .form-control:focus ~ label,
.security-testing-form .form-floating > .form-control:not(:placeholder-shown) ~ label,
.security-monitoring-form .form-floating > .form-control:focus ~ label,
.security-monitoring-form .form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 表单控件样式 - 现代化优化 */
.form-control, .form-select {
  border-radius: 8px;
  padding: 0.75rem 1rem;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  font-size: 1rem;
  width: 100%;
  background-color: #fff;
  font-weight: 500;
}

.form-control:focus, .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
  background-color: #fff;
  transform: translateY(-1px);
}

.form-control:hover:not(:focus), .form-select:hover:not(:focus) {
  border-color: #b3d7ff;
  background-color: #f8f9fa;
}

.form-label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  letter-spacing: 0.025em;
}

/* 必填字段标识 */
.form-label.required::after {
  content: ' *';
  color: #dc3545;
  font-weight: 700;
}

.input-group {
  margin-bottom: 1.5rem;
  width: 100%;
  max-width: 100%;
}

.input-group .form-control,
.input-group .form-select {
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

.input-group .form-control:not(:first-child),
.input-group .form-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.security-testing-form .input-group-text,
.security-monitoring-form .input-group-text,
.app-hardening-form .input-group-text,
.generic-form .input-group-text {
  min-width: 180px;
  width: 180px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #e9ecef;
  font-weight: 600;
  border-radius: 8px 0 0 8px;
  padding: 0.75rem 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  justify-content: flex-start;
  color: #495057;
  transition: all 0.3s ease;
}

.security-testing-form .input-group:hover .input-group-text,
.security-monitoring-form .input-group:hover .input-group-text,
.app-hardening-form .input-group:hover .input-group-text,
.generic-form .input-group:hover .input-group-text {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  border-color: #b3d7ff;
}

.security-testing-form .text-danger,
.security-monitoring-form .text-danger {
  font-weight: bold;
}

/* 快照列表样式 */
.snapshot-list {
  max-height: 300px;
  overflow-y: auto;
}

.snapshot-item {
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.snapshot-item:hover {
  background-color: #f8f9fa;
}

.snapshot-item.active {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: white;
}

.snapshot-item .btn-outline-danger {
  opacity: 0.7;
  transition: all 0.2s ease;
}

.snapshot-item:hover .btn-outline-danger {
  opacity: 1;
}

.snapshot-item.active .btn-outline-danger {
  color: white;
  border-color: white;
}

/* 动态添加项样式 */
.security-testing-form .border-rounded,
.security-monitoring-form .border-rounded {
  transition: all 0.2s ease;
}

.security-testing-form .border-rounded:hover,
.security-monitoring-form .border-rounded:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式 - 现代化优化 */
.security-testing-form button.btn,
.security-monitoring-form button.btn,
.app-hardening-form button.btn,
.generic-form button.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.security-testing-form button.btn:hover,
.security-monitoring-form button.btn:hover,
.app-hardening-form button.btn:hover,
.generic-form button.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 主要按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
}

/* 次要按钮样式 */
.btn-secondary {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  border: none;
  color: white;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #5a6268, #495057);
}

/* 成功按钮样式 */
.btn-success {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  border: none;
  color: white;
}

.btn-success:hover {
  background: linear-gradient(135deg, #1e7e34, #155724);
}

/* 危险按钮样式 */
.btn-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  border: none;
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #c82333, #bd2130);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .security-testing-form .input-group,
  .security-monitoring-form .input-group,
  .app-hardening-form .input-group,
  .generic-form .input-group {
    flex-direction: column;
  }

  .security-testing-form .input-group > *,
  .security-monitoring-form .input-group > *,
  .app-hardening-form .input-group > *,
  .generic-form .input-group > * {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .security-testing-form .input-group-text,
  .security-monitoring-form .input-group-text,
  .app-hardening-form .input-group-text,
  .generic-form .input-group-text {
    border-radius: 0.375rem;
    justify-content: flex-start;
    width: 100%;
    min-width: 100%;
    margin-bottom: 0.25rem;
  }

  .security-testing-form .form-control,
  .security-monitoring-form .form-control,
  .security-testing-form .form-select,
  .security-monitoring-form .form-select,
  .app-hardening-form .form-control,
  .app-hardening-form .form-select,
  .generic-form .form-control,
  .generic-form .form-select {
    width: 100%;
    border-radius: 0.375rem !important;
  }
}

/* 表单特有样式 */
.form-check-inline .badge {
  font-size: 0.85rem;
  padding: 0.35em 0.65em;
  margin-left: 0.2rem;
}

/* 部署应用多选框样式 */
.form-check-inline {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.form-check-inline:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
}

.form-check-inline .form-check-input:checked + .form-check-label .badge {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

/* 服务器折叠/展开样式 */
.server-details {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 折叠状态下的服务器信息摘要 */
.ms-3.text-muted .badge {
  font-size: 0.85rem;
  padding: 0.35em 0.65em;
  margin-right: 0.5rem;
}

/* 端口输入框样式 */
.port-input {
  cursor: pointer;
  transition: all 0.2s ease;
}

.port-input:hover {
  background-color: #f8f9fa;
}

.port-input:focus {
  background-color: #fff;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 组件分组样式 - 现代化优化 */
.component-group {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 1.5rem;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.component-group:hover {
  border-color: #b3d7ff;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);
}

.component-group-title {
  font-size: 1rem;
  margin: 0;
  font-weight: 600;
  color: #2c3e50;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.component-group-title i {
  color: #007bff;
  margin-right: 0.5rem;
}

/* 全选按钮样式 */
.component-group-title .btn-sm {
  font-size: 0.75rem;
  padding: 0.1rem 0.5rem;
  border-radius: 0.2rem;
  transition: all 0.2s ease;
}

.component-group-title .btn-sm:hover {
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 端口配置开关样式 */
.form-check.form-switch {
  min-height: 1.5rem;
  padding: 0.25rem 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.form-check.form-switch:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
}

.form-check.form-switch .form-check-input {
  width: 2.5em;
  margin-top: 0.1em;
  cursor: pointer;
}

.form-check.form-switch .form-check-label {
  cursor: pointer;
  font-weight: 500;
}

.form-check.form-switch .form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* 端口信息展示样式 */
.port-info-display .alert {
  padding: 0.75rem;
  margin-bottom: 0;
}

.port-info-display .badge {
  font-size: 0.75rem;
  min-width: 100px;
  text-align: left;
}

/* 端口修改区域样式 */
.port-edit-area {
  animation: fadeIn 0.3s ease;
}

/* 组件端口配置样式 */
.input-group .input-group-text {
  font-size: 0.85rem;
  min-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 表单行样式 */
.row > [class*="col-"] {
  margin-bottom: 1.25rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

/* 表单组样式 */
.form-group {
  margin-bottom: 1.25rem;
}

/* 输入组间距 */
.input-group + .input-group {
  margin-top: 1.5rem;
}

/* ==================== 快照管理样式 ==================== */

/* 快照过滤栏样式 */
.snapshot-filter-bar {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 1rem;
}

.filter-left {
  flex: 2;
  min-width: 200px;
}

.filter-center,
.filter-right {
  flex: 1;
  min-width: 150px;
}

.snapshot-filter-bar .input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
  color: #6c757d;
}

.snapshot-filter-bar .form-control,
.snapshot-filter-bar .form-select {
  border-color: #ced4da;
  transition: all 0.2s ease;
}

.snapshot-filter-bar .form-control:focus,
.snapshot-filter-bar .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 响应式过滤栏 */
@media (max-width: 768px) {
  .snapshot-filter-bar {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-left,
  .filter-center,
  .filter-right {
    width: 100%;
    min-width: unset;
  }
}

/* 空状态样式 */
.empty-state-container {
  padding: 3rem 2rem;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 2px dashed #dee2e6;
  margin: 1rem 0;
}

.empty-state-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-state-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
}

.empty-state-icon i {
  font-size: 2rem;
  color: white;
}

.empty-state-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1rem;
}

.empty-state-description {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.empty-state-actions .btn {
  border-radius: 25px;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.empty-state-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* 快照头部样式 */
.snapshot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 1.5rem;
}

.snapshot-stats .badge {
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
}

.snapshot-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.snapshot-actions .form-check-label {
  color: #495057;
  cursor: pointer;
  font-weight: 500;
}

.snapshot-actions .btn {
  border-radius: 20px;
  font-weight: 500;
  padding: 0.4rem 1rem;
  transition: all 0.2s ease;
}

.snapshot-actions .btn:hover {
  transform: translateY(-1px);
}

.snapshot-actions .btn-outline-danger {
  border: 2px solid #dc3545;
  color: #dc3545;
  background: transparent;
}

.snapshot-actions .btn-outline-danger:hover {
  background: linear-gradient(135deg, #dc3545, #c82333);
  border-color: #dc3545;
  color: white;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

/* 快照网格布局 */
.snapshot-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
  max-height: 500px;
  overflow-y: auto;
}

/* 在更大的模态框中显示更多列 */
@media (min-width: 1200px) {
  .modal-size-xl .snapshot-grid,
  .modal-size-xxl .snapshot-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    max-height: 600px;
  }
}

@media (min-width: 1400px) {
  .modal-size-xxl .snapshot-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    max-height: 650px;
  }
}

/* 快照卡片样式 */
.snapshot-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.snapshot-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.snapshot-card.selected {
  border-color: #007bff;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.2);
}

.snapshot-card.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #007bff, #0056b3);
}

/* 快照卡片头部 */
.snapshot-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1rem 0.5rem;
}

.snapshot-checkbox {
  display: flex;
  align-items: center;
}

.snapshot-type-badge .badge {
  font-size: 0.75rem;
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-weight: 500;
}

.snapshot-actions .btn {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.snapshot-actions .btn:hover {
  transform: scale(1.1);
}

/* 快照卡片主体 */
.snapshot-card-body {
  padding: 0.5rem 1rem;
}

.snapshot-title {
  font-weight: 600;
  color: #212529;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.snapshot-meta {
  display: flex;
  align-items: center;
  color: #6c757d;
  font-size: 0.85rem;
}

/* 快照卡片底部 */
.snapshot-card-footer {
  padding: 0.5rem 1rem 1rem;
}

.snapshot-card-footer .btn {
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  color: white;
}

.snapshot-card-footer .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
  background: linear-gradient(135deg, #0056b3, #004085);
}

/* 快照底部操作栏 */
.snapshot-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-top: 1px solid #e9ecef;
  border-radius: 0 0 8px 8px;
  margin: 1rem -1rem -1rem;
}

.snapshot-footer-left,
.snapshot-footer-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.snapshot-footer .btn {
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.2s ease;
  /* 移除自定义padding，让btn-sm类控制尺寸 */
  min-width: 100px;
}

.snapshot-footer .btn:hover {
  transform: translateY(-1px);
}

/* 统一快照管理按钮样式 */
.snapshot-footer .btn-success {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  border: none;
  color: white;
}

.snapshot-footer .btn-success:hover {
  background: linear-gradient(135deg, #1e7e34, #155724);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.snapshot-footer .btn-outline-primary {
  border: 2px solid #007bff;
  color: #007bff;
  background: transparent;
}

.snapshot-footer .btn-outline-primary:hover {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-color: #007bff;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.snapshot-footer .btn-outline-secondary {
  border: 2px solid #6c757d;
  color: #6c757d;
  background: transparent;
}

.snapshot-footer .btn-outline-secondary:hover {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  border-color: #6c757d;
  color: white;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

.snapshot-footer .btn-outline-warning {
  border: 2px solid #ffc107;
  color: #856404;
  background: transparent;
}

.snapshot-footer .btn-outline-warning:hover {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  border-color: #ffc107;
  color: #212529;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.snapshot-footer .btn-outline-info {
  border: 2px solid #17a2b8;
  color: #0c5460;
  background: transparent;
}

.snapshot-footer .btn-outline-info:hover {
  background: linear-gradient(135deg, #17a2b8, #138496);
  border-color: #17a2b8;
  color: white;
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

/* 下拉菜单样式优化 */
.dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e9ecef;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

.dropdown-item i {
  width: 16px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .snapshot-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .snapshot-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .snapshot-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .snapshot-footer-left,
  .snapshot-footer-right {
    justify-content: center;
  }
}

/* 滚动条样式 */
.snapshot-grid::-webkit-scrollbar {
  width: 6px;
}

.snapshot-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.snapshot-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.snapshot-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.snapshot-card {
  animation: fadeIn 0.3s ease;
}

/* 加载状态样式 */
.snapshot-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  color: #6c757d;
}

.snapshot-loading i {
  font-size: 2rem;
  margin-right: 1rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ==================== 现代化动画效果 ==================== */

/* 页面加载动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片进入动画 */
.main-form-card {
  animation: slideInUp 0.6s ease-out;
}

/* 表单控件聚焦动画 - 限制在当前页面 */
@keyframes focusPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
  }
}

/* 只在FillSheet页面内的表单控件应用动画 */
.main-form-card .form-control:focus,
.main-form-card .form-select:focus {
  animation: focusPulse 0.6s ease-out;
}

/* 按钮点击动画 */
@keyframes buttonPress {
  0% {
    transform: translateY(-2px) scale(1);
  }
  50% {
    transform: translateY(0) scale(0.98);
  }
  100% {
    transform: translateY(-2px) scale(1);
  }
}

/* 只在FillSheet页面内的按钮应用动画 */
.main-form-card .btn:active {
  animation: buttonPress 0.2s ease-out;
}

/* 成功状态动画 */
@keyframes successPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

/* 只在FillSheet页面内的成功提示应用动画 */
.main-form-card .alert-success {
  animation: successPulse 0.6s ease-out;
}

/* 错误状态动画 */
@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* 只在FillSheet页面内的错误提示应用动画 */
.main-form-card .alert-danger,
.main-form-card .is-invalid {
  animation: errorShake 0.5s ease-out;
}

/* 加载状态动画 */
@keyframes loadingDots {
  0%, 20% {
    color: rgba(0, 0, 0, 0.4);
    text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0.2), 0.5em 0 0 rgba(0, 0, 0, 0.2);
  }
  40% {
    color: rgba(0, 0, 0, 1);
    text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0.4), 0.5em 0 0 rgba(0, 0, 0, 0.2);
  }
  60% {
    text-shadow: 0.25em 0 0 rgba(0, 0, 0, 1), 0.5em 0 0 rgba(0, 0, 0, 0.4);
  }
  80%, 100% {
    text-shadow: 0.25em 0 0 rgba(0, 0, 0, 1), 0.5em 0 0 rgba(0, 0, 0, 1);
  }
}

.loading-text::after {
  content: '...';
  animation: loadingDots 1.4s infinite;
}
</style>
