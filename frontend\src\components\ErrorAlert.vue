<template>
  <div v-if="errorMessage" class="alert" :class="alertClass" role="alert">
    <i :class="iconClass" class="me-2"></i>
    <div>
      <strong>{{ errorTitle }}</strong>
      <div class="mt-1">{{ errorMessage }}</div>
      <div v-if="errorDetails" class="mt-2 small">
        <details>
          <summary class="text-decoration-underline" style="cursor: pointer;">
            查看详细信息
          </summary>
          <div class="mt-2" style="white-space: pre-line;">{{ errorDetails }}</div>
        </details>
      </div>
      <div v-if="showRetryButton" class="mt-2">
        <button @click="$emit('retry')" class="btn btn-sm btn-outline-primary">
          <i class="bi bi-arrow-clockwise me-1"></i>
          重试
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorAlert',
  props: {
    errorMessage: {
      type: String,
      default: ''
    },
    errorType: {
      type: String,
      default: 'general'
    },
    errorDetails: {
      type: String,
      default: ''
    },
    showRetryButton: {
      type: Boolean,
      default: false
    }
  },
  emits: ['retry'],
  computed: {
    alertClass() {
      switch (this.errorType) {
        case 'database_connection':
        case 'database_permission':
        case 'database_missing':
        case 'database_schema':
          return 'alert-warning'
        case 'validation':
          return 'alert-info'
        case 'permission':
          return 'alert-warning'
        case 'not_found':
          return 'alert-info'
        case 'conflict':
          return 'alert-warning'
        case 'general':
        default:
          return 'alert-danger'
      }
    },
    iconClass() {
      switch (this.errorType) {
        case 'database_connection':
          return 'bi bi-wifi-off'
        case 'database_permission':
          return 'bi bi-shield-exclamation'
        case 'database_missing':
        case 'database_schema':
          return 'bi bi-database-exclamation'
        case 'validation':
          return 'bi bi-exclamation-circle'
        case 'permission':
          return 'bi bi-shield-x'
        case 'not_found':
          return 'bi bi-search'
        case 'conflict':
          return 'bi bi-exclamation-diamond'
        case 'general':
        default:
          return 'bi bi-exclamation-triangle'
      }
    },
    errorTitle() {
      switch (this.errorType) {
        case 'database_connection':
          return '连接问题'
        case 'database_permission':
          return '权限问题'
        case 'database_missing':
          return '数据库缺失'
        case 'database_schema':
          return '数据结构异常'
        case 'validation':
          return '输入验证失败'
        case 'permission':
          return '权限不足'
        case 'not_found':
          return '资源不存在'
        case 'conflict':
          return '资源冲突'
        case 'general':
        default:
          return '操作失败'
      }
    }
  }
}
</script>

<style scoped>
.alert {
  border-radius: 8px;
  border: none;
}

.alert-warning {
  background-color: #fff3cd;
  color: #856404;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

details summary {
  outline: none;
}

details summary:hover {
  color: #0056b3;
}

.btn-outline-primary {
  border-color: currentColor;
  color: currentColor;
}

.btn-outline-primary:hover {
  background-color: currentColor;
  border-color: currentColor;
  color: white;
}
</style>
