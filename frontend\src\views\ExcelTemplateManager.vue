<template>
  <div class="excel-template-manager">
    <div class="container-fluid">
      <!-- 面包屑导航 -->
      <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <router-link to="/form-template-manager" class="text-decoration-none">
              <i class="bi bi-house me-1"></i>表单模板管理
            </router-link>
          </li>
          <li class="breadcrumb-item active" aria-current="page">
            <i class="bi bi-file-earmark-excel me-1"></i>Excel模板管理
          </li>
        </ol>
      </nav>

      <!-- 页面标题和快速操作 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="page-title mb-1">
                    <i class="bi bi-file-earmark-excel me-2"></i>
                    Excel模板管理
                  </h2>
                  <p class="text-muted mb-0" v-if="selectedFormType">
                    当前管理：<strong>{{ selectedFormType }}</strong> 表单类型的Excel模板
                  </p>
                </div>
                <div>
                  <button class="btn btn-outline-secondary me-2" @click="goBack">
                    <i class="bi bi-arrow-left me-1"></i>返回管理页面
                  </button>
                  <button class="btn btn-primary" @click="showUploadModal" :disabled="!selectedFormType">
                    <i class="bi bi-upload me-1"></i>上传模板
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单类型选择和统计 -->
      <div class="row mb-4">
        <div class="col-md-6">
          <div class="form-floating">
            <select
              class="form-select"
              id="formTypeSelect"
              v-model="selectedFormType"
              @change="loadTemplates"
            >
              <option value="">所有表单类型</option>
              <option v-for="formType in availableFormTypes" :key="formType" :value="formType">
                {{ formType }}
              </option>
            </select>
            <label for="formTypeSelect">表单类型</label>
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex align-items-end justify-content-end">
            <!-- 统计信息 -->
            <div class="me-3">
              <span class="badge bg-info me-2">共 {{ templates.length }} 个模板</span>
              <span v-if="activeTemplatesCount > 0" class="badge bg-success me-2">{{ activeTemplatesCount }} 个启用</span>
              <span v-if="inactiveTemplatesCount > 0" class="badge bg-secondary">{{ inactiveTemplatesCount }} 个禁用</span>
            </div>

            <!-- 视图切换按钮 -->
            <div class="btn-group me-3" role="group">
              <input type="radio" class="btn-check" name="viewMode" id="cardView" v-model="viewMode" value="card">
              <label class="btn btn-outline-secondary btn-sm" for="cardView">
                <i class="bi bi-grid-3x3-gap"></i> 卡片视图
              </label>

              <input type="radio" class="btn-check" name="viewMode" id="tableView" v-model="viewMode" value="table">
              <label class="btn btn-outline-secondary btn-sm" for="tableView">
                <i class="bi bi-table"></i> 表格视图
              </label>
            </div>

            <!-- 调试信息 -->
            <small class="text-muted me-3">当前视图: {{ viewMode }}</small>

            <button class="btn btn-outline-info" @click="showBatchUploadModal">
              <i class="bi bi-cloud-upload me-1"></i>
              批量上传
            </button>
          </div>
        </div>
      </div>

      <!-- 模板列表 -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="bi bi-files me-2"></i>
                Excel模板文件
              </h5>
            </div>
            <div class="card-body">
              <!-- 加载状态 -->
              <div v-if="loading" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载模板数据...</p>
              </div>

              <!-- 空状态 -->
              <div v-else-if="templates.length === 0" class="text-center py-5">
                <div class="empty-state">
                  <i class="bi bi-file-earmark-excel text-muted display-4"></i>
                  <h6 class="text-muted mt-3">暂无Excel模板</h6>
                  <p class="text-muted" v-if="selectedFormType">该表单类型还没有上传任何Excel模板</p>
                  <p class="text-muted" v-else>请选择表单类型来查看对应的Excel模板</p>
                  <button v-if="selectedFormType" class="btn btn-primary" @click="showUploadModal">
                    <i class="bi bi-upload me-1"></i>
                    上传第一个模板
                  </button>
                </div>
              </div>

              <!-- 有数据时的视图切换 -->
              <div v-else>
                <!-- 调试信息 -->
                <div class="alert alert-info mb-3">
                  <small>
                    <strong>调试信息:</strong><br>
                    viewMode: {{ viewMode }}<br>
                    templates.length: {{ templates.length }}<br>
                    loading: {{ loading }}<br>
                    selectedFormType: {{ selectedFormType }}<br>
                    templates数据: {{ JSON.stringify(templates.slice(0, 2), null, 2) }}
                  </small>
                </div>

                <!-- 卡片视图 -->
                <div v-if="viewMode === 'card'" class="row">
                  <div v-for="template in templates" :key="template.id" class="col-md-6 col-lg-4 mb-4">
                    <div class="card template-card h-100">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ template.template_name }}</h6>
                        <span :class="template.is_active ? 'badge bg-success' : 'badge bg-secondary'">
                          {{ template.is_active ? '启用' : '禁用' }}
                        </span>
                      </div>
                      <div class="card-body">
                        <div class="template-info">
                          <p class="text-muted small mb-2">
                            <i class="bi bi-file-earmark me-1"></i>
                            {{ template.file_name }}
                          </p>
                          <div class="row text-center">
                            <div class="col-6">
                              <small class="text-muted">版本</small>
                              <div class="fw-bold">v{{ template.version || '1.0' }}</div>
                            </div>
                            <div class="col-6">
                              <small class="text-muted">大小</small>
                              <div class="fw-bold">{{ formatFileSize(template.file_size) }}</div>
                            </div>
                          </div>
                          <div class="mt-2">
                            <small class="text-muted">上传时间：{{ formatDate(template.created_at) }}</small>
                          </div>
                        </div>
                      </div>
                      <div class="card-footer">
                        <div class="btn-group w-100">
                          <button class="btn btn-outline-primary btn-sm" @click="downloadTemplate(template)">
                            <i class="bi bi-download"></i> 下载
                          </button>
                          <button class="btn btn-outline-success btn-sm" @click="previewTemplate(template)">
                            <i class="bi bi-eye"></i> 预览
                          </button>
                          <button
                            class="btn btn-sm"
                            :class="template.is_active ? 'btn-outline-warning' : 'btn-outline-success'"
                            @click="toggleTemplateStatus(template)"
                          >
                            <i :class="template.is_active ? 'bi bi-pause' : 'bi bi-play'"></i>
                            {{ template.is_active ? '禁用' : '启用' }}
                          </button>
                          <button class="btn btn-outline-danger btn-sm" @click="deleteTemplate(template)">
                            <i class="bi bi-trash"></i> 删除
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 表格视图 -->
                <div v-else class="template-table-container">
                  <div class="alert alert-warning mb-3">
                    <small>表格视图渲染中... templates.length = {{ templates.length }}</small>
                  </div>
                <div class="table-responsive">
                  <table class="table table-hover template-table">
                    <thead class="table-light">
                      <tr>
                        <th>模板名称</th>
                        <th>文件名</th>
                        <th>版本</th>
                        <th>状态</th>
                        <th>文件大小</th>
                        <th>上传时间</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="template in templates" :key="template.id" class="template-row">
                        <td class="template-name">
                          <div class="d-flex align-items-center">
                            <div class="template-icon me-2">
                              <i class="bi bi-file-earmark-excel text-success"></i>
                            </div>
                            <div>
                              <div class="fw-semibold">{{ template.template_name }}</div>
                              <small class="text-muted">{{ template.description || '无描述' }}</small>
                            </div>
                          </div>
                        </td>
                        <td>
                          <span class="badge bg-light text-dark">{{ template.file_name }}</span>
                        </td>
                        <td>
                          <span class="badge bg-info">v{{ template.version || '1.0' }}</span>
                        </td>
                        <td class="text-center">
                          <span :class="template.is_active ? 'badge bg-success' : 'badge bg-secondary'">
                            <i :class="template.is_active ? 'bi bi-check-circle' : 'bi bi-x-circle'" class="me-1"></i>
                            {{ template.is_active ? '启用' : '禁用' }}
                          </span>
                        </td>
                        <td>
                          <span class="text-muted">{{ formatFileSize(template.file_size) }}</span>
                        </td>
                        <td>
                          <small class="text-muted">{{ formatDate(template.created_at) }}</small>
                        </td>
                        <td>
                          <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" @click="downloadTemplate(template)" title="下载">
                              <i class="bi bi-download"></i>
                            </button>
                            <button class="btn btn-outline-success" @click="previewTemplate(template)" title="预览">
                              <i class="bi bi-eye"></i>
                            </button>
                            <button
                              class="btn"
                              :class="template.is_active ? 'btn-outline-warning' : 'btn-outline-success'"
                              @click="toggleTemplateStatus(template)"
                              :title="template.is_active ? '禁用' : '启用'"
                            >
                              <i :class="template.is_active ? 'bi bi-pause' : 'bi bi-play'"></i>
                            </button>
                            <button class="btn btn-outline-danger" @click="deleteTemplate(template)" title="删除">
                              <i class="bi bi-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传模板模态框 -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">上传Excel模板</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="uploadTemplate">
              <div class="mb-3">
                <label for="templateName" class="form-label">模板名称 *</label>
                <input
                  type="text"
                  class="form-control"
                  id="templateName"
                  v-model="uploadForm.template_name"
                  required
                  placeholder="如: 安全测评标准模板"
                >
              </div>

              <div class="mb-3">
                <label for="templateFile" class="form-label">Excel文件 *</label>
                <input
                  type="file"
                  class="form-control"
                  id="templateFile"
                  @change="handleFileSelect"
                  accept=".xlsx,.xls"
                  required
                >
                <div class="form-text">支持 .xlsx 和 .xls 格式，文件大小不超过 10MB</div>
              </div>

              <div class="mb-3">
                <label for="templateVersion" class="form-label">版本号</label>
                <input
                  type="text"
                  class="form-control"
                  id="templateVersion"
                  v-model="uploadForm.version"
                  placeholder="如: 1.0, 2.1"
                >
              </div>

              <div class="mb-3">
                <label for="templateDescription" class="form-label">描述</label>
                <textarea
                  class="form-control"
                  id="templateDescription"
                  v-model="uploadForm.description"
                  rows="3"
                  placeholder="描述模板的用途和特点"
                ></textarea>
              </div>

              <div class="mb-3">
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="setAsActive"
                    v-model="uploadForm.set_as_active"
                  >
                  <label class="form-check-label" for="setAsActive">
                    设为启用状态
                  </label>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" @click="uploadTemplate" :disabled="uploading">
              <span v-if="uploading" class="spinner-border spinner-border-sm me-2"></span>
              {{ uploading ? '上传中...' : '上传' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Modal } from 'bootstrap'
import axios from 'axios'

export default {
  name: 'ExcelTemplateManager',
  setup() {
    const router = useRouter()
    const route = useRoute()

    // 响应式数据
    const selectedFormType = ref('')
    const availableFormTypes = ref(['安全测评', '安全监测', '应用加固'])
    const templates = ref([])
    const loading = ref(false)
    const viewMode = ref('table')
    const uploading = ref(false)

    // 上传表单数据
    const uploadForm = reactive({
      template_name: '',
      version: '1.0',
      description: '',
      form_type: '',
      file: null,
      set_as_active: true
    })

    // 计算属性
    const activeTemplatesCount = computed(() => {
      return templates.value.filter(t => t.is_active).length
    })

    const inactiveTemplatesCount = computed(() => {
      return templates.value.filter(t => !t.is_active).length
    })

    // 方法
    const loadFormTypes = async () => {
      try {
        const response = await axios.get('/excel/form-types')
        if (response.data.status === 'success') {
          availableFormTypes.value = response.data.data.map(formType => formType.name)
        }
      } catch (error) {
        console.error('加载表单类型失败:', error)
        availableFormTypes.value = ['安全测评', '安全监测', '应用加固']
      }
    }

    const loadTemplates = async () => {
      if (!selectedFormType.value) {
        templates.value = []
        return
      }

      try {
        loading.value = true
        const response = await axios.get(`/excel/templates?form_type=${encodeURIComponent(selectedFormType.value)}`)

        if (response.data.status === 'success') {
          templates.value = response.data.data || []

          // 调试：打印模板数据结构
          console.log('=== Excel模板数据结构调试 ===')
          console.log('模板数量:', templates.value.length)
          if (templates.value.length > 0) {
            console.log('第一个模板的完整数据:', templates.value[0])
            console.log('所有模板的字段名:', Object.keys(templates.value[0]))
          }
          console.log('=== 调试信息结束 ===')

          // 如果没有数据，添加测试数据
          if (templates.value.length === 0) {
            console.log('没有真实数据，添加测试数据')
            templates.value = [
              {
                id: 1,
                template_name: '测试模板1',
                file_name: 'test1.xlsx',
                version: '1.0',
                is_active: true,
                file_size: 1024000,
                created_at: new Date().toISOString(),
                description: '这是一个测试模板'
              },
              {
                id: 2,
                template_name: '测试模板2',
                file_name: 'test2.xlsx',
                version: '2.0',
                is_active: false,
                file_size: 2048000,
                created_at: new Date().toISOString(),
                description: '这是另一个测试模板'
              }
            ]
          }
        } else {
          console.error('加载模板失败:', response.data.message)
          templates.value = []
        }
      } catch (error) {
        console.error('加载模板失败:', error)
        templates.value = []
      } finally {
        loading.value = false
      }
    }

    const handleUrlParams = () => {
      const formType = route.query.formType
      if (formType) {
        selectedFormType.value = formType
        loadTemplates()
      }
    }

    const goBack = () => {
      router.push('/form-template-manager')
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleString('zh-CN')
    }

    const formatFileSize = (bytes) => {
      if (!bytes) return '-'
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }

    // 模板操作方法
    const showUploadModal = () => {
      if (!selectedFormType.value) {
        alert('请先选择表单类型')
        return
      }

      // 重置表单数据
      Object.assign(uploadForm, {
        template_name: '',
        version: '1.0',
        description: '',
        form_type: selectedFormType.value,
        file: null,
        set_as_active: true
      })

      const modal = new Modal(document.getElementById('uploadModal'))
      modal.show()
    }

    const showBatchUploadModal = () => {
      alert('批量上传功能开发中...')
    }

    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        // 验证文件类型
        const allowedTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel'
        ]

        if (!allowedTypes.includes(file.type)) {
          alert('请选择有效的Excel文件（.xlsx 或 .xls）')
          event.target.value = ''
          return
        }

        // 验证文件大小（10MB）
        if (file.size > 10 * 1024 * 1024) {
          alert('文件大小不能超过 10MB')
          event.target.value = ''
          return
        }

        uploadForm.file = file
      }
    }

    const uploadTemplate = async (confirmOverwrite = false) => {
      if (!uploadForm.file) {
        alert('请选择要上传的Excel文件')
        return
      }

      try {
        uploading.value = true

        const formData = new FormData()
        formData.append('file', uploadForm.file)
        formData.append('template_name', uploadForm.template_name)
        formData.append('version', uploadForm.version)
        formData.append('description', uploadForm.description)
        formData.append('form_type', uploadForm.form_type)
        formData.append('set_as_active', uploadForm.set_as_active)
        if (confirmOverwrite) {
          formData.append('confirm', 'true')
        }

        const response = await axios.post('/excel/templates/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })



        if (response.data.status === 'success') {
          const modal = Modal.getInstance(document.getElementById('uploadModal'))
          modal.hide()

          // 显示成功信息
          if (response.data.data && response.data.data.conflict_info) {
            const conflictInfo = response.data.data.conflict_info
            let message = response.data.message || '模板上传成功!'

            if (conflictInfo.has_conflict) {
              message += '\n\n📋 处理详情:'
              message += `\n✅ ${conflictInfo.action_taken === 'replaced' ? '已替换现有模板' :
                                conflictInfo.action_taken === 'updated' ? '已更新数据库记录' :
                                conflictInfo.action_taken === 'overwritten' ? '已覆盖现有文件' : '已创建新模板'}`
            }

            alert(message)
          } else {
            alert(response.data.message || '模板上传成功!')
          }

          await loadTemplates()
        } else {
          alert('上传失败: ' + response.data.message)
        }
      } catch (error) {
        // 处理409冲突状态码
        if (error.response && error.response.status === 409 && error.response.data.status === 'conflict') {
          // 显示冲突确认对话框
          const conflictInfo = error.response.data.data.conflict_info
          let message = '⚠️ 检测到模板文件冲突！\n\n'
          message += `📁 将要上传的文件: ${error.response.data.data.filename}\n`
          message += `📋 模板类型: ${error.response.data.data.type}\n\n`

          if (conflictInfo.existing_info) {
            message += '📄 现有模板信息:\n'
            message += `• 文件名: ${conflictInfo.existing_info.filename}\n`
            message += `• 模板类型: ${conflictInfo.existing_info.template_type}\n`
            message += `• 表单类型: ${conflictInfo.existing_info.form_type}\n`
            message += `• 别名: ${conflictInfo.existing_info.alias}\n`
            message += `• 创建时间: ${conflictInfo.existing_info.created_at}\n`
            message += `• 最后修改: ${conflictInfo.existing_info.last_modified}\n`

            if (conflictInfo.existing_info.is_active) {
              message += '• ⚠️ 状态: 正在使用中\n'
            }
          }

          message += `\n🔄 操作影响: ${conflictInfo.action_taken === 'replaced' ? '将替换现有模板文件和数据库记录' :
                                    conflictInfo.action_taken === 'updated' ? '将更新数据库记录' :
                                    conflictInfo.action_taken === 'overwritten' ? '将覆盖现有文件' : '将创建新模板'}\n\n`
          message += '是否要继续上传并覆盖现有模板？'

          if (confirm(message)) {
            // 用户确认，重新上传并确认覆盖
            await uploadTemplate(true)
            return
          } else {
            // 用户取消，不执行任何操作
            alert('上传已取消')
            return
          }
        } else {
          console.error('上传模板失败:', error)
          alert('上传失败: ' + (error.response?.data?.message || error.message))
        }
      } finally {
        uploading.value = false
      }
    }

    const downloadTemplate = async (template) => {
      try {
        const response = await axios.get(`/excel/templates/${template.id}/download`, {
          responseType: 'blob'
        })

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', template.file_name)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('下载模板失败:', error)
        alert('下载失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const previewTemplate = (template) => {
      alert(`预览模板功能开发中: ${template.template_name}`)
    }

    const toggleTemplateStatus = async (template) => {
      try {
        const response = await axios.put(`/excel/templates/${template.id}`, {
          ...template,
          is_active: !template.is_active
        })

        if (response.data.status === 'success') {
          await loadTemplates()
        } else {
          alert('操作失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('切换模板状态失败:', error)
        alert('操作失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const deleteTemplate = async (template) => {
      if (!confirm(`确定要删除模板"${template.template_name}"吗？此操作不可恢复。`)) {
        return
      }

      try {
        const response = await axios.delete(`/excel/templates/${template.id}`)

        if (response.data.status === 'success') {
          await loadTemplates()
          alert('模板删除成功')
        } else {
          alert('删除失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('删除模板失败:', error)
        alert('删除失败: ' + (error.response?.data?.message || error.message))
      }
    }

    // 生命周期
    onMounted(async () => {
      await loadFormTypes()
      handleUrlParams()
    })

    return {
      selectedFormType,
      availableFormTypes,
      templates,
      loading,
      viewMode,
      uploading,
      uploadForm,
      activeTemplatesCount,
      inactiveTemplatesCount,
      loadTemplates,
      goBack,
      formatDate,
      formatFileSize,
      showUploadModal,
      showBatchUploadModal,
      handleFileSelect,
      uploadTemplate,
      downloadTemplate,
      previewTemplate,
      toggleTemplateStatus,
      deleteTemplate
    }
  }
}
</script>

<style scoped>
.card {
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

.page-title {
  color: #2c3e50;
}

.breadcrumb-item a {
  color: #007bff;
}

.breadcrumb-item.active {
  color: #6c757d;
}

/* 模板卡片样式 */
.template-card {
  border: 1px solid #e3f2fd;
  transition: all 0.2s ease-in-out;
}

.template-card:hover {
  border-color: #2196f3;
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.1);
}

.template-info {
  min-height: 120px;
}

/* 模板表格样式 */
.template-table-container {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.template-table {
  margin-bottom: 0;
  border: none;
}

.template-table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 1rem 0.75rem;
  vertical-align: middle;
}

.template-row {
  transition: background-color 0.15s ease-in-out;
}

.template-row:hover {
  background-color: #f8f9fa;
}

.template-name {
  min-width: 200px;
}

.template-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e8f5e8;
  border-radius: 50%;
  font-size: 0.875rem;
}

.template-table td {
  padding: 0.875rem 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid #f1f3f4;
}

.template-table tbody tr:last-child td {
  border-bottom: none;
}

/* 空状态样式 */
.empty-state {
  padding: 3rem 2rem;
}

.empty-state i {
  opacity: 0.5;
}

/* 徽章样式优化 */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

/* 上传模态框样式 */
.modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .template-table-container {
    font-size: 0.875rem;
  }

  .template-table td,
  .template-table th {
    padding: 0.5rem 0.375rem;
  }

  .template-name {
    min-width: 150px;
  }

  .btn-group-sm .btn {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
  }
}
</style>