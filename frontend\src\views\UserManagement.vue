<template>
  <div class="user-management">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="bi bi-people me-2"></i>用户管理
              </h5>
              <button
                class="btn btn-primary"
                @click="showCreateModal"
                v-if="hasPermission('user.create')"
              >
                <i class="bi bi-plus-circle me-1"></i>新建用户
              </button>
            </div>
            <div class="card-body">
              <!-- 搜索和筛选 -->
              <div class="row mb-3">
                <div class="col-md-4">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="bi bi-search"></i>
                    </span>
                    <input 
                      type="text" 
                      class="form-control" 
                      placeholder="搜索用户名、邮箱或姓名"
                      v-model="searchQuery"
                      @input="searchUsers"
                    >
                  </div>
                </div>
                <div class="col-md-3">
                  <select class="form-select" v-model="statusFilter" @change="filterUsers">
                    <option value="">全部状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">禁用</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <select class="form-select" v-model="roleFilter" @change="filterUsers">
                    <option value="">全部角色</option>
                    <option v-for="role in roles" :key="role.id" :value="role.id">
                      {{ role.name }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- 用户列表 -->
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>用户名</th>
                      <th>真实姓名</th>
                      <th>邮箱</th>
                      <th>部门</th>
                      <th>角色</th>
                      <th>用户组</th>
                      <th>状态</th>
                      <th>最后登录</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="user in paginatedUsers" :key="user.id">
                      <td>
                        <div class="d-flex align-items-center">
                          <i class="bi bi-person-circle me-2 text-primary"></i>
                          {{ user.username }}
                          <span v-if="user.is_admin" class="badge bg-danger ms-2">管理员</span>
                        </div>
                      </td>
                      <td>{{ user.real_name || '-' }}</td>
                      <td>{{ user.email }}</td>
                      <td>{{ user.department || '-' }}</td>
                      <td>
                        <span
                          v-for="role in user.roles"
                          :key="role.id"
                          class="badge bg-secondary me-1"
                        >
                          {{ role.name }}
                        </span>
                        <span v-if="!user.roles?.length" class="text-muted">-</span>
                      </td>
                      <td>
                        <span
                          v-for="group in user.groups"
                          :key="group.id"
                          class="badge bg-info me-1"
                        >
                          {{ group.name }}
                        </span>
                        <span v-if="!user.groups?.length" class="text-muted">-</span>
                      </td>
                      <td>
                        <span
                          :class="user.is_active ? 'badge bg-success' : 'badge bg-danger'"
                        >
                          {{ user.is_active ? '活跃' : '禁用' }}
                        </span>
                      </td>
                      <td>{{ formatDate(user.last_login) }}</td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button
                            class="btn btn-outline-info"
                            @click="viewUser(user)"
                            title="查看详情"
                            v-if="hasPermission('user.view')"
                          >
                            <i class="bi bi-eye"></i>
                          </button>
                          <button
                            class="btn btn-outline-primary"
                            @click="editUser(user)"
                            title="编辑"
                            v-if="hasPermission('user.edit')"
                          >
                            <i class="bi bi-pencil"></i>
                          </button>
                          <button
                            class="btn btn-outline-warning"
                            @click="toggleUserStatus(user)"
                            :title="user.is_active ? '禁用' : '启用'"
                            v-if="hasPermission('user.edit')"
                          >
                            <i :class="user.is_active ? 'bi bi-lock' : 'bi bi-unlock'"></i>
                          </button>
                          <button
                            class="btn btn-outline-danger"
                            @click="deleteUser(user)"
                            :title="user.is_admin ? '系统管理员不可删除' : '删除用户'"
                            :disabled="user.is_admin"
                            v-if="hasPermission('user.delete')"
                          >
                            <i class="bi bi-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- 分页 -->
              <nav v-if="totalPages > 1">
                <ul class="pagination justify-content-center">
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <button class="page-link" @click="changePage(currentPage - 1)">上一页</button>
                  </li>
                  <li 
                    v-for="page in visiblePages" 
                    :key="page" 
                    class="page-item" 
                    :class="{ active: page === currentPage }"
                  >
                    <button class="page-link" @click="changePage(page)">{{ page }}</button>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <button class="page-link" @click="changePage(currentPage + 1)">下一页</button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户编辑模态框 -->
    <UserEditModal
      v-if="showEditModal"
      :user="selectedUser"
      :roles="roles"
      :groups="groups"
      @close="closeEditModal"
      @save="saveUser"
    />

    <!-- 用户详情模态框 -->
    <div class="modal fade show" id="userDetailModal" tabindex="-1" v-if="showDetailModal"
         style="display: block; background-color: rgba(0,0,0,0.5);"
         @click.self="closeDetailModal">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">用户详情</h5>
            <button type="button" class="btn-close" @click="closeDetailModal"></button>
          </div>
          <div class="modal-body" v-if="selectedUser">
            <div class="row">
              <div class="col-md-6">
                <h6 class="text-primary">基本信息</h6>
                <table class="table table-sm">
                  <tbody>
                    <tr>
                      <td><strong>用户名:</strong></td>
                      <td>{{ selectedUser.username }}</td>
                    </tr>
                    <tr>
                      <td><strong>真实姓名:</strong></td>
                      <td>{{ selectedUser.real_name || '-' }}</td>
                    </tr>
                    <tr>
                      <td><strong>邮箱:</strong></td>
                      <td>{{ selectedUser.email }}</td>
                    </tr>
                    <tr>
                      <td><strong>手机号:</strong></td>
                      <td>{{ selectedUser.phone || '-' }}</td>
                    </tr>
                    <tr>
                      <td><strong>部门:</strong></td>
                      <td>{{ selectedUser.department || '-' }}</td>
                    </tr>
                    <tr>
                      <td><strong>职位:</strong></td>
                      <td>{{ selectedUser.position || '-' }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="col-md-6">
                <h6 class="text-primary">状态信息</h6>
                <table class="table table-sm">
                  <tbody>
                    <tr>
                      <td><strong>状态:</strong></td>
                      <td>
                        <span :class="selectedUser.is_active ? 'badge bg-success' : 'badge bg-danger'">
                          {{ selectedUser.is_active ? '激活' : '禁用' }}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td><strong>管理员:</strong></td>
                      <td>
                        <span :class="selectedUser.is_admin ? 'badge bg-danger' : 'badge bg-secondary'">
                          {{ selectedUser.is_admin ? '是' : '否' }}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td><strong>登录次数:</strong></td>
                      <td>{{ selectedUser.login_count || 0 }}</td>
                    </tr>
                    <tr>
                      <td><strong>最后登录:</strong></td>
                      <td>{{ formatDate(selectedUser.last_login) }}</td>
                    </tr>
                    <tr>
                      <td><strong>创建时间:</strong></td>
                      <td>{{ formatDate(selectedUser.created_at) }}</td>
                    </tr>
                    <tr>
                      <td><strong>更新时间:</strong></td>
                      <td>{{ formatDate(selectedUser.updated_at) }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="row mt-3">
              <div class="col-md-6">
                <h6 class="text-primary">角色信息</h6>
                <div v-if="selectedUser.roles && selectedUser.roles.length > 0">
                  <span
                    v-for="role in selectedUser.roles"
                    :key="role.id"
                    class="badge bg-secondary me-2 mb-2"
                  >
                    {{ role.name }} ({{ role.code }})
                  </span>
                </div>
                <div v-else class="text-muted">暂无角色</div>
              </div>
              <div class="col-md-6">
                <h6 class="text-primary">用户组信息</h6>
                <div v-if="selectedUser.groups && selectedUser.groups.length > 0">
                  <span
                    v-for="group in selectedUser.groups"
                    :key="group.id"
                    class="badge bg-info me-2 mb-2"
                  >
                    {{ group.name }} ({{ group.code }})
                  </span>
                </div>
                <div v-else class="text-muted">暂无用户组</div>
              </div>
            </div>

            <div class="row mt-3">
              <div class="col-12">
                <h6 class="text-primary">
                  <i class="bi bi-shield-check me-1"></i>权限信息
                </h6>

                <!-- 管理员特殊显示 -->
                <div v-if="selectedUser.is_admin" class="alert alert-info mb-3">
                  <i class="bi bi-crown me-2"></i>
                  <strong>系统管理员</strong> - 拥有所有系统权限
                </div>

                <!-- 普通用户权限显示 -->
                <div v-if="selectedUser.permissions && selectedUser.permissions.length > 0">
                  <!-- 权限概览卡片 -->
                  <div class="permission-overview">
                    <div class="row g-3">
                      <div
                        v-for="(moduleData, moduleName) in groupPermissionsByModule(selectedUser.permissions)"
                        :key="moduleName"
                        class="col-md-6 col-lg-4"
                      >
                        <div
                          class="permission-card"
                          :class="{ 'expanded': expandedModule === moduleName }"
                          @click="toggleModule(moduleName)"
                        >
                          <div class="card-header">
                            <div class="module-info">
                              <i :class="[moduleData.icon, `text-${moduleData.color}`]"></i>
                              <div class="module-details">
                                <div class="module-name">{{ moduleData.name }}</div>
                                <div class="module-count">{{ moduleData.permissions.length }} 个权限</div>
                              </div>
                            </div>
                            <div class="expand-icon">
                              <i class="bi bi-chevron-right" :class="{ 'rotated': expandedModule === moduleName }"></i>
                            </div>
                          </div>

                          <!-- 展开的权限详情 -->
                          <div v-if="expandedModule === moduleName" class="card-body">
                            <!-- 权限标签云 -->
                            <div class="permission-tags">
                              <span
                                v-for="(permission, index) in moduleData.permissions"
                                :key="index"
                                :class="`permission-tag bg-${moduleData.color}`"
                                :title="`${formatPermissionName(permission)}\n代码: ${permission}`"
                              >
                                {{ formatPermissionName(permission) }}
                              </span>
                            </div>

                            <!-- 权限统计 -->
                            <div class="permission-stats mt-3">
                              <small class="text-muted">
                                <i class="bi bi-shield-check me-1"></i>
                                该模块包含 <strong>{{ moduleData.permissions.length }}</strong> 个权限
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 权限统计 -->
                  <div class="permission-summary mt-4">
                    <div class="row text-center">
                      <div class="col-4">
                        <div class="summary-item">
                          <div class="summary-number">{{ Object.keys(groupPermissionsByModule(selectedUser.permissions)).length }}</div>
                          <div class="summary-label">权限模块</div>
                        </div>
                      </div>
                      <div class="col-4">
                        <div class="summary-item">
                          <div class="summary-number">{{ selectedUser.permissions.length }}</div>
                          <div class="summary-label">权限总数</div>
                        </div>
                      </div>
                      <div class="col-4">
                        <div class="summary-item">
                          <div class="summary-number">{{ selectedUser.roles?.length || 0 }}</div>
                          <div class="summary-label">关联角色</div>
                        </div>
                      </div>
                    </div>
                    <div class="text-center mt-3">
                      <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        点击权限模块卡片查看详细权限列表
                      </small>
                    </div>
                  </div>
                </div>

                <!-- 无权限显示 -->
                <div v-else-if="!selectedUser.is_admin" class="text-muted">
                  <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle me-2 text-warning"></i>
                    <span>该用户暂无直接权限</span>
                  </div>
                  <small class="text-muted mt-1 d-block">
                    权限通过角色和用户组分配，请检查用户的角色和用户组配置
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeDetailModal">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import UserEditModal from '@/components/modals/UserEditModal.vue'

export default {
  name: 'UserManagement',
  components: {
    UserEditModal
  },
  data() {
    return {
      users: [],
      roles: [],
      groups: [],
      filteredUsers: [],
      searchQuery: '',
      statusFilter: '',
      roleFilter: '',
      currentPage: 1,
      pageSize: 10,
      totalPages: 1,
      showEditModal: false,
      showDetailModal: false,
      selectedUser: null,
      loading: false,
      expandedModule: null // 当前展开的权限模块
    }
  },
  computed: {
    ...mapState(['user']),
    currentUser() {
      return this.user
    },
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      return pages
    },
    paginatedUsers() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredUsers.slice(start, end)
    }
  },
  async mounted() {
    await this.loadData()

    // 添加页面可见性检测，当页面重新可见时刷新数据
    this.handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('页面重新可见，刷新用户数据') // 调试日志
        this.loadUsers()
      }
    }

    // 添加键盘事件监听器
    this.handleKeydown = (event) => {
      if (event.key === 'Escape' && this.showDetailModal) {
        this.closeDetailModal()
      }
    }

    document.addEventListener('visibilitychange', this.handleVisibilityChange)
    document.addEventListener('keydown', this.handleKeydown)
  },

  beforeUnmount() {
    // 清理事件监听器
    if (this.handleVisibilityChange) {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange)
    }
    if (this.handleKeydown) {
      document.removeEventListener('keydown', this.handleKeydown)
    }
  },
  methods: {
    ...mapActions(['showToast']),

    // 权限检查
    hasPermission(permission) {
      // 使用Vuex的hasPermission getter
      return this.$store.getters.hasPermission(permission) ||
             (this.currentUser && this.currentUser.is_admin)
    },
    
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadUsers(),
          this.loadRoles(),
          this.loadGroups()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        this.showToast('加载数据失败', 'error')
      } finally {
        this.loading = false
      }
    },

    async loadUsers() {
      try {
        console.log('开始加载用户列表...') // 调试日志
        const response = await fetch('/api/rbac/users', {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          console.log('用户列表API响应:', result) // 调试日志

          // 处理后端API的嵌套响应格式
          if (result.data && result.data.users) {
            this.users = result.data.users
          } else if (result.data) {
            this.users = result.data
          } else {
            this.users = result
          }
          console.log('用户列表数据:', this.users) // 调试日志
          this.filterUsers()
          console.log('用户列表加载完成，共', this.users.length, '个用户') // 调试日志
        } else {
          const errorText = await response.text()
          console.error('加载用户列表失败:', response.status, errorText)

          if (response.status === 401) {
            alert('请先登录')
            this.$router.push('/login')
          } else {
            alert('加载用户列表失败')
          }
        }
      } catch (error) {
        console.error('加载用户列表异常:', error)
        alert('加载用户列表失败')
      }
    },

    async loadRoles() {
      try {
        const response = await fetch('/api/rbac/roles', {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          // 处理后端API的嵌套响应格式
          if (result.data && result.data.roles) {
            this.roles = result.data.roles
          } else if (result.data) {
            this.roles = result.data
          } else {
            this.roles = result
          }

        } else {
          console.error('加载角色列表失败:', response.status)
        }
      } catch (error) {
        console.error('加载角色列表异常:', error)
      }
    },

    async loadGroups() {
      try {
        const response = await fetch('/api/rbac/groups', {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          // 处理后端API的嵌套响应格式
          if (result.data && result.data.groups) {
            this.groups = result.data.groups
          } else if (result.data) {
            this.groups = result.data
          } else {
            this.groups = result
          }

        } else {
          console.error('加载用户组列表失败:', response.status)
        }
      } catch (error) {
        console.error('加载用户组列表异常:', error)
      }
    },

    searchUsers() {
      this.filterUsers()
    },

    filterUsers() {
      let filtered = [...this.users]
      
      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(user => 
          user.username.toLowerCase().includes(query) ||
          user.email.toLowerCase().includes(query) ||
          (user.real_name && user.real_name.toLowerCase().includes(query))
        )
      }
      
      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(user => 
          this.statusFilter === 'active' ? user.is_active : !user.is_active
        )
      }
      
      // 角色过滤
      if (this.roleFilter) {
        filtered = filtered.filter(user => 
          user.roles.some(role => role.id === parseInt(this.roleFilter))
        )
      }
      
      this.filteredUsers = filtered
      this.updatePagination()
    },

    updatePagination() {
      this.totalPages = Math.ceil(this.filteredUsers.length / this.pageSize)
      this.currentPage = Math.min(this.currentPage, this.totalPages || 1)
    },

    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
      }
    },

    showCreateModal() {
      this.selectedUser = null
      this.showEditModal = true
    },

    async viewUser(user) {
      try {
        console.log('查看用户详情:', user) // 调试日志
        // 获取用户详细信息，包括权限
        const response = await fetch(`/api/rbac/users/${user.id}`, {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          console.log('用户详情API响应:', result) // 调试日志
          if (result.status === 'success') {
            this.selectedUser = result.data.user
            console.log('用户权限数据:', this.selectedUser.permissions) // 调试权限数据
            console.log('权限数据类型:', typeof this.selectedUser.permissions) // 调试权限数据类型
            console.log('权限数据长度:', this.selectedUser.permissions?.length) // 调试权限数据长度
            this.showDetailModal = true
            console.log('设置showDetailModal为true，selectedUser:', this.selectedUser) // 调试日志
          } else {
            this.showToast('获取用户详情失败', 'error')
          }
        } else {
          this.showToast('获取用户详情失败', 'error')
        }
      } catch (error) {
        console.error('获取用户详情失败:', error)
        this.showToast('获取用户详情失败', 'error')
      }
    },

    editUser(user) {
      this.selectedUser = { ...user }
      this.showEditModal = true
    },



    closeEditModal() {
      this.showEditModal = false
      this.selectedUser = null
    },

    closeDetailModal() {
      console.log('关闭用户详情模态框') // 调试日志
      this.showDetailModal = false
      this.selectedUser = null
      this.expandedModule = null // 重置展开状态
    },

    // 切换权限模块展开/收起
    toggleModule(moduleName) {
      if (this.expandedModule === moduleName) {
        this.expandedModule = null // 收起当前模块
      } else {
        this.expandedModule = moduleName // 展开新模块
      }
    },

    async saveUser(userData) {
      try {
        const isEdit = !!userData.id
        let response

        if (isEdit) {
          // 编辑用户
          response = await fetch(`/api/rbac/users/${userData.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.$store.state.token}`
            },
            body: JSON.stringify({
              email: userData.email,
              real_name: userData.real_name || '',
              phone: userData.phone || '',
              department: userData.department || '',
              position: userData.position || '',
              is_active: userData.is_active !== false,
              role_ids: userData.role_ids || [],
              group_ids: userData.group_ids || []
            })
          })
        } else {
          // 创建新用户
          response = await fetch('/api/rbac/users', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.$store.state.token}`
            },
            body: JSON.stringify({
              username: userData.username,
              email: userData.email,
              password: userData.password,
              real_name: userData.real_name || '',
              phone: userData.phone || '',
              department: userData.department || '',
              position: userData.position || '',
              is_active: userData.is_active !== false,
              role_ids: userData.role_ids || [],
              group_ids: userData.group_ids || []
            })
          })
        }

        if (response.ok) {
          const result = await response.json()
          console.log('用户保存响应:', result) // 调试日志
          if (result.status === 'success') {
            console.log('用户保存成功，准备重新加载用户列表') // 调试日志
            this.showToast(isEdit ? '用户编辑成功' : '用户创建成功', 'success')
            this.closeEditModal()
            await this.loadUsers() // 重新加载用户列表
            console.log('用户列表重新加载完成') // 调试日志
          } else {
            console.error('用户保存失败:', result.message) // 调试日志
            this.showToast(result.message || '保存用户失败', 'error')
          }
        } else {
          const errorResult = await response.json()
          console.error('HTTP错误:', response.status, errorResult) // 调试日志
          this.showToast(errorResult.message || '保存用户失败', 'error')
        }
      } catch (error) {
        console.error('保存用户失败:', error)
        this.showToast('保存用户失败', 'error')
      }
    },

    async toggleUserStatus(user) {
      try {
        const newStatus = !user.is_active
        const statusText = newStatus ? '启用' : '禁用'

        const response = await fetch(`/api/rbac/users/${user.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.$store.state.token}`
          },
          body: JSON.stringify({
            is_active: newStatus
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            // 更新本地数据
            const userIndex = this.users.findIndex(u => u.id === user.id)
            if (userIndex !== -1) {
              this.users[userIndex].is_active = newStatus
              this.filterUsers() // 重新过滤以更新显示
            }
            this.showToast(`用户已${statusText}`, 'success')
          } else {
            this.showToast(result.message || '操作失败', 'error')
          }
        } else {
          const errorResult = await response.json()
          this.showToast(errorResult.message || '操作失败', 'error')
        }
      } catch (error) {
        console.error('切换用户状态失败:', error)
        this.showToast('操作失败', 'error')
      }
    },

    async deleteUser(user) {
      if (!confirm(`确定要删除用户 "${user.username}" 吗？此操作不可恢复。`)) {
        return
      }

      try {
        const response = await fetch(`/api/rbac/users/${user.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.status === 'success') {
            this.showToast('用户删除成功', 'success')
            await this.loadUsers() // 重新加载用户列表
          } else {
            this.showToast(result.message || '删除用户失败', 'error')
          }
        } else {
          const errorResult = await response.json()
          this.showToast(errorResult.message || '删除用户失败', 'error')
        }
      } catch (error) {
        console.error('删除用户失败:', error)
        this.showToast('删除用户失败', 'error')
      }
    },

    formatDate(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    },

    formatPermissionName(permissionCode) {
      // 根据数据库实际权限数据的完整映射
      const permissionMap = {
        // 用户管理模块 (user)
        'user.view': '查看用户',
        'user.create': '创建用户',
        'user.edit': '编辑用户',
        'user.delete': '删除用户',

        // 角色管理模块 (role)
        'role.view': '查看角色',
        'role.create': '创建角色',
        'role.edit': '编辑角色',
        'role.delete': '删除角色',

        // 权限管理模块 (permission)
        'permission.view': '查看权限',
        'permission.create': '创建权限',
        'permission.edit': '编辑权限',
        'permission.delete': '删除权限',

        // 用户组管理模块 (group)
        'group.view': '查看用户组',
        'group.create': '创建用户组',
        'group.edit': '编辑用户组',
        'group.delete': '删除用户组',

        // 表单管理模块 (form)
        'form.view': '查看表单',
        'form.create': '创建表单',
        'form.edit': '编辑表单',
        'form.delete': '删除表单',
        'form.export': '导出表单',
        'form.download': '下载表单',
        'form.import': '导入表单',
        'form.submit': '提交表单',

        // 组件管理模块 (component)
        'component.view': '查看组件',
        'component.create': '创建组件',
        'component.edit': '编辑组件',
        'component.delete': '删除组件',

        // 模板管理模块 (template)
        'template.view': '查看模板',
        'template.create': '创建模板',
        'template.edit': '编辑模板',
        'template.delete': '删除模板',
        'template.upload': '上传模板',

        // 系统管理模块 (system)
        'system.view': '查看系统信息',
        'system.config': '系统配置',
        'system.log': '查看日志',

        // 缓存管理模块 (cache)
        'system.cache.view': '查看缓存',
        'system.cache.manage': '管理缓存',

        // 历史记录模块 (history)
        'history.view': '查看历史',
        'history.cache.view': '查看历史缓存',
        'history.cache.manage': '管理历史缓存',

        // 快照管理模块 (snapshot)
        'form.snapshot.view': '查看表单快照',
        'form.snapshot.create': '创建表单快照',
        'form.snapshot.delete': '删除表单快照',

        // 重复检查模块 (duplicate)
        'form.duplicate.view': '查看重复检查',
        'form.duplicate.manage': '管理重复检查',

        // 限流管理模块 (rate_limit)
        'system.rate_limit.view': '查看限流',
        'system.rate_limit.manage': '管理限流'
      }

      return permissionMap[permissionCode] || permissionCode
    },

    // 按模块分组权限
    groupPermissionsByModule(permissions) {
      const groups = {}
      const moduleMap = {
        // 基础管理模块
        'user': { name: '用户管理', icon: 'bi-people', color: 'primary' },
        'role': { name: '角色管理', icon: 'bi-person-badge', color: 'success' },
        'group': { name: '用户组管理', icon: 'bi-people-fill', color: 'info' },
        'permission': { name: '权限管理', icon: 'bi-shield-check', color: 'warning' },

        // 业务功能模块
        'form': { name: '表单管理', icon: 'bi-file-earmark-text', color: 'primary' },
        'component': { name: '组件管理', icon: 'bi-puzzle', color: 'success' },
        'template': { name: '模板管理', icon: 'bi-file-earmark-spreadsheet', color: 'info' },

        // 系统功能模块
        'system': { name: '系统管理', icon: 'bi-gear', color: 'danger' },
        'cache': { name: '缓存管理', icon: 'bi-hdd-stack', color: 'warning' },
        'history': { name: '历史记录', icon: 'bi-clock-history', color: 'secondary' },
        'snapshot': { name: '快照管理', icon: 'bi-camera', color: 'dark' },
        'duplicate': { name: '重复检查', icon: 'bi-files', color: 'primary' },
        'rate_limit': { name: '限流管理', icon: 'bi-speedometer2', color: 'success' }
      }

      permissions.forEach(permission => {
        // 处理复合权限代码，如 system.cache.view
        let module = permission.split('.')[0]

        // 特殊处理复合模块
        if (permission.startsWith('system.cache.')) {
          module = 'cache'
        } else if (permission.startsWith('history.cache.')) {
          module = 'history'
        } else if (permission.startsWith('form.snapshot.')) {
          module = 'snapshot'
        } else if (permission.startsWith('form.duplicate.')) {
          module = 'duplicate'
        } else if (permission.startsWith('system.rate_limit.')) {
          module = 'rate_limit'
        }

        if (!groups[module]) {
          groups[module] = {
            ...moduleMap[module] || { name: module, icon: 'bi-gear', color: 'secondary' },
            permissions: []
          }
        }
        groups[module].permissions.push(permission)
      })

      return groups
    }
  }
}
</script>

<style scoped>
.user-management {
  padding: 20px 0;
}

.table th {
  border-top: none;
  font-weight: 600;
}

.badge {
  font-size: 0.75em;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
}

.pagination {
  margin-top: 20px;
}

/* 用户详情模态框样式 */
.modal.show {
  display: block !important;
}

.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 确保模态框在最顶层 */
#userDetailModal {
  z-index: 1050;
}

/* 模态框动画 */
.modal.fade.show .modal-dialog {
  transform: none;
}

/* 用户详情表格样式 */
.modal-body .table-sm td {
  padding: 0.5rem;
  border-top: 1px solid #dee2e6;
}

.modal-body .table-sm td:first-child {
  width: 30%;
  font-weight: 500;
  color: #495057;
}

/* 徽章样式优化 */
.modal-body .badge {
  font-size: 0.75em;
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
}

/* 权限概览卡片 */
.permission-overview {
  max-height: 500px;
  overflow-y: auto;
  padding: 0.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0.75rem;
  border: 1px solid #dee2e6;
}

.permission-card {
  background: white;
  border-radius: 0.75rem;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.permission-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.1);
  border-color: #007bff;
}

.permission-card.expanded {
  border-color: #007bff;
  box-shadow: 0 4px 16px rgba(0,123,255,0.15);
}

.card-header {
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f8f9fa;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.module-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.module-info i {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  width: 24px;
  text-align: center;
}

.module-details {
  flex: 1;
}

.module-name {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
}

.module-count {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 400;
}

.expand-icon {
  margin-left: 1rem;
  color: #6c757d;
  transition: all 0.3s ease;
}

.expand-icon i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.expand-icon i.rotated {
  transform: rotate(90deg);
}

.card-body {
  padding: 1rem;
  background: #fafafa;
  border-top: 1px solid #e9ecef;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 300px;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

/* 权限标签云 */
.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
  background: #fafafa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
}

.permission-tag {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  border-radius: 1.5rem;
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: help;
  white-space: nowrap;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.permission-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.6s ease;
}

.permission-tag:hover::before {
  left: 100%;
}

.permission-tag:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  border-color: rgba(255,255,255,0.3);
}

/* 权限统计 */
.permission-stats {
  text-align: center;
  padding: 0.75rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0.5rem;
  border: 1px solid #dee2e6;
}

.permission-stats i {
  color: #28a745;
}

/* 简化的权限项样式（备用） */
.permission-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.permission-item:last-child {
  border-bottom: none;
}

.permission-item:hover {
  background: rgba(0,123,255,0.05);
  border-radius: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.permission-name {
  flex: 1;
  font-weight: 500;
  color: #495057;
  margin-right: 0.75rem;
  font-size: 0.9rem;
}

.permission-code {
  font-size: 0.75rem;
  background: #f8f9fa;
  color: #6c757d;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  border: 1px solid #e9ecef;
  white-space: nowrap;
}

/* 管理员提示样式 */
.alert-info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border: 1px solid #b6d4da;
  color: #0c5460;
}

/* 权限统计区域 */
.permission-summary {
  margin-top: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 0.75rem;
  border: 2px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.summary-item {
  padding: 0.75rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: all 0.3s ease;
}

.summary-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.summary-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 0.25rem;
}

.summary-label {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 自定义滚动条 */
.permission-overview::-webkit-scrollbar,
.permission-tags::-webkit-scrollbar {
  width: 6px;
}

.permission-overview::-webkit-scrollbar-track,
.permission-tags::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.permission-overview::-webkit-scrollbar-thumb,
.permission-tags::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 3px;
}

.permission-overview::-webkit-scrollbar-thumb:hover,
.permission-tags::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .permission-overview {
    max-height: 400px;
  }

  .permission-card .card-header {
    padding: 0.75rem;
  }

  .module-info i {
    font-size: 1.25rem;
    margin-right: 0.5rem;
  }

  .module-name {
    font-size: 0.9rem;
  }

  .module-count {
    font-size: 0.8rem;
  }

  .card-body {
    padding: 0.75rem;
  }

  /* 权限标签响应式 */
  .permission-tags {
    gap: 0.4rem;
    max-height: 180px;
    padding: 0.4rem;
  }

  .permission-tag {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
  }

  .permission-stats {
    padding: 0.5rem;
  }

  .summary-number {
    font-size: 1.25rem;
  }

  .summary-label {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .permission-overview .col-md-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .permission-overview .col-lg-4 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .permission-summary .row {
    gap: 0.5rem;
  }

  .permission-summary .col-4 {
    flex: 1;
    max-width: none;
  }

  .card-header {
    padding: 0.5rem !important;
  }

  .module-info i {
    font-size: 1rem;
    margin-right: 0.5rem;
  }

  .module-name {
    font-size: 0.85rem;
  }

  .module-count {
    font-size: 0.75rem;
  }

  /* 移动端权限标签优化 */
  .permission-tags {
    gap: 0.3rem;
    max-height: 150px;
    padding: 0.3rem;
  }

  .permission-tag {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }

  .permission-stats {
    padding: 0.4rem;
  }

  .permission-stats small {
    font-size: 0.75rem;
  }
}
</style>
