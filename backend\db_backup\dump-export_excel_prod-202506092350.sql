-- MySQL dump 10.13  Distrib 8.0.19, for Win64 (x86_64)
--
-- Host: ************    Database: export_excel_prod
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `component_category`
--

DROP TABLE IF EXISTS `component_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `component_category` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '分类ID，主键自增',
  `key` varchar(50) NOT NULL COMMENT '分类键名，唯一标识',
  `display_name` varchar(100) NOT NULL COMMENT '分类显示名称',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标类名，如bi-box',
  `color` varchar(20) DEFAULT NULL COMMENT '颜色样式，如bg-primary',
  `order` int DEFAULT '0' COMMENT '排序权重，数字越小越靠前',
  `form_types` text COMMENT '适用表单类型列表，JSON格式（兼容旧版）',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间（北京时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间（北京时间）',
  `form_type` varchar(50) DEFAULT NULL COMMENT '支持的表单类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb3 COMMENT='组件分类表 - 管理组件的分类信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `component_category`
--

LOCK TABLES `component_category` WRITE;
/*!40000 ALTER TABLE `component_category` DISABLE KEYS */;
INSERT INTO `component_category` VALUES (2,'database','数据库组件','bi-database','bg-info',2,NULL,0,'2025-06-04 17:32:12','2025-06-04 22:07:48',NULL),(3,'tools','安全工具','bi-tools','bg-warning',3,NULL,0,'2025-06-04 17:32:12','2025-06-04 14:01:32',NULL),(7,'frontend','前端服务','bi-display','bg-info',6,NULL,0,'2025-06-04 17:32:12','2025-06-04 14:01:32',NULL),(8,'backend','后端服务','bi-server','bg-dark',7,NULL,0,'2025-06-04 17:32:12','2025-06-04 14:01:32',NULL),(9,'server','服务器组件','bi-server','bg-secondary',9,'[\"安全监测\"]',1,'2025-06-04 17:32:12','2025-06-04 22:44:16','安全监测'),(10,'app','应用组件','bi-box','bg-primary',10,'[\"安全监测\"]',1,'2025-06-04 17:32:12','2025-06-04 22:44:16','安全监测'),(11,'ops','运维组件','bi-tools','bg-info',11,'[\"安全监测\"]',1,'2025-06-04 17:32:12','2025-06-04 22:44:16','安全监测'),(12,'anquan-database','安全监测数据库','bi-database','bg-success',12,'[\"安全监测\"]',0,'2025-06-04 17:32:12','2025-06-04 22:44:16','安全监测'),(13,'aimrsk-dependence','测评依赖','bi-app','bg-primary',0,'[\"安全测评\"]',1,'2025-06-04 13:14:26','2025-06-04 15:10:43','安全测评'),(15,'uncategorized-testing','未分组-安全测评','bi-question-circle','bg-secondary',10000,'[\"安全测评\"]',1,'2025-06-04 22:33:25','2025-06-04 15:17:56','安全测评'),(16,'uncategorized-security','未分组-安全监测','bi-question-circle','bg-secondary',10001,'[\"安全监测\"]',1,'2025-06-04 22:33:25','2025-06-04 15:17:56','安全监测'),(18,'platform-sectest','平台组件-安全测评','bi-layers','bg-warning',5,'[\"安全测评\"]',0,'2025-06-04 22:40:52','2025-06-04 22:44:16','安全测评'),(19,'uncategorized-secmonitor','未分组-安全监测','bi-question-circle','bg-secondary',10009,'[\"安全监测\"]',0,'2025-06-04 22:40:52','2025-06-04 15:17:56','安全监测'),(20,'aimrsk-engine','AIMRSK引擎组件','bi-gear-fill','bg-warning',11,'[\"安全测评\"]',1,'2025-06-04 15:08:35','2025-06-04 15:08:35','安全测评'),(21,'aimrsk-web','AIMRSK Web组件','bi-globe','bg-success',12,'[\"安全测评\"]',1,'2025-06-04 15:08:35','2025-06-04 15:08:35','安全测评'),(22,'toolplatform','工具平台组件','bi-tools','bg-primary',13,'[\"安全测评\"]',1,'2025-06-04 15:08:35','2025-06-04 15:08:35','安全测评'),(23,'reinforce-engine','加固引擎组件','bi-cpu','bg-warning',1,'[\"应用加固\"]',1,'2025-06-04 15:08:35','2025-06-04 15:08:35','应用加固'),(24,'reinforce-web','加固Web组件','bi-globe','bg-success',2,'[\"应用加固\"]',1,'2025-06-04 15:08:35','2025-06-04 15:08:35','应用加固'),(25,'toolplatform-reinforce','工具平台组件','bi-tools','bg-primary',3,'[\"应用加固\"]',1,'2025-06-04 15:08:35','2025-06-04 15:08:35','应用加固');
/*!40000 ALTER TABLE `component_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `component_config`
--

DROP TABLE IF EXISTS `component_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `component_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '组件ID，主键自增',
  `name` varchar(100) NOT NULL COMMENT '组件名称',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `version` varchar(100) DEFAULT NULL COMMENT '组件版本信息',
  `default_port` varchar(20) DEFAULT NULL COMMENT '默认端口号',
  `description` text COMMENT '组件描述',
  `category_key` varchar(50) NOT NULL COMMENT '分类键名，外键关联component_category.key',
  `form_type` varchar(50) NOT NULL COMMENT '适用的表单类型',
  `protocol` varchar(10) DEFAULT NULL COMMENT '协议类型：http、https、tcp等',
  `alias` varchar(100) DEFAULT NULL COMMENT '组件别名',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `order` int DEFAULT '0' COMMENT '在分类中的排序权重',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间（北京时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间（北京时间）',
  PRIMARY KEY (`id`),
  KEY `category_key` (`category_key`),
  CONSTRAINT `component_config_ibfk_1` FOREIGN KEY (`category_key`) REFERENCES `component_category` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb3 COMMENT='组件配置表 - 存储不同表单类型可选择的组件信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `component_config`
--

LOCK TABLES `component_config` WRITE;
/*!40000 ALTER TABLE `component_config` DISABLE KEYS */;
INSERT INTO `component_config` VALUES (1,'app-sender','app-sender','ver2.0.0_GENPLAT_rel_20240112.2','8080','应用发送器','app','安全监测','http','',1,2,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(2,'cleaner','cleaner','ver2.0.0_GENPLAT_rel_20240112.2','8080','清理器','app','安全监测','http','',1,3,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(3,'init','init','ver2.0.0_GENPLAT_rel_20240112.2','无','初始化','app','安全监测','http','',1,4,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(4,'receiver','receiver','ver2.0.0_GENPLAT_rel_20240112.2','8080','接收器','app','安全监测','http','',1,5,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(5,'security-event','security-event','ver2.0.0_GENPLAT_rel_20240112.2','8080','安全事件','app','安全监测','http','',1,6,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(6,'threat','threat','ver2.0.0_GENPLAT_rel_20240112.2','8080','威胁检测','app','安全监测','http','',1,7,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(7,'threat-index','threat-index','ver2.0.0_GENPLAT_rel_20240112.2','8080','威胁索引','app','安全监测','http','',1,8,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(8,'transfer','transfer','ver2.0.0_GENPLAT_rel_20240112.2','8080','传输器','app','安全监测','http','',1,9,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(9,'crash','crash','ver2.0.0_GENPLAT_rel_20240112.2','8080','崩溃分析','app','安全监测','http','',1,10,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(10,'nginx','nginx','1.18.0','80','Web服务器','server','安全监测','http','',1,1,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(11,'postgres','postgres','13.3','5432','数据库','server','安全监测','http','',1,2,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(12,'redis','redis','6.2.6','6379','缓存数据库','server','安全监测','http','',1,3,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(13,'minio','minio','RELEASE.2024-01-31T20-20-33Z-cpuv1','9000','对象存储','server','安全监测','http','',1,4,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(14,'zookeeper','zookeeper','3.7.0','2181','分布式协调','server','安全监测','http','',1,5,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(15,'kafka','kafka','2.8.1','9092','消息队列','server','安全监测','http','',1,6,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(16,'hbase','hbase','2.4.9','16010','分布式数据库','server','安全监测','http','',1,7,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(17,'elasticsearchMaster','elasticsearchMaster','7.15.2','9200','搜索引擎主节点','server','安全监测','http','',1,8,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(18,'elasticsearchClient','elasticsearchClient','7.15.2','9200','搜索引擎客户端','server','安全监测','http','',1,9,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(19,'kibana','kibana','7.15.2','5601','数据可视化','server','安全监测','http','',1,10,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(20,'web-service-nginx','web-service-nginx','1.18.0','80','Web服务','server','安全监测','http','',1,11,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(21,'web-service','web-service','ver2.0.0_GENPLAT_rel_20240112.2','8080','Web服务','server','安全监测','http','',1,12,'2025-06-04 17:32:12','2025-06-04 22:31:02'),(22,'docker','docker','20.10.11','无','容器运行时','ops','安全监测','http','',1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(23,'docker-compose','docker-compose','1.25.0','无','容器编排','ops','安全监测','http','',1,2,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(24,'monitor','monitor','ver2.0.0_GENPLAT_rel_20240112.2','8080','监控服务','ops','安全监测','http','',1,3,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(25,'luna','luna','latest','9001','Luna分析引擎','reinforce-engine','应用加固','http','luna',1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(26,'tp-appshield-engine','tp-appshield-engine','latest','8008','应用加固引擎','reinforce-engine','应用加固','http','tp-appshield-engine',1,2,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(27,'tp-html5-engine','tp-html5-engine','latest','8008','HTML5应用引擎','reinforce-engine','应用加固','http','tp-html5-engine',1,3,'2025-06-04 17:32:12','2025-06-04 22:33:25'),(28,'tp-ipa-service','tp-ipa-service','latest','8008','IPA服务引擎','reinforce-engine','应用加固','http','tp-ipa-service',1,4,'2025-06-04 17:32:12','2025-06-04 22:33:25'),(29,'tp-mini-program-engine','tp-mini-program-engine','latest','8008','小程序加固引擎','reinforce-engine','应用加固','http','tp-mini-program-engine',1,5,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(30,'tp-sdkshield-engine','tp-sdkshield-engine','latest','8008','SDK加固引擎','reinforce-engine','应用加固','http','tp-sdkshield-engine',1,6,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(31,'secweb','secweb','latest','8000','加固Web平台','reinforce-web','应用加固','http','secweb',1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(32,'tp-engine-scheduler','tp-engine-scheduler','latest','8080','引擎调度器','toolplatform-reinforce','应用加固','http','tp-engine-scheduler',1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(33,'tp-minio','tp-minio','latest','9000','对象存储服务','toolplatform-reinforce','应用加固','http','tp-minio',1,2,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(34,'tp-mysql','tp-mysql','latest','33060','MySQL数据库','toolplatform-reinforce','应用加固','tcp','tp-mysql',1,3,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(35,'tp-nacos','tp-nacos','latest','8848','服务注册中心','toolplatform-reinforce','应用加固','http','tp-nacos',1,4,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(36,'tp-web-backend','tp-web-backend','latest','8002','Web后端服务','toolplatform-reinforce','应用加固','http','tp-web-backend',1,5,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(37,'test-mysql','测试MySQL','8.0.0','3306','测试用MySQL数据库','uncategorized-testing','安全测评','tcp','mysql-test',1,1,'2025-06-04 22:07:15','2025-06-04 22:33:25'),(38,'test-redis','测试Redis','7.0.0','6379','测试用Redis缓存','uncategorized-testing','安全测评','tcp','redis-test',1,2,'2025-06-04 22:07:15','2025-06-04 22:33:25'),(39,'test-mongodb','测试MongoDB','5.0.0','27017','测试用MongoDB数据库','uncategorized-testing','安全测评','tcp','mongo-test',1,3,'2025-06-04 22:07:15','2025-06-04 22:33:25'),(40,'adbd','ADB守护进程','1.0.0','5037','Android Debug Bridge守护进程，用于Android设备调试','aimrsk-dependence','安全测评','tcp','adbd',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(41,'openstf','OpenSTF设备管理','1.0.0','7100','OpenSTF设备管理平台，用于远程设备控制','aimrsk-dependence','安全测评','http','openstf',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(42,'tp-applet-url-scanner','URL扫描器','1.0.0','8834','Nessus URL漏洞扫描器','aimrsk-dependence','安全测评','https','tp-applet-url-scanner',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(43,'tp-mongo','MongoDB数据库','4.4','27017','MongoDB文档数据库','aimrsk-dependence','安全测评','tcp','tp-mongo',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(44,'tp-redis','Redis缓存','6.2','6379','Redis内存数据库和缓存','aimrsk-dependence','安全测评','tcp','tp-redis',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(45,'tp-rethinkdb','RethinkDB数据库','2.4','28015','RethinkDB实时数据库','aimrsk-dependence','安全测评','tcp','tp-rethinkdb',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(46,'tp-sonarqube','SonarQube代码质量','8.9','9000','SonarQube代码质量分析平台','aimrsk-dependence','安全测评','http','tp-sonarqube',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(47,'luna','Luna分析引擎','latest','9001','Luna核心分析引擎','aimrsk-engine','安全测评','http','luna',1,1,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(48,'tp-android-dynamic-engine','Android动态分析引擎','latest','8004','Android应用动态分析引擎','aimrsk-engine','安全测评','http','tp-android-dynamic-engine',1,2,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(49,'tp-android-static-engine','Android静态分析引擎','latest','8008','Android应用静态分析引擎','aimrsk-engine','安全测评','http','tp-android-static-engine',1,3,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(50,'tp-applet-engine','小程序分析引擎','latest','8008','微信小程序安全分析引擎','aimrsk-engine','安全测评','http','tp-applet-engine',1,4,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(51,'tp-ceping-web-engine','Web测评引擎','latest','8008','Web应用安全测评引擎','aimrsk-engine','安全测评','http','tp-ceping-web-engine',1,5,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(52,'tp-harmony-engine','HarmonyOS分析引擎','latest','8008','HarmonyOS应用分析引擎','aimrsk-engine','安全测评','http','tp-harmony-engine',1,6,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(53,'tp-harmony-next-engine','HarmonyOS Next引擎','latest','8008','HarmonyOS Next应用分析引擎','aimrsk-engine','安全测评','http','tp-harmony-next-engine',1,7,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(54,'tp-ios-engine','iOS分析引擎','latest','8008','iOS应用安全分析引擎','aimrsk-engine','安全测评','http','tp-ios-engine',1,8,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(55,'tp-ios-sdk-engine','iOS SDK分析引擎','latest','8008','iOS SDK安全分析引擎','aimrsk-engine','安全测评','http','tp-ios-sdk-engine',1,9,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(56,'tp-sca-android-engine','Android SCA引擎','latest','8007','Android软件成分分析引擎','aimrsk-engine','安全测评','http','tp-sca-android-engine',1,10,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(57,'tp-sca-ios-engine','iOS SCA引擎','latest','8008','iOS软件成分分析引擎','aimrsk-engine','安全测评','http','tp-sca-ios-engine',1,11,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(58,'tp-sourcecode-engine','源码分析引擎','latest','8008','源代码安全分析引擎','aimrsk-engine','安全测评','http','tp-sourcecode-engine',1,12,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(59,'backend-ssp-admin','管理端后台服务','1.0.0','8998','SSP管理端后台API服务','aimrsk-web','安全测评','http','backend-ssp-admin',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(60,'backend-ssp-user','用户端后台服务','1.0.0','9100','SSP用户端后台API服务','aimrsk-web','安全测评','http','backend-ssp-user',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(61,'front-ssp-admin','管理端前台界面','1.0.0','8200','SSP管理端Web界面','aimrsk-web','安全测评','http','front-ssp-admin',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(62,'front-ssp-user','用户端前台界面','1.0.0','8100','SSP用户端Web界面','aimrsk-web','安全测评','http','front-ssp-user',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(63,'tp-engine-scheduler','引擎调度器','1.0.0','8080','测评引擎任务调度器','toolplatform','安全测评','http','tp-engine-scheduler',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(64,'tp-minio','MinIO对象存储','2021.6.17','9000','MinIO分布式对象存储服务','toolplatform','安全测评','http','tp-minio',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(65,'tp-mysql','MySQL数据库','8.0','33060','MySQL关系型数据库','toolplatform','安全测评','tcp','tp-mysql',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(66,'tp-nacos','Nacos服务发现','2.0.3','8848','Nacos服务发现和配置管理','toolplatform','安全测评','http','tp-nacos',1,0,'2025-06-04 15:12:43','2025-06-04 15:12:43'),(67,'analyzer-dev','analyzer-dev','ver2.0.0_GENPLAT_rel_20240112.2 ','无','','app','安全监测','http','',1,0,'2025-06-09 23:48:55','2025-06-09 23:48:55');
/*!40000 ALTER TABLE `component_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `excel_file`
--

DROP TABLE IF EXISTS `excel_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `excel_file` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '文件ID，主键自增',
  `filename` varchar(255) NOT NULL COMMENT '存储的文件名',
  `original_filename` varchar(255) NOT NULL COMMENT '原始文件名',
  `created_at` datetime DEFAULT NULL COMMENT '文件创建时间（北京时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '文件更新时间（北京时间）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb3 COMMENT='Excel文件表 - 存储生成的Excel文件信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `excel_file`
--

LOCK TABLES `excel_file` WRITE;
/*!40000 ALTER TABLE `excel_file` DISABLE KEYS */;
INSERT INTO `excel_file` VALUES (1,'客户公司名称-运维文档-安全监测_2025-06-04.xlsx','客户公司名称-运维文档-安全监测_2025-06-04.xlsx','2025-06-04 15:00:42','2025-06-04 15:00:42'),(2,'客户公司名称-运维文档-安全测评_20250604.xlsx','客户公司名称-运维文档-安全测评_20250604.xlsx','2025-06-04 15:16:15','2025-06-04 15:16:15'),(3,'客户公司名称-运维文档-安全测评_20250604.xlsx','客户公司名称-运维文档-安全测评_20250604.xlsx','2025-06-04 15:27:31','2025-06-04 15:27:31'),(4,'测试公司-运维文档-安全测评_20241204.xlsx','测试公司-运维文档-安全测评_20241204.xlsx','2025-06-04 15:27:35','2025-06-04 15:27:35'),(5,'测试公司-运维文档-安全测评_20241204.xlsx','测试公司-运维文档-安全测评_20241204.xlsx','2025-06-04 15:28:41','2025-06-04 15:28:41'),(6,'客户公司名称-运维文档-应用加固_20250607.xlsx','客户公司名称-运维文档-应用加固_20250607.xlsx','2025-06-07 05:05:44','2025-06-07 05:05:44'),(7,'珠海金山办公软件有限公司-运维文档-应用加固_20250605.xlsx','珠海金山办公软件有限公司-运维文档-应用加固_20250605.xlsx','2025-06-09 23:47:28','2025-06-09 23:47:28');
/*!40000 ALTER TABLE `excel_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `field_type_definitions`
--

DROP TABLE IF EXISTS `field_type_definitions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `field_type_definitions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type_name` varchar(50) NOT NULL,
  `type_label` varchar(100) NOT NULL,
  `type_description` text,
  `default_validation` text,
  `has_options` tinyint(1) DEFAULT NULL,
  `icon_class` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_name` (`type_name`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `field_type_definitions`
--

LOCK TABLES `field_type_definitions` WRITE;
/*!40000 ALTER TABLE `field_type_definitions` DISABLE KEYS */;
INSERT INTO `field_type_definitions` VALUES (1,'text','单行文本','普通的文本输入框','{\"maxLength\": 255}',0,'bi-input-cursor-text','2025-06-04 17:32:12'),(2,'textarea','多行文本','多行文本输入框','{\"maxLength\": 1000}',0,'bi-textarea','2025-06-04 17:32:12'),(3,'select','下拉选择','下拉选择框','{}',1,'bi-menu-button-wide','2025-06-04 17:32:12'),(4,'checkbox','复选框','多选复选框','{}',1,'bi-check-square','2025-06-04 17:32:12'),(5,'radio','单选框','单选按钮组','{}',1,'bi-record-circle','2025-06-04 17:32:12'),(6,'date','日期选择','日期选择器','{}',0,'bi-calendar-date','2025-06-04 17:32:12'),(7,'datetime','日期时间','日期时间选择器','{}',0,'bi-calendar-event','2025-06-04 17:32:12'),(8,'number','数字输入','数字输入框','{\"min\": 0}',0,'bi-123','2025-06-04 17:32:12'),(9,'email','邮箱地址','邮箱输入框','{}',0,'bi-envelope','2025-06-04 17:32:12'),(10,'url','网址链接','URL输入框','{}',0,'bi-link','2025-06-04 17:32:12'),(11,'password','密码输入','密码输入框','{\"minLength\": 6}',0,'bi-key','2025-06-04 17:32:12'),(12,'file','文件上传','文件上传组件','{}',0,'bi-file-earmark-arrow-up','2025-06-04 17:32:12'),(13,'switch','开关切换','开关切换组件','{}',0,'bi-toggle-on','2025-06-04 17:32:12'),(14,'slider','滑块输入','数值滑块','{\"min\": 0, \"max\": 100}',0,'bi-sliders','2025-06-04 17:32:12'),(15,'color','颜色选择','颜色选择器','{}',0,'bi-palette','2025-06-04 17:32:12');
/*!40000 ALTER TABLE `field_type_definitions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `form_config_versions`
--

DROP TABLE IF EXISTS `form_config_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `form_config_versions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `form_type` varchar(50) NOT NULL,
  `version_name` varchar(100) NOT NULL,
  `version_description` text,
  `config_data` text NOT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `form_type` (`form_type`,`version_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `form_config_versions`
--

LOCK TABLES `form_config_versions` WRITE;
/*!40000 ALTER TABLE `form_config_versions` DISABLE KEYS */;
/*!40000 ALTER TABLE `form_config_versions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `form_field_configs`
--

DROP TABLE IF EXISTS `form_field_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `form_field_configs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `form_type` varchar(50) NOT NULL,
  `group_id` int DEFAULT NULL,
  `field_name` varchar(100) NOT NULL,
  `field_label` varchar(100) NOT NULL,
  `field_type` varchar(50) NOT NULL,
  `field_description` text,
  `placeholder` varchar(200) DEFAULT NULL,
  `default_value` text,
  `is_required` tinyint(1) DEFAULT NULL,
  `is_readonly` tinyint(1) DEFAULT NULL,
  `is_auto_fill` tinyint(1) DEFAULT NULL,
  `display_order` int DEFAULT NULL,
  `validation_rules` text,
  `field_options` text,
  `css_classes` varchar(200) DEFAULT NULL,
  `grid_columns` int DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `form_type` (`form_type`,`field_name`),
  KEY `group_id` (`group_id`),
  CONSTRAINT `form_field_configs_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `form_field_groups` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `form_field_configs`
--

LOCK TABLES `form_field_configs` WRITE;
/*!40000 ALTER TABLE `form_field_configs` DISABLE KEYS */;
INSERT INTO `form_field_configs` VALUES (1,'安全监测',6,'公司名称','公司名称','text',NULL,'请输入公司名称',NULL,1,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(2,'安全监测',6,'记录日期','记录日期','date',NULL,NULL,NULL,1,0,0,2,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(3,'安全监测',6,'前端版本','前端版本','text',NULL,'请输入前端版本',NULL,1,0,0,3,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(4,'安全监测',6,'后端版本','后端版本','text',NULL,'请输入后端版本',NULL,1,0,0,4,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(5,'安全监测',6,'标准或定制','标准/定制','select',NULL,NULL,'标准版',0,0,0,5,NULL,'[\"\\u6807\\u51c6\\u7248\", \"\\u5b9a\\u5236\\u7248\"]',NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(6,'安全监测',7,'客户标识','客户标识','text',NULL,'请输入客户标识',NULL,0,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(7,'安全监测',7,'日活','日活','number',NULL,'请输入日活数量',NULL,0,0,0,2,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(8,'安全监测',8,'业务功能页面地址','业务功能页面地址','url',NULL,'请输入业务功能页面地址',NULL,0,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(9,'安全监测',8,'超级管理员账号','超级管理员账号','text',NULL,NULL,'<EMAIL>',0,0,0,2,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(10,'安全监测',8,'超级管理员密码','超级管理员密码','password',NULL,NULL,'everisk@!QAZ2wsx',0,0,0,3,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(11,'安全监测',8,'客户管理员账号','客户管理员账号','text',NULL,'请输入客户管理员账号',NULL,0,0,0,4,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(12,'安全监测',8,'客户管理员密码','客户管理员密码','password',NULL,NULL,'everisk@!QAZ2wsx',0,0,0,5,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(13,'安全监测',8,'init地址','init地址','url',NULL,'请输入init地址',NULL,0,0,0,6,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(14,'安全监测',8,'init用户名','init用户名','text',NULL,NULL,'prometheus_user',0,0,0,7,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(15,'安全监测',8,'init密码','init密码','password',NULL,NULL,'1qaz@WSX',0,0,0,8,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(16,'安全监测',8,'kibana地址','kibana地址','url',NULL,'请输入kibana地址',NULL,0,0,0,9,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(17,'安全监测',8,'kibana认证信息','kibana认证信息','text',NULL,NULL,'账号：elastic 密码: beap123',0,0,0,10,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(18,'安全监测',9,'SDK外网流量入口','SDK外网流量入口','text',NULL,'请输入SDK外网流量入口',NULL,0,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(19,'安全监测',9,'SDK流量转发到Nginx入口','SDK流量转发到Nginx入口','text',NULL,'请输入SDK流量转发到Nginx入口',NULL,0,0,0,2,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(20,'安全测评',1,'公司名称','公司名称','text',NULL,'请输入公司名称',NULL,1,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(21,'安全测评',1,'记录日期','记录日期','date',NULL,NULL,NULL,1,0,0,2,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(22,'安全测评',2,'客户标识','客户标识','text',NULL,'请输入客户标识',NULL,0,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(23,'安全测评',3,'部署包版本','部署包版本','text',NULL,'请输入部署包版本',NULL,1,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(24,'安全测评',3,'产品功能','产品功能','textarea',NULL,'请输入产品功能描述',NULL,0,0,0,2,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(25,'安全测评',3,'授权功能','授权功能','textarea',NULL,'请输入授权功能描述',NULL,0,0,0,3,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(26,'安全测评',3,'授权开始日期','授权开始日期','date',NULL,NULL,NULL,0,0,0,4,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(27,'安全测评',3,'授权结束日期','授权结束日期','date',NULL,NULL,NULL,0,0,0,5,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(28,'应用加固',12,'公司名称','公司名称','text',NULL,'请输入公司名称',NULL,1,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(29,'应用加固',12,'记录日期','记录日期','date',NULL,NULL,NULL,1,0,0,2,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(30,'应用加固',13,'客户','客户','text',NULL,'请输入客户名称',NULL,1,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(31,'应用加固',14,'平台地址','平台地址','url',NULL,'请输入平台地址',NULL,1,0,0,1,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(32,'应用加固',14,'部署的平台版本','部署的平台版本','text',NULL,'请输入部署的平台版本',NULL,0,0,0,2,NULL,NULL,NULL,12,'2025-06-04 09:32:12','2025-06-04 09:32:12');
/*!40000 ALTER TABLE `form_field_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `form_field_groups`
--

DROP TABLE IF EXISTS `form_field_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `form_field_groups` (
  `id` int NOT NULL AUTO_INCREMENT,
  `form_type` varchar(50) NOT NULL,
  `group_name` varchar(100) NOT NULL,
  `group_label` varchar(100) NOT NULL,
  `group_description` text,
  `display_order` int DEFAULT NULL,
  `is_collapsible` tinyint(1) DEFAULT NULL,
  `is_expanded_by_default` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `form_type` (`form_type`,`group_name`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `form_field_groups`
--

LOCK TABLES `form_field_groups` WRITE;
/*!40000 ALTER TABLE `form_field_groups` DISABLE KEYS */;
INSERT INTO `form_field_groups` VALUES (1,'安全测评','basic_info','基本信息','项目的基本信息',1,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(2,'安全测评','customer_info','客户信息','客户相关信息',2,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(3,'安全测评','version_info','版本信息','系统版本相关信息',3,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(4,'安全测评','server_info','服务器信息','服务器部署信息',4,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(5,'安全测评','maintenance_records','维护记录','系统维护记录',5,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(6,'安全监测','basic_info','基本信息','项目的基本信息',1,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(7,'安全监测','customer_info','客户信息','客户相关信息',2,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(8,'安全监测','access_info','访问信息','系统访问相关信息',3,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(9,'安全监测','network_config','网络配置','网络配置信息',4,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(10,'安全监测','server_info','服务器信息','服务器部署信息',5,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(11,'安全监测','maintenance_records','维护记录','系统维护记录',6,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(12,'应用加固','basic_info','基本信息','项目的基本信息',1,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(13,'应用加固','customer_info','客户信息','客户相关信息',2,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(14,'应用加固','access_info','访问信息','系统访问相关信息',3,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(15,'应用加固','server_info','服务器信息','服务器部署信息',4,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(16,'应用加固','maintenance_records','维护记录','系统维护记录',5,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12');
/*!40000 ALTER TABLE `form_field_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `form_submission`
--

DROP TABLE IF EXISTS `form_submission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `form_submission` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `company_name` varchar(200) NOT NULL COMMENT '公司名称，支持别名格式（如：公司名-别名）',
  `form_type` varchar(50) NOT NULL COMMENT '表单类型：安全监测、安全测评、应用加固',
  `record_date` date NOT NULL COMMENT '表单记录日期',
  `form_data` text NOT NULL COMMENT '完整的表单数据，JSON格式存储',
  `excel_filename` varchar(255) DEFAULT NULL COMMENT '生成的Excel文件名',
  `excel_filepath` varchar(500) DEFAULT NULL COMMENT 'Excel文件的完整存储路径',
  `server_count` int DEFAULT NULL COMMENT '服务器数量统计',
  `component_count` int DEFAULT NULL COMMENT '组件数量统计',
  `created_at` datetime DEFAULT NULL COMMENT '提交时间（北京时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间（北京时间）',
  `created_by` varchar(100) DEFAULT NULL COMMENT '创建者/编辑人姓名',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '提交者IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '浏览器用户代理信息',
  `status` varchar(20) DEFAULT NULL COMMENT '处理状态：success-成功、failed-失败、processing-处理中',
  `error_message` text COMMENT '错误信息（处理失败时记录详细错误）',
  PRIMARY KEY (`id`),
  KEY `ix_form_submission_company_name` (`company_name`),
  KEY `ix_form_submission_created_at` (`created_at`),
  KEY `ix_form_submission_form_type` (`form_type`),
  KEY `ix_form_submission_record_date` (`record_date`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 COMMENT='表单提交记录表 - 存储所有表单提交的完整数据，是历史数据页面的主要数据源';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `form_submission`
--

LOCK TABLES `form_submission` WRITE;
/*!40000 ALTER TABLE `form_submission` DISABLE KEYS */;
INSERT INTO `form_submission` VALUES (2,'珠海金山办公软件有限公司','应用加固','2025-06-05','{\n  \"公司名称\": \"珠海金山办公软件有限公司\",\n  \"升级平台信息\": \"http://***********:9001\",\n  \"客户\": \"珠海金山办公软件有限公司\",\n  \"客户标识\": \"14dd7b7d-1336-4673-92bf-5fcde943098e\",\n  \"平台访问地址\": \"http://***********:8000\",\n  \"文档后缀\": \"应用加固\",\n  \"服务器信息\": [\n    {\n      \"CPU\": \"8核\",\n      \"CPU自定义\": \"\",\n      \"IP地址\": \"***********\",\n      \"SSH端口\": \"22\",\n      \"内存\": \"16GB\",\n      \"内存自定义\": \"\",\n      \"启用端口修改\": false,\n      \"折叠\": false,\n      \"用途\": \"\",\n      \"用途类型\": \"内网\",\n      \"磁盘\": \"500GB\",\n      \"磁盘自定义\": \"\",\n      \"系统发行版\": \"Ubuntu 22.04 LTS\",\n      \"系统发行版自定义\": \"\",\n      \"系统架构\": \"x86\",\n      \"组件折叠\": false,\n      \"组件端口\": {\n        \"luna\": \"9001\",\n        \"secweb\": \"8000\",\n        \"tp-appshield-engine\": \"8008\",\n        \"tp-engine-scheduler\": \"8080\",\n        \"tp-html5-engine\": \"8008\",\n        \"tp-ipa-service\": \"8008\",\n        \"tp-mini-program-engine\": \"8008\",\n        \"tp-minio\": \"9000\",\n        \"tp-mysql\": \"33060\",\n        \"tp-nacos\": \"8848\",\n        \"tp-sdkshield-engine\": \"8008\",\n        \"tp-web-backend\": \"8002\"\n      },\n      \"运维用户\": [\n        {\n          \"isDefault\": true,\n          \"showPassword\": false,\n          \"密码\": \"\",\n          \"用户名\": \"root\"\n        },\n        {\n          \"isDefault\": false,\n          \"showPassword\": false,\n          \"密码\": \"1qaz2wsx@123\",\n          \"用户名\": \"secneo\"\n        }\n      ],\n      \"部署应用\": [\n        \"luna\",\n        \"tp-appshield-engine\",\n        \"tp-html5-engine\",\n        \"tp-ipa-service\",\n        \"tp-mini-program-engine\",\n        \"tp-sdkshield-engine\",\n        \"secweb\",\n        \"tp-engine-scheduler\",\n        \"tp-minio\",\n        \"tp-mysql\",\n        \"tp-nacos\",\n        \"tp-web-backend\"\n      ],\n      \"组件端口_str\": \"luna: 9001\\ntp-appshield-engine: 8008\\ntp-html5-engine: 8008\\ntp-ipa-service: 8008\\ntp-mini-program-engine: 8008\\ntp-sdkshield-engine: 8008\\nsecweb: 8000\\ntp-engine-scheduler: 8080\\ntp-minio: 9000\\ntp-mysql: 33060\\ntp-nacos: 8848\\ntp-web-backend: 8002\",\n      \"Root密码\": \"\",\n      \"运维用户1\": \"secneo\",\n      \"运维用户1密码\": \"1qaz2wsx@123\",\n      \"运维用户2\": \"\",\n      \"运维用户2密码\": \"\",\n      \"运维信息\": \"运维用户1：secneo / 1qaz2wsx@123\"\n    }\n  ],\n  \"管理员信息\": \"平台用户账号：jsbg 密码：jsbg12#$\\n管理员账号：admin 密码：admin@secneo\\n超级管理员账号：sadmin 密码：admin@secneo#2017\",\n  \"维护记录\": [\n    {\n      \"content\": \"#303708 【黑盒7.8.3】珠海金山办公软件有限公司-应用加固-license授权任务\\nhttps://ones.bangcle.com/project/#/team/RZxvwUZ8/task/AGE7K8CBaF5ITFZt\",\n      \"onesLink\": \"#303708 【黑盒7.8.3】珠海金山办公软件有限公司-应用加固-license授权任务 https://ones.bangcle.com/project/#/team/RZxvwUZ8/task/AGE7K8CBaF5ITFZt\",\n      \"staff\": \"陈俊广\",\n      \"time\": \"2025-06-04T17:47\",\n      \"type\": \"平台部署\",\n      \"SSH端口\": \"\",\n      \"Root密码\": \"\",\n      \"运维用户1\": \"\",\n      \"运维用户1密码\": \"\",\n      \"运维用户2\": \"\",\n      \"运维用户2密码\": \"\"\n    }\n  ],\n  \"编辑人\": \"系统管理员\",\n  \"记录日期\": \"2025-06-05\",\n  \"部署的平台版本\": \"ver7.8.3_reinforce_rel_20241219_1018\",\n  \"component_aliases\": {},\n  \"form_type\": \"应用加固\"\n}','珠海金山办公软件有限公司-运维文档-应用加固_20250605.xlsx','/opt/export_excel/current/backend/excel_files/generated/珠海金山办公软件有限公司-运维文档-应用加固_20250605.xlsx',1,12,'2025-06-09 23:47:28','2025-06-09 23:47:28','系统管理员','127.0.0.1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0','success',NULL);
/*!40000 ALTER TABLE `form_submission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `form_submission_edit`
--

DROP TABLE IF EXISTS `form_submission_edit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `form_submission_edit` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `submission_id` int NOT NULL COMMENT '关联的表单提交记录ID，外键关联form_submission.id',
  `edit_type` varchar(50) NOT NULL COMMENT '编辑类型：update-更新、restore-恢复、regenerate-重新生成',
  `edit_description` varchar(500) DEFAULT NULL COMMENT '编辑操作描述',
  `old_data` text COMMENT '编辑前的数据，JSON格式',
  `new_data` text COMMENT '编辑后的数据，JSON格式',
  `changed_fields` text COMMENT '变更的字段列表，JSON格式',
  `edited_by` varchar(100) DEFAULT NULL COMMENT '编辑者姓名',
  `edit_reason` varchar(500) DEFAULT NULL COMMENT '编辑原因说明',
  `created_at` datetime DEFAULT NULL COMMENT '编辑时间（北京时间）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '编辑者IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '编辑者浏览器用户代理',
  PRIMARY KEY (`id`),
  KEY `ix_form_submission_edit_submission_id` (`submission_id`),
  KEY `ix_form_submission_edit_created_at` (`created_at`),
  CONSTRAINT `form_submission_edit_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `form_submission` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='表单编辑历史表 - 记录每次表单编辑的详细历史，用于审计和数据恢复';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `form_submission_edit`
--

LOCK TABLES `form_submission_edit` WRITE;
/*!40000 ALTER TABLE `form_submission_edit` DISABLE KEYS */;
/*!40000 ALTER TABLE `form_submission_edit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `form_type`
--

DROP TABLE IF EXISTS `form_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `form_type` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '表单类型ID，主键自增',
  `name` varchar(50) NOT NULL COMMENT '表单类型名称，唯一',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `description` text COMMENT '表单类型描述',
  `is_default` tinyint(1) DEFAULT NULL COMMENT '是否默认类型：1-是，0-否',
  `is_active` tinyint(1) DEFAULT NULL COMMENT '是否启用：1-启用，0-禁用',
  `order` int DEFAULT NULL COMMENT '排序权重，数字越小越靠前',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间（北京时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间（北京时间）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_form_type_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3 COMMENT='表单类型表 - 定义系统支持的表单类型';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `form_type`
--

LOCK TABLES `form_type` WRITE;
/*!40000 ALTER TABLE `form_type` DISABLE KEYS */;
INSERT INTO `form_type` VALUES (1,'安全测评','安全测评','移动应用安全测评表单',1,1,1,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(2,'安全监测','安全监测','安全监测平台表单',1,1,2,'2025-06-04 17:32:12','2025-06-04 17:32:12'),(3,'应用加固','应用加固','移动应用加固表单',1,1,3,'2025-06-04 17:32:12','2025-06-04 17:32:12');
/*!40000 ALTER TABLE `form_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `group_roles`
--

DROP TABLE IF EXISTS `group_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `group_roles` (
  `group_id` int NOT NULL,
  `role_id` int NOT NULL,
  PRIMARY KEY (`group_id`,`role_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `group_roles_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `user_group` (`id`),
  CONSTRAINT `group_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户组角色关联表 - 多对多关系，用户组与角色的关联';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `group_roles`
--

LOCK TABLES `group_roles` WRITE;
/*!40000 ALTER TABLE `group_roles` DISABLE KEYS */;
INSERT INTO `group_roles` VALUES (1,2),(2,2),(3,3),(1,4);
/*!40000 ALTER TABLE `group_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `history_record`
--

DROP TABLE IF EXISTS `history_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `history_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID，主键自增',
  `excel_file_id` int NOT NULL COMMENT '关联的Excel文件ID，外键',
  `action` varchar(50) NOT NULL COMMENT '操作类型：create-创建、update-更新、delete-删除',
  `description` text COMMENT '操作描述',
  `created_at` datetime DEFAULT NULL COMMENT '操作时间（北京时间）',
  `user` varchar(100) DEFAULT NULL COMMENT '操作用户',
  PRIMARY KEY (`id`),
  KEY `excel_file_id` (`excel_file_id`),
  CONSTRAINT `history_record_ibfk_1` FOREIGN KEY (`excel_file_id`) REFERENCES `excel_file` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb3 COMMENT='历史记录表 - 记录Excel文件的操作历史';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `history_record`
--

LOCK TABLES `history_record` WRITE;
/*!40000 ALTER TABLE `history_record` DISABLE KEYS */;
INSERT INTO `history_record` VALUES (1,1,'create','未知用户在2025-06-04 23:00:41创建了文件','2025-06-04 15:00:42','未知用户'),(2,2,'create','未知用户在2025-06-04 23:16:15创建了文件','2025-06-04 15:16:15','未知用户'),(3,3,'overwrite','未知用户在2025-06-04 23:27:31覆盖了文件','2025-06-04 15:27:31','未知用户'),(4,4,'create','测试用户在2025-06-04 23:27:35创建了文件','2025-06-04 15:27:35','测试用户'),(5,5,'overwrite','测试用户在2025-06-04 23:28:41覆盖了文件','2025-06-04 15:28:41','测试用户'),(6,6,'create','系统管理员在2025-06-07 05:05:44创建了文件','2025-06-07 05:05:44','系统管理员'),(7,7,'create','系统管理员在2025-06-09 23:47:27创建了文件','2025-06-09 23:47:28','系统管理员');
/*!40000 ALTER TABLE `history_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permission`
--

DROP TABLE IF EXISTS `permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '权限ID，主键自增',
  `code` varchar(100) NOT NULL COMMENT '权限代码，唯一标识',
  `name` varchar(100) NOT NULL COMMENT '权限名称',
  `description` text COMMENT '权限描述',
  `module` varchar(50) NOT NULL COMMENT '所属模块：user-用户、form-表单、component-组件等',
  `is_active` tinyint(1) DEFAULT NULL COMMENT '是否激活：1-激活，0-禁用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间（北京时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间（北京时间）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_permission_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb3 COMMENT='权限表 - 定义系统中的各种操作权限';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permission`
--

LOCK TABLES `permission` WRITE;
/*!40000 ALTER TABLE `permission` DISABLE KEYS */;
INSERT INTO `permission` VALUES (1,'user.view','查看用户','查看用户列表和详情','user',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(2,'user.create','创建用户','创建新用户','user',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(3,'user.edit','编辑用户','编辑用户信息','user',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(4,'user.delete','删除用户','删除用户','user',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(5,'role.view','查看角色','查看角色列表和详情','role',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(6,'role.create','创建角色','创建新角色','role',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(7,'role.edit','编辑角色','编辑角色信息','role',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(8,'role.delete','删除角色','删除角色','role',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(9,'permission.view','查看权限','查看权限列表和详情','permission',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(10,'permission.create','创建权限','创建新权限','permission',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(11,'permission.edit','编辑权限','编辑权限信息','permission',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(12,'permission.delete','删除权限','删除权限','permission',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(13,'group.view','查看用户组','查看用户组列表和详情','group',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(14,'group.create','创建用户组','创建新用户组','group',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(15,'group.edit','编辑用户组','编辑用户组信息','group',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(16,'group.delete','删除用户组','删除用户组','group',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(17,'form.view','查看表单','查看表单数据','form',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(18,'form.create','创建表单','创建新表单','form',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(19,'form.edit','编辑表单','编辑表单数据','form',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(20,'form.delete','删除表单','删除表单数据','form',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(21,'form.export','导出表单','导出表单为Excel','form',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(22,'component.view','查看组件','查看组件列表和详情','component',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(23,'component.create','创建组件','创建新组件','component',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(24,'component.edit','编辑组件','编辑组件信息','component',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(25,'component.delete','删除组件','删除组件','component',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(26,'template.view','查看模板','查看模板列表和详情','template',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(27,'template.create','创建模板','创建新模板','template',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(28,'template.edit','编辑模板','编辑模板信息','template',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(29,'template.delete','删除模板','删除模板','template',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(30,'system.view','查看系统信息','查看系统状态和信息','system',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(31,'system.config','系统配置','修改系统配置','system',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(32,'system.log','查看日志','查看系统日志','system',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(33,'form.download','下载表单','下载表单文件','form',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(34,'form.import','导入表单','导入表单数据','form',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(35,'form.submit','提交表单','提交表单数据','form',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(36,'template.upload','上传模板','上传模板文件','template',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(37,'system.cache.view','查看缓存','查看缓存状态和统计','cache',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(38,'system.cache.manage','管理缓存','清理和管理缓存','cache',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(39,'history.view','查看历史','查看历史记录','history',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(40,'history.cache.view','查看历史缓存','查看历史记录缓存','history',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(41,'history.cache.manage','管理历史缓存','管理历史记录缓存','history',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(42,'form.snapshot.view','查看表单快照','查看表单快照','snapshot',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(43,'form.snapshot.create','创建表单快照','创建表单快照','snapshot',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(44,'form.snapshot.delete','删除表单快照','删除表单快照','snapshot',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(45,'form.duplicate.view','查看重复检查','查看重复检查统计','duplicate',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(46,'form.duplicate.manage','管理重复检查','管理重复检查记录','duplicate',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(47,'system.rate_limit.view','查看限流','查看限流状态','rate_limit',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(48,'system.rate_limit.manage','管理限流','管理限流配置','rate_limit',1,'2025-06-04 09:32:12','2025-06-04 09:32:12');
/*!40000 ALTER TABLE `permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role`
--

DROP TABLE IF EXISTS `role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '角色ID，主键自增',
  `code` varchar(50) NOT NULL COMMENT '角色代码，唯一标识',
  `name` varchar(100) NOT NULL COMMENT '角色名称',
  `description` text COMMENT '角色描述',
  `is_active` tinyint(1) DEFAULT NULL COMMENT '是否激活：1-激活，0-禁用',
  `is_system` tinyint(1) DEFAULT NULL COMMENT '是否系统角色：1-系统角色（不可删除），0-自定义角色',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间（北京时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间（北京时间）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_role_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb3 COMMENT='角色表 - 定义系统中的各种角色和权限组';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role`
--

LOCK TABLES `role` WRITE;
/*!40000 ALTER TABLE `role` DISABLE KEYS */;
INSERT INTO `role` VALUES (1,'admin','系统管理员','拥有所有权限的系统管理员',1,1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(2,'operator','运维人员','负责日常运维工作的人员',1,1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(3,'viewer','查看者','只能查看数据的用户',1,1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(4,'form_manager','表单管理员','负责表单管理的用户',1,0,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(5,'test_role','测试角色','用于测试按钮功能的角色',1,0,'2025-06-04 10:51:00','2025-06-04 10:51:00');
/*!40000 ALTER TABLE `role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permissions` (
  `role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`),
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='角色权限关联表 - 多对多关系，角色与权限的关联';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (1,1),(5,1),(1,2),(1,3),(1,4),(1,5),(1,6),(1,7),(1,8),(1,9),(1,10),(1,11),(1,12),(1,13),(1,14),(1,15),(1,16),(1,17),(2,17),(3,17),(4,17),(5,17),(1,18),(2,18),(4,18),(1,19),(2,19),(4,19),(1,20),(4,20),(1,21),(2,21),(4,21),(1,22),(2,22),(3,22),(4,22),(1,23),(4,23),(1,24),(2,24),(4,24),(1,25),(2,25),(1,26),(2,26),(3,26),(4,26),(1,27),(4,27),(1,28),(2,28),(4,28),(1,29),(2,29),(1,30),(1,31),(1,32),(1,33),(2,33),(3,33),(4,33),(1,34),(2,34),(4,34),(1,35),(2,35),(4,35),(1,36),(2,36),(4,36),(1,37),(2,37),(3,37),(1,38),(1,39),(2,39),(3,39),(1,40),(1,41),(1,42),(1,43),(1,44),(1,45),(1,46),(1,47),(1,48);
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `template_version`
--

DROP TABLE IF EXISTS `template_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `template_version` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '模板ID，主键自增',
  `filename` varchar(255) NOT NULL COMMENT '模板文件名，唯一',
  `template_type` varchar(50) NOT NULL COMMENT '模板类型',
  `is_active` tinyint(1) DEFAULT NULL COMMENT '是否为当前活动模板：1-是，0-否',
  `alias` varchar(100) DEFAULT NULL COMMENT '模板别名',
  `form_type` varchar(50) DEFAULT NULL COMMENT '关联的表单类型',
  `is_backup` tinyint(1) DEFAULT NULL COMMENT '是否为备份模板：1-是，0-否',
  `backup_of` varchar(255) DEFAULT NULL COMMENT '备份的原模板文件名',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间（北京时间）',
  `last_modified` datetime DEFAULT NULL COMMENT '最后修改时间（北京时间）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `filename` (`filename`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 COMMENT='模板版本表 - 存储Excel模板文件的版本信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `template_version`
--

LOCK TABLES `template_version` WRITE;
/*!40000 ALTER TABLE `template_version` DISABLE KEYS */;
INSERT INTO `template_version` VALUES (1,'安全测评-运维信息登记模板.xlsx','安全测评',1,'安全测评','安全测评',0,NULL,'2025-06-04 13:16:06','2025-06-04 13:16:06'),(2,'安全监测-运维信息登记模板.xlsx','安全监测',1,'安全监测','安全监测',0,NULL,'2025-05-13 11:03:44','2025-06-04 13:16:06'),(3,'应用加固-运维信息登记模板.xlsx','应用加固',1,'应用加固','应用加固',0,NULL,'2025-06-04 13:16:06','2025-06-04 13:16:06'),(4,'API平台---API_20250529.xlsx','未知',0,'API平台---API_20250529','未知',0,NULL,'2025-06-04 13:16:06','2025-06-04 13:16:06');
/*!40000 ALTER TABLE `template_version` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键自增',
  `username` varchar(80) NOT NULL COMMENT '用户名，登录账号，唯一',
  `email` varchar(120) NOT NULL COMMENT '邮箱地址，唯一',
  `password_hash` varchar(128) NOT NULL COMMENT '密码哈希值，使用bcrypt加密',
  `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `department` varchar(100) DEFAULT NULL COMMENT '所属部门',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `is_active` tinyint(1) DEFAULT NULL COMMENT '是否激活：1-激活，0-禁用',
  `is_admin` tinyint(1) DEFAULT NULL COMMENT '是否管理员：1-是，0-否',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间（北京时间）',
  `login_count` int DEFAULT NULL COMMENT '登录次数统计',
  `created_at` datetime DEFAULT NULL COMMENT '账户创建时间（北京时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '账户信息更新时间（北京时间）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_user_username` (`username`),
  UNIQUE KEY `ix_user_email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3 COMMENT='用户信息表 - 存储系统用户的基本信息和权限相关数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (1,'admin','<EMAIL>','$2b$12$/sXNb3/ivE2B3ZZ/.dxvzOhSiSXFOIY/xCoYJiGgCVzU/uAhtydFW','系统管理员','1','1','1',1,1,'2025-06-09 10:48:56',17,'2025-06-04 09:32:12','2025-06-09 18:48:56'),(2,'junguangchen','<EMAIL>','$2b$12$dScfzsB35OiomD.erieyF.f8VL3FXAOjIEmpn3/i6EpmMw93.PSCy','陈俊广','15915908219','交付','运维',1,0,'2025-06-04 10:12:43',2,'2025-06-04 09:43:52','2025-06-04 10:12:43'),(3,'testuser','<EMAIL>','$2b$12$DlSRrXXk7.st9cOJi9YFOeXJ.qkpCP/ynluE9i6gBYhJlXCU7ssq.','测试用户','','','',1,0,NULL,0,'2025-06-04 10:17:10','2025-06-04 10:17:10');
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_group`
--

DROP TABLE IF EXISTS `user_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_group` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户组ID，主键自增',
  `code` varchar(50) NOT NULL COMMENT '用户组代码，唯一标识',
  `name` varchar(100) NOT NULL COMMENT '用户组名称',
  `description` text COMMENT '用户组描述',
  `is_active` tinyint(1) DEFAULT NULL COMMENT '是否激活：1-激活，0-禁用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间（北京时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间（北京时间）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_user_group_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3 COMMENT='用户组表 - 用于组织和管理用户的分组';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_group`
--

LOCK TABLES `user_group` WRITE;
/*!40000 ALTER TABLE `user_group` DISABLE KEYS */;
INSERT INTO `user_group` VALUES (1,'security_team','安全团队','负责安全相关工作的团队',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(2,'ops_team','运维团队','负责运维工作的团队',1,'2025-06-04 09:32:12','2025-06-04 09:32:12'),(3,'management','管理层','管理层用户组',1,'2025-06-04 09:32:12','2025-06-04 09:32:12');
/*!40000 ALTER TABLE `user_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_groups`
--

DROP TABLE IF EXISTS `user_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_groups` (
  `user_id` int NOT NULL,
  `group_id` int NOT NULL,
  PRIMARY KEY (`user_id`,`group_id`),
  KEY `group_id` (`group_id`),
  CONSTRAINT `user_groups_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `user_groups_ibfk_2` FOREIGN KEY (`group_id`) REFERENCES `user_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户组关联表 - 多对多关系，用户与用户组的关联';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_groups`
--

LOCK TABLES `user_groups` WRITE;
/*!40000 ALTER TABLE `user_groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_roles` (
  `user_id` int NOT NULL,
  `role_id` int NOT NULL,
  PRIMARY KEY (`user_id`,`role_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户角色关联表 - 多对多关系，用户与角色的关联';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES (1,1),(2,2),(3,3);
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'export_excel_prod'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-09 23:51:04
