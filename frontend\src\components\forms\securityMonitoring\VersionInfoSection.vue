<template>
  <collapsible-card card-class="border-success" storage-key="version-info-section">
    <template #header>
      <i class="bi bi-tag me-2"></i>版本信息
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-success">前端: {{ frontendVer || '未填写' }}</span>
        <span class="badge bg-success">后端: {{ backendVer || '未填写' }}</span>
        <span class="badge bg-info">{{ standardOrCustom }}</span>
      </div>
    </template>
    <div class="row mb-3">
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="frontendVersion"
            v-model="frontendVer"
            placeholder="例如：V5.1.2sp2"
            @input="updateFrontendVersion"
          >
          <label for="frontendVersion">前端版本 <span class="text-danger">*</span></label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="backendVersion"
            v-model="backendVer"
            placeholder="例如：V5.1.2sp2"
            @input="updateBackendVersion"
          >
          <label for="backendVersion">后端版本 <span class="text-danger">*</span></label>
        </div>
      </div>
    </div>

    <div class="row mb-3">
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <select
            class="form-select"
            id="standardOrCustom"
            v-model="standardOrCustom"
            @change="updateStandardOrCustom"
          >
            <option value="标准版">标准版</option>
            <option value="定制版">定制版</option>
          </select>
          <label for="standardOrCustom">标准/定制</label>
        </div>
      </div>
      <div class="col-md-6">
        <!-- 预留空间，保持布局平衡 -->
      </div>
    </div>
  </collapsible-card>
</template>

<script>
/**
 * 版本信息部分组件
 * 用于填写前端和后端版本信息
 */
import CollapsibleCard from '../common/CollapsibleCard.vue'

export default {
  name: 'VersionInfoSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 前端版本
    frontendVersion: {
      type: String,
      default: ''
    },
    // 后端版本
    backendVersion: {
      type: String,
      default: ''
    },
    // 标准或定制
    standard: {
      type: String,
      default: '标准版'
    }
  },
  data() {
    return {
      frontendVer: this.frontendVersion,
      backendVer: this.backendVersion,
      standardOrCustom: this.standard
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    frontendVersion(newVal) {
      this.frontendVer = newVal
    },
    backendVersion(newVal) {
      this.backendVer = newVal
    },
    standard(newVal) {
      this.standardOrCustom = newVal
    }
  },
  methods: {
    /**
     * 更新前端版本
     * 向父组件发送更新事件
     */
    updateFrontendVersion() {
      this.$emit('update:frontendVersion', this.frontendVer)
    },

    /**
     * 更新后端版本
     * 向父组件发送更新事件
     */
    updateBackendVersion() {
      this.$emit('update:backendVersion', this.backendVer)
    },

    /**
     * 更新标准或定制
     * 向父组件发送更新事件
     */
    updateStandardOrCustom() {
      this.$emit('update:standard', this.standardOrCustom)
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-weight: bold;
}

.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.text-danger {
  font-weight: bold;
}
</style>
