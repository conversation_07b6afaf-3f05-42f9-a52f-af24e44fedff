<template>
  <div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div 
      class="toast show" 
      :class="toastClass"
      role="alert" 
      aria-live="assertive" 
      aria-atomic="true"
      v-if="visible"
    >
      <div class="toast-header">
        <i :class="'bi ' + icon + ' me-2'"></i>
        <strong class="me-auto">{{ title }}</strong>
        <button 
          type="button" 
          class="btn-close" 
          @click="hideToast"
          aria-label="Close"
        ></button>
      </div>
      <div class="toast-body">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script>
/**
 * Toast通知组件
 * 用于显示操作提示、成功或错误消息
 */
export default {
  name: 'ToastNotification',
  props: {
    // 自动隐藏的延迟时间（毫秒）
    autoHideDelay: {
      type: Number,
      default: 3000
    }
  },
  data() {
    return {
      visible: false,
      message: '',
      title: '提示',
      type: 'info',
      timer: null
    }
  },
  computed: {
    /**
     * 根据类型获取图标
     */
    icon() {
      const icons = {
        success: 'bi-check-circle-fill text-success',
        error: 'bi-exclamation-circle-fill text-danger',
        warning: 'bi-exclamation-triangle-fill text-warning',
        info: 'bi-info-circle-fill text-info'
      }
      return icons[this.type] || icons.info
    },
    
    /**
     * 根据类型获取Toast的样式类
     */
    toastClass() {
      const classes = {
        success: 'border-success',
        error: 'border-danger',
        warning: 'border-warning',
        info: 'border-info'
      }
      return classes[this.type] || classes.info
    }
  },
  methods: {
    /**
     * 显示Toast通知
     * @param {String} message - 通知消息
     * @param {String} title - 通知标题
     * @param {String} type - 通知类型（success/error/warning/info）
     */
    showToast(message, title = '提示', type = 'info') {
      // 清除之前的定时器
      if (this.timer) {
        clearTimeout(this.timer)
      }
      
      // 设置Toast内容
      this.message = message
      this.title = title
      this.type = type
      this.visible = true
      
      // 设置自动隐藏
      this.timer = setTimeout(() => {
        this.hideToast()
      }, this.autoHideDelay)
    },
    
    /**
     * 隐藏Toast通知
     */
    hideToast() {
      this.visible = false
    },
    
    /**
     * 显示成功通知
     * @param {String} message - 通知消息
     * @param {String} title - 通知标题
     */
    showSuccess(message, title = '成功') {
      this.showToast(message, title, 'success')
    },
    
    /**
     * 显示错误通知
     * @param {String} message - 通知消息
     * @param {String} title - 通知标题
     */
    showError(message, title = '错误') {
      this.showToast(message, title, 'error')
    },
    
    /**
     * 显示警告通知
     * @param {String} message - 通知消息
     * @param {String} title - 通知标题
     */
    showWarning(message, title = '警告') {
      this.showToast(message, title, 'warning')
    },
    
    /**
     * 显示信息通知
     * @param {String} message - 通知消息
     * @param {String} title - 通知标题
     */
    showInfo(message, title = '信息') {
      this.showToast(message, title, 'info')
    }
  }
}
</script>

<style scoped>
.toast-container {
  z-index: 1050;
}

.toast {
  min-width: 250px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left-width: 4px !important;
  opacity: 1;
}

.toast-header {
  border-bottom: none;
}

.toast-body {
  padding-top: 0;
}
</style>
