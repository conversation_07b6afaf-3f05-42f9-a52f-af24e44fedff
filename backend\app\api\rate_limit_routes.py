"""
限流管理API路由
提供API限流和管理功能
"""

from flask import Blueprint, jsonify, request, current_app
from app.auth.decorators import permission_required, get_current_user
from app.utils.cache_utils import RateLimitCacheManager
from functools import wraps
import traceback
from datetime import datetime

# 创建限流管理蓝图
rate_limit_bp = Blueprint('rate_limit', __name__, url_prefix='/api/rate-limit')

# 默认限流配置
DEFAULT_RATE_LIMITS = {
    'form_submit': {'limit': 10, 'window': 300},      # 表单提交：5分钟内最多10次
    'api_request': {'limit': 100, 'window': 60},      # API请求：1分钟内最多100次
    'login_attempt': {'limit': 5, 'window': 900},     # 登录尝试：15分钟内最多5次
    'excel_generate': {'limit': 20, 'window': 3600},  # Excel生成：1小时内最多20次
    'search_history': {'limit': 50, 'window': 300},   # 历史搜索：5分钟内最多50次
}


def rate_limit(action: str, limit: int = None, window: int = None, identifier_func=None):
    """
    限流装饰器
    
    Args:
        action: 操作类型
        limit: 限制次数
        window: 时间窗口（秒）
        identifier_func: 标识符生成函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 获取限流配置
                config = DEFAULT_RATE_LIMITS.get(action, {})
                actual_limit = limit or config.get('limit', 100)
                actual_window = window or config.get('window', 60)
                
                # 生成标识符
                if identifier_func:
                    identifier = identifier_func()
                else:
                    # 默认使用用户ID或IP地址
                    current_user = get_current_user()
                    if current_user:
                        identifier = f"user_{current_user.id}"
                    else:
                        identifier = f"ip_{request.remote_addr}"
                
                # 检查限流
                rate_limit_result = RateLimitCacheManager.check_rate_limit(
                    identifier, action, actual_limit, actual_window
                )
                
                if not rate_limit_result['allowed']:
                    current_app.logger.warning(f"触发限流: identifier={identifier}, action={action}")
                    return jsonify({
                        'status': 'error',
                        'message': '请求过于频繁，请稍后再试',
                        'data': {
                            'rate_limit': True,
                            'current_count': rate_limit_result['current_count'],
                            'limit': rate_limit_result['limit'],
                            'reset_time': rate_limit_result['reset_time']
                        }
                    }), 429
                
                # 执行原函数
                return func(*args, **kwargs)
                
            except Exception as e:
                current_app.logger.error(f"限流检查失败: {str(e)}")
                # 限流检查失败时允许通过
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


@rate_limit_bp.route('/check', methods=['POST'])
@permission_required('system.rate_limit.view')
def check_rate_limit():
    """
    检查限流状态
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400
        
        action = data.get('action')
        identifier = data.get('identifier', f"user_{current_user.id}")
        
        if not action:
            return jsonify({
                'status': 'error',
                'message': '操作类型不能为空'
            }), 400
        
        # 获取限流配置
        config = DEFAULT_RATE_LIMITS.get(action, {})
        if not config:
            return jsonify({
                'status': 'error',
                'message': f'未知的操作类型: {action}'
            }), 400
        
        # 检查限流状态（不增加计数）
        from app.utils.cache_utils import cache
        key = RateLimitCacheManager.get_rate_limit_key(identifier, action)
        current_count = cache.get(key) or 0
        
        result = {
            'identifier': identifier,
            'action': action,
            'current_count': current_count,
            'limit': config['limit'],
            'window': config['window'],
            'remaining': max(0, config['limit'] - current_count),
            'is_limited': current_count >= config['limit']
        }
        
        return jsonify({
            'status': 'success',
            'message': '获取限流状态成功',
            'data': result
        })
        
    except Exception as e:
        current_app.logger.error(f"检查限流状态失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'检查限流状态失败: {str(e)}'
        }), 500


@rate_limit_bp.route('/reset', methods=['POST'])
@permission_required('system.rate_limit.manage')
def reset_rate_limit():
    """
    重置限流计数
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400
        
        action = data.get('action')
        identifier = data.get('identifier')
        
        if not action or not identifier:
            return jsonify({
                'status': 'error',
                'message': '操作类型和标识符不能为空'
            }), 400
        
        # 重置限流计数
        success = RateLimitCacheManager.reset_rate_limit(identifier, action)
        
        if success:
            current_app.logger.info(f"重置限流计数: identifier={identifier}, action={action}, operator={current_user.username}")
            return jsonify({
                'status': 'success',
                'message': '限流计数重置成功'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '限流计数重置失败'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"重置限流计数失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'重置限流计数失败: {str(e)}'
        }), 500


@rate_limit_bp.route('/config', methods=['GET'])
@permission_required('system.rate_limit.view')
def get_rate_limit_config():
    """
    获取限流配置
    """
    try:
        return jsonify({
            'status': 'success',
            'message': '获取限流配置成功',
            'data': {
                'default_configs': DEFAULT_RATE_LIMITS,
                'description': {
                    'form_submit': '表单提交限流',
                    'api_request': 'API请求限流',
                    'login_attempt': '登录尝试限流',
                    'excel_generate': 'Excel生成限流',
                    'search_history': '历史搜索限流'
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取限流配置失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取限流配置失败: {str(e)}'
        }), 500


@rate_limit_bp.route('/stats', methods=['GET'])
@permission_required('system.rate_limit.view')
def get_rate_limit_stats():
    """
    获取限流统计信息
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        # 获取限流相关的缓存键
        from app.utils.cache_utils import cache
        
        try:
            rate_limit_keys = cache.cache._write_client.keys('rate_limit:*')
            
            # 统计信息
            stats = {
                'total_rate_limits': len(rate_limit_keys),
                'active_limits': 0,
                'cache_status': 'connected'
            }
            
            # 分析限流键
            action_stats = {}
            for key in rate_limit_keys:
                try:
                    key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                    parts = key_str.split(':')
                    if len(parts) >= 3:
                        action = parts[2]
                        if action not in action_stats:
                            action_stats[action] = 0
                        action_stats[action] += 1
                        
                        # 检查是否达到限制
                        count = cache.get(key) or 0
                        config = DEFAULT_RATE_LIMITS.get(action, {})
                        if count >= config.get('limit', 100):
                            stats['active_limits'] += 1
                except:
                    continue
            
            stats['action_stats'] = action_stats
            
            return jsonify({
                'status': 'success',
                'message': '获取限流统计成功',
                'data': stats
            })
            
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'获取限流统计失败: {str(e)}'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"获取限流统计失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取限流统计失败: {str(e)}'
        }), 500


@rate_limit_bp.route('/health', methods=['GET'])
def rate_limit_health():
    """
    限流系统健康检查（无需权限）
    """
    try:
        # 测试缓存连接
        from app.utils.cache_utils import cache
        cache.cache._write_client.ping()
        
        return jsonify({
            'status': 'success',
            'data': {
                'rate_limit_system': 'Redis',
                'cache_status': 'connected',
                'features': [
                    'rate_limit_check',
                    'rate_limit_reset',
                    'rate_limit_config',
                    'rate_limit_stats'
                ],
                'supported_actions': list(DEFAULT_RATE_LIMITS.keys())
            }
        })
    except Exception as e:
        current_app.logger.error(f"限流系统健康检查失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'限流系统异常: {str(e)}'
        }), 500
