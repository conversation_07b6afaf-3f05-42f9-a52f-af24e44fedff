/**
 * 组件管理服务
 * 提供组件分类和组件的CRUD操作
 */

const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')
  : (process.env.VUE_APP_API_URL || 'http://localhost:5000')

/**
 * 获取组件分类列表
 * @param {String} formType - 表单类型（可选）
 * @returns {Promise} - 分类列表
 */
export const getComponentCategories = async (formType = '') => {
  try {
    const url = formType 
      ? `${API_BASE_URL}/excel/component-categories?form_type=${encodeURIComponent(formType)}`
      : `${API_BASE_URL}/excel/component-categories`
    
    const response = await fetch(url)
    const data = await response.json()
    
    if (data.status === 'success') {
      return data.data
    } else {
      throw new Error(data.message || '获取组件分类失败')
    }
  } catch (error) {
    console.error('获取组件分类失败:', error)
    throw error
  }
}

/**
 * 创建组件分类
 * @param {Object} categoryData - 分类数据
 * @returns {Promise} - 创建结果
 */
export const createComponentCategory = async (categoryData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/excel/component-categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(categoryData)
    })
    
    const data = await response.json()
    
    if (data.status === 'success') {
      return data.data
    } else {
      throw new Error(data.message || '创建组件分类失败')
    }
  } catch (error) {
    console.error('创建组件分类失败:', error)
    throw error
  }
}

/**
 * 更新组件分类
 * @param {Number} categoryId - 分类ID
 * @param {Object} categoryData - 分类数据
 * @returns {Promise} - 更新结果
 */
export const updateComponentCategory = async (categoryId, categoryData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/excel/component-categories/${categoryId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(categoryData)
    })
    
    const data = await response.json()
    
    if (data.status === 'success') {
      return data.data
    } else {
      throw new Error(data.message || '更新组件分类失败')
    }
  } catch (error) {
    console.error('更新组件分类失败:', error)
    throw error
  }
}

/**
 * 获取组件列表
 * @param {String} formType - 表单类型（可选）
 * @param {String} categoryKey - 分类键名（可选）
 * @returns {Promise} - 组件列表
 */
export const getComponents = async (formType = '', categoryKey = '') => {
  try {
    const params = new URLSearchParams()
    if (formType) params.append('form_type', formType)
    if (categoryKey) params.append('category_key', categoryKey)
    
    const url = `${API_BASE_URL}/excel/components${params.toString() ? '?' + params.toString() : ''}`
    
    const response = await fetch(url)
    const data = await response.json()
    
    if (data.status === 'success') {
      return data.data
    } else {
      throw new Error(data.message || '获取组件列表失败')
    }
  } catch (error) {
    console.error('获取组件列表失败:', error)
    throw error
  }
}

/**
 * 按分类获取组件列表
 * @param {String} formType - 表单类型
 * @returns {Promise} - 按分类组织的组件数据
 */
export const getComponentsByCategory = async (formType) => {
  try {
    const response = await fetch(`${API_BASE_URL}/excel/components/by-category?form_type=${encodeURIComponent(formType)}`)
    const data = await response.json()
    
    if (data.status === 'success') {
      return data.data
    } else {
      throw new Error(data.message || '获取组件数据失败')
    }
  } catch (error) {
    console.error('获取组件数据失败:', error)
    throw error
  }
}

/**
 * 创建组件
 * @param {Object} componentData - 组件数据
 * @returns {Promise} - 创建结果
 */
export const createComponent = async (componentData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/excel/components`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(componentData)
    })
    
    const data = await response.json()
    
    if (data.status === 'success') {
      return data.data
    } else {
      throw new Error(data.message || '创建组件失败')
    }
  } catch (error) {
    console.error('创建组件失败:', error)
    throw error
  }
}

/**
 * 更新组件
 * @param {Number} componentId - 组件ID
 * @param {Object} componentData - 组件数据
 * @returns {Promise} - 更新结果
 */
export const updateComponent = async (componentId, componentData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/excel/components/${componentId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(componentData)
    })
    
    const data = await response.json()
    
    if (data.status === 'success') {
      return data.data
    } else {
      throw new Error(data.message || '更新组件失败')
    }
  } catch (error) {
    console.error('更新组件失败:', error)
    throw error
  }
}

/**
 * 删除组件
 * @param {Number} componentId - 组件ID
 * @returns {Promise} - 删除结果
 */
export const deleteComponent = async (componentId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/excel/components/${componentId}`, {
      method: 'DELETE'
    })
    
    const data = await response.json()
    
    if (data.status === 'success') {
      return true
    } else {
      throw new Error(data.message || '删除组件失败')
    }
  } catch (error) {
    console.error('删除组件失败:', error)
    throw error
  }
}

/**
 * 将数据库组件数据转换为前端兼容格式
 * 保持与现有前端配置的兼容性
 * @param {Array} dbComponents - 数据库组件数据
 * @returns {Array} - 前端兼容格式的组件数据
 */
export const convertDbComponentsToFrontendFormat = (dbComponents) => {
  return dbComponents.map(comp => ({
    id: comp.name, // 使用name作为id，保持兼容性
    name: comp.name,
    alias: comp.alias || comp.display_name,
    defaultPort: comp.default_port,
    description: comp.description,
    category: comp.category_key,
    protocol: comp.protocol || 'http',
    version: comp.version || ''
  }))
}

/**
 * 将前端组件数据转换为数据库格式
 * @param {Object} frontendComponent - 前端组件数据
 * @param {String} formType - 表单类型
 * @returns {Object} - 数据库格式的组件数据
 */
export const convertFrontendComponentToDbFormat = (frontendComponent, formType) => {
  return {
    name: frontendComponent.name || frontendComponent.id,
    display_name: frontendComponent.alias || frontendComponent.name || frontendComponent.id,
    category_key: frontendComponent.category,
    form_type: formType,
    default_port: frontendComponent.defaultPort || '无',
    description: frontendComponent.description || '',
    protocol: frontendComponent.protocol || 'http',
    version: frontendComponent.version || '',
    alias: frontendComponent.alias || ''
  }
}

/**
 * 获取组件的默认端口
 * @param {String} componentName - 组件名称
 * @param {String} formType - 表单类型
 * @returns {Promise<String>} - 默认端口
 */
export const getComponentDefaultPort = async (componentName, formType) => {
  try {
    const components = await getComponents(formType)
    const component = components.find(comp => comp.name === componentName)
    return component ? component.default_port : '无'
  } catch (error) {
    console.error('获取组件默认端口失败:', error)
    return '无'
  }
}

/**
 * 获取组件的协议
 * @param {String} componentName - 组件名称
 * @param {String} formType - 表单类型
 * @returns {Promise<String>} - 协议
 */
export const getComponentProtocol = async (componentName, formType) => {
  try {
    const components = await getComponents(formType)
    const component = components.find(comp => comp.name === componentName)
    return component ? (component.protocol || 'http') : 'http'
  } catch (error) {
    console.error('获取组件协议失败:', error)
    return 'http'
  }
}

/**
 * 删除组件分类
 * @param {Number} categoryId - 分类ID
 * @returns {Promise} - 删除结果
 */
export const deleteComponentCategory = async (categoryId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/excel/component-categories/${categoryId}`, {
      method: 'DELETE'
    })
    const data = await response.json()
    if (data.status === 'success') {
      return true
    } else {
      throw new Error(data.message || '删除组件分类失败')
    }
  } catch (error) {
    console.error('删除组件分类失败:', error)
    throw error
  }
}

/**
 * 切换组件启用状态
 * @param {Number} componentId - 组件ID
 * @param {Boolean} isActive - 是否启用
 * @returns {Promise} - 更新结果
 */
export const toggleComponentActiveStatus = async (componentId, isActive) => {
  try {
    const response = await fetch(`${API_BASE_URL}/excel/components/${componentId}/active`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ is_active: isActive })
    })
    
    const data = await response.json()
    
    if (data.status === 'success') {
      return data.data
    } else {
      throw new Error(data.message || '切换组件启用状态失败')
    }
  } catch (error) {
    console.error('切换组件启用状态失败:', error)
    throw error
  }
}
