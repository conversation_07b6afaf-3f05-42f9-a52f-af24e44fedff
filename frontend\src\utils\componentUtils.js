/**
 * 组件工具函数
 * 提供处理组件相关的工具函数
 */

/**
 * 获取组件的默认端口
 * @param {String} componentName - 组件名称
 * @param {String} formType - 表单类型
 * @returns {String} - 组件的默认端口
 */
export const getComponentDefaultPort = (componentName, formType) => {
  // 首先尝试从数据库获取（如果可用）
  if (window.componentCache && window.componentCache[formType]) {
    const dbComponents = window.componentCache[formType]
    for (const categoryKey in dbComponents) {
      const categoryData = dbComponents[categoryKey]
      if (categoryData && categoryData.components) {
        const component = categoryData.components.find(comp =>
          comp.name === componentName || comp.id === componentName
        )
        if (component && component.default_port) {
          return component.default_port
        }
      }
    }
  }

  // 如果数据库中没有找到，尝试使用硬编码的默认端口作为回退
  const defaultPorts = {
    'analyzer-dev': '15003',
    'app-sender': '无',
    'cleaner': '15000',
    'init': '8181',
    'receiver': '无',
    'security-event': '无',
    'threat': '无',
    'threat-index': '无',
    'transfer': '无',
    'web-service': '9990',
    'web-service-nginx': '9991',
    'crash': '9000',
    'nginx': '6279',
    'postgres': '5432',
    'redis': '6379',
    'minio': '9000',
    'zookeeper': '2181',
    'kafka': '9092',
    'hbase': '16010',
    'elasticsearchMaster': '9200',
    'elasticsearchClient': '9201',
    'kibana': '5601',
    'monitor': '9090'
  }

  const fallbackPort = defaultPorts[componentName] || '无'

  if (fallbackPort !== '无') {
    console.log(`使用硬编码端口配置: ${componentName} -> ${fallbackPort}`)
  } else {
    console.warn(`未找到组件 ${componentName} 的端口配置`)
  }

  return fallbackPort
}

/**
 * 处理服务器组件端口信息
 * 确保每个组件都有端口信息，如果没有则使用默认端口
 * @param {Object} formData - 表单数据
 * @returns {Object} - 处理后的表单数据
 */
export const processComponentPorts = (formData) => {
  // 创建一个副本，避免修改原始对象
  const processedData = JSON.parse(JSON.stringify(formData))

  // 修复键名中的空格问题
  if (processedData[' 服务器信息'] && !processedData['服务器信息']) {
    processedData['服务器信息'] = processedData[' 服务器信息']
    delete processedData[' 服务器信息']
  }

  if (processedData[' 记录日期'] && !processedData['记录日期']) {
    processedData['记录日期'] = processedData[' 记录日期']
    delete processedData[' 记录日期']
  }

  // 如果没有服务器信息，直接返回
  if (!processedData.服务器信息 || !Array.isArray(processedData.服务器信息)) {
    return processedData
  }

  // 获取表单类型
  const formType = processedData.文档后缀 || '安全测评'

  // 处理每个服务器的组件端口信息
  for (const server of processedData.服务器信息) {
    // 修复键名中的空格问题
    if (server['组件 折叠'] !== undefined && server['组件折叠'] === undefined) {
      server['组件折叠'] = server['组件 折叠']
      delete server['组件 折叠']
    }

    if (server['CPU自定 义'] !== undefined && server['CPU自定义'] === undefined) {
      server['CPU自定义'] = server['CPU自定 义']
      delete server['CPU自定 义']
    }

    // 如果没有部署应用或组件端口，跳过
    if (!server.部署应用 || !Array.isArray(server.部署应用)) {
      continue
    }

    // 确保组件端口对象存在
    if (!server.组件端口) {
      server.组件端口 = {}
    }

    // 为每个部署的应用设置端口
    for (const appName of server.部署应用) {
      // 如果组件端口中没有该应用的端口信息，或者端口为空，使用默认端口
      if (!server.组件端口[appName] || server.组件端口[appName] === '') {
        const defaultPort = getComponentDefaultPort(appName, formType)
        if (defaultPort && defaultPort !== '无') {
          server.组件端口[appName] = defaultPort
        }
      }
    }

    // 确保启用端口修改属性存在
    if (typeof server.启用端口修改 === 'undefined') {
      server.启用端口修改 = false
    }
  }

  return processedData
}
