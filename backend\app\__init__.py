import logging
import os
import sys
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from flask_bcrypt import Bcrypt
from config import Config, config
from app.utils.cache_utils import cache

# 配置日志
def setup_logging(app):
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    app.logger.addHandler(handler)
    app.logger.setLevel(logging.DEBUG)
    app.logger.debug('日志配置已添加')

db = SQLAlchemy()
jwt = JWTManager()
bcrypt = Bcrypt()

def create_app(config_class=None):
    app = Flask(__name__)

    # 加载配置
    if config_class:
        app.config.from_object(config_class)
    else:
        # 根据环境变量选择配置
        env = os.environ.get('FLASK_ENV', 'development')
        config_class = config.get(env, config['default'])
        app.config.from_object(config_class)

    # 启用CORS
    CORS(app, resources={r"/*": {"origins": "*"}})

    # 确保Excel目录及其子目录存在
    os.makedirs(app.config['EXCEL_FOLDER'], exist_ok=True)
    # 确保模板目录存在
    os.makedirs(app.config['EXCEL_TEMPLATES_FOLDER'], exist_ok=True)
    # 确保生成文件目录存在
    os.makedirs(app.config['EXCEL_GENERATED_FOLDER'], exist_ok=True)

    # 配置日志
    setup_logging(app)

    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    bcrypt.init_app(app)
    cache.init_app(app)

    # 注册蓝图
    from app.main.routes import bp as main_bp
    app.register_blueprint(main_bp)

    from app.excel import bp as excel_bp
    app.register_blueprint(excel_bp, url_prefix='/excel')

    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.rbac import bp as rbac_bp
    app.register_blueprint(rbac_bp, url_prefix='/rbac')

    # 注册缓存管理蓝图
    from app.api.cache_routes import cache_bp
    app.register_blueprint(cache_bp)

    # 注册表单快照蓝图
    from app.api.form_snapshot_routes import form_snapshot_bp
    app.register_blueprint(form_snapshot_bp)

    # 注册历史记录缓存蓝图
    from app.api.history_cache_routes import history_cache_bp
    app.register_blueprint(history_cache_bp)

    # 注册重复检测蓝图
    from app.api.duplicate_check_routes import duplicate_check_bp
    app.register_blueprint(duplicate_check_bp)

    # 注册限流管理蓝图
    from app.api.rate_limit_routes import rate_limit_bp
    app.register_blueprint(rate_limit_bp)

    # 注册性能监控蓝图
    from app.api.performance_routes import performance_bp
    app.register_blueprint(performance_bp)

    # 注册API文档蓝图
    from app.api.docs_routes import docs_bp
    app.register_blueprint(docs_bp, url_prefix='/api/docs')

    # 注册分享链接蓝图
    from app.api.share_routes import share_bp
    app.register_blueprint(share_bp)

    # 初始化性能监控中间件
    from app.utils.performance_monitor import PerformanceMiddleware
    PerformanceMiddleware(app)

    # 确保模板文件存在
    with app.app_context():
        # 应用加固模板现在由utils.py统一处理，不需要单独创建
        pass

    return app
