<template>
  <collapsible-card storage-key="server-info-section" card-class="border-warning">
    <template #header>
      <i class="bi bi-server me-2"></i>服务器信息
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-warning text-dark">IP: {{ serverIpAddress || '未填写' }}</span>
        <span class="badge bg-warning text-dark">系统: {{ serverOsValue || '未填写' }}</span>
        <span class="badge bg-warning text-dark">配置: {{ serverConfigValue || '未填写' }}</span>
      </div>
    </template>
    <div class="row mb-3">
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="serverIpAddress"
            v-model="serverIpAddress"
            placeholder="服务器IP地址"
            @input="updateServerIp"
          >
          <label for="serverIpAddress">服务器IP地址</label>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="serverOs"
            v-model="serverOsValue"
            placeholder="服务器操作系统"
            @input="updateServerOs"
          >
          <label for="serverOs">服务器操作系统</label>
          <small class="form-text text-muted">例如：Kylin Linux Advanced Server</small>
        </div>
      </div>
    </div>

    <div class="row mb-3">
      <div class="col-md-12">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="serverConfig"
            v-model="serverConfigValue"
            placeholder="服务器配置"
            @input="updateServerConfig"
          >
          <label for="serverConfig">服务器配置</label>
          <small class="form-text text-muted">例如：8核 16G /data 500G</small>
        </div>
      </div>
    </div>

    <!-- 组件配置部分 -->
    <div v-if="showComponents">
      <h5 class="mb-3">部署组件</h5>
      <div class="row mb-3" v-for="(component, index) in availableComponents" :key="component.id">
        <div class="col-md-4">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              :id="'component-' + component.id"
              v-model="selectedComponents[component.id]"
              @change="updateComponents"
            >
            <label class="form-check-label" :for="'component-' + component.id">
              {{ component.name }}
            </label>
          </div>
        </div>
        <div class="col-md-8" v-if="selectedComponents[component.id]">
          <div class="input-group">
            <span class="input-group-text">端口</span>
            <input
              type="text"
              class="form-control"
              :id="'port-' + component.id"
              v-model="localComponentPorts[component.id]"
              :placeholder="'默认: ' + component.defaultPort"
              @input="updateComponents"
            >
            <span class="input-group-text">{{ component.description }}</span>
          </div>
        </div>
      </div>
    </div>
  </collapsible-card>
</template>

<script>
import CollapsibleCard from './CollapsibleCard.vue'

export default {
  name: 'CommonServerInfo',
  components: {
    CollapsibleCard
  },
  props: {
    // 服务器IP
    serverIp: {
      type: String,
      default: ''
    },
    // 服务器配置
    serverConfig: {
      type: String,
      default: ''
    },
    // 服务器操作系统
    serverOs: {
      type: String,
      default: ''
    },
    // 表单类型
    formType: {
      type: String,
      default: '应用加固'
    },
    // 是否显示组件配置
    showComponents: {
      type: Boolean,
      default: true
    },
    // 已选择的组件
    deployedComponents: {
      type: Object,
      default: () => ({})
    },
    // 组件端口
    componentPorts: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      serverIpAddress: this.serverIp,
      serverConfigValue: this.serverConfig,
      serverOsValue: this.serverOs,
      selectedComponents: { ...this.deployedComponents },
      localComponentPorts: { ...this.componentPorts }
    }
  },
  computed: {
    /**
     * 获取当前表单类型可用的组件
     * 现在从数据库获取，如果没有则返回空数组
     */
    availableComponents() {
      // 这个组件已经不再使用，返回空数组
      console.warn('CommonServerInfo.vue 已废弃，请使用新的组件选择器')
      return []
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    serverIp(newVal) {
      this.serverIpAddress = newVal
    },
    serverConfig(newVal) {
      this.serverConfigValue = newVal
    },
    serverOs(newVal) {
      this.serverOsValue = newVal
    },
    deployedComponents: {
      handler(newVal) {
        this.selectedComponents = { ...newVal }
      },
      deep: true
    },
    componentPorts: {
      handler(newVal) {
        this.localComponentPorts = { ...newVal }
      },
      deep: true
    }
  },
  methods: {
    // 更新方法
    updateServerIp() {
      this.$emit('update:server-ip', this.serverIpAddress)
    },
    updateServerConfig() {
      this.$emit('update:server-config', this.serverConfigValue)
    },
    updateServerOs() {
      this.$emit('update:server-os', this.serverOsValue)
    },
    /**
     * 更新组件配置
     */
    updateComponents() {
      this.$emit('update:deployed-components', this.selectedComponents)
      this.$emit('update:component-ports', this.localComponentPorts)
    }
  }
}
</script>

<style scoped>
.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.form-check-input:checked {
  background-color: #ffc107;
  border-color: #ffc107;
}
</style>
