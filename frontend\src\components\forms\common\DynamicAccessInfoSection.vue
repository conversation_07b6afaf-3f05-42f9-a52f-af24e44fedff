<template>
  <collapsible-card
    storage-key="access-info-section"
    card-class="access-info-section"
  >
    <template #header>
      <i class="bi bi-key me-2"></i>访问信息
    </template>
    
    <!-- 访问信息分组显示 -->
    <div class="access-info-groups">
      <div v-for="group in accessGroups" :key="group.name" class="access-group-card card mb-3 border-light">
        <div class="card-header py-2" :style="{ backgroundColor: group.color + '15', borderBottom: '2px solid ' + group.color }">
          <div class="group-header-content">
            <div class="group-title">
              <i :class="group.icon" class="me-2"></i>
              {{ group.title }}
            </div>
            <!-- 显示该分组的主要URL地址 -->
            <div v-if="group.primaryUrl && localData[group.primaryUrl]" class="group-url">
              <a :href="formatGroupUrl(localData[group.primaryUrl])" target="_blank" class="group-url-link">
                <i class="bi bi-box-arrow-up-right me-1"></i>
                {{ localData[group.primaryUrl] }}
              </a>
              <button class="group-url-copy" @click="copyToClipboard(localData[group.primaryUrl])" title="复制地址">
                <i class="bi bi-clipboard"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="card-body py-2">


          <div class="row g-3">
            <!-- 处理认证组合 -->
            <div v-for="authGroup in getAuthGroupsForFields(group.fields)" :key="authGroup.name" :class="getAuthGroupColumnClass(authGroup, group.fields.length)">
              <div class="access-item auth-item">
                <div class="auth-header">
                  <div class="auth-icon-wrapper">
                    <i class="bi bi-shield-lock auth-icon"></i>
                  </div>
                  <div class="auth-label">{{ authGroup.title }}</div>
                </div>
                <div class="auth-content">
                  <!-- 编辑输入框 -->
                  <div class="auth-field" v-if="authGroup.accountField">
                    <div class="auth-field-label">
                      <i class="bi bi-person me-1"></i>账号
                    </div>
                    <div class="auth-field-value">
                      <input
                        v-model="localData[authGroup.accountField]"
                        type="text"
                        class="form-control form-control-sm"
                        :placeholder="fieldConfig[authGroup.accountField]?.placeholder || '输入账号'"
                      />
                    </div>
                  </div>
                  <div class="auth-field" v-if="authGroup.passwordField">
                    <div class="auth-field-label">
                      <i class="bi bi-key me-1"></i>密码
                    </div>
                    <div class="auth-field-value">
                      <div class="input-group input-group-sm">
                        <input
                          v-model="localData[authGroup.passwordField]"
                          :type="passwordVisibility[authGroup.passwordField] ? 'text' : 'password'"
                          class="form-control"
                          :placeholder="fieldConfig[authGroup.passwordField]?.placeholder || '输入密码'"
                        />
                        <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility(authGroup.passwordField)" :title="passwordVisibility[authGroup.passwordField] ? '隐藏密码' : '显示密码'">
                          <i :class="passwordVisibility[authGroup.passwordField] ? 'bi-eye-slash' : 'bi-eye'"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 处理非认证字段 -->
            <div v-for="fieldKey in getNonAuthFieldsForFields(group.fields)" :key="fieldKey"
                 :class="fieldConfig[fieldKey] ? getItemColumnClass(fieldConfig[fieldKey], group.fields.length) : 'col-12'">
              <!-- 认证类型字段（单独的认证字段，不成对） -->
              <div v-if="isAuthField(fieldKey, fieldConfig[fieldKey])" class="access-item auth-item">
                <div class="auth-header">
                  <div class="auth-icon-wrapper">
                    <i class="bi bi-shield-lock auth-icon"></i>
                  </div>
                  <div class="auth-label">
                    {{ fieldConfig[fieldKey].field }}
                  </div>
                </div>
                <div class="auth-content">
                  <div class="auth-field">
                    <div v-if="fieldConfig[fieldKey].type === 'password'" class="input-group input-group-sm">
                      <input
                        v-model="localData[fieldKey]"
                        :type="passwordVisibility[fieldKey] ? 'text' : 'password'"
                        class="form-control"
                        :placeholder="fieldConfig[fieldKey].placeholder"
                        :required="fieldConfig[fieldKey].required"
                      />
                      <button
                        v-if="fieldConfig[fieldKey].showToggle"
                        class="btn btn-outline-secondary"
                        type="button"
                        @click="togglePasswordVisibility(fieldKey)"
                      >
                        <i :class="passwordVisibility[fieldKey] ? 'bi-eye-slash' : 'bi-eye'"></i>
                      </button>
                    </div>
                    <input
                      v-else
                      v-model="localData[fieldKey]"
                      type="text"
                      class="form-control form-control-sm"
                      :placeholder="fieldConfig[fieldKey].placeholder"
                      :required="fieldConfig[fieldKey].required"
                    />
                  </div>
                </div>
              </div>
              
              <!-- URL类型字段 -->
              <div v-else-if="isUrlField(fieldKey, fieldConfig[fieldKey])" class="access-item url-item">
                <div class="url-header">
                  <div class="url-icon-wrapper">
                    <i class="bi bi-link-45deg url-icon"></i>
                  </div>
                  <div class="url-label">
                    {{ fieldConfig[fieldKey].field }}
                  </div>
                </div>
                <div class="url-content">
                  <div class="url-field">
                    <input
                      v-model="localData[fieldKey]"
                      type="text"
                      class="form-control form-control-sm"
                      :placeholder="fieldConfig[fieldKey].placeholder"
                      :required="fieldConfig[fieldKey].required"
                    />
                    <div v-if="localData[fieldKey]" class="url-preview mt-2">
                      <a :href="formatGroupUrl(localData[fieldKey])" target="_blank" class="text-primary text-decoration-none">
                        <i class="bi bi-box-arrow-up-right me-1"></i>
                        {{ localData[fieldKey] }}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 文本类型字段 -->
              <div v-else class="access-item text-item">
                <div class="text-header">
                  <div class="text-icon-wrapper">
                    <i class="bi bi-info-circle text-icon"></i>
                  </div>
                  <div class="text-label">
                    {{ fieldConfig[fieldKey].field }}
                  </div>
                </div>
                <div class="text-content">
                  <div class="text-value">
                    <textarea
                      v-if="fieldConfig[fieldKey].type === 'textarea'"
                      v-model="localData[fieldKey]"
                      class="form-control form-control-sm"
                      :rows="fieldConfig[fieldKey].rows || 3"
                      :placeholder="fieldConfig[fieldKey].placeholder"
                      :required="fieldConfig[fieldKey].required"
                    ></textarea>
                    <select
                      v-else-if="fieldConfig[fieldKey].type === 'select'"
                      v-model="localData[fieldKey]"
                      class="form-select form-select-sm"
                      :required="fieldConfig[fieldKey].required"
                    >
                      <option value="">{{ fieldConfig[fieldKey].placeholder || '请选择' }}</option>
                      <option v-for="option in fieldConfig[fieldKey].options" :key="option.value" :value="option.value">
                        {{ option.label }}
                      </option>
                    </select>
                    <input
                      v-else
                      v-model="localData[fieldKey]"
                      :type="fieldConfig[fieldKey].type || 'text'"
                      class="form-control form-control-sm"
                      :placeholder="fieldConfig[fieldKey].placeholder"
                      :required="fieldConfig[fieldKey].required"
                    />
                  </div>
                </div>
              </div>
              
            </div>
          </div>
        </div>
      </div>
    </div>
  </collapsible-card>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import CollapsibleCard from './CollapsibleCard.vue'

export default {
  name: 'DynamicAccessInfoSection',
  components: {
    CollapsibleCard
  },
  props: {
    modelValue: { 
      type: Object, 
      required: true 
    },
    fieldConfig: { 
      type: Object, 
      required: true 
    },
    formType: {
      type: String,
      required: true
    },
    serverList: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const localData = ref({})
    const passwordVisibility = ref({})

    // 访问信息分组配置
    const accessGroups = computed(() => {
      const groups = []
      
      if (props.formType === '安全测评') {
        groups.push({
          name: 'web-access',
          title: 'Web访问',
          icon: 'bi-globe',
          color: '#007bff',
          primaryUrl: 'adminPageIP',
          fields: ['adminPageIP', 'userPageIP', 'upgradePageIP']
        })
        groups.push({
          name: 'admin-access',
          title: '管理访问',
          icon: 'bi-person-gear',
          color: '#28a745',
          fields: ['superAdminAccount', 'superAdminPassword', 'adminAccount', 'adminPassword', 'userAccount', 'userPassword']
        })
        groups.push({
          name: 'upgrade-access',
          title: '升级访问',
          icon: 'bi-arrow-up-circle',
          color: '#ffc107',
          fields: ['upgradeAccount', 'upgradePassword']
        })
        groups.push({
          name: 'service-access',
          title: '服务访问',
          icon: 'bi-server',
          color: '#6c757d',
          fields: ['externalServicePort']
        })
      } else if (props.formType === '安全监测') {
        groups.push({
          name: 'business-access',
          title: '业务访问',
          icon: 'bi-building',
          color: '#007bff',
          primaryUrl: 'businessPageAddress',
          fields: ['businessPageAddress']
        })
        groups.push({
          name: 'admin-access',
          title: '管理访问',
          icon: 'bi-person-gear',
          color: '#28a745',
          fields: ['superAdminAccount', 'superAdminPassword', 'customerAdminAccount', 'customerAdminPassword']
        })
        groups.push({
          name: 'init-access',
          title: 'init访问',
          icon: 'bi-gear',
          color: '#ffc107',
          primaryUrl: 'initAddress',
          fields: ['initAddress', 'initUser', 'initPassword']
        })
        groups.push({
          name: 'kibana-access',
          title: 'kibana访问',
          icon: 'bi-bar-chart',
          color: '#17a2b8',
          primaryUrl: 'kibanaAddress',
          fields: ['kibanaAddress', 'kibanaAccount', 'kibanaPassword']
        })
      } else if (props.formType === '应用加固') {
        groups.push({
          name: 'platform-access',
          title: '平台访问',
          icon: 'bi-shield-check',
          color: '#007bff',
          primaryUrl: 'platformAccessUrl',
          fields: ['platformAccessUrl', 'platformUserAccount', 'platformUserPassword']
        })
        groups.push({
          name: 'admin-access',
          title: '管理访问',
          icon: 'bi-person-gear',
          color: '#28a745',
          fields: ['adminAccount', 'adminPassword', 'superAdminAccount', 'superAdminPassword']
        })
        groups.push({
          name: 'upgrade-access',
          title: '升级访问',
          icon: 'bi-arrow-up-circle',
          color: '#ffc107',
          fields: ['upgradeInfo', 'upgradeAccount', 'upgradePassword']
        })
      }
      
      return groups.filter(group =>
        group.fields.some(field => props.fieldConfig[field])
      )
    })

    // 判断是否为认证字段
    const isAuthField = (fieldKey, fieldConfig) => {
      return fieldConfig.type === 'password' || 
             fieldKey.includes('账号') || 
             fieldKey.includes('密码') ||
             fieldKey.includes('Account') ||
             fieldKey.includes('Password') ||
             fieldKey.includes('User') ||
             fieldKey.includes('Auth')
    }

    // 判断是否为URL字段
    const isUrlField = (fieldKey, fieldConfig) => {
      return fieldKey.includes('地址') || 
             fieldKey.includes('URL') || 
             fieldKey.includes('页面') ||
             fieldKey.includes('Address') ||
             fieldKey.includes('Page') ||
             fieldKey.includes('IP')
    }

    // 获取项目列类
    const getItemColumnClass = (fieldConfig, totalFields) => {
      if (totalFields <= 2) return 'col-md-6'
      if (totalFields <= 3) return 'col-md-4'
      return 'col-md-3'
    }

    // 获取认证组合
    const getAuthGroupsForFields = (fields) => {
      const authGroups = []

      // 定义认证组合规则
      const authPairs = [
        { account: 'superAdminAccount', password: 'superAdminPassword', title: '超级管理员' },
        { account: 'customerAdminAccount', password: 'customerAdminPassword', title: '客户管理员' },
        { account: 'adminAccount', password: 'adminPassword', title: '管理员' },
        { account: 'userAccount', password: 'userPassword', title: '用户' },
        { account: 'upgradeAccount', password: 'upgradePassword', title: '升级账号' },
        { account: 'platformUserAccount', password: 'platformUserPassword', title: '平台用户' },
        { account: 'initUser', password: 'initPassword', title: 'init认证' },
        { account: 'kibanaAccount', password: 'kibanaPassword', title: 'kibana认证' }
      ]

      // 查找成对的认证字段
      authPairs.forEach(pair => {
        if (fields.includes(pair.account) && fields.includes(pair.password)) {
          authGroups.push({
            name: pair.account + '_' + pair.password,
            title: pair.title,
            accountField: pair.account,
            passwordField: pair.password
          })
        }
      })

      return authGroups
    }

    // 获取非认证字段（包括单独的认证字段）
    const getNonAuthFieldsForFields = (fields) => {
      const authGroups = getAuthGroupsForFields(fields)
      const processedFields = new Set()

      // 标记已处理的成对认证字段
      authGroups.forEach(group => {
        processedFields.add(group.accountField)
        processedFields.add(group.passwordField)
      })

      // 返回未处理的字段
      return fields.filter(field => !processedFields.has(field))
    }

    // 获取认证组合的列类
    const getAuthGroupColumnClass = (authGroup, totalFields) => {
      // 认证组合通常占用更多空间
      return 'col-md-4'
    }

    // 格式化URL
    const formatGroupUrl = (url) => {
      if (!url) return ''
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url
      }
      return `http://${url}`
    }

    // 复制到剪贴板
    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        console.log('已复制到剪贴板:', text)
      } catch (err) {
        console.error('复制失败:', err)
      }
    }

    // 切换密码可见性
    const togglePasswordVisibility = (fieldKey) => {
      passwordVisibility.value[fieldKey] = !passwordVisibility.value[fieldKey]
    }

    // 初始化字段
    const initializeFields = () => {
      const data = {}
      Object.keys(props.fieldConfig).forEach(key => {
        const field = props.fieldConfig[key]
        data[key] = field.default || ''

        // 初始化密码可见性
        if (field.type === 'password') {
          passwordVisibility.value[key] = false
        }
      })
      localData.value = { ...data, ...props.modelValue }
    }

    // 初始化字段
    initializeFields()

    // 监听本地数据变化
    watch(localData, (newData) => {
      emit('update:modelValue', newData)
    }, { deep: true })

    // 监听外部数据变化
    watch(() => props.modelValue, (newData) => {
      if (newData && typeof newData === 'object') {
        localData.value = { ...localData.value, ...newData }
      }
    }, { deep: true })

    // 监听字段配置变化，重新初始化
    watch(() => props.fieldConfig, () => {
      initializeFields()
    }, { immediate: true })

    /**
     * 自动填充访问信息
     * 根据服务器列表中的组件自动填充对应的访问地址
     * @param {Array} serverList - 服务器列表
     */
    const autoFillAccessInfo = (serverList) => {
      if (!serverList || !Array.isArray(serverList) || serverList.length === 0) {
        return
      }

      // 根据表单类型执行不同的自动填充逻辑
      if (props.formType === '安全测评') {
        autoFillSecurityTestingFields(serverList)
      } else if (props.formType === '安全监测') {
        autoFillSecurityMonitoringFields(serverList)
      } else if (props.formType === '应用加固') {
        autoFillAppHardeningFields(serverList)
      }
    }

    /**
     * 安全测评表单自动填充
     */
    const autoFillSecurityTestingFields = (serverList) => {
      // 自动填充管理员页面IP (front-ssp-admin)
      autoFillFieldFromComponent(serverList, 'front-ssp-admin', '管理员页面IP', (ip, port) => `${ip}:${port}`)

      // 自动填充用户页面IP (front-ssp-user)
      autoFillFieldFromComponent(serverList, 'front-ssp-user', '用户页面IP', (ip, port) => `${ip}:${port}`)

      // 自动填充升级页面IP (luna)
      autoFillFieldFromComponent(serverList, 'luna', '升级页面IP', (ip, port) => `${ip}:${port}`)

      // 自动填充对外服务端口 (backend-ssp-user)
      autoFillFieldFromComponent(serverList, 'backend-ssp-user', '对外服务端口', (ip, port) => `${ip}:${port}`)
    }

    /**
     * 安全监测表单自动填充
     */
    const autoFillSecurityMonitoringFields = (serverList) => {
      // 自动填充业务功能页面地址 (web-service-nginx)
      autoFillFieldFromComponent(serverList, 'web-service-nginx', '业务功能页面地址', (ip, port) => `https://${ip}:${port}`)

      // 自动填充init地址 (init)
      autoFillFieldFromComponent(serverList, 'init', 'init地址', (ip, port) => `http://${ip}:${port}`)

      // 自动填充kibana地址 (kibana)
      autoFillFieldFromComponent(serverList, 'kibana', 'kibana地址', (ip, port) => `http://${ip}:${port}`)
    }

    /**
     * 应用加固表单自动填充
     */
    const autoFillAppHardeningFields = (serverList) => {
      // 自动填充平台访问地址 (secweb)
      const platformAddresses = []
      serverList.forEach(server => {
        if (server.部署应用 && server.部署应用.includes('secweb')) {
          const ip = server.IP地址
          const port = server.组件端口?.['secweb'] || '8000'
          if (ip) {
            platformAddresses.push(`http://${ip}:${port}`)
          }
        }
      })

      if (platformAddresses.length > 0) {
        updateFieldValue('平台访问地址', platformAddresses.join(', '))
      }

      // 自动填充升级平台地址 (luna)
      autoFillFieldFromComponent(serverList, 'luna', '升级平台地址', (ip, port) => `http://${ip}:${port}`, true)
    }

    /**
     * 从指定组件自动填充字段
     */
    const autoFillFieldFromComponent = (serverList, componentName, fieldName, formatUrl, onlyFirst = false) => {
      console.log(`🔍 尝试自动填充字段: ${fieldName} (组件: ${componentName})`)
      const matchingServers = []

      serverList.forEach((server, index) => {
        console.log(`📋 检查服务器 ${index}:`, {
          IP地址: server.IP地址,
          部署应用: server.部署应用,
          组件端口: server.组件端口
        })

        if (server.部署应用 && server.部署应用.includes(componentName)) {
          const ip = server.IP地址
          // 优先使用服务器配置的端口，如果没有则使用默认端口
          const configuredPort = server.组件端口?.[componentName]
          const defaultPort = getComponentDefaultPort(componentName)
          const port = configuredPort || defaultPort

          console.log(`✅ 找到匹配的服务器 ${index}:`, {
            组件: componentName,
            IP: ip,
            配置端口: configuredPort,
            默认端口: defaultPort,
            最终端口: port
          })

          if (ip) {
            const formattedUrl = formatUrl(ip, port)
            matchingServers.push(formattedUrl)
            console.log(`🎯 生成URL: ${formattedUrl}`)
          } else {
            console.log(`⚠️ 服务器 ${index} 缺少IP地址`)
          }
        }
      })

      if (matchingServers.length > 0) {
        const value = onlyFirst ? matchingServers[0] : matchingServers.join(', ')
        console.log(`💾 自动填充 ${fieldName}: ${value}`)
        updateFieldValue(fieldName, value)
      } else {
        console.log(`❌ 未找到包含组件 ${componentName} 的服务器`)
      }
    }

    /**
     * 获取组件默认端口
     */
    const getComponentDefaultPort = (componentName) => {
      const defaultPorts = {
        'front-ssp-admin': '8080',
        'front-ssp-user': '8081',
        'backend-ssp-user': '8082',
        'luna': '9001',
        'secweb': '8000',
        'web-service-nginx': '443',
        'init': '8181',
        'kibana': '5601'
      }
      return defaultPorts[componentName] || '8080'
    }

    /**
     * 更新字段值
     */
    const updateFieldValue = (fieldName, value, force = false) => {
      // 查找对应的字段key
      const fieldKey = Object.keys(props.fieldConfig).find(key =>
        props.fieldConfig[key].field === fieldName
      )

      if (fieldKey) {
        if (!localData.value[fieldKey] || force) {
          localData.value[fieldKey] = value
          // 立即触发emit更新父组件
          emit('update:modelValue', { ...localData.value })
        }
      }
    }

    // 监听服务器列表变化，触发自动填充
    watch(() => props.serverList, (newServerList) => {
      console.log('🔄 DynamicAccessInfoSection: 服务器列表变化')
      console.log('📦 新服务器列表:', newServerList)
      console.log('📋 表单类型:', props.formType)

      if (newServerList && newServerList.length > 0) {
        console.log('✅ 开始自动填充访问信息')
        autoFillAccessInfo(newServerList)
      } else {
        console.log('⚠️ 服务器列表为空，跳过自动填充')
      }
    }, { deep: true, immediate: true })

    return {
      localData,
      passwordVisibility,
      accessGroups,
      isAuthField,
      isUrlField,
      getItemColumnClass,
      getAuthGroupsForFields,
      getNonAuthFieldsForFields,
      getAuthGroupColumnClass,
      formatGroupUrl,
      copyToClipboard,
      togglePasswordVisibility,
      autoFillAccessInfo
    }
  }
}
</script>

<style scoped>
/* 访问信息分组样式 - 与表单历史详情页面保持一致 */
.access-info-groups {
  margin: 0;
}

.access-group-card {
  border: 1px solid #dee2e6 !important;
  border-radius: 8px !important;
  margin-bottom: 16px !important;
  transition: all 0.2s ease;
}

.access-group-card .card-header {
  border-bottom: 1px solid #dee2e6 !important;
  border-radius: 8px 8px 0 0 !important;
  padding: 12px 16px !important;
}

.access-group-card .card-body {
  padding: 16px !important;
  background: #fafbfc;
}

.group-header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.group-title {
  font-size: 16px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
}

.group-title i {
  margin-right: 8px;
  font-size: 18px;
}

.group-url {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 13px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.group-url-link {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
  flex: 1;
  margin-right: 8px;
  word-break: break-all;
}

.group-url-link:hover {
  text-decoration: underline;
  color: #0056b3;
}

.group-url-copy {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 12px;
  flex-shrink: 0;
}

.group-url-copy:hover {
  background-color: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

.group-url-copy:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #495057;
}

/* 访问项目样式 */
.access-item {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.2s;
}

.access-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

/* 认证项目样式 */
.auth-item {
  border-left: 4px solid #28a745;
}

.auth-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.auth-icon-wrapper {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #28a745, #20c997);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.auth-icon {
  color: white;
  font-size: 0.875rem;
}

.auth-label {
  font-weight: 600;
  color: #28a745;
  font-size: 0.875rem;
}

.auth-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.auth-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.auth-field-label {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.auth-field-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-field-value input {
  flex: 1;
}

.password-toggle-btn {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 0.25rem;
}

.password-toggle-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

/* 密码文本样式 */
.password-text {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.password-dots {
  color: #6c757d;
  letter-spacing: 2px;
}

/* 编辑区域样式 */
.auth-edit-section {
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  margin: 0.75rem -1rem -1rem -1rem;
  padding: 0.75rem 1rem 1rem 1rem;
  border-radius: 0 0 8px 8px;
}

/* URL项目样式 */
.url-item {
  border-left: 4px solid #007bff;
}

.url-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.url-icon-wrapper {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.url-icon {
  color: white;
  font-size: 0.875rem;
}

.url-label {
  font-weight: 600;
  color: #007bff;
  font-size: 0.875rem;
}

.url-content {
  margin-left: 2.5rem;
}

.url-preview {
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.url-preview a {
  font-size: 0.875rem;
  word-break: break-all;
}

/* 文本项目样式 */
.text-item {
  border-left: 4px solid #6c757d;
}

.text-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.text-icon-wrapper {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #6c757d, #495057);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.text-icon {
  color: white;
  font-size: 0.875rem;
}

.text-label {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.875rem;
}

.text-content {
  margin-left: 2.5rem;
}

/* 表单控件样式 */
.form-control-sm, .form-select-sm {
  border-radius: 4px;
  border: 1px solid #ced4da;
  transition: all 0.2s;
}

.form-control-sm:focus, .form-select-sm:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 输入组样式 */
.input-group-sm .btn {
  border-radius: 0 4px 4px 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .group-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .auth-content,
  .url-content,
  .text-content {
    margin-left: 0;
    margin-top: 0.75rem;
  }

  .auth-header,
  .url-header,
  .text-header {
    margin-bottom: 0.5rem;
  }
}
</style>
