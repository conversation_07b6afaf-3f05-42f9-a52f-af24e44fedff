/**
 * 表单数据映射混入
 * 提供统一的数据映射功能，可在所有表单组件中复用
 */

import { DataMappingProcessor, validateFormDataMapping } from '@/utils/dataMapping'

export const formDataMappingMixin = {
  data() {
    return {
      // 数据映射处理器（在组件中需要根据表单类型初始化）
      dataMappingProcessor: null,
      // 数据映射验证结果
      mappingValidation: {
        isValid: true,
        errors: [],
        warnings: []
      },
      // 是否启用自动数据映射
      enableAutoMapping: true,
      // 数据映射处理的防抖定时器
      mappingDebounceTimer: null,
      // 防止递归更新的标志
      isUpdatingCompanyFields: false
    }
  },

  watch: {
    // 监听表单数据变化，进行数据映射验证
    // 注意：这里不使用深度监听，避免递归更新
    // 具体的字段监听应该在各个组件中单独实现
  },

  methods: {
    /**
     * 初始化数据映射处理器
     * @param {String} formType - 表单类型
     */
    initDataMappingProcessor(formType) {
      this.dataMappingProcessor = new DataMappingProcessor(formType)
      console.log(`数据映射处理器已初始化: ${formType}`)
    },

    /**
     * 防抖的数据映射验证
     * 避免频繁验证影响性能
     */
    debouncedValidateMapping() {
      if (this.mappingDebounceTimer) {
        clearTimeout(this.mappingDebounceTimer)
      }

      this.mappingDebounceTimer = setTimeout(() => {
        this.validateDataMapping()
      }, 1000) // 增加防抖时间到1000ms
    },

    /**
     * 验证数据映射
     * 检查表单数据的映射完整性
     */
    validateDataMapping() {
      if (!this.dataMappingProcessor || !this.formData) {
        return
      }

      try {
        const formType = this.dataMappingProcessor.formType
        this.mappingValidation = validateFormDataMapping(this.formData, formType)
        
        if (!this.mappingValidation.isValid) {
          console.warn(`${formType}表单数据映射验证失败:`, this.mappingValidation.errors)
          this.handleMappingValidationErrors(this.mappingValidation.errors)
        }
        
        if (this.mappingValidation.warnings && this.mappingValidation.warnings.length > 0) {
          console.warn(`${formType}表单数据映射警告:`, this.mappingValidation.warnings)
          this.handleMappingValidationWarnings(this.mappingValidation.warnings)
        }
      } catch (error) {
        console.error('数据映射验证失败:', error)
      }
    },

    /**
     * 处理表单数据映射
     * 在表单提交前进行数据映射处理
     * @returns {Boolean} - 处理是否成功
     */
    processFormDataMapping() {
      if (!this.dataMappingProcessor || !this.formData) {
        console.warn('数据映射处理器未初始化或表单数据为空')
        return false
      }

      try {
        const processedData = this.dataMappingProcessor.processDataMapping(this.formData)
        
        // 更新表单数据
        Object.assign(this.formData, processedData)
        
        const formType = this.dataMappingProcessor.formType
        console.log(`${formType}表单数据映射处理完成`)
        return true
      } catch (error) {
        console.error('表单数据映射处理失败:', error)
        this.showMappingError('数据映射处理失败，请检查表单数据')
        return false
      }
    },

    /**
     * 同步公司名称相关字段
     * 确保公司名称在不同字段间保持一致
     * @param {String} newValue - 新值
     * @param {String} sourceField - 源字段名
     */
    syncCompanyNameFields(newValue, sourceField) {
      // 防止递归更新
      if (this.isUpdatingCompanyFields) {
        return
      }

      if (!newValue || !newValue.trim()) return

      const trimmedValue = newValue.trim()

      // 设置更新标志，防止递归
      this.isUpdatingCompanyFields = true

      try {
        // 获取表单类型特定的公司名称字段
        const formType = this.dataMappingProcessor?.formType || '安全测评'
        const companyFields = this.getCompanyFieldsForFormType(formType)

        // 同步所有公司名称字段
        companyFields.forEach(field => {
          if (field !== sourceField && this.formData[field] !== trimmedValue) {
            this.formData[field] = trimmedValue
          }
        })

        // 同步到基本信息字段值（如果存在）
        if (this.basicInfoValues && this.basicInfoValues.customer !== trimmedValue) {
          this.basicInfoValues.customer = trimmedValue
        }

        console.log(`公司名称字段同步: ${sourceField} -> ${trimmedValue}`)
      } finally {
        // 使用nextTick确保所有更新完成后再重置标志
        this.$nextTick(() => {
          this.isUpdatingCompanyFields = false
        })
      }
    },

    /**
     * 获取表单类型对应的公司名称字段
     * @param {String} formType - 表单类型
     * @returns {Array} - 公司名称字段数组
     */
    getCompanyFieldsForFormType(formType) {
      const fieldMapping = {
        '应用加固': ['公司名称', '客户'],
        '安全监测': ['公司名称'],
        '安全测评': ['公司名称'],
        'default': ['公司名称']
      }
      
      return fieldMapping[formType] || fieldMapping.default
    },

    /**
     * 处理保存表单事件
     * 在保存前进行数据映射处理
     */
    handleSaveForm() {
      const formType = this.dataMappingProcessor?.formType || '未知'
      console.log(`${formType}表单保存事件触发`)
      
      // 进行数据映射处理
      if (this.processFormDataMapping()) {
        // 数据映射成功，发送保存事件
        this.$emit('save-form')
        this.showMappingSuccess('表单数据已处理并保存')
      } else {
        // 数据映射失败，不进行保存
        this.showMappingError('表单数据处理失败，无法保存')
      }
    },

    /**
     * 处理加载表单事件
     * 在加载后进行数据映射处理
     */
    handleLoadForm() {
      const formType = this.dataMappingProcessor?.formType || '未知'
      console.log(`${formType}表单加载事件触发`)
      
      // 发送加载事件
      this.$emit('load-form')
      
      // 延迟进行数据映射处理，确保数据已加载
      this.$nextTick(() => {
        this.processFormDataMapping()
        this.showMappingSuccess('表单数据已加载并处理')
      })
    },

    /**
     * 处理数据映射验证错误
     * @param {Array} errors - 错误列表
     */
    handleMappingValidationErrors(errors) {
      // 子组件可以重写此方法来自定义错误处理
      console.error('数据映射验证错误:', errors)
    },

    /**
     * 处理数据映射验证警告
     * @param {Array} warnings - 警告列表
     */
    handleMappingValidationWarnings(warnings) {
      // 子组件可以重写此方法来自定义警告处理
      console.warn('数据映射验证警告:', warnings)
    },

    /**
     * 显示映射成功消息
     * @param {String} message - 消息内容
     */
    showMappingSuccess(message) {
      if (this.showToast) {
        this.showToast(message, '成功', 'success')
      } else {
        console.log(message)
      }
    },

    /**
     * 显示映射错误消息
     * @param {String} message - 消息内容
     */
    showMappingError(message) {
      if (this.showToast) {
        this.showToast(message, '错误', 'error')
      } else {
        console.error(message)
      }
    },

    /**
     * 显示映射警告消息
     * @param {String} message - 消息内容
     */
    showMappingWarning(message) {
      if (this.showToast) {
        this.showToast(message, '警告', 'warning')
      } else {
        console.warn(message)
      }
    },

    /**
     * 启用自动数据映射
     */
    enableAutoDataMapping() {
      this.enableAutoMapping = true
      console.log('自动数据映射已启用')
    },

    /**
     * 禁用自动数据映射
     */
    disableAutoDataMapping() {
      this.enableAutoMapping = false
      if (this.mappingDebounceTimer) {
        clearTimeout(this.mappingDebounceTimer)
        this.mappingDebounceTimer = null
      }
      console.log('自动数据映射已禁用')
    },

    /**
     * 手动触发数据映射处理
     * @returns {Boolean} - 处理是否成功
     */
    manualProcessDataMapping() {
      console.log('手动触发数据映射处理')
      return this.processFormDataMapping()
    }
  },

  beforeUnmount() {
    // 清理防抖定时器
    if (this.mappingDebounceTimer) {
      clearTimeout(this.mappingDebounceTimer)
    }
  }
}

export default formDataMappingMixin
