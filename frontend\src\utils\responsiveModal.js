/**
 * 响应式模态框工具类
 * 提供自适应浏览器大小的模态框解决方案
 */

/**
 * 获取当前视口信息
 */
export function getViewportInfo() {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    isMobile: window.innerWidth <= 768,
    isTablet: window.innerWidth > 768 && window.innerWidth <= 1024,
    isDesktop: window.innerWidth > 1024,
    isLargeScreen: window.innerWidth > 1400
  }
}

/**
 * 计算响应式模态框尺寸
 * @param {string} size - 模态框尺寸 (sm, md, lg, xl, xxl)
 * @param {Object} options - 自定义选项
 */
export function calculateModalSize(size = 'md', options = {}) {
  const viewport = getViewportInfo()
  const { 
    minWidth = 300, 
    minHeight = 200, 
    maxWidthRatio = 0.95, 
    maxHeightRatio = 0.95,
    mobileFullscreen = true 
  } = options

  // 移动端全屏
  if (viewport.isMobile && mobileFullscreen) {
    return {
      width: '100vw',
      height: '100vh',
      maxWidth: 'none',
      maxHeight: 'none'
    }
  }

  // 预定义尺寸映射
  const sizeMap = {
    sm: { widthRatio: 0.4, heightRatio: 0.6, maxWidth: 400 },
    md: { widthRatio: 0.6, heightRatio: 0.7, maxWidth: 600 },
    lg: { widthRatio: 0.8, heightRatio: 0.8, maxWidth: 900 },
    xl: { widthRatio: 0.9, heightRatio: 0.9, maxWidth: 1200 },
    xxl: { widthRatio: 0.95, heightRatio: 0.95, maxWidth: 1400 }
  }

  const config = sizeMap[size] || sizeMap.md

  // 计算实际尺寸
  const calculatedWidth = Math.min(
    viewport.width * config.widthRatio,
    config.maxWidth
  )
  
  const calculatedHeight = Math.min(
    viewport.height * config.heightRatio,
    viewport.height * maxHeightRatio
  )

  return {
    width: Math.max(calculatedWidth, minWidth) + 'px',
    height: Math.max(calculatedHeight, minHeight) + 'px',
    maxWidth: config.maxWidth + 'px',
    maxHeight: (viewport.height * maxHeightRatio) + 'px'
  }
}

/**
 * 应用响应式样式到模态框元素
 * @param {HTMLElement} element - 模态框元素
 * @param {string} size - 尺寸
 * @param {Object} options - 选项
 */
export function applyResponsiveStyles(element, size = 'md', options = {}) {
  if (!element) return

  const styles = calculateModalSize(size, options)
  const viewport = getViewportInfo()

  // 应用基础样式
  Object.assign(element.style, {
    width: styles.width,
    height: styles.height,
    maxWidth: styles.maxWidth,
    maxHeight: styles.maxHeight
  })

  // 移动端特殊处理
  if (viewport.isMobile) {
    element.style.borderRadius = '0'
    element.style.margin = '0'
  } else {
    element.style.borderRadius = '12px'
    element.style.margin = 'auto'
  }

  console.log(`📱 应用响应式样式: ${size}, 视口: ${viewport.width}x${viewport.height}`)
}

/**
 * 创建响应式模态框CSS类
 */
export function generateResponsiveCSS() {
  return `
/* 响应式模态框基础样式 */
.responsive-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  background-color: rgba(0, 0, 0, 0.5);
}

.responsive-modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: scale(0.9) translateY(-20px); }
  to { opacity: 1; transform: scale(1) translateY(0); }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .responsive-modal-container {
    width: 100vw !important;
    height: 100vh !important;
    border-radius: 0 !important;
    margin: 0 !important;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .responsive-modal-container {
    max-width: 90vw;
    max-height: 85vh;
  }
}

/* 大屏幕适配 */
@media (min-width: 1400px) {
  .responsive-modal-container {
    max-width: 1400px;
    max-height: 90vh;
  }
}
`
}

/**
 * 监听窗口大小变化并自动调整模态框
 * @param {HTMLElement} element - 模态框元素
 * @param {string} size - 尺寸
 * @param {Object} options - 选项
 */
export function setupResponsiveListener(element, size = 'md', options = {}) {
  if (!element) return

  let resizeTimeout
  
  const handleResize = () => {
    clearTimeout(resizeTimeout)
    resizeTimeout = setTimeout(() => {
      applyResponsiveStyles(element, size, options)
    }, 100)
  }

  window.addEventListener('resize', handleResize)
  window.addEventListener('orientationchange', handleResize)

  // 初始应用样式
  applyResponsiveStyles(element, size, options)

  // 返回清理函数
  return () => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('orientationchange', handleResize)
    clearTimeout(resizeTimeout)
  }
}

/**
 * Vue 3 Composition API Hook
 */
export function useResponsiveModal(size = 'md', options = {}) {
  const { ref, onMounted, onUnmounted } = require('vue')
  
  const modalRef = ref(null)
  let cleanup = null

  onMounted(() => {
    if (modalRef.value) {
      cleanup = setupResponsiveListener(modalRef.value, size, options)
    }
  })

  onUnmounted(() => {
    if (cleanup) {
      cleanup()
    }
  })

  return {
    modalRef,
    applyStyles: () => applyResponsiveStyles(modalRef.value, size, options),
    getViewport: getViewportInfo
  }
}

/**
 * 注入全局CSS样式
 */
export function injectResponsiveCSS() {
  const styleId = 'responsive-modal-styles'
  
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = generateResponsiveCSS()
    document.head.appendChild(style)
    console.log('📱 响应式模态框样式已注入')
  }
}

// 自动注入样式
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectResponsiveCSS)
  } else {
    injectResponsiveCSS()
  }

  // 开发环境暴露到全局
  if (process.env.NODE_ENV === 'development') {
    window.responsiveModal = {
      getViewportInfo,
      calculateModalSize,
      applyResponsiveStyles,
      setupResponsiveListener,
      injectResponsiveCSS
    }
    console.log('📱 响应式模态框工具已加载到 window.responsiveModal')
  }
}

export default {
  getViewportInfo,
  calculateModalSize,
  applyResponsiveStyles,
  setupResponsiveListener,
  useResponsiveModal,
  injectResponsiveCSS
}
