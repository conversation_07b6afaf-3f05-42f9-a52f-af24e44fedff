<template>
  <collapsible-card card-class="border-warning" storage-key="access-info-section">
    <template #header>
      <i class="bi bi-door-open me-2"></i>访问信息
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-warning text-dark">业务页面: {{ businessPageUrl ? '已配置' : '未配置' }}</span>
        <span class="badge bg-warning text-dark">init: {{ initUrl ? '已配置' : '未配置' }}</span>
        <span class="badge bg-warning text-dark">kibana: {{ kibanaUrl ? '已配置' : '未配置' }}</span>
      </div>
    </template>

    <!-- 业务功能页面访问 -->
    <div class="card mb-3 border-light access-group-card">
      <div class="card-header py-2" style="background-color: rgba(0, 123, 255, 0.082); border-bottom: 2px solid rgb(0, 123, 255);">
        <div class="group-header-content">
          <div class="group-title">
            <i class="bi-globe me-2"></i>
            业务功能页面访问
          </div>
          <!-- 显示该分组的URL地址 -->
          <div v-if="businessPageUrl" class="group-url">
            <a :href="businessPageUrl" target="_blank" class="group-url-link">
              <i class="bi bi-box-arrow-up-right me-1"></i>
              {{ businessPageUrl }}
            </a>
            <button class="group-url-copy" @click="copyToClipboard(businessPageUrl)" title="复制地址">
              <i class="bi bi-clipboard"></i>
            </button>
          </div>
          <!-- URL输入框（当没有URL时显示） -->
          <div v-else class="group-url-input mt-2">
            <div class="form-floating">
              <input
                type="text"
                class="form-control form-control-sm"
                id="businessPageUrl"
                v-model="businessPageUrl"
                placeholder="例如：https://admin.example.com"
                @focus="autoFillWebServiceUrl"
                @input="updateBusinessPageUrl"
                :readonly="autoFilledFields.businessPage"
              >
              <label for="businessPageUrl">
                业务功能页面地址 <span class="text-danger">*</span>
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body py-2">
        <div class="row g-3">
          <!-- 超级管理员认证 -->
          <div class="col-md-4">
            <div class="access-item auth-item">
              <div class="auth-header">
                <div class="auth-icon-wrapper">
                  <i class="bi bi-shield-lock auth-icon"></i>
                </div>
                <div class="auth-label">超级管理员</div>
              </div>
              <div class="auth-content">
                <div class="auth-field">
                  <div class="auth-field-label">
                    <i class="bi bi-person me-1"></i>账号
                  </div>
                  <div class="auth-field-value">{{ superAdminAccount || '-' }}</div>
                </div>
                <div class="auth-field">
                  <div class="auth-field-label">
                    <i class="bi bi-key me-1"></i>密码
                  </div>
                  <div class="auth-field-value">
                    <span class="password-text">
                      <span v-if="passwordVisibility.superAdmin">{{ superAdminPassword || '-' }}</span>
                      <span v-else class="password-dots">{{ superAdminPassword ? '●●●●●●' : '-' }}</span>
                    </span>
                    <button v-if="superAdminPassword" class="password-toggle-btn" @click="togglePasswordVisibility('superAdmin')" :title="passwordVisibility.superAdmin ? '隐藏密码' : '显示密码'">
                      <i :class="passwordVisibility.superAdmin ? 'bi-eye-slash' : 'bi-eye'"></i>
                    </button>
                  </div>
                </div>
                <!-- 编辑输入框 -->
                <div class="auth-edit-section mt-2">
                  <div class="mb-2">
                    <input
                      type="text"
                      class="form-control form-control-sm"
                      v-model="superAdminAccount"
                      @input="updateSuperAdminAccount"
                      placeholder="输入超级管理员账号"
                    >
                  </div>
                  <div class="input-group input-group-sm">
                    <input
                      :type="passwordVisibility.superAdmin ? 'text' : 'password'"
                      class="form-control"
                      v-model="superAdminPassword"
                      @input="updateSuperAdminPassword"
                      placeholder="输入超级管理员密码"
                    >
                    <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('superAdmin')" :title="passwordVisibility.superAdmin ? '隐藏密码' : '显示密码'">
                      <i :class="passwordVisibility.superAdmin ? 'bi-eye-slash' : 'bi-eye'"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 客户管理员认证 -->
          <div class="col-md-4">
            <div class="access-item auth-item">
              <div class="auth-header">
                <div class="auth-icon-wrapper">
                  <i class="bi bi-shield-lock auth-icon"></i>
                </div>
                <div class="auth-label">客户管理员</div>
              </div>
              <div class="auth-content">
                <div class="auth-field">
                  <div class="auth-field-label">
                    <i class="bi bi-person me-1"></i>账号
                  </div>
                  <div class="auth-field-value">{{ customerAdminAccount || '-' }}</div>
                </div>
                <div class="auth-field">
                  <div class="auth-field-label">
                    <i class="bi bi-key me-1"></i>密码
                  </div>
                  <div class="auth-field-value">
                    <span class="password-text">
                      <span v-if="passwordVisibility.customerAdmin">{{ customerAdminPassword || '-' }}</span>
                      <span v-else class="password-dots">{{ customerAdminPassword ? '●●●●●●' : '-' }}</span>
                    </span>
                    <button v-if="customerAdminPassword" class="password-toggle-btn" @click="togglePasswordVisibility('customerAdmin')" :title="passwordVisibility.customerAdmin ? '隐藏密码' : '显示密码'">
                      <i :class="passwordVisibility.customerAdmin ? 'bi-eye-slash' : 'bi-eye'"></i>
                    </button>
                  </div>
                </div>
                <!-- 编辑输入框 -->
                <div class="auth-edit-section mt-2">
                  <div class="mb-2">
                    <input
                      type="text"
                      class="form-control form-control-sm"
                      v-model="customerAdminAccount"
                      @input="updateCustomerAdminAccount"
                      placeholder="输入客户管理员账号"
                    >
                  </div>
                  <div class="input-group input-group-sm">
                    <input
                      :type="passwordVisibility.customerAdmin ? 'text' : 'password'"
                      class="form-control"
                      v-model="customerAdminPassword"
                      @input="updateCustomerAdminPassword"
                      placeholder="输入客户管理员密码"
                    >
                    <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('customerAdmin')" :title="passwordVisibility.customerAdmin ? '隐藏密码' : '显示密码'">
                      <i :class="passwordVisibility.customerAdmin ? 'bi-eye-slash' : 'bi-eye'"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- init访问 -->
    <div class="card mb-3 border-light access-group-card">
      <div class="card-header py-2" style="background-color: rgba(253, 126, 20, 0.082); border-bottom: 2px solid rgb(253, 126, 20);">
        <div class="group-header-content">
          <div class="group-title">
            <i class="bi-gear me-2"></i>
            init访问
          </div>
          <!-- 显示该分组的URL地址 -->
          <div v-if="initUrl" class="group-url">
            <a :href="initUrl" target="_blank" class="group-url-link">
              <i class="bi bi-box-arrow-up-right me-1"></i>
              {{ initUrl }}
            </a>
            <button class="group-url-copy" @click="copyToClipboard(initUrl)" title="复制地址">
              <i class="bi bi-clipboard"></i>
            </button>
          </div>
          <!-- URL输入框（当没有URL时显示） -->
          <div v-else class="group-url-input mt-2">
            <div class="form-floating">
              <input
                type="text"
                class="form-control form-control-sm"
                id="initUrl"
                v-model="initUrl"
                placeholder="例如：http://init.example.com:8181"
                @focus="autoFillInitUrl"
                @input="updateInitUrl"
                :readonly="autoFilledFields.initUrl"
              >
              <label for="initUrl">
                init地址 <span class="text-danger">*</span>
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body py-2">
        <div class="row g-3">
          <!-- init认证 -->
          <div class="col-md-4">
            <div class="access-item auth-item">
              <div class="auth-header">
                <div class="auth-icon-wrapper">
                  <i class="bi bi-shield-lock auth-icon"></i>
                </div>
                <div class="auth-label">init认证</div>
              </div>
              <div class="auth-content">
                <div class="auth-field">
                  <div class="auth-field-label">
                    <i class="bi bi-person me-1"></i>账号
                  </div>
                  <div class="auth-field-value">{{ initUsername || '-' }}</div>
                </div>
                <div class="auth-field">
                  <div class="auth-field-label">
                    <i class="bi bi-key me-1"></i>密码
                  </div>
                  <div class="auth-field-value">
                    <span class="password-text">
                      <span v-if="passwordVisibility.init">{{ initPassword || '-' }}</span>
                      <span v-else class="password-dots">{{ initPassword ? '●●●●●●' : '-' }}</span>
                    </span>
                    <button v-if="initPassword" class="password-toggle-btn" @click="togglePasswordVisibility('init')" :title="passwordVisibility.init ? '隐藏密码' : '显示密码'">
                      <i :class="passwordVisibility.init ? 'bi-eye-slash' : 'bi-eye'"></i>
                    </button>
                  </div>
                </div>
                <!-- 编辑输入框 -->
                <div class="auth-edit-section mt-2">
                  <div class="mb-2">
                    <input
                      type="text"
                      class="form-control form-control-sm"
                      v-model="initUsername"
                      @input="updateInitUsername"
                      placeholder="输入init用户名"
                    >
                  </div>
                  <div class="input-group input-group-sm">
                    <input
                      :type="passwordVisibility.init ? 'text' : 'password'"
                      class="form-control"
                      v-model="initPassword"
                      @input="updateInitPassword"
                      placeholder="输入init密码"
                    >
                    <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('init')" :title="passwordVisibility.init ? '隐藏密码' : '显示密码'">
                      <i :class="passwordVisibility.init ? 'bi-eye-slash' : 'bi-eye'"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- kibana访问 -->
    <div class="card mb-3 border-light access-group-card">
      <div class="card-header py-2" style="background-color: rgba(23, 162, 184, 0.082); border-bottom: 2px solid rgb(23, 162, 184);">
        <div class="group-header-content">
          <div class="group-title">
            <i class="bi-bar-chart me-2"></i>
            kibana访问
          </div>
          <!-- 显示该分组的URL地址 -->
          <div v-if="kibanaUrl" class="group-url">
            <a :href="kibanaUrl" target="_blank" class="group-url-link">
              <i class="bi bi-box-arrow-up-right me-1"></i>
              {{ kibanaUrl }}
            </a>
            <button class="group-url-copy" @click="copyToClipboard(kibanaUrl)" title="复制地址">
              <i class="bi bi-clipboard"></i>
            </button>
          </div>
          <!-- URL输入框（当没有URL时显示） -->
          <div v-else class="group-url-input mt-2">
            <div class="form-floating">
              <input
                type="text"
                class="form-control form-control-sm"
                id="kibanaUrl"
                v-model="kibanaUrl"
                placeholder="例如：http://kibana.example.com:5601"
                @focus="autoFillKibanaUrl"
                @input="updateKibanaUrl"
                :readonly="autoFilledFields.kibanaUrl"
              >
              <label for="kibanaUrl">
                kibana地址 <span class="text-danger">*</span>
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body py-2">
        <div class="row g-3">
          <!-- kibana认证 -->
          <div class="col-md-4">
            <div class="access-item auth-item">
              <div class="auth-header">
                <div class="auth-icon-wrapper">
                  <i class="bi bi-shield-lock auth-icon"></i>
                </div>
                <div class="auth-label">kibana认证</div>
              </div>
              <div class="auth-content">
                <div class="auth-field">
                  <div class="auth-field-label">
                    <i class="bi bi-person me-1"></i>账号
                  </div>
                  <div class="auth-field-value">{{ kibanaUsername || '-' }}</div>
                </div>
                <div class="auth-field">
                  <div class="auth-field-label">
                    <i class="bi bi-key me-1"></i>密码
                  </div>
                  <div class="auth-field-value">
                    <span class="password-text">
                      <span v-if="passwordVisibility.kibana">{{ kibanaPassword || '-' }}</span>
                      <span v-else class="password-dots">{{ kibanaPassword ? '●●●●●●' : '-' }}</span>
                    </span>
                    <button v-if="kibanaPassword" class="password-toggle-btn" @click="togglePasswordVisibility('kibana')" :title="passwordVisibility.kibana ? '隐藏密码' : '显示密码'">
                      <i :class="passwordVisibility.kibana ? 'bi-eye-slash' : 'bi-eye'"></i>
                    </button>
                  </div>
                </div>
                <!-- 编辑输入框 -->
                <div class="auth-edit-section mt-2">
                  <div class="mb-2">
                    <input
                      type="text"
                      class="form-control form-control-sm"
                      v-model="kibanaUsername"
                      @input="updateKibanaAuth"
                      placeholder="输入kibana用户名"
                    >
                  </div>
                  <div class="input-group input-group-sm">
                    <input
                      :type="passwordVisibility.kibana ? 'text' : 'password'"
                      class="form-control"
                      v-model="kibanaPassword"
                      @input="updateKibanaAuth"
                      placeholder="输入kibana密码"
                    >
                    <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('kibana')" :title="passwordVisibility.kibana ? '隐藏密码' : '显示密码'">
                      <i :class="passwordVisibility.kibana ? 'bi-eye-slash' : 'bi-eye'"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="form-text mt-3">
          <i class="bi bi-exclamation-triangle me-1"></i>
          Kibana认证信息（arm架构可能不设置安全认证）
        </div>
      </div>
    </div>
  </collapsible-card>
</template>

<script>
/**
 * 访问信息部分组件
 * 用于填写各种访问信息和账号密码
 */
import CollapsibleCard from '../common/CollapsibleCard.vue'

export default {
  name: 'AccessInfoSection',
  components: {
    CollapsibleCard
  },
  props: {
    // 业务功能页面地址
    businessPage: {
      type: String,
      default: ''
    },
    // 超级管理员账号
    superAdmin: {
      type: String,
      default: '<EMAIL>'
    },
    // 超级管理员密码
    superAdminPwd: {
      type: String,
      default: 'everisk@!QAZ2wsx'
    },
    // 客户管理员账号
    customerAdmin: {
      type: String,
      default: ''
    },
    // 客户管理员密码
    customerAdminPwd: {
      type: String,
      default: 'everisk@!QAZ2wsx'
    },
    // init地址
    initAddress: {
      type: String,
      default: ''
    },
    // init用户名
    initUser: {
      type: String,
      default: 'prometheus_user'
    },
    // init密码
    initPwd: {
      type: String,
      default: '1qaz@WSX'
    },
    // kibana地址
    kibanaAddress: {
      type: String,
      default: ''
    },
    // kibana认证信息
    kibanaAuthInfo: {
      type: String,
      default: 'elastic:beap123'
    },
    // 服务器信息列表，用于自动填充地址
    serverList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      businessPageUrl: this.businessPage,
      superAdminAccount: this.superAdmin,
      superAdminPassword: this.superAdminPwd,
      customerAdminAccount: this.customerAdmin,
      customerAdminPassword: this.customerAdminPwd,
      initUrl: this.initAddress,
      initUsername: this.initUser,
      initPassword: this.initPwd,
      kibanaUrl: this.kibanaAddress,
      kibanaUsername: '',
      kibanaPassword: '',
      passwordVisibility: {
        superAdmin: false,
        customerAdmin: false,
        init: false,
        kibana: false
      },
      autoFilledFields: {
        businessPage: false,
        initUrl: false,
        kibanaUrl: false
      },
      // 跟踪用户是否手动编辑过字段
      userEditedFields: {
        superAdminAccount: false,
        superAdminPassword: false,
        customerAdminAccount: false,
        customerAdminPassword: false,
        initUsername: false,
        initPassword: false
      }
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    businessPage(newVal) {
      this.businessPageUrl = newVal
    },
    superAdmin(newVal) {
      console.log('superAdmin prop 变化:', newVal, '用户是否已编辑:', this.userEditedFields.superAdminAccount)
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.superAdminAccount) {
        this.superAdminAccount = newVal
        console.log('更新超级管理员账号为:', newVal)
      } else {
        console.log('忽略 superAdmin prop 变化，因为用户已手动编辑')
      }
    },
    superAdminPwd(newVal) {
      console.log('superAdminPwd prop 变化:', newVal, '用户是否已编辑:', this.userEditedFields.superAdminPassword)
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.superAdminPassword) {
        this.superAdminPassword = newVal
        console.log('更新超级管理员密码为:', newVal)
      } else {
        console.log('忽略 superAdminPwd prop 变化，因为用户已手动编辑')
      }
    },
    customerAdmin(newVal) {
      console.log('customerAdmin prop 变化:', newVal, '用户是否已编辑:', this.userEditedFields.customerAdminAccount)
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.customerAdminAccount) {
        this.customerAdminAccount = newVal
        console.log('更新客户管理员账号为:', newVal)
      } else {
        console.log('忽略 customerAdmin prop 变化，因为用户已手动编辑')
      }
    },
    customerAdminPwd(newVal) {
      console.log('customerAdminPwd prop 变化:', newVal, '用户是否已编辑:', this.userEditedFields.customerAdminPassword)
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.customerAdminPassword) {
        this.customerAdminPassword = newVal
        console.log('更新客户管理员密码为:', newVal)
      } else {
        console.log('忽略 customerAdminPwd prop 变化，因为用户已手动编辑')
      }
    },
    initAddress(newVal) {
      this.initUrl = newVal
    },
    initUser(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.initUsername) {
        this.initUsername = newVal
      }
    },
    initPwd(newVal) {
      // 只有在用户没有手动编辑过时才更新
      if (!this.userEditedFields.initPassword) {
        this.initPassword = newVal
      }
    },
    kibanaAddress(newVal) {
      this.kibanaUrl = newVal
    },
    kibanaAuthInfo(newVal) {
      this.parseKibanaAuth(newVal)
    },
    // 监听服务器列表变化，自动更新各个地址
    serverList: {
      handler() {
        this.autoFillWebServiceUrl(true)
        this.autoFillInitUrl(true)
        this.autoFillKibanaUrl(true)
      },
      deep: true
    }
  },
  mounted() {
    // 组件挂载时自动填充
    this.autoFillWebServiceUrl(true)
    this.autoFillInitUrl(true)
    this.autoFillKibanaUrl(true)
    // 初始化时解析kibana认证信息
    this.parseKibanaAuth(this.kibanaAuthInfo)
  },
  methods: {
    /**
     * 解析kibana认证信息
     * 支持多种格式：elastic:beap123 或 账号：elastic 密码: beap123
     */
    parseKibanaAuth(authInfo) {
      if (!authInfo) {
        this.kibanaUsername = ''
        this.kibanaPassword = ''
        return
      }

      // 格式1: elastic:beap123
      if (authInfo.includes(':') && !authInfo.includes('账号')) {
        const parts = authInfo.split(':')
        this.kibanaUsername = parts[0] || ''
        this.kibanaPassword = parts[1] || ''
      }
      // 格式2: 账号：elastic 密码: beap123
      else if (authInfo.includes('账号') && authInfo.includes('密码')) {
        const usernameMatch = authInfo.match(/账号[：:]\s*(\S+)/)
        const passwordMatch = authInfo.match(/密码[：:]\s*(\S+)/)
        this.kibanaUsername = usernameMatch ? usernameMatch[1] : ''
        this.kibanaPassword = passwordMatch ? passwordMatch[1] : ''
      }
      // 其他格式，尝试简单分割
      else {
        const parts = authInfo.split(/[\s:：]+/)
        if (parts.length >= 2) {
          this.kibanaUsername = parts[0] || ''
          this.kibanaPassword = parts[1] || ''
        }
      }
    },

    /**
     * 切换密码可见性
     * @param {String} field - 密码字段名称
     */
    togglePasswordVisibility(field) {
      this.passwordVisibility[field] = !this.passwordVisibility[field]
    },

    /**
     * 自动填充web-service-nginx组件所在的IP地址
     * 用于业务功能页面访问地址
     * @param {Boolean} force - 是否强制更新，即使已有值
     */
    autoFillWebServiceUrl(force = false) {
      // 如果已经有值且不是强制更新，不自动填充
      if (this.businessPageUrl && !force) return

      // 查找包含web-service-nginx组件的服务器
      const webServiceServer = this.findServerWithComponent('web-service-nginx')
      if (webServiceServer) {
        const ip = webServiceServer.IP地址
        const port = webServiceServer.组件端口['web-service-nginx'] || this.getComponentDefaultPort('web-service-nginx')
        this.businessPageUrl = `https://${ip}:${port}`
        this.autoFilledFields.businessPage = true // 标记为已自动填充，使输入框变为只读
        this.updateBusinessPageUrl()
      } else {
        // 如果没有找到web-service-nginx组件，清空字段
        if (force && this.businessPageUrl) {
          this.businessPageUrl = ''
          this.autoFilledFields.businessPage = false // 取消只读状态
          this.updateBusinessPageUrl()
        }
      }
    },

    /**
     * 自动填充init组件所在的IP地址
     * 用于init访问地址
     * @param {Boolean} force - 是否强制更新，即使已有值
     */
    autoFillInitUrl(force = false) {
      // 如果已经有值且不是强制更新，不自动填充
      if (this.initUrl && !force) return

      // 查找包含init组件的服务器
      const initServer = this.findServerWithComponent('init')
      if (initServer) {
        const ip = initServer.IP地址
        const port = initServer.组件端口['init'] || this.getComponentDefaultPort('init')
        this.initUrl = `http://${ip}:${port}`
        this.autoFilledFields.initUrl = true // 标记为已自动填充，使输入框变为只读
        this.updateInitUrl()
      } else {
        // 如果没有找到init组件，清空字段
        if (force && this.initUrl) {
          this.initUrl = ''
          this.autoFilledFields.initUrl = false // 取消只读状态
          this.updateInitUrl()
        }
      }
    },

    /**
     * 自动填充kibana组件所在的IP地址
     * 用于kibana访问地址
     * @param {Boolean} force - 是否强制更新，即使已有值
     */
    autoFillKibanaUrl(force = false) {
      // 如果已经有值且不是强制更新，不自动填充
      if (this.kibanaUrl && !force) return

      // 查找包含kibana组件的服务器
      const kibanaServer = this.findServerWithComponent('kibana')
      if (kibanaServer) {
        const ip = kibanaServer.IP地址
        const port = kibanaServer.组件端口['kibana'] || this.getComponentDefaultPort('kibana')
        this.kibanaUrl = `http://${ip}:${port}`
        this.autoFilledFields.kibanaUrl = true // 标记为已自动填充，使输入框变为只读
        this.updateKibanaUrl()
      } else {
        // 如果没有找到kibana组件，清空字段
        if (force && this.kibanaUrl) {
          this.kibanaUrl = ''
          this.autoFilledFields.kibanaUrl = false // 取消只读状态
          this.updateKibanaUrl()
        }
      }
    },

    /**
     * 查找包含指定组件的服务器
     * @param {String} componentName - 组件名称
     * @returns {Object|null} - 包含该组件的服务器对象或null
     */
    findServerWithComponent(componentName) {
      if (!this.serverList || this.serverList.length === 0) {
        return null
      }

      // 查找包含指定组件的服务器
      for (const server of this.serverList) {
        if (server.部署应用 && server.部署应用.includes(componentName) && server.IP地址) {
          return server
        }
      }

      return null
    },

    /**
     * 获取组件的默认端口
     * @param {String} componentName - 组件名称
     * @returns {String} - 组件的默认端口
     */
    getComponentDefaultPort(componentName) {
      // 默认端口映射
      const defaultPorts = {
        'web-service': '9990',
        'web-service-nginx': '9991',
        'init': '8181',
        'kibana': '5601'
      }
      return defaultPorts[componentName] || '无'
    },

    // 更新方法
    updateBusinessPageUrl() {
      this.$emit('update:businessPage', this.businessPageUrl)
    },
    updateSuperAdminAccount() {
      // 标记为用户手动编辑（即使是清空也算编辑）
      this.userEditedFields.superAdminAccount = true
      console.log('用户编辑超级管理员账号:', this.superAdminAccount, '已标记为手动编辑')
      this.$emit('update:superAdmin', this.superAdminAccount)
    },
    updateSuperAdminPassword() {
      // 标记为用户手动编辑（即使是清空也算编辑）
      this.userEditedFields.superAdminPassword = true
      console.log('用户编辑超级管理员密码:', this.superAdminPassword, '已标记为手动编辑')
      this.$emit('update:superAdminPwd', this.superAdminPassword)
    },
    updateCustomerAdminAccount() {
      // 标记为用户手动编辑（即使是清空也算编辑）
      this.userEditedFields.customerAdminAccount = true
      console.log('用户编辑客户管理员账号:', this.customerAdminAccount, '已标记为手动编辑')
      this.$emit('update:customerAdmin', this.customerAdminAccount)
    },
    updateCustomerAdminPassword() {
      // 标记为用户手动编辑（即使是清空也算编辑）
      this.userEditedFields.customerAdminPassword = true
      console.log('用户编辑客户管理员密码:', this.customerAdminPassword, '已标记为手动编辑')
      this.$emit('update:customerAdminPwd', this.customerAdminPassword)
    },
    updateInitUrl() {
      this.$emit('update:initAddress', this.initUrl)
    },
    updateInitUsername() {
      // 标记为用户手动编辑
      this.userEditedFields.initUsername = true
      this.$emit('update:initUser', this.initUsername)
    },
    updateInitPassword() {
      // 标记为用户手动编辑
      this.userEditedFields.initPassword = true
      this.$emit('update:initPwd', this.initPassword)
    },
    updateKibanaUrl() {
      this.$emit('update:kibanaAddress', this.kibanaUrl)
    },
    updateKibanaAuth() {
      // 将用户名和密码组合成认证信息字符串
      const authInfo = this.kibanaUsername && this.kibanaPassword
        ? `${this.kibanaUsername}:${this.kibanaPassword}`
        : ''
      this.$emit('update:kibanaAuthInfo', authInfo)
    },

    /**
     * 复制文本到剪贴板
     */
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        // 可以添加一个简单的提示
        console.log('已复制到剪贴板:', text)
      } catch (err) {
        console.error('复制失败:', err)
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
      }
    },

    /**
     * 重置用户编辑状态（用于调试或特殊情况）
     */
    resetUserEditedFields() {
      console.log('重置用户编辑状态')
      this.userEditedFields = {
        superAdminAccount: false,
        superAdminPassword: false,
        customerAdminAccount: false,
        customerAdminPassword: false,
        initUsername: false,
        initPassword: false
      }
    }
  }
}
</script>

<style scoped>
/* ==================== 访问信息统一样式 ==================== */

/* 访问分组卡片样式 */
.access-group-card {
  border: 1px solid #dee2e6 !important;
  border-radius: 8px !important;
  margin-bottom: 16px !important;
  transition: all 0.2s ease;
}

.access-group-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 分组头部内容 */
.group-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.group-title {
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  margin: 0;
}

.group-title i {
  font-size: 1.1rem;
}

/* URL地址样式 */
.group-url {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.group-url-link {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
  word-break: break-all;
  font-size: 0.9rem;
}

.group-url-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

.group-url-copy {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 0.8rem;
}

.group-url-copy:hover {
  background: #e9ecef;
  color: #495057;
}

.group-url-input {
  flex: 1;
  min-width: 300px;
}

/* 认证项目样式 */
.access-item {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.access-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.auth-item .auth-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.auth-icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.auth-icon {
  color: #007bff;
  font-size: 1rem;
}

.auth-label {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.auth-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.auth-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.auth-field-label {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.auth-field-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-field-value input {
  flex: 1;
}

.password-toggle-btn {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 0.25rem;
}

.password-toggle-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

/* 密码文本样式 */
.password-text {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.password-dots {
  color: #6c757d;
  letter-spacing: 2px;
}

/* 编辑区域样式 */
.auth-edit-section {
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  margin: 0.75rem -1rem -1rem -1rem;
  padding: 0.75rem 1rem 1rem 1rem;
  border-radius: 0 0 8px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .group-header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .group-url {
    width: 100%;
  }

  .group-url-input {
    min-width: 100%;
  }

  .auth-edit-section {
    margin: 0.5rem -0.75rem -0.75rem -0.75rem;
    padding: 0.5rem 0.75rem 0.75rem 0.75rem;
  }
}
</style>
