from flask import request, jsonify, send_file, current_app
from app import db
from app.models.models import ExcelFile, HistoryRecord, FormSubmission, FormSubmissionEdit, TemplateVersion, FormType, get_beijing_time
from sqlalchemy import text
from app.models.form_field_config import FormFieldGroup, FormFieldConfig, FormConfigVersion, FieldTypeDefinition
from app.utils.cache_utils import cache
import traceback
from app.excel.utils import generate_bangbang_excel, generate_security_monitoring_excel
from app.excel.excel_importer import ExcelImporter
from app.excel.json_importer import JsonImporter
from app.utils.cache_utils import TemplateCacheManager
from openpyxl import load_workbook


def get_standard_form_config():
    """获取标准表单配置 - 与应用加固表单完全相同的字段配置"""
    return {
        'groups': [
            {
                'group_name': 'basic_info',
                'group_label': '基本信息',
                'group_description': '项目的基本信息',
                'display_order': 1,
                'default_field_count': 4,
                'fields': [
                    {
                        'field_name': 'companyName',
                        'field_label': '公司名称',
                        'field_type': 'text',
                        'is_required': True,
                        'placeholder': '请输入公司名称',
                        'field_description': '客户公司的名称',
                        'display_order': 1,
                        'validation_rules': {'minLength': 2, 'maxLength': 100}
                    },
                    {
                        'field_name': 'recordDate',
                        'field_label': '记录日期',
                        'field_type': 'date',
                        'is_required': True,
                        'placeholder': '',
                        'field_description': '表单记录的日期',
                        'is_auto_fill': True,
                        'display_order': 2
                    },
                    {
                        'field_name': 'editorName',
                        'field_label': '编辑人',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '编辑人',
                        'field_description': '表单编辑人员',
                        'is_auto_fill': True,
                        'display_order': 3,
                        'validation_rules': {'maxLength': 50}
                    },
                    {
                        'field_name': 'documentSuffix',
                        'field_label': '文档后缀',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '文档后缀',
                        'field_description': '文档类型标识',
                        'display_order': 4,
                        'validation_rules': {'maxLength': 50}
                    }
                ]
            },
            {
                'group_name': 'customer_info',
                'group_label': '客户信息',
                'group_description': '客户相关信息',
                'display_order': 2,
                'default_field_count': 3,
                'fields': [
                    {
                        'field_name': 'customerId',
                        'field_label': '客户标识',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '请输入客户标识',
                        'field_description': '客户的唯一标识符',
                        'display_order': 1,
                        'validation_rules': {'maxLength': 50}
                    },
                    {
                        'field_name': 'customer',
                        'field_label': '客户',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '请输入客户名称',
                        'field_description': '客户名称（应用加固表单专用）',
                        'display_order': 2,
                        'validation_rules': {'maxLength': 100}
                    },
                    {
                        'field_name': 'platformVersion',
                        'field_label': '部署的平台版本',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '请输入部署的平台版本',
                        'field_description': '当前部署的平台版本号',
                        'display_order': 3,
                        'validation_rules': {'maxLength': 50}
                    }
                ]
            },
            {
                'group_name': 'access_info',
                'group_label': '访问信息',
                'group_description': '系统访问相关信息',
                'display_order': 3,
                'default_field_count': 3,
                'fields': [
                    {
                        'field_name': 'platformUrl',
                        'field_label': '平台访问地址',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '请输入平台访问地址',
                        'field_description': '系统平台的访问地址',
                        'display_order': 1,
                        'validation_rules': {'maxLength': 200}
                    },
                    {
                        'field_name': 'adminInfo',
                        'field_label': '管理员信息',
                        'field_type': 'textarea',
                        'is_required': False,
                        'placeholder': '请输入管理员信息',
                        'field_description': '详细的管理员信息（应用加固表单专用）',
                        'display_order': 2,
                        'validation_rules': {'maxLength': 500}
                    },
                    {
                        'field_name': 'upgradeInfo',
                        'field_label': '升级平台地址',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '请输入升级平台地址',
                        'field_description': '平台升级地址信息',
                        'display_order': 3,
                        'validation_rules': {'maxLength': 200}
                    }
                ]
            },
            {
                'group_name': 'server_info',
                'group_label': '服务器信息',
                'group_description': '服务器部署信息',
                'display_order': 4,
                'default_field_count': 4,
                'fields': [
                    {
                        'field_name': 'serverIp',
                        'field_label': 'IP地址',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '请输入服务器IP地址',
                        'field_description': '服务器的IP地址',
                        'display_order': 1,
                        'validation_rules': {'maxLength': 50}
                    },
                    {
                        'field_name': 'hostname',
                        'field_label': '主机名',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '请输入主机名',
                        'field_description': '服务器的主机名',
                        'display_order': 2,
                        'validation_rules': {'maxLength': 100}
                    },
                    {
                        'field_name': 'operatingSystem',
                        'field_label': '操作系统',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '请输入操作系统信息',
                        'field_description': '服务器的操作系统版本',
                        'display_order': 3,
                        'validation_rules': {'maxLength': 100}
                    },
                    {
                        'field_name': 'deployedApps',
                        'field_label': '部署应用',
                        'field_type': 'textarea',
                        'is_required': False,
                        'placeholder': '请输入部署的应用信息',
                        'field_description': '服务器上部署的应用程序',
                        'display_order': 4,
                        'validation_rules': {'maxLength': 500}
                    }
                ]
            },
            {
                'group_name': 'maintenance_records',
                'group_label': '维护记录',
                'group_description': '系统维护记录',
                'display_order': 5,
                'default_field_count': 3,
                'fields': [
                    {
                        'field_name': 'maintenanceDate',
                        'field_label': '维护日期',
                        'field_type': 'date',
                        'is_required': False,
                        'placeholder': '',
                        'field_description': '系统维护的日期',
                        'display_order': 1
                    },
                    {
                        'field_name': 'maintenanceContent',
                        'field_label': '维护内容',
                        'field_type': 'textarea',
                        'is_required': False,
                        'placeholder': '请输入维护内容描述',
                        'field_description': '本次维护的具体内容',
                        'display_order': 2,
                        'validation_rules': {'maxLength': 1000}
                    },
                    {
                        'field_name': 'maintenanceBy',
                        'field_label': '维护人员',
                        'field_type': 'text',
                        'is_required': False,
                        'placeholder': '请输入维护人员姓名',
                        'field_description': '执行维护的人员',
                        'display_order': 3,
                        'validation_rules': {'maxLength': 50}
                    }
                ]
            }
        ]
    }
from werkzeug.utils import secure_filename
from app.excel import bp
from app.auth.decorators import permission_required
import os
import shutil
import tempfile
import json
import uuid
from datetime import datetime, timedelta
import traceback
from urllib.parse import quote


@bp.route('/', methods=['GET'])
def excel_index():
    """Excel模块根路由"""
    return jsonify({
        'status': 'success',
        'message': 'Excel模块API正常运行',
        'endpoints': [
            '/import_excel/preview',
            '/import_excel/confirm',
            '/import_excel/templates'
        ]
    })


def get_active_template_path(form_type):
    """
    根据表单类型获取活动模板的文件路径（带缓存）
    """
    try:
        # 尝试从缓存获取模板路径
        cached_path = TemplateCacheManager.get_template_path(form_type)
        if cached_path is not None:
            # 验证缓存的文件是否仍然存在
            if os.path.exists(cached_path):
                current_app.logger.debug(f"模板路径缓存命中: form_type={form_type}, path={cached_path}")
                return cached_path
            else:
                # 文件不存在，清除缓存
                current_app.logger.warning(f"缓存的模板文件不存在，清除缓存: {cached_path}")
                TemplateCacheManager.clear_template_cache(form_type)

        # 缓存未命中或文件不存在，从数据库查询
        current_app.logger.debug(f"模板路径缓存未命中，从数据库查询: form_type={form_type}")

        # 查找指定表单类型的活动模板
        active_template = TemplateVersion.query.filter_by(
            form_type=form_type,
            is_active=True
        ).first()

        template_path = None

        if active_template:
            template_path = os.path.join(
                current_app.config['EXCEL_TEMPLATES_FOLDER'],
                active_template.filename
            )

            # 检查文件是否存在
            if os.path.exists(template_path):
                current_app.logger.info(f"找到 {form_type} 的活动模板: {active_template.filename}")
                # 缓存模板路径
                TemplateCacheManager.cache_template_path(form_type, template_path)
                return template_path
            else:
                current_app.logger.warning(f"活动模板文件不存在: {template_path}")

        # 如果没有找到活动模板，尝试查找默认模板文件
        default_template_filename = f"{form_type}-运维信息登记模板.xlsx"
        default_template_path = os.path.join(
            current_app.config['EXCEL_TEMPLATES_FOLDER'],
            default_template_filename
        )

        if os.path.exists(default_template_path):
            current_app.logger.info(f"使用默认模板文件: {default_template_filename}")
            # 缓存默认模板路径
            TemplateCacheManager.cache_template_path(form_type, default_template_path)
            return default_template_path

        # 最后的回退选项：使用安全测评模板
        fallback_template_path = os.path.join(
            current_app.config['EXCEL_TEMPLATES_FOLDER'],
            '安全测评-运维信息登记模板.xlsx'
        )

        if os.path.exists(fallback_template_path):
            current_app.logger.warning(f"使用安全测评模板作为 {form_type} 的回退模板")
            # 缓存回退模板路径（较短的缓存时间）
            TemplateCacheManager.cache_template_path(form_type, fallback_template_path)
            return fallback_template_path

        current_app.logger.error(f"找不到 {form_type} 的任何可用模板")
        return None

    except Exception as e:
        current_app.logger.error(f"获取活动模板路径失败: {str(e)}")
        return None





@bp.route('/history/<int:file_id>', methods=['GET'])
def file_history(file_id):
    """
    获取文件历史记录
    """
    try:
        excel_file = ExcelFile.query.get_or_404(file_id)
        history_records = [record.to_dict() for record in excel_file.history_records]

        return jsonify({
            'status': 'success',
            'data': {
                'file': excel_file.to_dict(),
                'history': history_records
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取文件历史记录失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取文件历史记录失败: {str(e)}'
        }), 500



@bp.route('/delete/<int:file_id>', methods=['DELETE'])
def delete_file(file_id):
    try:
        excel_file = ExcelFile.query.get_or_404(file_id)

        # 先删除关联的历史记录
        HistoryRecord.query.filter_by(excel_file_id=file_id).delete()

        # 删除文件
        file_path = os.path.join(current_app.config['EXCEL_GENERATED_FOLDER'], excel_file.filename)
        if os.path.exists(file_path):
            os.remove(file_path)

        # 删除数据库记录
        db.session.delete(excel_file)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '文件删除成功'
        })
    except Exception as e:
        db.session.rollback()  # 发生错误时回滚事务
        current_app.logger.error(f"删除文件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'删除文件失败: {str(e)}'
        }), 500

@bp.route('/delete_multiple', methods=['POST'])
def delete_multiple_files():
    """
    批量删除Excel文件
    """
    try:
        if not request.is_json:
            return jsonify({
                'status': 'error',
                'message': '请求体必须为JSON格式'
            }), 400

        data = request.get_json()
        if data is None or 'file_ids' not in data:
            return jsonify({
                'status': 'error',
                'message': '请求必须包含file_ids字段'
            }), 400

        file_ids = data['file_ids']
        if not isinstance(file_ids, list):
            return jsonify({
                'status': 'error',
                'message': 'file_ids必须是一个数组'
            }), 400

        # 记录成功和失败的文件ID
        success_ids = []
        failed_ids = []

        for file_id in file_ids:
            try:
                excel_file = ExcelFile.query.get(file_id)
                if excel_file:
                    # 先删除关联的历史记录
                    HistoryRecord.query.filter_by(excel_file_id=file_id).delete()

                    # 删除文件
                    file_path = os.path.join(current_app.config['EXCEL_GENERATED_FOLDER'], excel_file.filename)
                    if os.path.exists(file_path):
                        os.remove(file_path)

                    # 删除数据库记录
                    db.session.delete(excel_file)
                    success_ids.append(file_id)
                else:
                    failed_ids.append(file_id)
            except Exception as e:
                current_app.logger.error(f"删除文件ID {file_id} 失败: {str(e)}")
                current_app.logger.error(traceback.format_exc())
                failed_ids.append(file_id)

        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"提交事务失败: {str(e)}")
            current_app.logger.error(traceback.format_exc())

        if failed_ids:
            return jsonify({
                'status': 'partial',
                'message': f'部分文件删除成功，{len(success_ids)}个成功，{len(failed_ids)}个失败',
                'data': {
                    'success_ids': success_ids,
                    'failed_ids': failed_ids
                }
            })
        else:
            return jsonify({
                'status': 'success',
                'message': f'所有文件删除成功，共{len(success_ids)}个文件',
                'data': {
                    'success_ids': success_ids
                }
            })
    except Exception as e:
        current_app.logger.error(f"批量删除文件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'批量删除文件失败: {str(e)}'
        }), 500

@bp.route('/edit/<int:file_id>', methods=['PUT'])
def edit_file(file_id):
    try:
        if not request.is_json:
            return jsonify({
                'status': 'error',
                'message': '请求体必须为JSON格式'
            }), 400

        data = request.get_json()
        if data is None:
            return jsonify({
                'status': 'error',
                'message': '无法解析JSON数据'
            }), 400

        excel_file = ExcelFile.query.get_or_404(file_id)

        if 'original_filename' in data:
            excel_file.original_filename = data['original_filename']

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '文件信息更新成功'
        })
    except Exception as e:
        current_app.logger.error(f"更新文件信息失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'更新文件信息失败: {str(e)}'
        }), 500

@bp.route('/update_content/<int:file_id>', methods=['PUT'])
def update_file_content(file_id):
    """
    更新Excel文件内容，保留原文件名和ID
    """
    try:
        if not request.is_json:
            return jsonify({
                'status': 'error',
                'message': '请求体必须为JSON格式'
            }), 400

        data = request.get_json()
        if data is None:
            return jsonify({
                'status': 'error',
                'message': '无法解析JSON数据'
            }), 400

        # 获取文件信息
        excel_file = ExcelFile.query.get_or_404(file_id)
        file_path = os.path.join(current_app.config['EXCEL_GENERATED_FOLDER'], excel_file.filename)

        if not os.path.exists(file_path):
            return jsonify({
                'status': 'error',
                'message': '文件不存在'
            }), 404

        # 创建备份目录
        backup_dir = os.path.join(current_app.config['EXCEL_FOLDER'], 'backups')
        os.makedirs(backup_dir, exist_ok=True)

        # 备份原文件，使用原始文件名和时间戳
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        backup_filename = f"{os.path.splitext(excel_file.filename)[0]}_backup_{timestamp}.xlsx"
        backup_path = os.path.join(backup_dir, backup_filename)
        shutil.copy2(file_path, backup_path)

        # 记录备份信息到日志
        current_app.logger.info(f"已创建文件备份: {backup_path}")

        # 更新Excel文件内容
        try:
            # 读取原始模板
            wb = load_workbook(file_path)
            sheet = wb.active

            # 确保标题行正确（xx-运维文档-xx格式）
            # 检查标题行是否完整
            title_cell = sheet.cell(row=1, column=1).value
            if title_cell and isinstance(title_cell, str):
                # 如果标题行只包含修改时间而没有公司名称
                if '修改时间' in title_cell and '-运维文档' not in title_cell:
                    # 从原始文件名或数据中获取公司名称
                    company_name = data.get('公司名称', '')
                    if not company_name and '-运维文档' in excel_file.original_filename:
                        company_name = excel_file.original_filename.split('-运维文档')[0].strip()

                    if company_name:
                        # 恢复完整标题
                        current_date = datetime.now().strftime('%Y/%m/%d')
                        sheet.cell(row=1, column=1).value = f"{company_name}-运维文档（修改时间：{current_date}）"
                        current_app.logger.info(f"修复标题行: {sheet.cell(row=1, column=1).value}")

            # 更新部署包版本
            if '部署包版本' in data:
                sheet.cell(row=4, column=2).value = data['部署包版本']

            # 更新管理员页面信息
            admin_info = []
            if '管理员页面IP' in data:
                admin_info.append(data['管理员页面IP'])

            # 添加账号信息
            account_info = []
            if '超级管理员账号' in data and '超级管理员密码' in data:
                account_info.append(f"超级管理员账号：{data['超级管理员账号']} 密码：{data['超级管理员密码']}")
            if '管理员账号' in data and '管理员密码' in data:
                account_info.append(f"管理员账号：{data['管理员账号']} 密码：{data['管理员密码']}")
            if '用户账号' in data and '用户密码' in data:
                account_info.append(f"用户账号：{data['用户账号']} 密码：{data['用户密码']}")

            # 合并管理员页面信息
            if admin_info or account_info:
                sheet.cell(row=5, column=2).value = "\n".join(admin_info + account_info)

            # 更新用户页面IP
            if '用户页面IP' in data:
                sheet.cell(row=6, column=2).value = data['用户页面IP']

            # 更新升级页面IP
            if '升级页面IP' in data:
                sheet.cell(row=7, column=2).value = data['升级页面IP']

            # 更新对外服务端口
            if '对外服务端口' in data:
                sheet.cell(row=8, column=2).value = data['对外服务端口']

            # 更新产品功能
            if '产品功能' in data:
                sheet.cell(row=10, column=2).value = data['产品功能']

            # 更新授权功能
            if '授权功能' in data:
                sheet.cell(row=11, column=2).value = data['授权功能']

            # 更新授权期限
            if '授权开始日期' in data and '授权结束日期' in data:
                sheet.cell(row=12, column=2).value = f"{data['授权开始日期']} - {data['授权结束日期']}"

            # 保存文件
            wb.save(file_path)

            # 不更新文件名，保持原始文件名不变
            # 标题格式（xx-运维文档-xx）是不能修改的

            # 生成修改内容的详细描述
            changes = []

            # 读取原始Excel文件内容（用于比较）
            original_wb = load_workbook(backup_path)
            original_sheet = original_wb.active

            # 检查各个字段是否有变化 - 只记录内容变更，不记录文件名变更

            # 检查部署包版本
            if '部署包版本' in data and data['部署包版本'] != original_sheet.cell(row=4, column=2).value:
                changes.append(f"部署包版本: '{original_sheet.cell(row=4, column=2).value}' -> '{data['部署包版本']}'")

            # 检查管理员页面IP
            original_admin_ip = ""
            admin_cell_value = original_sheet.cell(row=5, column=2).value
            if admin_cell_value and '\n' in admin_cell_value:
                original_admin_ip = admin_cell_value.split('\n')[0].strip()
            else:
                original_admin_ip = admin_cell_value or ""

            if '管理员页面IP' in data and data['管理员页面IP'] != original_admin_ip:
                changes.append(f"管理员页面IP: '{original_admin_ip}' -> '{data['管理员页面IP']}'")

            # 检查超级管理员账号和密码
            original_super_admin = ""
            original_super_pwd = ""
            if admin_cell_value and '超级管理员账号：' in admin_cell_value:
                super_admin_line = [line for line in admin_cell_value.split('\n') if '超级管理员账号：' in line]
                if super_admin_line:
                    parts = super_admin_line[0].split('超级管理员账号：')[1].split('密码：')
                    if len(parts) > 0:
                        original_super_admin = parts[0].strip()
                    if len(parts) > 1:
                        original_super_pwd = parts[1].strip()

            if '超级管理员账号' in data and data['超级管理员账号'] != original_super_admin:
                changes.append(f"超级管理员账号: '{original_super_admin}' -> '{data['超级管理员账号']}'")

            if '超级管理员密码' in data and data['超级管理员密码'] != original_super_pwd:
                changes.append(f"超级管理员密码: '{original_super_pwd}' -> '{data['超级管理员密码']}'")

            # 检查管理员账号和密码
            original_admin = ""
            original_admin_pwd = ""
            if admin_cell_value and '管理员账号：' in admin_cell_value:
                admin_line = [line for line in admin_cell_value.split('\n') if '管理员账号：' in line and '超级管理员账号：' not in line]
                if admin_line:
                    parts = admin_line[0].split('管理员账号：')[1].split('密码：')
                    if len(parts) > 0:
                        original_admin = parts[0].strip()
                    if len(parts) > 1:
                        original_admin_pwd = parts[1].strip()

            if '管理员账号' in data and data['管理员账号'] != original_admin:
                changes.append(f"管理员账号: '{original_admin}' -> '{data['管理员账号']}'")

            if '管理员密码' in data and data['管理员密码'] != original_admin_pwd:
                changes.append(f"管理员密码: '{original_admin_pwd}' -> '{data['管理员密码']}'")

            # 检查用户账号和密码
            original_user = ""
            original_user_pwd = ""
            if admin_cell_value and '用户账号：' in admin_cell_value:
                user_line = [line for line in admin_cell_value.split('\n') if '用户账号：' in line]
                if user_line:
                    parts = user_line[0].split('用户账号：')[1].split('密码：')
                    if len(parts) > 0:
                        original_user = parts[0].strip()
                    if len(parts) > 1:
                        original_user_pwd = parts[1].strip()

            if '用户账号' in data and data['用户账号'] != original_user:
                changes.append(f"用户账号: '{original_user}' -> '{data['用户账号']}'")

            if '用户密码' in data and data['用户密码'] != original_user_pwd:
                changes.append(f"用户密码: '{original_user_pwd}' -> '{data['用户密码']}'")

            # 检查用户页面IP
            if '用户页面IP' in data and data['用户页面IP'] != original_sheet.cell(row=6, column=2).value:
                changes.append(f"用户页面IP: '{original_sheet.cell(row=6, column=2).value}' -> '{data['用户页面IP']}'")

            # 检查升级页面IP
            if '升级页面IP' in data and data['升级页面IP'] != original_sheet.cell(row=7, column=2).value:
                changes.append(f"升级页面IP: '{original_sheet.cell(row=7, column=2).value}' -> '{data['升级页面IP']}'")

            # 检查对外服务端口
            if '对外服务端口' in data and data['对外服务端口'] != original_sheet.cell(row=8, column=2).value:
                changes.append(f"对外服务端口: '{original_sheet.cell(row=8, column=2).value}' -> '{data['对外服务端口']}'")

            # 检查产品功能
            original_product_func = original_sheet.cell(row=10, column=2).value or ""
            if '产品功能' in data and data['产品功能'] != original_product_func:
                # 如果内容较长，只显示简短的摘要
                if len(original_product_func) > 50 or len(data['产品功能']) > 50:
                    changes.append(f"产品功能: 已更新")
                else:
                    changes.append(f"产品功能: '{original_product_func}' -> '{data['产品功能']}'")

            # 检查授权功能
            original_auth_func = original_sheet.cell(row=11, column=2).value or ""
            if '授权功能' in data and data['授权功能'] != original_auth_func:
                # 如果内容较长，只显示简短的摘要
                if len(original_auth_func) > 50 or len(data['授权功能']) > 50:
                    changes.append(f"授权功能: 已更新")
                else:
                    changes.append(f"授权功能: '{original_auth_func}' -> '{data['授权功能']}'")

            # 检查授权期限
            original_auth_period = original_sheet.cell(row=12, column=2).value or ""
            new_auth_period = f"{data.get('授权开始日期', '')} - {data.get('授权结束日期', '')}"
            if '授权开始日期' in data and '授权结束日期' in data and new_auth_period != original_auth_period:
                changes.append(f"授权期限: '{original_auth_period}' -> '{new_auth_period}'")

            # 不记录公司名称和文档后缀的变化，因为标题行不会被修改

            # 生成描述文本
            change_description = "无修改"
            if changes:
                change_description = "修改了以下内容：\n" + "\n".join(changes)

            # 添加历史记录
            history = HistoryRecord(
                excel_file=excel_file,
                action='update',
                description=f"{data.get('维护人员', '未知用户')}在{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}进行了编辑。\n{change_description}",
                user=data.get('维护人员', '未知用户')
            )
            db.session.add(history)
            db.session.commit()

            return jsonify({
                'status': 'success',
                'message': '文件内容更新成功',
                'data': {
                    'file_id': excel_file.id,
                    'filename': excel_file.filename,
                    'original_filename': excel_file.original_filename
                }
            })
        except Exception as e:
            # 如果更新失败，恢复备份
            if os.path.exists(backup_path):
                current_app.logger.warning(f"更新失败，正在恢复备份: {backup_path} -> {file_path}")
                shutil.copy2(backup_path, file_path)
                current_app.logger.info(f"备份恢复成功")
            else:
                current_app.logger.error(f"无法恢复备份，备份文件不存在: {backup_path}")
            raise e

    except Exception as e:
        current_app.logger.error(f"更新文件内容失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'更新文件内容失败: {str(e)}'
        }), 500

@bp.route('/backups/<int:file_id>', methods=['GET'])
def get_file_backups(file_id):
    """
    获取文件的备份列表
    """
    try:
        # 获取文件信息
        excel_file = ExcelFile.query.get_or_404(file_id)

        # 获取备份目录
        backup_dir = os.path.join(current_app.config['EXCEL_FOLDER'], 'backups')
        if not os.path.exists(backup_dir):
            return jsonify({
                'status': 'success',
                'data': {
                    'backups': []
                }
            })

        # 查找该文件的所有备份
        base_filename = os.path.splitext(excel_file.filename)[0]
        backups = []

        for filename in os.listdir(backup_dir):
            if filename.startswith(f"{base_filename}_backup_") and filename.endswith(".xlsx"):
                # 从文件名中提取时间戳
                try:
                    timestamp_str = filename.replace(f"{base_filename}_backup_", "").replace(".xlsx", "")
                    timestamp = datetime.strptime(timestamp_str, '%Y%m%d%H%M%S')

                    backups.append({
                        'filename': filename,
                        'path': os.path.join(backup_dir, filename),
                        'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                        'size': os.path.getsize(os.path.join(backup_dir, filename))
                    })
                except Exception as e:
                    current_app.logger.error(f"解析备份文件名失败: {filename}, 错误: {str(e)}")

        # 按时间戳排序，最新的在前
        backups.sort(key=lambda x: x['timestamp'], reverse=True)

        return jsonify({
            'status': 'success',
            'data': {
                'backups': backups
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取文件备份失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取文件备份失败: {str(e)}'
        }), 500



@bp.route('/download_backup', methods=['GET'])
def download_backup():
    """
    下载备份文件
    """
    try:
        backup_path = request.args.get('path')
        if not backup_path:
            return jsonify({
                'status': 'error',
                'message': '缺少备份文件路径参数'
            }), 400

        # 安全检查：确保路径在备份目录内
        backup_dir = os.path.join(current_app.config['EXCEL_FOLDER'], 'backups')
        if not os.path.normpath(backup_path).startswith(os.path.normpath(backup_dir)):
            return jsonify({
                'status': 'error',
                'message': '无效的备份文件路径'
            }), 403

        if not os.path.exists(backup_path):
            return jsonify({
                'status': 'error',
                'message': '备份文件不存在'
            }), 404

        # 从路径中提取文件名
        filename = os.path.basename(backup_path)

        # 处理中文文件名编码
        response = send_file(
            backup_path,
            as_attachment=True,
            download_name=filename
        )

        # 设置正确的Content-Disposition头
        encoded_filename = quote(filename.encode('utf-8'))
        response.headers['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'{encoded_filename}'

        return response
    except Exception as e:
        current_app.logger.error(f"下载备份文件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'下载备份文件失败: {str(e)}'
        }), 500

@bp.route('/download/<int:file_id>', methods=['GET'])
def download_file(file_id):
    """
    下载Excel文件
    """
    try:
        excel_file = ExcelFile.query.get_or_404(file_id)
        file_path = os.path.join(current_app.config['EXCEL_GENERATED_FOLDER'], excel_file.filename)

        if not os.path.exists(file_path):
            return jsonify({
                'status': 'error',
                'message': '文件不存在'
            }), 404

        # 处理中文文件名编码
        response = send_file(
            file_path,
            as_attachment=True,
            download_name=excel_file.original_filename
        )

        # 设置正确的Content-Disposition头
        encoded_filename = quote(excel_file.original_filename.encode('utf-8'))
        response.headers['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'{encoded_filename}'

        return response
    except Exception as e:
        current_app.logger.error(f"下载文件失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'下载文件失败: {str(e)}'
        }), 500

@bp.route('/fill_sheet', methods=['POST'])
def fill_sheet():
    """
    填写并生成Excel表格
    """
    current_app.logger.debug("进入 fill_sheet 路由")
    try:
        # 获取JSON数据
        data = request.json
        current_app.logger.debug(f"接收到的数据: {data}")

        # 检查是否需要强制覆盖
        force_overwrite = data.pop('force_overwrite', False)

        # 根据模板类型确定必要字段
        doc_suffix = data.get('文档后缀', '安全测评')
        if doc_suffix == '安全监测':
            required_fields = ['公司名称', '前端版本', '后端版本', '记录日期']
        elif doc_suffix == '应用加固':
            required_fields = ['公司名称', '客户', '记录日期']
        elif doc_suffix == '安全测评':
            # 安全测评表单的必填字段
            required_fields = ['公司名称', '部署包版本', '管理员页面IP', '用户页面IP', '记录日期']
        else:
            # 通用表单（包括API平台等新表单类型）的必填字段
            required_fields = ['公司名称', '版本信息', '管理员页面IP', '用户页面IP', '记录日期']

        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            current_app.logger.error(f"缺少必要字段: {missing_fields}")
            return jsonify({
                'status': 'error',
                'message': f'缺少必要字段: {", ".join(missing_fields)}'
            }), 400

        # 预先计算文件名，用于检查是否存在同名文件
        company_name = data.get('公司名称', '')
        doc_suffix = data.get('文档后缀', '应用加固')
        record_date = data.get('记录日期', datetime.now().strftime('%Y%m%d'))

        # 处理日期格式
        if isinstance(record_date, str):
            if '-' in record_date:
                record_date = record_date.replace('-', '')[:8]
            elif '/' in record_date:
                record_date = record_date.replace('/', '')[:8]

        # 生成预期的文件名
        custom_alias = data.get('custom_alias', '')
        if custom_alias:
            expected_filename = f"{company_name}-运维文档-{doc_suffix}-{custom_alias}_{record_date}.xlsx"
        else:
            expected_filename = f"{company_name}-运维文档-{doc_suffix}_{record_date}.xlsx"
        expected_filename = expected_filename.replace(':', '_').replace('/', '_').replace('\\', '_')

        # 如果有自定义别名，将别名合并到公司名称中
        final_company_name = company_name
        if custom_alias:
            final_company_name = f"{company_name}-{custom_alias}"
            current_app.logger.info(f"检测到自定义别名 '{custom_alias}'，最终公司名称: {final_company_name}")

        # 检查数据库中是否已存在相似的表单提交记录
        current_app.logger.info(f"开始检查重复记录: 公司={final_company_name}, 类型={doc_suffix}, 强制覆盖={force_overwrite}")

        existing_submissions = FormSubmission.query.filter_by(
            company_name=final_company_name,
            form_type=doc_suffix
        ).order_by(FormSubmission.created_at.desc()).all()

        current_app.logger.info(f"找到 {len(existing_submissions)} 条匹配记录")

        if existing_submissions and not force_overwrite:
            current_app.logger.info(f"发现相似的表单提交记录: 公司={final_company_name}, 类型={doc_suffix}")

            # 准备现有记录的信息
            existing_records = []
            for submission in existing_submissions[:5]:  # 最多显示5条记录
                existing_records.append({
                    'id': submission.id,
                    'company_name': submission.company_name,
                    'form_type': submission.form_type,
                    'record_date': submission.record_date.strftime('%Y-%m-%d') if submission.record_date else '',
                    'created_at': submission.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'created_by': submission.created_by,
                    'excel_filename': submission.excel_filename
                })

            current_app.logger.info(f"返回重复检查响应，记录数量: {len(existing_records)}")

            return jsonify({
                'status': 'duplicate_submission',
                'message': f'发现相同公司和表单类型的历史记录',
                'data': {
                    'company_name': final_company_name,
                    'form_type': doc_suffix,
                    'expected_filename': expected_filename,
                    'existing_records': existing_records,
                    'total_count': len(existing_submissions)
                }
            }), 409  # 409 Conflict

        # 如果需要覆盖，处理现有的表单提交记录
        if force_overwrite and existing_submissions:
            current_app.logger.info(f"强制覆盖模式，将更新现有的表单提交记录")
            # 选择最新的记录进行更新
            existing_submission = existing_submissions[0]  # 已经按创建时间倒序排列

        # 检查用户是否选择了特定的模板
        selected_template_id = data.get('selectedTemplateId')
        template_path = None

        if selected_template_id:
            # 用户选择了特定模板，根据模板ID获取模板路径
            current_app.logger.info(f"用户选择了模板ID: {selected_template_id}")
            try:
                from app.models.models import TemplateVersion
                selected_template = TemplateVersion.query.get(selected_template_id)
                if selected_template:
                    template_path = os.path.join(
                        current_app.config['EXCEL_TEMPLATES_FOLDER'],
                        selected_template.filename
                    )
                    if os.path.exists(template_path):
                        current_app.logger.info(f"使用用户选择的模板: {selected_template.filename}")
                    else:
                        current_app.logger.warning(f"用户选择的模板文件不存在: {template_path}")
                        template_path = None
                else:
                    current_app.logger.warning(f"找不到模板ID为 {selected_template_id} 的模板")
            except Exception as e:
                current_app.logger.error(f"获取用户选择的模板失败: {str(e)}")
                template_path = None

        # 如果用户没有选择模板或选择的模板无效，使用默认的活动模板
        if not template_path:
            template_path = get_active_template_path(doc_suffix)
            current_app.logger.info(f"使用默认活动模板: {template_path}")

        if not template_path:
            current_app.logger.error(f"找不到 {doc_suffix} 的可用模板")
            return jsonify({
                'status': 'error',
                'message': f'找不到 {doc_suffix} 的可用模板，请在模板管理中配置或选择其他模板'
            }), 500

        current_app.logger.info(f"最终使用模板: {template_path} 生成 {doc_suffix} 表单")

        # 根据表单类型选择生成函数
        if doc_suffix == '安全监测':
            filename = generate_security_monitoring_excel(data, template_path)
        else:
            # 其他表单类型都使用通用生成函数
            filename = generate_bangbang_excel(data, template_path)

        if not filename:
            current_app.logger.error("生成Excel文件失败")
            return jsonify({
                'status': 'error',
                'message': '生成Excel文件失败'
            }), 500

        current_app.logger.debug(f"生成的文件名: {filename}")

        # 记录到数据库
        try:
            if force_overwrite and existing_submissions:
                # 强制覆盖模式：更新现有记录
                existing_submission = existing_submissions[0]  # 最新的记录

                # 保存编辑前的数据
                old_form_data = existing_submission.get_form_data()

                # 创建编辑记录
                from app.models.models import FormSubmissionEdit
                edit_record = FormSubmissionEdit(
                    submission_id=existing_submission.id,
                    edit_type='overwrite',
                    edit_description=f"用户 {data.get('编辑人', data.get('记录人', '未知用户'))} 强制覆盖了表单数据",
                    edited_by=data.get('编辑人', data.get('记录人', '未知用户')),
                    edit_reason='强制覆盖提交',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )

                # 设置编辑前后的数据
                edit_record.set_old_data(old_form_data)
                edit_record.set_new_data(data)

                # 计算变更字段
                edit_record.calculate_changes(old_form_data, data)

                # 更新现有记录
                existing_submission.set_form_data(data)
                existing_submission.excel_filename = filename
                existing_submission.excel_filepath = os.path.join(current_app.config['EXCEL_GENERATED_FOLDER'], filename)
                existing_submission.updated_at = datetime.now()
                existing_submission.calculate_statistics()

                # 创建新的Excel文件记录
                excel_file = ExcelFile(
                    filename=filename,
                    original_filename=filename
                )
                db.session.add(excel_file)

                # 添加覆盖历史记录
                history = HistoryRecord(
                    excel_file=excel_file,
                    action='overwrite',
                    description=f"{data.get('编辑人', data.get('记录人', '未知用户'))}在{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}覆盖了文件",
                    user=data.get('编辑人', data.get('记录人', '未知用户'))
                )
                db.session.add(history)
                db.session.add(edit_record)

                form_submission = existing_submission
                current_app.logger.info(f"强制覆盖模式：已更新现有记录 ID={existing_submission.id}")
            else:
                # 正常模式：创建新记录
                excel_file = ExcelFile(
                    filename=filename,
                    original_filename=filename
                )
                db.session.add(excel_file)

                # 添加创建历史记录
                history = HistoryRecord(
                    excel_file=excel_file,
                    action='create',
                    description=f"{data.get('编辑人', data.get('记录人', '未知用户'))}在{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}创建了文件",
                    user=data.get('编辑人', data.get('记录人', '未知用户'))
                )
                db.session.add(history)

                # 创建表单提交记录
                form_submission = FormSubmission(
                    company_name=final_company_name,
                    form_type=doc_suffix,
                    record_date=datetime.strptime(record_date, '%Y%m%d').date() if len(record_date) == 8 else datetime.now().date(),
                    excel_filename=filename,
                    excel_filepath=os.path.join(current_app.config['EXCEL_GENERATED_FOLDER'], filename),
                    created_by=data.get('编辑人', data.get('记录人', '未知用户')),
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', ''),
                    status='success'
                )

                # 设置表单数据
                form_submission.set_form_data(data)

                # 计算统计信息
                form_submission.calculate_statistics()

                db.session.add(form_submission)
                current_app.logger.info("正常模式：已创建新的表单提交记录")

            db.session.commit()
            current_app.logger.debug("文件信息和表单提交记录已保存到数据库")
        except Exception as e:
            current_app.logger.error(f"保存到数据库失败: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            # 继续执行，即使数据库保存失败

        # 返回文件ID，前端可以使用这个ID来下载文件
        return jsonify({
            'status': 'success',
            'message': '文件生成成功',
            'data': {
                'file_id': excel_file.id,
                'filename': filename
            }
        })
    except Exception as e:
        current_app.logger.error(f"处理请求出错: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'处理请求出错: {str(e)}'
        }), 500




# ==================== 表单提交历史记录相关API ====================

@bp.route('/form_submissions', methods=['GET'])
def get_form_submissions():
    """
    获取表单提交历史记录列表，支持分页和筛选
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        company_name = request.args.get('company_name', '').strip()
        form_type = request.args.get('form_type', '').strip()
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()

        # 验证每页显示数量
        if per_page not in [10, 20, 50, 100]:
            per_page = 20

        # 构建查询
        query = FormSubmission.query

        # 公司名称筛选
        if company_name:
            query = query.filter(FormSubmission.company_name.contains(company_name))

        # 表单类型筛选
        if form_type:
            query = query.filter(FormSubmission.form_type == form_type)

        # 日期范围筛选
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(FormSubmission.record_date >= start_date_obj)
            except ValueError:
                pass

        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(FormSubmission.record_date <= end_date_obj)
            except ValueError:
                pass

        # 按创建时间倒序排列
        query = query.order_by(FormSubmission.created_at.desc())

        # 分页查询
        total = query.count()
        submissions = query.limit(per_page).offset((page - 1) * per_page).all()

        # 计算总页数
        total_pages = (total + per_page - 1) // per_page

        # 转换为字典格式
        submissions_data = [submission.to_summary_dict() for submission in submissions]

        return jsonify({
            'status': 'success',
            'data': {
                'submissions': submissions_data,
                'pagination': {
                    'total': total,
                    'per_page': per_page,
                    'current_page': page,
                    'total_pages': total_pages
                }
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取表单提交记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取表单提交记录失败: {str(e)}'
        }), 500


@bp.route('/form_submissions/<int:submission_id>', methods=['GET'])
def get_form_submission_detail(submission_id):
    """
    获取表单提交记录详情
    """
    try:
        submission = FormSubmission.query.get_or_404(submission_id)

        return jsonify({
            'status': 'success',
            'data': submission.to_dict(include_form_data=True)
        })
    except Exception as e:
        current_app.logger.error(f"获取表单提交记录详情失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取表单提交记录详情失败: {str(e)}'
        }), 500


@bp.route('/form_submissions/<int:submission_id>/regenerate', methods=['POST'])
def regenerate_excel_from_submission(submission_id):
    """
    根据历史表单提交记录重新生成Excel文件
    支持指定模板ID或使用默认活动模板
    """
    try:
        submission = FormSubmission.query.get_or_404(submission_id)
        form_data = submission.get_form_data()

        if not form_data:
            return jsonify({
                'status': 'error',
                'message': '表单数据为空，无法重新生成'
            }), 400

        # 获取请求参数
        request_data = request.get_json() or {}
        template_id = request_data.get('template_id')
        template_type = request_data.get('template_type')
        specified_filename = request_data.get('filename')

        # 确保表单数据中的公司名称与数据库记录一致
        form_data['公司名称'] = submission.company_name

        # 对于应用加固表单，还需要设置客户字段
        if submission.form_type == '应用加固':
            form_data['客户'] = submission.company_name

        # 调试信息
        current_app.logger.info(f"重新生成Excel - 数据库公司名称: '{submission.company_name}'")
        current_app.logger.info(f"重新生成Excel - 原始表单类型: '{submission.form_type}'")
        current_app.logger.info(f"重新生成Excel - 指定模板ID: {template_id}")
        current_app.logger.info(f"重新生成Excel - 指定模板类型: '{template_type}'")
        current_app.logger.info(f"重新生成Excel - 指定文件名: '{specified_filename}'")

        # 确定使用的模板
        if template_id:
            # 使用指定的模板ID
            from app.models.models import TemplateVersion
            template = TemplateVersion.query.get(template_id)
            if not template:
                return jsonify({
                    'status': 'error',
                    'message': f'指定的模板ID {template_id} 不存在'
                }), 400

            template_path = os.path.join(current_app.config['EXCEL_TEMPLATES_FOLDER'], template.filename)
            doc_suffix = template.template_type
            current_app.logger.info(f"重新生成Excel - 使用指定模板: {template.filename} (类型: {doc_suffix})")
        else:
            # 使用默认活动模板
            doc_suffix = template_type or submission.form_type
            template_path = get_active_template_path(doc_suffix)
            current_app.logger.info(f"重新生成Excel - 使用默认活动模板: {template_path} (类型: {doc_suffix})")

        if not template_path:
            current_app.logger.error(f"重新生成: 找不到 {doc_suffix} 的活动模板")
            return jsonify({
                'status': 'error',
                'message': f'找不到 {doc_suffix} 的活动模板，请在模板管理中配置'
            }), 500

        current_app.logger.info(f"重新生成: 使用模板 {template_path} 生成 {doc_suffix} 表单")

        # 设置文档后缀，用于生成函数
        form_data['文档后缀'] = doc_suffix

        # 根据表单类型选择生成函数
        if doc_suffix == '安全监测':
            filename = generate_security_monitoring_excel(form_data, template_path)
        else:
            # 其他表单类型都使用通用生成函数
            filename = generate_bangbang_excel(form_data, template_path)

        if not filename:
            return jsonify({
                'status': 'error',
                'message': '重新生成Excel文件失败'
            }), 500

        # 如果使用了不同类型的模板，修改文件名以反映这一点
        if template_type and template_type != submission.form_type:
            # 在文件名中添加模板类型标识
            name_parts = filename.rsplit('.', 1)
            if len(name_parts) == 2:
                base_name, extension = name_parts
                filename = f"{base_name}_{template_type}模板.{extension}"
                current_app.logger.info(f"跨模板类型生成，修改文件名为: {filename}")

        # 直接返回文件下载，而不是创建新记录
        file_path = os.path.join(current_app.config['EXCEL_GENERATED_FOLDER'], filename)

        if not os.path.exists(file_path):
            return jsonify({
                'status': 'error',
                'message': '生成的文件不存在'
            }), 500

        current_app.logger.info(f"重新生成Excel文件成功，直接下载: {filename}")

        # 直接返回文件下载
        response = send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # 确保设置正确的Content-Disposition头，处理中文文件名
        encoded_filename = quote(filename.encode('utf-8'))
        response.headers['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'{encoded_filename}'
        current_app.logger.info(f"设置Content-Disposition头: attachment; filename*=UTF-8\'\'{encoded_filename}")

        return response
    except Exception as e:
        current_app.logger.error(f"重新生成Excel文件失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'重新生成Excel文件失败: {str(e)}'
        }), 500


@bp.route('/form_submissions/statistics', methods=['GET'])
def get_form_submissions_statistics():
    """
    获取表单提交统计信息
    """
    try:
        # 总提交数
        total_submissions = FormSubmission.query.count()

        # 按表单类型统计
        form_type_stats = db.session.query(
            FormSubmission.form_type,
            db.func.count(FormSubmission.id).label('count')
        ).group_by(FormSubmission.form_type).all()

        # 按公司统计
        company_stats = db.session.query(
            FormSubmission.company_name,
            db.func.count(FormSubmission.id).label('count')
        ).group_by(FormSubmission.company_name).order_by(db.func.count(FormSubmission.id).desc()).limit(10).all()

        # 最近30天的提交趋势
        thirty_days_ago = datetime.now().date() - timedelta(days=30)
        recent_submissions = db.session.query(
            FormSubmission.record_date,
            db.func.count(FormSubmission.id).label('count')
        ).filter(
            FormSubmission.record_date >= thirty_days_ago
        ).group_by(FormSubmission.record_date).order_by(FormSubmission.record_date).all()

        return jsonify({
            'status': 'success',
            'data': {
                'total_submissions': total_submissions,
                'form_type_stats': [{'form_type': stat[0], 'count': stat[1]} for stat in form_type_stats],
                'company_stats': [{'company_name': stat[0], 'count': stat[1]} for stat in company_stats],
                'recent_trend': [{'date': stat[0].isoformat(), 'count': stat[1]} for stat in recent_submissions]
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取统计信息失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取统计信息失败: {str(e)}'
        }), 500


@bp.route('/form_submissions/<int:submission_id>', methods=['DELETE'])
def delete_form_submission(submission_id):
    """
    删除表单提交记录
    """
    try:
        submission = FormSubmission.query.get_or_404(submission_id)

        # 删除关联的Excel文件（如果存在）
        if submission.excel_filepath and os.path.exists(submission.excel_filepath):
            os.remove(submission.excel_filepath)

        # 删除数据库记录
        db.session.delete(submission)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '表单提交记录删除成功'
        })
    except Exception as e:
        current_app.logger.error(f"删除表单提交记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'删除表单提交记录失败: {str(e)}'
        }), 500


@bp.route('/form_submissions/<int:submission_id>/edit', methods=['PUT'])
def edit_form_submission(submission_id):
    """
    编辑表单提交记录
    """
    try:
        submission = FormSubmission.query.get_or_404(submission_id)

        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400

        # 获取编辑信息
        new_form_data = data.get('form_data', {})
        edit_reason = data.get('edit_reason', '')
        edited_by = data.get('edited_by', '未知用户')

        if not new_form_data:
            return jsonify({
                'status': 'error',
                'message': '表单数据不能为空'
            }), 400

        # 保存编辑前的数据
        old_form_data = submission.get_form_data()

        # 创建编辑记录
        edit_record = FormSubmissionEdit(
            submission_id=submission_id,
            edit_type='update',
            edit_description=f"用户 {edited_by} 编辑了表单数据",
            edited_by=edited_by,
            edit_reason=edit_reason,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )

        # 设置编辑前后的数据
        edit_record.set_old_data(old_form_data)
        edit_record.set_new_data(new_form_data)

        # 计算变更字段
        changes = edit_record.calculate_changes(old_form_data, new_form_data)

        # 更新表单提交记录
        submission.set_form_data(new_form_data)
        submission.updated_at = get_beijing_time()

        # 更新基本信息（如果有变化）
        if 'company_name' in data:
            submission.company_name = data['company_name']
        if 'form_type' in data:
            submission.form_type = data['form_type']
        if 'record_date' in data:
            try:
                submission.record_date = datetime.strptime(data['record_date'], '%Y-%m-%d').date()
            except ValueError:
                pass

        # 重新计算统计信息
        submission.calculate_statistics(new_form_data)

        # 保存到数据库
        db.session.add(edit_record)
        db.session.commit()

        current_app.logger.info(f"表单提交记录 {submission_id} 已被编辑，变更字段数: {len(changes)}")

        return jsonify({
            'status': 'success',
            'message': '表单数据编辑成功',
            'data': {
                'submission': submission.to_dict(include_form_data=True),
                'edit_record': edit_record.to_dict(),
                'changes_count': len(changes)
            }
        })
    except Exception as e:
        current_app.logger.error(f"编辑表单提交记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'编辑表单提交记录失败: {str(e)}'
        }), 500


@bp.route('/form_submissions/<int:submission_id>/edit_history', methods=['GET'])
def get_form_submission_edit_history(submission_id):
    """
    获取表单提交记录的编辑历史
    """
    try:
        submission = FormSubmission.query.get_or_404(submission_id)

        # 获取编辑历史，按时间倒序
        edit_history = FormSubmissionEdit.query.filter_by(
            submission_id=submission_id
        ).order_by(FormSubmissionEdit.created_at.desc()).all()

        # 转换为字典格式
        history_data = []
        for edit in edit_history:
            edit_dict = edit.to_dict()
            # 添加变更详情
            edit_dict['changes_detail'] = edit.get_changed_fields()
            history_data.append(edit_dict)

        return jsonify({
            'status': 'success',
            'data': {
                'submission_id': submission_id,
                'company_name': submission.company_name,
                'form_type': submission.form_type,
                'edit_history': history_data,
                'total_edits': len(history_data)
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取编辑历史失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取编辑历史失败: {str(e)}'
        }), 500


@bp.route('/form_submissions/<int:submission_id>/restore/<int:edit_id>', methods=['POST'])
def restore_form_submission_from_edit(submission_id, edit_id):
    """
    从编辑历史中恢复表单数据
    """
    try:
        submission = FormSubmission.query.get_or_404(submission_id)
        edit_record = FormSubmissionEdit.query.get_or_404(edit_id)

        # 验证编辑记录是否属于该提交记录
        if edit_record.submission_id != submission_id:
            return jsonify({
                'status': 'error',
                'message': '编辑记录与提交记录不匹配'
            }), 400

        # 获取请求数据
        data = request.get_json() or {}
        restore_to = data.get('restore_to', 'old')  # 'old' 或 'new'
        edit_reason = data.get('edit_reason', '')
        edited_by = data.get('edited_by', '未知用户')

        # 获取要恢复的数据
        if restore_to == 'old':
            restore_data = edit_record.get_old_data()
            description = f"恢复到编辑前状态 (编辑记录#{edit_id})"
        else:
            restore_data = edit_record.get_new_data()
            description = f"恢复到编辑后状态 (编辑记录#{edit_id})"

        if not restore_data:
            return jsonify({
                'status': 'error',
                'message': '没有可恢复的数据'
            }), 400

        # 保存当前数据作为编辑前数据
        current_data = submission.get_form_data()

        # 创建新的编辑记录
        new_edit_record = FormSubmissionEdit(
            submission_id=submission_id,
            edit_type='restore',
            edit_description=description,
            edited_by=edited_by,
            edit_reason=edit_reason,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )

        # 设置编辑前后的数据
        new_edit_record.set_old_data(current_data)
        new_edit_record.set_new_data(restore_data)

        # 计算变更字段
        changes = new_edit_record.calculate_changes(current_data, restore_data)

        # 更新表单提交记录
        submission.set_form_data(restore_data)
        submission.updated_at = get_beijing_time()
        submission.calculate_statistics(restore_data)

        # 保存到数据库
        db.session.add(new_edit_record)
        db.session.commit()

        current_app.logger.info(f"表单提交记录 {submission_id} 已从编辑记录 {edit_id} 恢复")

        return jsonify({
            'status': 'success',
            'message': '数据恢复成功',
            'data': {
                'submission': submission.to_dict(include_form_data=True),
                'edit_record': new_edit_record.to_dict(),
                'changes_count': len(changes)
            }
        })
    except Exception as e:
        current_app.logger.error(f"恢复表单数据失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'恢复表单数据失败: {str(e)}'
        }), 500


# ==================== 表单字段配置相关API ====================

@bp.route('/field-types', methods=['GET'])
def get_field_types():
    """获取所有字段类型定义"""
    try:
        field_types = FieldTypeDefinition.query.order_by(FieldTypeDefinition.type_name).all()
        return jsonify({
            'status': 'success',
            'data': [field_type.to_dict() for field_type in field_types]
        })
    except Exception as e:
        current_app.logger.error(f"获取字段类型失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取字段类型失败: {str(e)}'
        }), 500


@bp.route('/form-field-groups/<form_type>', methods=['GET'])
def get_form_field_groups(form_type):
    """获取指定表单类型的字段分组"""
    try:
        groups = FormFieldGroup.query.filter_by(form_type=form_type)\
                                   .order_by(FormFieldGroup.display_order).all()
        return jsonify({
            'status': 'success',
            'data': [group.to_dict() for group in groups]
        })
    except Exception as e:
        current_app.logger.error(f"获取表单字段分组失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取表单字段分组失败: {str(e)}'
        }), 500


@bp.route('/form-field-configs/<form_type>', methods=['GET'])
def get_form_field_configs(form_type):
    """获取指定表单类型的字段配置"""
    try:
        configs = FormFieldConfig.query.filter_by(form_type=form_type)\
                                     .order_by(FormFieldConfig.display_order).all()
        return jsonify({
            'status': 'success',
            'data': [config.to_dict() for config in configs]
        })
    except Exception as e:
        current_app.logger.error(f"获取表单字段配置失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取表单字段配置失败: {str(e)}'
        }), 500


@bp.route('/form-field-groups', methods=['POST'])
def create_form_field_group():
    """创建表单字段分组"""
    try:
        data = request.get_json()

        # 如果没有指定显示顺序，自动分配到最后
        display_order = data.get('display_order')
        if display_order is None or display_order == 0:
            # 查找当前表单类型的最大显示顺序
            max_order = db.session.query(db.func.max(FormFieldGroup.display_order))\
                                 .filter_by(form_type=data['form_type']).scalar() or 0
            display_order = max_order + 1

        group = FormFieldGroup(
            form_type=data['form_type'],
            group_name=data['group_name'],
            group_label=data['group_label'],
            group_description=data.get('group_description'),
            display_order=display_order,
            is_collapsible=data.get('is_collapsible', True),
            is_expanded_by_default=data.get('is_expanded_by_default', True)
        )

        db.session.add(group)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '字段分组创建成功',
            'data': group.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建表单字段分组失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'创建表单字段分组失败: {str(e)}'
        }), 500


@bp.route('/form-field-configs', methods=['POST'])
@permission_required('system.config')
def create_form_field_config():
    """创建表单字段配置"""
    try:
        data = request.get_json()

        config = FormFieldConfig(
            form_type=data['form_type'],
            group_id=data.get('group_id'),
            field_name=data['field_name'],
            field_label=data['field_label'],
            field_type=data['field_type'],
            field_description=data.get('field_description'),
            placeholder=data.get('placeholder'),
            default_value=data.get('default_value'),
            is_required=data.get('is_required', False),
            is_readonly=data.get('is_readonly', False),
            is_auto_fill=data.get('is_auto_fill', False),
            display_order=data.get('display_order', 0),
            css_classes=data.get('css_classes'),
            grid_columns=data.get('grid_columns', 12)
        )

        # 设置验证规则和字段选项
        if 'validation_rules' in data:
            config.set_validation_rules(data['validation_rules'])
        if 'field_options' in data:
            config.set_field_options(data['field_options'])

        db.session.add(config)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '字段配置创建成功',
            'data': config.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建表单字段配置失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'创建表单字段配置失败: {str(e)}'
        }), 500


@bp.route('/form-field-groups/<int:group_id>', methods=['PUT'])
def update_form_field_group(group_id):
    """更新表单字段分组"""
    try:
        group = FormFieldGroup.query.get_or_404(group_id)
        data = request.get_json()

        # 检查是否为内置表单类型
        built_in_form_types = ['安全测评', '安全监测', '应用加固']
        if group.form_type in built_in_form_types:
            return jsonify({
                'status': 'error',
                'message': '内置表单类型的字段分组不能修改'
            }), 400

        # 更新字段
        if 'group_label' in data:
            group.group_label = data['group_label']
        if 'group_description' in data:
            group.group_description = data['group_description']
        if 'display_order' in data:
            group.display_order = data['display_order']
        if 'is_collapsible' in data:
            group.is_collapsible = data['is_collapsible']
        if 'is_expanded_by_default' in data:
            group.is_expanded_by_default = data['is_expanded_by_default']

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '字段分组更新成功',
            'data': group.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新表单字段分组失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'更新表单字段分组失败: {str(e)}'
        }), 500


@bp.route('/form-field-groups/<int:group_id>', methods=['DELETE'])
def delete_form_field_group(group_id):
    """删除表单字段分组"""
    try:
        group = FormFieldGroup.query.get_or_404(group_id)

        # 检查是否为内置表单类型
        built_in_form_types = ['安全测评', '安全监测', '应用加固']
        if group.form_type in built_in_form_types:
            return jsonify({
                'status': 'error',
                'message': '内置表单类型的字段分组不能删除'
            }), 400

        # 检查是否有关联的字段
        if group.fields:
            return jsonify({
                'status': 'error',
                'message': f'该分组下还有 {len(group.fields)} 个字段，请先删除字段'
            }), 400

        db.session.delete(group)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '字段分组删除成功'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除表单字段分组失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'删除表单字段分组失败: {str(e)}'
        }), 500


@bp.route('/form-field-configs/<int:config_id>', methods=['PUT'])
@permission_required('system.config')
def update_form_field_config(config_id):
    """更新表单字段配置"""
    try:
        config = FormFieldConfig.query.get_or_404(config_id)
        data = request.get_json()

        # 检查是否为内置表单类型
        built_in_form_types = ['安全测评', '安全监测', '应用加固']
        if config.form_type in built_in_form_types:
            return jsonify({
                'status': 'error',
                'message': '内置表单类型的字段配置不能修改'
            }), 400

        # 更新字段
        if 'field_label' in data:
            config.field_label = data['field_label']
        if 'field_type' in data:
            config.field_type = data['field_type']
        if 'field_description' in data:
            config.field_description = data['field_description']
        if 'placeholder' in data:
            config.placeholder = data['placeholder']
        if 'default_value' in data:
            config.default_value = data['default_value']
        if 'is_required' in data:
            config.is_required = data['is_required']
        if 'is_readonly' in data:
            config.is_readonly = data['is_readonly']
        if 'is_auto_fill' in data:
            config.is_auto_fill = data['is_auto_fill']
        if 'display_order' in data:
            config.display_order = data['display_order']
        if 'css_classes' in data:
            config.css_classes = data['css_classes']
        if 'grid_columns' in data:
            config.grid_columns = data['grid_columns']
        if 'group_id' in data:
            config.group_id = data['group_id']

        # 更新验证规则和字段选项
        if 'validation_rules' in data:
            config.set_validation_rules(data['validation_rules'])
        if 'field_options' in data:
            config.set_field_options(data['field_options'])

        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '字段配置更新成功',
            'data': config.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新表单字段配置失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'更新表单字段配置失败: {str(e)}'
        }), 500


@bp.route('/form-field-configs/<int:config_id>', methods=['DELETE'])
@permission_required('system.config')
def delete_form_field_config(config_id):
    """删除表单字段配置"""
    try:
        config = FormFieldConfig.query.get_or_404(config_id)

        # 检查是否为内置表单类型
        built_in_form_types = ['安全测评', '安全监测', '应用加固']
        if config.form_type in built_in_form_types:
            return jsonify({
                'status': 'error',
                'message': '内置表单类型的字段配置不能删除'
            }), 400

        db.session.delete(config)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': '字段配置删除成功'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除表单字段配置失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'删除表单字段配置失败: {str(e)}'
        }), 500


@bp.route('/common-fields', methods=['GET'])
def get_common_fields():
    """获取公共字段定义"""
    try:
        # 返回标准字段配置
        standard_config = get_standard_form_config()

        # 提取所有字段定义
        common_fields = {}
        for group in standard_config['groups']:
            for field in group['fields']:
                common_fields[field['field_name']] = field

        return jsonify({
            'status': 'success',
            'data': {
                'fields': common_fields,
                'groups': standard_config['groups']
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取公共字段定义失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取公共字段定义失败: {str(e)}'
        }), 500


@bp.route('/form-field-configs/init-basic-fields/<form_type>', methods=['POST'])
@permission_required('system.config')
def init_basic_form_fields(form_type):
    """为指定表单类型初始化基础字段配置"""
    try:
        # 检查是否为内置表单类型
        built_in_form_types = ['安全测评', '安全监测', '应用加固']
        if form_type in built_in_form_types:
            return jsonify({
                'status': 'error',
                'message': '内置表单类型不能初始化基础字段'
            }), 400

        # 检查表单类型是否已有配置
        existing_groups = FormFieldGroup.query.filter_by(form_type=form_type).count()
        existing_fields = FormFieldConfig.query.filter_by(form_type=form_type).count()

        if existing_groups > 0 or existing_fields > 0:
            return jsonify({
                'status': 'error',
                'message': f'表单类型 "{form_type}" 已有配置，不能重复初始化'
            }), 400

        # 定义基础字段分组和字段配置（使用标准化配置）
        basic_config = get_standard_form_config()

        created_groups = []
        created_fields = []

        # 创建字段分组和字段
        for group_config in basic_config['groups']:
            # 创建字段分组
            group = FormFieldGroup(
                form_type=form_type,
                group_name=group_config['group_name'],
                group_label=group_config['group_label'],
                group_description=group_config['group_description'],
                display_order=group_config['display_order'],
                is_collapsible=True,
                is_expanded_by_default=True
            )

            db.session.add(group)
            db.session.flush()  # 获取group.id
            created_groups.append(group)

            # 创建字段配置
            for field_config in group_config['fields']:
                field = FormFieldConfig(
                    form_type=form_type,
                    group_id=group.id,
                    field_name=field_config['field_name'],
                    field_label=field_config['field_label'],
                    field_type=field_config['field_type'],
                    is_required=field_config['is_required'],
                    placeholder=field_config['placeholder'],
                    display_order=field_config['display_order'],
                    grid_columns=12  # 默认占满一行
                )

                db.session.add(field)
                created_fields.append(field)

        db.session.commit()

        current_app.logger.info(f"为表单类型 '{form_type}' 初始化了 {len(created_groups)} 个分组和 {len(created_fields)} 个字段")

        return jsonify({
            'status': 'success',
            'message': f'基础字段配置初始化成功',
            'data': {
                'form_type': form_type,
                'groups_created': len(created_groups),
                'fields_created': len(created_fields),
                'groups': [group.to_dict() for group in created_groups],
                'fields': [field.to_dict() for field in created_fields]
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"初始化基础字段配置失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'初始化基础字段配置失败: {str(e)}'
        }), 500



@bp.route('/form-types', methods=['GET'])
def get_form_types():
    """获取所有表单类型（性能优化版本，带缓存）"""
    try:
        # 尝试从缓存获取数据
        from app.utils.cache_utils import FormTypeCacheManager

        cached_data = FormTypeCacheManager.get_form_types_list()
        if cached_data is not None:
            current_app.logger.debug("表单类型列表缓存命中")
            return jsonify({
                'status': 'success',
                'data': cached_data
            })
        # 先尝试使用FormType表，如果失败则回退到旧逻辑
        form_type_dict = {}
        try:
            # 从FormType表获取所有表单类型
            form_types_from_db = FormType.query.all()
            for ft in form_types_from_db:
                form_type_dict[ft.name] = ft
            current_app.logger.debug(f"从FormType表获取到{len(form_types_from_db)}条记录")
        except Exception as e:
            current_app.logger.warning(f"FormType表查询失败，使用兼容模式: {str(e)}")
            # FormType表可能不存在，继续使用旧逻辑

        # 从数据库中获取所有已配置的表单类型（用于兼容旧数据）
        form_types_from_groups = db.session.query(FormFieldGroup.form_type).distinct().all()
        form_types_from_configs = db.session.query(FormFieldConfig.form_type).distinct().all()

        # 合并并去重
        all_form_types = set()

        # 添加FormType表中的数据
        for ft_name in form_type_dict.keys():
            all_form_types.add(ft_name)

        # 添加从配置中发现的表单类型（兼容旧数据）
        for (form_type,) in form_types_from_groups:
            all_form_types.add(form_type)
        for (form_type,) in form_types_from_configs:
            all_form_types.add(form_type)

        # 添加内置表单类型（如果不存在）
        built_in_types = ['安全测评', '安全监测', '应用加固']
        for built_in_type in built_in_types:
            all_form_types.add(built_in_type)

        # 性能优化：批量查询统计数据，避免N+1查询问题
        # 一次性获取所有分组的统计数据
        group_stats = db.session.query(
            FormFieldGroup.form_type,
            db.func.count(FormFieldGroup.id).label('count')
        ).group_by(FormFieldGroup.form_type).all()

        # 一次性获取所有字段的统计数据
        field_stats = db.session.query(
            FormFieldConfig.form_type,
            db.func.count(FormFieldConfig.id).label('count')
        ).group_by(FormFieldConfig.form_type).all()

        # 转换为字典以便快速查找
        group_count_dict = {stat.form_type: stat.count for stat in group_stats}
        field_count_dict = {stat.form_type: stat.count for stat in field_stats}

        # 构造返回数据
        form_types = []
        for form_type in sorted(all_form_types):
            # 从预先查询的统计数据中获取计数
            group_count = group_count_dict.get(form_type, 0)
            field_count = field_count_dict.get(form_type, 0)

            # 获取FormType记录信息
            form_type_record = form_type_dict.get(form_type)
            is_built_in = form_type in built_in_types

            form_type_data = {
                'name': form_type,
                'is_built_in': is_built_in,
                'group_count': group_count,
                'field_count': field_count
            }

            # 如果有FormType记录，添加ID和其他信息
            if form_type_record:
                form_type_data.update({
                    'id': form_type_record.id,
                    'display_name': form_type_record.display_name,
                    'description': form_type_record.description,
                    'is_default': form_type_record.is_default,
                    'is_active': form_type_record.is_active,
                    'created_at': form_type_record.created_at.isoformat() if form_type_record.created_at else None,
                    'updated_at': form_type_record.updated_at.isoformat() if form_type_record.updated_at else None
                })
            else:
                # 对于没有FormType记录的（旧数据或内置类型），使用name作为标识
                form_type_data['id'] = None

            form_types.append(form_type_data)

        current_app.logger.debug(f"表单类型API执行完成，返回{len(form_types)}个表单类型")

        # 缓存结果
        FormTypeCacheManager.cache_form_types_list(form_types)
        current_app.logger.debug("表单类型列表已缓存")

        return jsonify({
            'status': 'success',
            'data': form_types
        })
    except Exception as e:
        current_app.logger.error(f"获取表单类型失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取表单类型失败: {str(e)}'
        }), 500








@bp.route('/form-types', methods=['POST'])
@permission_required('template.create')
def create_form_type():
    """创建新的表单类型"""
    try:
        data = request.get_json()
        form_type_name = data.get('name', '').strip()

        if not form_type_name:
            return jsonify({
                'status': 'error',
                'message': '表单类型名称不能为空'
            }), 400

        # 检查是否为内置类型
        built_in_types = ['安全测评', '安全监测', '应用加固']
        if form_type_name in built_in_types:
            return jsonify({
                'status': 'error',
                'message': '不能创建与内置表单类型同名的表单'
            }), 400

        # 检查是否已存在（检查FormType表和配置表）
        existing_form_type = None
        try:
            existing_form_type = FormType.query.filter_by(name=form_type_name).first()
        except Exception as e:
            current_app.logger.debug(f"FormType表查询失败: {str(e)}")

        existing_group = FormFieldGroup.query.filter_by(form_type=form_type_name).first()
        existing_config = FormFieldConfig.query.filter_by(form_type=form_type_name).first()

        if existing_form_type or existing_group or existing_config:
            return jsonify({
                'status': 'error',
                'message': '该表单类型已存在'
            }), 400

        # 尝试创建FormType记录（如果表存在）
        form_type_record = None
        try:
            form_type_record = FormType(
                name=form_type_name,
                display_name=form_type_name,
                description=data.get('description', ''),
                is_default=False,
                is_active=True,
                order=0
            )
            db.session.add(form_type_record)
            db.session.flush()  # 获取ID
            current_app.logger.info(f"FormType记录创建成功: {form_type_record.id}")
        except Exception as e:
            current_app.logger.warning(f"FormType记录创建失败，继续创建分组: {str(e)}")
            # FormType表可能不存在，继续创建分组

        # 创建默认的分组
        default_groups = [
            {
                'group_name': 'basic_info',
                'group_label': '基本信息',
                'group_description': '项目的基本信息',
                'display_order': 1
            },
            {
                'group_name': 'access_info',
                'group_label': '访问信息',
                'group_description': '系统访问相关信息',
                'display_order': 2
            },
            {
                'group_name': 'maintenance_records',
                'group_label': '维护记录',
                'group_description': '系统维护记录',
                'display_order': 3
            },
            {
                'group_name': 'server_info',
                'group_label': '服务器信息',
                'group_description': '服务器部署信息',
                'display_order': 4
            }
        ]

        created_groups = []
        for group_data in default_groups:
            group = FormFieldGroup(
                form_type=form_type_name,
                group_name=group_data['group_name'],
                group_label=group_data['group_label'],
                group_description=group_data['group_description'],
                display_order=group_data['display_order'],
                is_collapsible=True,
                is_expanded_by_default=True
            )
            db.session.add(group)
            created_groups.append(group)

        db.session.commit()

        # 清除表单类型列表缓存
        from app.utils.cache_utils import FormTypeCacheManager
        FormTypeCacheManager.clear_form_types_cache()
        current_app.logger.debug("已清除表单类型列表缓存")

        # 构造返回数据
        response_data = {
            'name': form_type_name,
            'is_built_in': False,
            'group_count': len(default_groups),
            'field_count': 0,
            'default_groups': [group.group_label for group in created_groups]
        }

        # 如果FormType记录创建成功，添加相关信息
        if form_type_record:
            response_data.update({
                'id': form_type_record.id,
                'display_name': form_type_record.display_name,
                'description': form_type_record.description,
                'is_default': False,
                'is_active': True,
                'created_at': form_type_record.created_at.isoformat(),
                'updated_at': form_type_record.updated_at.isoformat()
            })
        else:
            response_data['id'] = None

        return jsonify({
            'status': 'success',
            'message': f'表单类型创建成功，已自动添加 {len(default_groups)} 个默认分组',
            'data': response_data
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建表单类型失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'创建表单类型失败: {str(e)}'
        }), 500


@bp.route('/form-types/<int:form_type_id>', methods=['PUT'])
@permission_required('template.edit')
def update_form_type(form_type_id):
    """更新表单类型"""
    try:
        data = request.get_json()
        form_type_name = data.get('name', '').strip()
        form_type_description = data.get('description', '').strip()

        if not form_type_name:
            return jsonify({
                'status': 'error',
                'message': '表单类型名称不能为空'
            }), 400

        # 检查是否为内置类型
        built_in_types = ['安全测评', '安全监测', '应用加固']

        # 查找要更新的表单类型
        form_type_record = None
        try:
            form_type_record = FormType.query.get(form_type_id)
            if not form_type_record:
                return jsonify({
                    'status': 'error',
                    'message': '表单类型不存在'
                }), 404

            # 检查是否为内置类型（不能修改内置类型的名称）
            if form_type_record.name in built_in_types and form_type_record.name != form_type_name:
                return jsonify({
                    'status': 'error',
                    'message': '内置表单类型的名称不能修改'
                }), 400

        except Exception as e:
            current_app.logger.error(f"查找表单类型失败: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': '表单类型不存在'
            }), 404

        # 如果修改了名称，检查新名称是否已存在
        if form_type_record.name != form_type_name:
            # 检查新名称是否为内置类型
            if form_type_name in built_in_types:
                return jsonify({
                    'status': 'error',
                    'message': '不能使用内置表单类型的名称'
                }), 400

            # 检查新名称是否已存在
            existing_form_type = FormType.query.filter_by(name=form_type_name).first()
            if existing_form_type and existing_form_type.id != form_type_id:
                return jsonify({
                    'status': 'error',
                    'message': '该表单类型名称已存在'
                }), 400

        # 更新表单类型记录
        old_name = form_type_record.name
        form_type_record.name = form_type_name
        form_type_record.display_name = form_type_name
        form_type_record.description = form_type_description
        form_type_record.updated_at = datetime.now()

        # 如果名称发生了变化，需要更新相关的配置表
        if old_name != form_type_name:
            # 更新FormFieldGroup表中的form_type字段
            FormFieldGroup.query.filter_by(form_type=old_name).update({'form_type': form_type_name})

            # 更新FormFieldConfig表中的form_type字段
            FormFieldConfig.query.filter_by(form_type=old_name).update({'form_type': form_type_name})

            # 更新ComponentConfig表中的form_type字段
            ComponentConfig.query.filter_by(form_type=old_name).update({'form_type': form_type_name})

            current_app.logger.info(f"表单类型名称从 '{old_name}' 更新为 '{form_type_name}'，已同步更新相关配置")

        db.session.commit()

        # 清除缓存
        from app.utils.cache_utils import FormTypeCacheManager
        FormTypeCacheManager.clear_form_types_cache()

        current_app.logger.info(f"表单类型更新成功: {form_type_name}")

        return jsonify({
            'status': 'success',
            'message': '表单类型更新成功',
            'data': {
                'form_type': {
                    'id': form_type_record.id,
                    'name': form_type_record.name,
                    'description': form_type_record.description,
                    'updated_at': form_type_record.updated_at.isoformat() if form_type_record.updated_at else None
                }
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新表单类型失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'更新表单类型失败: {str(e)}'
        }), 500


@bp.route('/form-types/clear-cache', methods=['POST'])
@permission_required('system.config')
def clear_form_types_cache():
    """清除表单类型缓存"""
    try:
        from app.utils.cache_utils import FormTypeCacheManager

        success = FormTypeCacheManager.clear_form_types_cache()
        current_app.logger.info("手动清除表单类型列表缓存")

        if success:
            return jsonify({
                'status': 'success',
                'message': '缓存清除成功'
            })
        else:
            return jsonify({
                'status': 'warning',
                'message': '缓存清除可能不完全成功'
            })
    except Exception as e:
        current_app.logger.error(f"清除缓存失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'清除缓存失败: {str(e)}'
        }), 500


@bp.route('/form-types/one-click-init', methods=['POST'])
@permission_required('template.create')
def one_click_init_form_type():
    """一键初始化表单类型：创建表单类型并初始化完整的字段配置"""
    try:
        data = request.get_json()
        form_type_name = data.get('name', '').strip()
        form_type_description = data.get('description', '').strip()

        if not form_type_name:
            return jsonify({
                'status': 'error',
                'message': '表单类型名称不能为空'
            }), 400

        # 检查是否为内置类型
        built_in_types = ['安全测评', '安全监测', '应用加固']
        if form_type_name in built_in_types:
            return jsonify({
                'status': 'error',
                'message': '不能使用内置表单类型的名称'
            }), 400

        # 检查是否已存在（检查FormType表和配置表）
        existing_form_type = None
        try:
            existing_form_type = FormType.query.filter_by(name=form_type_name).first()
        except Exception as e:
            current_app.logger.debug(f"FormType表查询失败: {str(e)}")

        existing_group = FormFieldGroup.query.filter_by(form_type=form_type_name).first()
        existing_config = FormFieldConfig.query.filter_by(form_type=form_type_name).first()

        if existing_form_type or existing_group or existing_config:
            return jsonify({
                'status': 'error',
                'message': '该表单类型已存在'
            }), 400

        # 开始事务
        try:
            # 第一步：创建FormType记录（如果表存在）
            form_type_record = None
            try:
                form_type_record = FormType(
                    name=form_type_name,
                    display_name=form_type_name,
                    description=form_type_description or f'{form_type_name}表单配置',
                    is_default=False,
                    is_active=True,
                    order=0
                )
                db.session.add(form_type_record)
                db.session.flush()  # 获取ID
                current_app.logger.info(f"FormType记录创建成功: {form_type_record.id}")
            except Exception as e:
                current_app.logger.warning(f"FormType记录创建失败，继续创建分组: {str(e)}")

            # 第二步：创建完整的字段配置（使用标准化配置）
            basic_config = get_standard_form_config()

            created_groups = []
            created_fields = []

            # 创建字段分组和字段
            for group_config in basic_config['groups']:
                # 创建字段分组
                group = FormFieldGroup(
                    form_type=form_type_name,
                    group_name=group_config['group_name'],
                    group_label=group_config['group_label'],
                    group_description=group_config['group_description'],
                    display_order=group_config['display_order'],
                    is_collapsible=True,
                    is_expanded_by_default=True
                )

                db.session.add(group)
                db.session.flush()  # 获取group.id
                created_groups.append(group)

                # 创建字段配置
                for field_config in group_config['fields']:
                    field = FormFieldConfig(
                        form_type=form_type_name,
                        group_id=group.id,
                        field_name=field_config['field_name'],
                        field_label=field_config['field_label'],
                        field_type=field_config['field_type'],
                        is_required=field_config['is_required'],
                        placeholder=field_config['placeholder'],
                        field_description=field_config.get('field_description', ''),
                        is_auto_fill=field_config.get('is_auto_fill', False),
                        display_order=field_config['display_order'],
                        validation_rules=json.dumps(field_config.get('validation_rules', {})),
                        grid_columns=12  # 默认占满一行
                    )

                    db.session.add(field)
                    created_fields.append(field)

            # 提交事务
            db.session.commit()

            # 清除表单类型列表缓存
            from app.utils.cache_utils import FormTypeCacheManager
            FormTypeCacheManager.clear_form_types_cache()
            current_app.logger.debug("已清除表单类型列表缓存")

            current_app.logger.info(f"一键初始化表单类型 '{form_type_name}' 成功：创建了 {len(created_groups)} 个分组和 {len(created_fields)} 个字段")

            # 构造返回数据
            response_data = {
                'form_type': form_type_name,
                'description': form_type_description,
                'groups_created': len(created_groups),
                'fields_created': len(created_fields),
                'groups': [group.to_dict() for group in created_groups],
                'fields': [field.to_dict() for field in created_fields]
            }

            # 如果FormType记录创建成功，添加相关信息
            if form_type_record:
                response_data.update({
                    'id': form_type_record.id,
                    'display_name': form_type_record.display_name,
                    'is_default': False,
                    'is_active': True,
                    'created_at': form_type_record.created_at.isoformat(),
                    'updated_at': form_type_record.updated_at.isoformat()
                })
            else:
                response_data['id'] = None

            return jsonify({
                'status': 'success',
                'message': f'表单类型"{form_type_name}"一键初始化成功！创建了 {len(created_groups)} 个分组和 {len(created_fields)} 个字段',
                'data': response_data
            })

        except Exception as e:
            db.session.rollback()
            raise e

    except Exception as e:
        current_app.logger.error(f"一键初始化表单类型失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'一键初始化表单类型失败: {str(e)}'
        }), 500


# ==================== Excel导入相关API ====================

@bp.route('/import_excel/preview', methods=['POST'])
def preview_excel_import():
    """
    预览Excel导入数据
    """
    try:
        # 检查文件上传
        if 'file' not in request.files:
            return jsonify({
                'status': 'error',
                'message': '没有上传文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'status': 'error',
                'message': '没有选择文件'
            }), 400

        # 验证文件类型
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            return jsonify({
                'status': 'error',
                'message': '只支持Excel文件格式(.xlsx, .xls)'
            }), 400

        # 获取指定的表单类型（可选）
        form_type = request.form.get('form_type', '').strip()

        # 保存临时文件
        filename = secure_filename(file.filename)
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, filename)
        file.save(temp_file_path)

        try:
            # 创建导入器并解析文件（优先使用模板解析器）
            print(f"🚀 API开始解析文件: {temp_file_path}")
            print(f"📋 原始文件名: {file.filename}")
            print(f"📋 指定表单类型: {form_type}")
            current_app.logger.info(f"API开始解析文件: {temp_file_path}, 原始文件名: {file.filename}, 指定表单类型: {form_type}")
            importer = ExcelImporter(temp_file_path, form_type, original_filename=file.filename)
            print(f"📋 导入器文件名: {importer.filename}")
            current_app.logger.info(f"导入器创建完成，文件名: {importer.filename}")
            parsed_data = importer.parse_excel_with_template()

            if not parsed_data:
                return jsonify({
                    'status': 'error',
                    'message': '解析Excel文件失败',
                    'errors': importer.errors
                }), 400

            # 获取导入摘要
            summary = importer.get_import_summary()

            # 计算组件数量
            component_count = 0

            # 方法1：从部署应用字段获取
            deployed_apps = parsed_data.get('部署应用', [])
            if isinstance(deployed_apps, list):
                component_count = len(deployed_apps)

            # 方法2：从selectedComponentDetails获取（如果部署应用为空）
            if component_count == 0:
                selected_details = parsed_data.get('selectedComponentDetails', {})
                if isinstance(selected_details, dict):
                    component_count = len(selected_details)

            # 方法3：从服务器信息中收集唯一组件（作为备选）
            if component_count == 0:
                unique_components = set()
                servers = parsed_data.get('服务器信息', [])
                if isinstance(servers, list):
                    for server in servers:
                        if isinstance(server, dict):
                            server_components = server.get('部署应用', [])
                            if isinstance(server_components, list):
                                unique_components.update(server_components)
                            elif isinstance(server_components, str):
                                # 处理字符串格式的组件列表
                                comp_list = [comp.strip() for comp in server_components.replace('\n', ',').split(',') if comp.strip()]
                                unique_components.update(comp_list)
                component_count = len(unique_components)

            # 准备预览数据
            preview_data = {
                'form_type': importer.form_type,
                'company_name': parsed_data.get('公司名称', ''),
                'record_date': parsed_data.get('记录日期', ''),
                'server_count': len(parsed_data.get('服务器信息', [])),
                'component_count': component_count,
                'basic_info': {
                    key: value for key, value in parsed_data.items()
                    if key not in ['服务器信息', '维护记录']
                },
                'servers': parsed_data.get('服务器信息', [])[:5],  # 只显示前5台服务器
                'total_servers': len(parsed_data.get('服务器信息', [])),
                'errors': importer.errors,
                'warnings': importer.warnings,
                'temp_file_id': os.path.basename(temp_dir)  # 用于后续导入
            }

            # 将临时文件路径存储在session或缓存中（这里简化处理）
            current_app.config.setdefault('TEMP_IMPORT_FILES', {})[preview_data['temp_file_id']] = temp_file_path

            return jsonify({
                'status': 'success',
                'data': preview_data
            })

        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
            raise e

    except Exception as e:
        current_app.logger.error(f"预览Excel导入失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'预览Excel导入失败: {str(e)}'
        }), 500


@bp.route('/import_excel/confirm', methods=['POST'])
def confirm_excel_import():
    """
    确认Excel导入，创建表单提交记录
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': '请求数据为空'
            }), 400

        temp_file_id = data.get('temp_file_id')
        if not temp_file_id:
            return jsonify({
                'status': 'error',
                'message': '缺少临时文件ID'
            }), 400

        # 获取临时文件路径
        temp_files = current_app.config.get('TEMP_IMPORT_FILES', {})
        temp_file_path = temp_files.get(temp_file_id)

        if not temp_file_path or not os.path.exists(temp_file_path):
            return jsonify({
                'status': 'error',
                'message': '临时文件不存在或已过期'
            }), 400

        try:
            # 重新解析文件（优先使用模板解析器）
            form_type = data.get('form_type')
            importer = ExcelImporter(temp_file_path, form_type)
            parsed_data = importer.parse_excel_with_template()

            if not parsed_data or importer.errors:
                return jsonify({
                    'status': 'error',
                    'message': '解析Excel文件失败',
                    'errors': importer.errors
                }), 400

            # 创建表单提交记录
            submission = importer.create_form_submission()

            if not submission:
                return jsonify({
                    'status': 'error',
                    'message': '创建表单提交记录失败',
                    'errors': importer.errors
                }), 500

            # 清理临时文件
            if temp_file_id in temp_files:
                del temp_files[temp_file_id]
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            temp_dir = os.path.dirname(temp_file_path)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)

            current_app.logger.info(f"Excel导入成功，创建表单提交记录 ID: {submission.id}")

            return jsonify({
                'status': 'success',
                'message': 'Excel导入成功',
                'data': {
                    'submission_id': submission.id,
                    'company_name': submission.company_name,
                    'form_type': submission.form_type,
                    'record_date': submission.record_date.isoformat() if submission.record_date else None,
                    'server_count': submission.server_count,
                    'component_count': submission.component_count
                }
            })

        except Exception as e:
            # 清理临时文件
            if temp_file_id in temp_files:
                del temp_files[temp_file_id]
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            temp_dir = os.path.dirname(temp_file_path)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
            raise e

    except Exception as e:
        current_app.logger.error(f"确认Excel导入失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'确认Excel导入失败: {str(e)}'
        }), 500


@bp.route('/import_json/preview', methods=['POST'])
def preview_json_import():
    """
    预览JSON导入数据
    """
    try:
        if not request.is_json:
            return jsonify({
                'status': 'error',
                'message': '请求体必须为JSON格式'
            }), 400

        data = request.get_json()
        print(f"🔍 收到的请求数据: {data}")

        if not data:
            print("❌ 请求数据为空")
            return jsonify({
                'status': 'error',
                'message': '表单数据不能为空'
            }), 400

        # 支持两种数据格式：json_data（字符串）或 form_data（对象）
        if 'json_data' in data:
            # 原有格式：json_data字段包含JSON字符串
            json_data = data['json_data']
            if not json_data:
                return jsonify({
                    'status': 'error',
                    'message': 'json_data不能为空'
                }), 400
            if not isinstance(json_data, str):
                json_data = json.dumps(json_data, ensure_ascii=False)
        elif 'form_data' in data:
            # 新格式：form_data字段包含表单数据对象
            form_data = data['form_data']
            if not form_data:
                return jsonify({
                    'status': 'error',
                    'message': 'form_data不能为空'
                }), 400
            if not isinstance(form_data, dict):
                return jsonify({
                    'status': 'error',
                    'message': 'form_data必须是一个对象'
                }), 400
            json_data = json.dumps(form_data, ensure_ascii=False)
        else:
            return jsonify({
                'status': 'error',
                'message': '请求必须包含json_data或form_data字段'
            }), 400

        # 创建JSON导入器
        importer = JsonImporter(json_data, 'imported_data.json')

        # 解析JSON数据
        parsed_data = importer.parse_json()
        if not parsed_data:
            return jsonify({
                'status': 'error',
                'message': '解析JSON数据失败',
                'errors': importer.errors
            }), 400

        # 获取导入摘要
        summary = importer.get_import_summary()

        # 创建临时文件ID用于确认导入
        temp_file_id = f"json_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 将解析的数据临时存储（可以使用缓存或临时文件）
        temp_file_path = os.path.join(tempfile.gettempdir(), f"{temp_file_id}.json")
        with open(temp_file_path, 'w', encoding='utf-8') as f:
            json.dump(parsed_data, f, ensure_ascii=False, indent=2)

        # 准备预览数据
        preview_data = {
            'form_type': summary['form_type'],
            'company_name': summary['company_name'],
            'record_date': summary['record_date'],
            'server_count': summary['server_count'],
            'component_count': summary['component_count'],
            'field_count': len(parsed_data.keys()),
            'temp_file_id': temp_file_id,
            'temp_data_id': temp_file_id,  # 兼容前端期望的字段名
            'warnings': importer.warnings,
            'basic_fields': {},
            'servers': parsed_data.get('服务器信息', []),
            'components': parsed_data.get('部署应用', []),
            'maintenance_records': parsed_data.get('维护记录', []),
            'selectedComponentDetails': parsed_data.get('selectedComponentDetails', {}),
            'component_ports': parsed_data.get('组件端口', {})
        }

        # 提取基本字段（排除特殊字段）
        exclude_fields = ['服务器信息', '维护记录', '部署应用', '组件端口', 'selectedComponentDetails', '组件信息']
        for key, value in parsed_data.items():
            if key not in exclude_fields:
                preview_data['basic_fields'][key] = value

        return jsonify({
            'status': 'success',
            'data': preview_data,
            'message': 'JSON数据预览成功'
        })

    except Exception as e:
        current_app.logger.error(f"预览JSON导入失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'预览JSON导入失败: {str(e)}'
        }), 500


@bp.route('/import_json/confirm', methods=['POST'])
def confirm_json_import():
    """
    确认JSON导入
    """
    try:
        if not request.is_json:
            return jsonify({
                'status': 'error',
                'message': '请求体必须为JSON格式'
            }), 400

        data = request.get_json()

        # 支持两种字段名：temp_file_id 或 temp_data_id
        temp_file_id = data.get('temp_file_id') or data.get('temp_data_id')
        if not temp_file_id:
            return jsonify({
                'status': 'error',
                'message': '请求必须包含temp_file_id或temp_data_id字段'
            }), 400
        temp_file_path = os.path.join(tempfile.gettempdir(), f"{temp_file_id}.json")

        if not os.path.exists(temp_file_path):
            return jsonify({
                'status': 'error',
                'message': '临时文件不存在或已过期'
            }), 400

        # 读取临时文件中的数据
        with open(temp_file_path, 'r', encoding='utf-8') as f:
            parsed_data = json.load(f)

        # 创建JSON导入器
        json_data_str = json.dumps(parsed_data, ensure_ascii=False)
        importer = JsonImporter(json_data_str, 'imported_data.json')
        importer.parsed_data = parsed_data
        importer.form_type = parsed_data.get('文档后缀', '')

        # 创建表单提交记录
        submission = importer.create_form_submission()
        if not submission:
            return jsonify({
                'status': 'error',
                'message': '创建表单提交记录失败',
                'errors': importer.errors,
                'warnings': importer.warnings,
                'debug_info': {
                    'form_type': importer.form_type,
                    'parsed_data_keys': list(parsed_data.keys()) if parsed_data else [],
                    'company_name': parsed_data.get('公司名称', '') if parsed_data else '',
                    'record_date': parsed_data.get('记录日期', '') if parsed_data else ''
                }
            }), 400

        # 清理临时文件
        try:
            os.remove(temp_file_path)
        except:
            pass

        # 获取导入摘要
        summary = importer.get_import_summary()

        return jsonify({
            'status': 'success',
            'data': {
                'submission_id': submission.id,
                'summary': summary,
                'message': 'JSON数据导入成功'
            }
        })

    except Exception as e:
        print(f"❌ 确认JSON导入失败: {str(e)}")
        print(f"❌ 错误堆栈: {traceback.format_exc()}")
        current_app.logger.error(f"确认JSON导入失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'确认JSON导入失败: {str(e)}'
        }), 500


@bp.route('/test_detection', methods=['POST'])
def test_detection():
    """测试表单类型检测"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '文件名为空'}), 400

        # 保存临时文件
        filename = secure_filename(file.filename)
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, filename)
        file.save(temp_file_path)

        print(f"🚀 测试检测API: {temp_file_path}")
        print(f"📋 原始文件名: {file.filename}")
        print(f"📋 安全文件名: {filename}")

        # 创建导入器并测试检测（传递原始文件名）
        importer = ExcelImporter(temp_file_path, original_filename=file.filename)

        # 检查工作簿是否加载成功
        workbook_loaded = importer.load_workbook()

        detected_type = None
        sheet_names = []
        errors = []

        if workbook_loaded:
            sheet_names = importer.workbook.sheetnames if importer.workbook else []
            detected_type = importer.detect_form_type()
        else:
            errors = importer.errors

        print(f"📊 检测结果: {detected_type}")

        result = {
            'filename': file.filename,
            'detected_type': detected_type,
            'temp_file_path': temp_file_path,
            'importer_filename': importer.filename,
            'workbook_loaded': workbook_loaded,
            'sheet_names': sheet_names,
            'errors': errors
        }

        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        if os.path.exists(temp_dir):
            os.rmdir(temp_dir)

        return jsonify(result)

    except Exception as e:
        print(f"❌ 测试检测失败: {e}")
        return jsonify({'error': str(e)}), 500

@bp.route('/import_excel/templates', methods=['GET'])
def get_import_templates():
    """
    获取Excel导入模板示例
    """
    try:
        # 返回各种表单类型的导入模板信息
        templates = {
            '安全测评': {
                'name': '安全测评导入模板',
                'description': '用于导入安全测评相关的运维信息',
                'required_fields': ['公司名称', '部署包版本', '记录日期', '管理员页面IP', '用户页面IP'],
                'optional_fields': ['客户标识', '管理员账号', '管理员密码'],
                'server_fields': ['IP地址', '用途', '系统发行版', '内存', 'CPU', '磁盘'],
                'example_data': {
                    '公司名称': '示例科技有限公司',
                    '部署包版本': 'v1.0.0',
                    '记录日期': '2024-01-01',
                    '管理员页面IP': '*************',
                    '用户页面IP': '*************'
                }
            },
            '安全监测': {
                'name': '安全监测导入模板',
                'description': '用于导入安全监测相关的运维信息',
                'required_fields': ['公司名称', '前端版本', '后端版本', '记录日期'],
                'optional_fields': ['客户标识'],
                'server_fields': ['IP地址', '用途', '系统发行版', '内存', 'CPU', '磁盘'],
                'example_data': {
                    '公司名称': '示例科技有限公司',
                    '前端版本': 'v2.0.0',
                    '后端版本': 'v2.0.0',
                    '记录日期': '2024-01-01'
                }
            },
            '应用加固': {
                'name': '应用加固导入模板',
                'description': '用于导入应用加固相关的运维信息',
                'required_fields': ['公司名称', '客户', '记录日期'],
                'optional_fields': ['客户标识', '平台地址', '管理员账号', '管理员密码'],
                'server_fields': ['IP地址', '用途', '系统发行版', '内存', 'CPU', '磁盘'],
                'example_data': {
                    '公司名称': '示例科技有限公司',
                    '客户': '客户联系人',
                    '记录日期': '2024-01-01',
                    '平台地址': 'https://example.com'
                }
            }
        }

        return jsonify({
            'status': 'success',
            'data': templates
        })

    except Exception as e:
        current_app.logger.error(f"获取导入模板失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取导入模板失败: {str(e)}'
        }), 500