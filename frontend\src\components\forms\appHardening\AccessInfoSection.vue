<template>
  <collapsible-card card-class="border-info" storage-key="access-info-section">
    <template #header>
      <i class="bi bi-shield-lock me-2"></i>访问信息
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-info">平台访问: {{ platformAccessUrl ? '已填写' : '未填写' }}</span>
        <span class="badge bg-info">管理员信息: {{ adminInfo ? '已填写' : '未填写' }}</span>
      </div>
    </template>

    <!-- 平台访问地址子部分 -->
    <div class="access-subsection mb-4">
      <div class="subsection-header">
        <h6 class="subsection-title">
          <i class="bi bi-globe me-2 text-primary"></i>
          平台访问地址
        </h6>
      </div>
      <div class="subsection-content">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="platform-access-url"
            :value="platformAccessUrl"
            @input="$emit('update:platformAccessUrl', $event.target.value)"
            :readonly="isAutoFilled"
            :class="{'bg-light': isAutoFilled}"
            placeholder="请输入平台访问地址"
          />
          <label for="platform-access-url">平台访问地址 <span class="text-danger">*</span></label>
          <!-- 插槽：添加平台访问地址提示信息 -->
          <slot name="append-platform-access"></slot>
        </div>
      </div>
    </div>

    <!-- 管理员信息子部分 -->
    <div class="access-subsection mb-4">
      <div class="subsection-header">
        <h6 class="subsection-title">
          <i class="bi bi-person-gear me-2 text-primary"></i>
          管理员信息及普通用户信息
        </h6>
      </div>
      <div class="subsection-content">
        <!-- 账号信息 -->
        <div class="row g-3">
          <!-- 超级管理员账号 -->
          <div class="col-md-6">
            <div class="account-card">
              <div class="account-card-header">
                <i class="bi bi-shield-fill-check me-1 text-danger"></i>
                <span class="fw-bold">超级管理员</span>
              </div>
              <div class="account-card-body">
                <div class="input-group mb-2">
                  <span class="input-group-text">账号</span>
                  <input
                    type="text"
                    class="form-control"
                    v-model="sadminAccount"
                    placeholder="超级管理员账号"
                  >
                </div>
                <div class="input-group">
                  <span class="input-group-text">密码</span>
                  <input
                    :type="showSadminPassword ? 'text' : 'password'"
                    class="form-control"
                    v-model="sadminPassword"
                    placeholder="超级管理员密码"
                  >
                  <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('sadmin')">
                    <i :class="showSadminPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 管理员账号 -->
          <div class="col-md-6">
            <div class="account-card">
              <div class="account-card-header">
                <i class="bi bi-person-badge me-1 text-warning"></i>
                <span class="fw-bold">管理员</span>
              </div>
              <div class="account-card-body">
                <div class="input-group mb-2">
                  <span class="input-group-text">账号</span>
                  <input
                    type="text"
                    class="form-control"
                    v-model="adminAccount"
                    placeholder="管理员账号"
                  >
                </div>
                <div class="input-group">
                  <span class="input-group-text">密码</span>
                  <input
                    :type="showAdminPassword ? 'text' : 'password'"
                    class="form-control"
                    v-model="adminPassword"
                    placeholder="管理员密码"
                  >
                  <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('admin')">
                    <i :class="showAdminPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 平台用户账号 -->
        <div class="row g-3 mt-3">
          <div class="col-md-6">
            <div class="account-card">
              <div class="account-card-header">
                <i class="bi bi-person me-1 text-info"></i>
                <span class="fw-bold">平台用户</span>
              </div>
              <div class="account-card-body">
                <div class="input-group mb-2">
                  <span class="input-group-text">账号</span>
                  <input
                    type="text"
                    class="form-control"
                    v-model="userAccount"
                    placeholder="平台用户账号"
                  >
                </div>
                <div class="input-group">
                  <span class="input-group-text">密码</span>
                  <input
                    :type="showUserPassword ? 'text' : 'password'"
                    class="form-control"
                    v-model="userPassword"
                    placeholder="平台用户密码"
                  >
                  <button class="btn btn-outline-secondary" type="button" @click="togglePasswordVisibility('user')">
                    <i :class="showUserPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 升级平台地址子部分 -->
    <div class="access-subsection mb-4">
      <div class="subsection-header">
        <h6 class="subsection-title">
          <i class="bi bi-arrow-up-circle me-2 text-success"></i>
          升级平台地址
        </h6>
      </div>
      <div class="subsection-content">
        <div class="form-floating mb-3">
          <input
            type="text"
            class="form-control"
            id="upgrade-info"
            :value="upgradeInfo"
            @input="$emit('update:upgradeInfo', $event.target.value)"
            :class="{ 'bg-light': isUpgradeInfoAutoFilled }"
            :readonly="isUpgradeInfoAutoFilled"
            placeholder="请输入升级平台地址"
          />
          <label for="upgrade-info">升级平台地址 <span class="text-danger">*</span></label>
          <slot name="append-upgrade-info"></slot>
        </div>

        <!-- 升级用户配置 -->
        <div class="mt-3">
          <upgrade-user-config
            v-model:user-config="localUpgradeUserConfig"
            @config-change="handleUpgradeUserConfigChange"
          />
        </div>
      </div>
    </div>
  </collapsible-card>
</template>

<script>
import CollapsibleCard from '../common/CollapsibleCard.vue'
import UpgradeUserConfig from './UpgradeUserConfig.vue'
import { appHardeningUserDefaults } from '@/config/formDataConfig'

/**
 * 访问信息部分组件
 * 用于填写平台访问地址、管理员信息及普通用户信息、升级平台地址等字段
 */
export default {
  name: 'AccessInfoSection',
  props: {
    // 平台访问地址
    platformAccessUrl: {
      type: String,
      default: ''
    },
    // 管理员信息
    adminInfo: {
      type: String,
      default: ''
    },
    // 升级平台地址
    upgradeInfo: {
      type: String,
      default: ''
    },
    // 服务器信息列表
    serverList: {
      type: Array,
      default: () => []
    },
    // 是否自动填充
    isAutoFilled: {
      type: Boolean,
      default: false
    },
    // 升级用户配置
    upgradeUserConfig: {
      type: Object,
      default: () => ({
        username: 'upgrader',
        password: 'upgrader@abc#2020'
      })
    }
  },

  emits: [
    'update:platformAccessUrl',
    'update:adminInfo',
    'update:upgradeInfo',
    'update:upgradeUserConfig',
    'auto-filled',
    'upgrade-auto-filled'
  ],
  components: {
    CollapsibleCard,
    UpgradeUserConfig
  },
  data() {
    return {
      // 标记是否正在更新管理员信息，避免循环更新
      isUpdatingAdminInfo: false,
      // 用户账号密码
      userAccount: appHardeningUserDefaults.platformUser.account,
      userPassword: appHardeningUserDefaults.platformUser.password,
      showUserPassword: false,
      // 管理员账号密码
      adminAccount: appHardeningUserDefaults.admin.account,
      adminPassword: appHardeningUserDefaults.admin.password,
      showAdminPassword: false,
      // 超级管理员账号密码
      sadminAccount: appHardeningUserDefaults.superAdmin.account,
      sadminPassword: appHardeningUserDefaults.superAdmin.password,
      showSadminPassword: false,
      // 自动填充状态
      isUpgradeInfoAutoFilled: false,
      // 升级用户配置
      localUpgradeUserConfig: {
        username: '',
        password: ''
      }
    }
  },
  emits: [
    'update:platformAccessUrl',
    'update:adminInfo',
    'update:upgradeInfo',
    'update:upgradeUserConfig',
    'auto-filled',
    'upgrade-auto-filled'
  ],

  // 组件创建后初始化数据
  created() {
    // 如果没有传入管理员信息，则使用默认值初始化
    if (!this.adminInfo) {
      this.$nextTick(() => {
        this.updateAdminInfo()
      })
    }
  },

  // 监听服务器列表变化，自动填充平台访问地址和升级平台地址
  watch: {
    serverList: {
      handler(newVal) {
        this.autoFillPlatformAccessUrl(newVal)
        this.autoFillUpgradeInfo(newVal)
      },
      deep: true,
      immediate: true
    },
    // 监听账号密码变化，更新adminInfo
    userAccount: { handler() { this.updateAdminInfo() } },
    userPassword: { handler() { this.updateAdminInfo() } },
    adminAccount: { handler() { this.updateAdminInfo() } },
    adminPassword: { handler() { this.updateAdminInfo() } },
    sadminAccount: { handler() { this.updateAdminInfo() } },
    sadminPassword: { handler() { this.updateAdminInfo() } },
    // 监听adminInfo变化，解析账号密码
    adminInfo: {
      handler(newVal) {
        if (newVal && !this.isUpdatingAdminInfo) {
          this.parseAdminInfo(newVal)
        }
      },
      immediate: true
    },
    // 监听外部升级用户配置变化
    upgradeUserConfig: {
      handler(newConfig) {
        if (newConfig) {
          this.localUpgradeUserConfig = { ...newConfig }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /**
     * 更新管理员信息
     */
    updateAdminInfo() {
      this.isUpdatingAdminInfo = true

      let adminInfoText = ''

      // 添加平台用户信息
      if (this.userAccount || this.userPassword) {
        adminInfoText += `平台用户账号：${this.userAccount || ''} 密码：${this.userPassword || ''}\n`
      }

      // 添加管理员信息
      if (this.adminAccount || this.adminPassword) {
        adminInfoText += `管理员账号：${this.adminAccount || ''} 密码：${this.adminPassword || ''}\n`
      }

      // 添加超级管理员信息
      if (this.sadminAccount || this.sadminPassword) {
        adminInfoText += `超级管理员账号：${this.sadminAccount || ''} 密码：${this.sadminPassword || ''}`
      }

      this.$emit('update:adminInfo', adminInfoText)

      setTimeout(() => {
        this.isUpdatingAdminInfo = false
      }, 100)
    },

    /**
     * 解析管理员信息
     * @param {String} adminInfoText - 管理员信息文本
     */
    parseAdminInfo(adminInfoText) {
      if (!adminInfoText || this.isUpdatingAdminInfo) return

      this.isUpdatingAdminInfo = true

      // 解析平台用户信息
      const userMatch = adminInfoText.match(/平台用户账号[：:]\s*([^\s]*)\s*密码[：:]\s*([^\n]*)/i)
      if (userMatch) {
        this.userAccount = userMatch[1] || ''
        this.userPassword = userMatch[2] || ''
      }

      // 解析管理员信息
      const adminMatch = adminInfoText.match(/管理员账号[：:]\s*([^\s]*)\s*密码[：:]\s*([^\n]*)/i)
      if (adminMatch) {
        this.adminAccount = adminMatch[1] || 'admin'
        this.adminPassword = adminMatch[2] || ''
      }

      // 解析超级管理员信息
      const sadminMatch = adminInfoText.match(/超级管理员账号[：:]\s*([^\s]*)\s*密码[：:]\s*([^\n]*)/i)
      if (sadminMatch) {
        this.sadminAccount = sadminMatch[1] || 'sadmin'
        this.sadminPassword = sadminMatch[2] || ''
      }

      setTimeout(() => {
        this.isUpdatingAdminInfo = false
      }, 100)
    },

    /**
     * 切换密码可见性
     * @param {String} type - 用户类型
     */
    togglePasswordVisibility(type) {
      if (type === 'user') {
        this.showUserPassword = !this.showUserPassword
      } else if (type === 'admin') {
        this.showAdminPassword = !this.showAdminPassword
      } else if (type === 'sadmin') {
        this.showSadminPassword = !this.showSadminPassword
      }
    },

    /**
     * 生成随机密码
     * @param {String} type - 用户类型
     */
    generateRandomPassword(type) {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
      let password = ''

      // 确保密码包含大写字母、小写字母、数字和特殊字符
      password += chars.charAt(Math.floor(Math.random() * 26)) // 大写字母
      password += chars.charAt(26 + Math.floor(Math.random() * 26)) // 小写字母
      password += chars.charAt(52 + Math.floor(Math.random() * 10)) // 数字
      password += chars.charAt(62 + Math.floor(Math.random() * 8)) // 特殊字符

      // 添加剩余随机字符
      for (let i = 0; i < 6; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length))
      }

      // 打乱密码字符顺序
      password = password.split('').sort(() => 0.5 - Math.random()).join('')

      // 设置对应类型的密码
      if (type === 'user') {
        this.userPassword = password
      } else if (type === 'admin') {
        this.adminPassword = password
      } else if (type === 'sadmin') {
        this.sadminPassword = password
      }
    },

    /**
     * 自动填充平台访问地址
     * @param {Array} serverList - 服务器列表
     */
    autoFillPlatformAccessUrl(serverList) {
      if (!serverList || !Array.isArray(serverList) || serverList.length === 0) {
        return
      }

      // 查找包含secweb组件的服务器
      const platformServers = []

      serverList.forEach(server => {
        if (server.部署应用 && server.部署应用.includes('secweb')) {
          const ip = server.IP地址
          const port = server.组件端口 && server.组件端口['secweb']
            ? server.组件端口['secweb']
            : '8000'

          if (ip) {
            platformServers.push(`http://${ip}:${port}`)
          }
        }
      })

      if (platformServers.length > 0) {
        // 发出更新事件
        this.$emit('update:platformAccessUrl', platformServers.join(', '))
        // 通知父组件已自动填充
        this.$emit('auto-filled', true)
      } else {
        // 如果没有找到匹配的服务器，清空平台访问地址
        this.$emit('update:platformAccessUrl', '')
        // 通知父组件未自动填充
        this.$emit('auto-filled', false)
      }
    },

    /**
     * 自动填充升级平台地址
     * @param {Array} serverList - 服务器列表
     */
    autoFillUpgradeInfo(serverList) {
      if (!serverList || !Array.isArray(serverList) || serverList.length === 0) {
        this.isUpgradeInfoAutoFilled = false
        return
      }

      // 查找包含luna组件的服务器
      const upgradeServers = []

      serverList.forEach(server => {
        if (server.部署应用 && server.部署应用.includes('luna')) {
          const ip = server.IP地址
          const port = server.组件端口 && server.组件端口['luna']
            ? server.组件端口['luna']
            : '9001'

          if (ip) {
            upgradeServers.push(`http://${ip}:${port}`)
          }
        }
      })

      if (upgradeServers.length > 0) {
        // 发出更新事件，只取第一个地址
        this.$emit('update:upgradeInfo', upgradeServers[0])
        // 标记为已自动填充
        this.isUpgradeInfoAutoFilled = true
        // 通知父组件升级平台地址已自动填充
        this.$emit('upgrade-auto-filled', true)
      } else {
        // 如果没有找到匹配的服务器，清空升级平台地址
        this.$emit('update:upgradeInfo', '')
        // 取消自动填充标记
        this.isUpgradeInfoAutoFilled = false
        // 通知父组件升级平台地址未自动填充
        this.$emit('upgrade-auto-filled', false)
      }
    },

    /**
     * 处理升级用户配置变化
     * @param {Object} config - 升级用户配置
     */
    handleUpgradeUserConfigChange(config) {
      this.localUpgradeUserConfig = { ...config }
      this.$emit('update:upgradeUserConfig', config)
    }
  }
}
</script>

<style scoped>

/* 基础样式 */
.input-group {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.input-group-text {
  min-width: 60px;
  justify-content: center;
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 按钮样式 */
.btn-outline-secondary {
  color: #6c757d;
  border-color: #dee2e6;
}

.btn-outline-secondary:hover {
  background-color: #f8f9fa;
  color: #495057;
}

/* 账号卡片样式 */
.account-card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
  transition: all 0.3s ease;
}

.account-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.account-card-header {
  background: #f8f9fa;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.9rem;
}

.account-card-body {
  padding: 1rem;
}

@media (max-width: 768px) {
  .account-card-body {
    padding: 0.75rem;
  }

  .input-group-text {
    min-width: 70px;
    font-size: 0.8rem;
  }
}
</style>
