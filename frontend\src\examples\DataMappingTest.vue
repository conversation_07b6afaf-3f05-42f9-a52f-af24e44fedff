<template>
  <div class="data-mapping-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-arrow-left-right me-2"></i>
                数据映射测试 - 安全监测
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 操作面板 -->
              <div class="row mb-4">
                <div class="col-md-6">
                  <button class="btn btn-primary me-2" @click="fillTestData">
                    填充测试数据
                  </button>
                  <button class="btn btn-success me-2" @click="validateData">
                    验证数据
                  </button>
                  <button class="btn btn-warning" @click="clearData">
                    清空数据
                  </button>
                </div>
                <div class="col-md-6">
                  <div class="alert alert-info mb-0 py-2">
                    <strong>测试目标：</strong>验证前端版本和后端版本是否正确映射到表单数据
                  </div>
                </div>
              </div>

              <!-- 表单测试 -->
              <div class="row mb-4">
                <div class="col-12">
                  <h6>安全监测表单</h6>
                  <div class="card">
                    <div class="card-body">
                      <base-form
                        v-model="testFormData"
                        form-type="安全监测"
                        :component-groups="componentGroups"
                        :key="`form-${refreshKey}`"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 数据检查 -->
              <div class="row">
                <div class="col-md-6">
                  <h6>表单数据检查</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div class="mb-3">
                        <strong>关键字段值：</strong>
                        <div class="small mt-2">
                          <div class="row">
                            <div class="col-6"><strong>前端版本:</strong></div>
                            <div class="col-6">
                              <span :class="testFormData.前端版本 ? 'text-success' : 'text-danger'">
                                {{ testFormData.前端版本 || '(空)' }}
                              </span>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-6"><strong>后端版本:</strong></div>
                            <div class="col-6">
                              <span :class="testFormData.后端版本 ? 'text-success' : 'text-danger'">
                                {{ testFormData.后端版本 || '(空)' }}
                              </span>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-6"><strong>公司名称:</strong></div>
                            <div class="col-6">
                              <span :class="testFormData.公司名称 ? 'text-success' : 'text-danger'">
                                {{ testFormData.公司名称 || '(空)' }}
                              </span>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-6"><strong>记录日期:</strong></div>
                            <div class="col-6">
                              <span :class="testFormData.记录日期 ? 'text-success' : 'text-danger'">
                                {{ testFormData.记录日期 || '(空)' }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="mb-3">
                        <strong>验证结果：</strong>
                        <div v-if="validationResult" class="mt-2">
                          <div v-if="validationResult.isValid" class="text-success">
                            ✅ 所有必填字段都已填写
                          </div>
                          <div v-else class="text-danger">
                            ❌ 缺少必填字段：{{ validationResult.missingFields.join(', ') }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-6">
                  <h6>完整表单数据</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <pre class="small">{{ JSON.stringify(testFormData, null, 2) }}</pre>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 模拟提交测试 -->
              <div class="row mt-4">
                <div class="col-12">
                  <h6>模拟提交测试</h6>
                  <div class="card">
                    <div class="card-body">
                      <button 
                        class="btn btn-primary me-2" 
                        @click="simulateSubmit"
                        :disabled="!canSubmit"
                      >
                        模拟提交到后端
                      </button>
                      <span v-if="!canSubmit" class="text-muted">
                        请先填写必填字段
                      </span>
                      
                      <div v-if="submitResult" class="mt-3">
                        <div class="alert" :class="submitResult.success ? 'alert-success' : 'alert-danger'">
                          <strong>{{ submitResult.success ? '提交成功' : '提交失败' }}:</strong>
                          {{ submitResult.message }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseForm from '@/components/forms/common/BaseForm.vue'
import { getInitialFormData } from '@/config/formDataConfig'

export default {
  name: 'DataMappingTest',
  components: {
    BaseForm
  },
  data() {
    return {
      testFormData: {},
      componentGroups: {},
      refreshKey: 0,
      validationResult: null,
      submitResult: null
    }
  },
  computed: {
    canSubmit() {
      return this.testFormData.前端版本 && 
             this.testFormData.后端版本 && 
             this.testFormData.公司名称 && 
             this.testFormData.记录日期
    }
  },
  methods: {
    /**
     * 填充测试数据
     */
    fillTestData() {
      // 获取初始数据
      this.testFormData = getInitialFormData('安全监测')
      
      // 填充测试值
      this.testFormData.前端版本 = 'V5.1.2sp2'
      this.testFormData.后端版本 = 'V5.1.2sp2'
      this.testFormData.公司名称 = '测试公司'
      this.testFormData.记录日期 = new Date().toISOString().split('T')[0]
      
      console.log('填充测试数据:', this.testFormData)
      this.refreshKey += 1
    },

    /**
     * 验证数据
     */
    validateData() {
      const requiredFields = ['公司名称', '前端版本', '后端版本', '记录日期']
      const missingFields = requiredFields.filter(field => !this.testFormData[field])
      
      this.validationResult = {
        isValid: missingFields.length === 0,
        missingFields
      }
      
      console.log('验证结果:', this.validationResult)
    },

    /**
     * 清空数据
     */
    clearData() {
      this.testFormData = getInitialFormData('安全监测')
      this.validationResult = null
      this.submitResult = null
      this.refreshKey += 1
    },

    /**
     * 模拟提交到后端
     */
    async simulateSubmit() {
      try {
        console.log('模拟提交数据:', this.testFormData)
        
        // 模拟后端验证逻辑
        const requiredFields = ['公司名称', '前端版本', '后端版本', '记录日期']
        const missingFields = requiredFields.filter(field => !this.testFormData[field])
        
        if (missingFields.length > 0) {
          this.submitResult = {
            success: false,
            message: `缺少必要字段: ${missingFields.join(', ')}`
          }
        } else {
          this.submitResult = {
            success: true,
            message: '数据验证通过，可以正常生成Excel'
          }
        }
        
        console.log('模拟提交结果:', this.submitResult)
        
      } catch (error) {
        console.error('模拟提交失败:', error)
        this.submitResult = {
          success: false,
          message: `提交失败: ${error.message}`
        }
      }
    }
  },
  watch: {
    testFormData: {
      handler(newData) {
        console.log('表单数据变化:', {
          前端版本: newData.前端版本,
          后端版本: newData.后端版本,
          公司名称: newData.公司名称,
          记录日期: newData.记录日期
        })
      },
      deep: true
    }
  },
  mounted() {
    console.log('DataMappingTest 组件已挂载')
    this.testFormData = getInitialFormData('安全监测')
    
    // 加载组件分组数据
    this.componentGroups = {
      security: {
        'security-monitor': [
          { name: 'web-service-nginx', description: 'Web服务', port: '443' },
          { name: 'init', description: '初始化服务', port: '8181' },
          { name: 'kibana', description: 'Kibana服务', port: '5601' }
        ]
      }
    }
  }
}
</script>

<style scoped>
.data-mapping-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
}

pre {
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.75rem;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
}

.text-success { color: #198754 !important; }
.text-danger { color: #dc3545 !important; }
.text-muted { color: #6c757d !important; }
</style>
