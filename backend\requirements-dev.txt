# ============================================================================
# Export Excel Backend - Development Dependencies
# ============================================================================
# 生成时间: 2025-01-15
# 用途: 开发环境专用依赖，包含测试、代码质量检查等工具
# 安装方式: pip install -r requirements-dev.txt
# ============================================================================

# 首先安装生产环境依赖
-r requirements.txt

# ============================================================================
# 🧪 测试框架
# ============================================================================
pytest>=7.0.0                   # Python测试框架
pytest-flask>=1.2.0             # Flask应用测试扩展
pytest-cov>=4.0.0               # 测试覆盖率
pytest-mock>=3.10.0             # Mock测试工具
pytest-xdist>=3.2.0             # 并行测试执行

# ============================================================================
# 🎨 代码格式化与质量
# ============================================================================
black>=22.0.0                   # 代码格式化工具
flake8>=5.0.0                   # 代码风格检查
isort>=5.12.0                   # import语句排序
autopep8>=2.0.0                 # PEP8自动格式化

# ============================================================================
# 🔍 类型检查与静态分析
# ============================================================================
mypy>=1.0.0                     # 静态类型检查
pylint>=2.17.0                  # 代码质量分析
bandit>=1.7.0                   # 安全漏洞检查

# ============================================================================
# 📚 文档生成
# ============================================================================
sphinx>=6.0.0                   # 文档生成工具
sphinx-rtd-theme>=1.2.0         # ReadTheDocs主题
sphinx-autodoc-typehints>=1.22.0 # 类型提示文档

# ============================================================================
# 🔧 开发工具
# ============================================================================
ipython>=8.10.0                 # 增强的Python交互式shell
ipdb>=0.13.0                    # 调试器
pre-commit>=3.0.0               # Git钩子管理
python-dotenv>=1.0.0            # 环境变量管理（已在主依赖中）

# ============================================================================
# 📊 性能分析
# ============================================================================
memory-profiler>=0.60.0         # 内存使用分析
line-profiler>=4.0.0            # 代码行级性能分析
py-spy>=0.3.0                   # Python性能分析器

# ============================================================================
# 🌐 API测试工具
# ============================================================================
httpx>=0.24.0                   # 异步HTTP客户端
responses>=0.23.0               # HTTP请求Mock工具

# ============================================================================
# 📝 配置文件
# ============================================================================
# 开发环境配置示例文件应该包含：
# - .env.development
# - pytest.ini
# - .flake8
# - pyproject.toml (已存在)
# - .pre-commit-config.yaml
# ============================================================================

# ============================================================================
# 🚀 使用说明
# ============================================================================
# 1. 安装开发依赖：
#    pip install -r requirements-dev.txt
#
# 2. 运行测试：
#    pytest
#    pytest --cov=app tests/  # 带覆盖率
#
# 3. 代码格式化：
#    black .
#    isort .
#
# 4. 代码检查：
#    flake8 .
#    mypy .
#    pylint app/
#
# 5. 安全检查：
#    bandit -r app/
#
# 6. 预提交检查：
#    pre-commit install
#    pre-commit run --all-files
# ============================================================================
