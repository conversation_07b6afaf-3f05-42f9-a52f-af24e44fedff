<template>
  <div class="number-field-component">
    <input 
      v-if="field.field_type === 'slider'"
      :id="id"
      type="range"
      :value="value"
      :min="minValue"
      :max="maxValue"
      :step="stepValue"
      :readonly="field.is_readonly"
      :required="field.is_required"
      :class="sliderClasses"
      @input="handleInput"
      @change="handleChange"
    >
    <input 
      v-else
      :id="id"
      type="number"
      :value="value"
      :placeholder="field.placeholder"
      :min="minValue"
      :max="maxValue"
      :step="stepValue"
      :readonly="field.is_readonly"
      :required="field.is_required"
      :class="fieldClasses"
      @input="handleInput"
      @change="handleChange"
    >
    <div v-if="field.field_type === 'slider'" class="slider-value mt-1 text-center">
      <small class="text-muted">当前值: {{ value }}</small>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NumberFieldComponent',
  props: {
    id: String,
    field: {
      type: Object,
      required: true
    },
    value: {
      type: [String, Number],
      default: 0
    }
  },
  emits: ['update:value', 'field-change'],
  computed: {
    validation() {
      return this.field.validation_rules || {}
    },
    minValue() {
      return this.validation.min !== undefined ? this.validation.min : null
    },
    maxValue() {
      return this.validation.max !== undefined ? this.validation.max : null
    },
    stepValue() {
      return this.validation.step !== undefined ? this.validation.step : null
    },
    fieldClasses() {
      let classes = ['form-control']
      
      if (this.field.css_classes) {
        classes.push(this.field.css_classes)
      }
      
      if (this.field.is_readonly) {
        classes.push('readonly-field')
      }
      
      return classes.join(' ')
    },
    sliderClasses() {
      let classes = ['form-range']
      
      if (this.field.css_classes) {
        classes.push(this.field.css_classes)
      }
      
      return classes.join(' ')
    }
  },
  methods: {
    handleInput(event) {
      const value = parseFloat(event.target.value) || 0
      this.$emit('update:value', value)
    },
    
    handleChange(event) {
      const value = parseFloat(event.target.value) || 0
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: value,
        event: 'change'
      })
    }
  }
}
</script>

<style scoped>
.readonly-field {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.slider-value {
  font-size: 0.875rem;
}
</style>
