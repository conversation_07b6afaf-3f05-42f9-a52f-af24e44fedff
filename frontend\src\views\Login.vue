<template>
  <div class="login-container">
    <!-- 背景装饰元素 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 主登录卡片 -->
    <div class="login-card">
      <!-- 品牌标识区域 -->
      <div class="brand-section">
        <div class="brand-logo">
          <i class="bi bi-shield-check"></i>
        </div>
        <h1 class="brand-title">梆梆安全</h1>
        <p class="brand-subtitle">运维信息登记平台</p>
      </div>

      <!-- 登录表单区域 -->
      <div class="login-section">
        <div class="login-header">
          <h2 class="login-title">欢迎回来</h2>
          <p class="login-subtitle">请登录您的账户以继续</p>
        </div>

        <form @submit.prevent="handleLogin" class="login-form">
          <!-- 用户名输入 -->
          <div class="form-group">
            <div class="input-wrapper">
              <i class="bi bi-person input-icon"></i>
              <input
                type="text"
                class="form-control modern-input"
                id="username"
                v-model="loginForm.username"
                :class="{ 'is-invalid': errors.username, 'has-value': loginForm.username }"
                placeholder="用户名或邮箱"
                required
              >
              <label for="username" class="floating-label">用户名/邮箱</label>
            </div>
            <div v-if="errors.username" class="error-message">
              <i class="bi bi-exclamation-circle me-1"></i>
              {{ errors.username }}
            </div>
          </div>

          <!-- 密码输入 -->
          <div class="form-group">
            <div class="input-wrapper">
              <i class="bi bi-lock input-icon"></i>
              <input
                :type="showPassword ? 'text' : 'password'"
                class="form-control modern-input"
                id="password"
                v-model="loginForm.password"
                :class="{ 'is-invalid': errors.password, 'has-value': loginForm.password }"
                placeholder="密码"
                required
              >
              <label for="password" class="floating-label">密码</label>
              <button
                type="button"
                class="password-toggle"
                @click="togglePassword"
              >
                <i :class="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
              </button>
            </div>
            <div v-if="errors.password" class="error-message">
              <i class="bi bi-exclamation-circle me-1"></i>
              {{ errors.password }}
            </div>
          </div>

          <!-- 记住我和忘记密码 -->
          <div class="form-options">
            <div class="remember-me">
              <input
                type="checkbox"
                class="modern-checkbox"
                id="rememberMe"
                v-model="loginForm.rememberMe"
              >
              <label for="rememberMe" class="checkbox-label">
                <span class="checkmark"></span>
                记住我
              </label>
            </div>
            <a href="#" class="forgot-password" @click.prevent>
              忘记密码？
            </a>
          </div>

          <!-- 错误信息 -->
          <div v-if="errorMessage" class="error-alert">
            <div class="error-content">
              <i class="bi bi-exclamation-triangle error-icon"></i>
              <div class="error-text">
                <div class="error-title">登录失败</div>
                <div class="error-message">{{ errorMessage }}</div>
              </div>
            </div>
            <button v-if="errorType === 'database_connection'"
                    @click="handleLogin"
                    class="retry-button">
              <i class="bi bi-arrow-clockwise me-1"></i>重试
            </button>
          </div>

          <!-- 登录按钮 -->
          <button
            type="submit"
            class="login-button"
            :disabled="loading"
            :class="{ 'loading': loading }"
          >
            <span v-if="loading" class="loading-spinner"></span>
            <i v-else class="bi bi-box-arrow-in-right button-icon"></i>
            <span class="button-text">{{ loading ? '登录中...' : '登录' }}</span>
          </button>

          <!-- 分割线 -->
          <div class="divider">
            <span class="divider-text">或</span>
          </div>

          <!-- 注册链接 -->
          <div class="register-section">
            <p class="register-text">还没有账户？</p>
            <router-link to="/register" class="register-link">
              <i class="bi bi-person-plus me-1"></i>
              立即注册
            </router-link>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
        rememberMe: false
      },
      showPassword: false,
      loading: false,
      errorMessage: '',
      errorDetails: '',
      errorType: '',
      errors: {}
    }
  },
  mounted() {
    // 如果已经登录，重定向到首页
    if (this.$store.getters.isAuthenticated) {
      this.$router.push('/')
    }
    
    // 从localStorage恢复记住的用户名
    const rememberedUsername = localStorage.getItem('rememberedUsername')
    if (rememberedUsername) {
      this.loginForm.username = rememberedUsername
      this.loginForm.rememberMe = true
    }
  },
  methods: {
    ...mapActions(['login']),
    
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    validateForm() {
      this.errors = {}
      
      if (!this.loginForm.username.trim()) {
        this.errors.username = '请输入用户名或邮箱'
      }
      
      if (!this.loginForm.password) {
        this.errors.password = '请输入密码'
      } else if (this.loginForm.password.length < 6) {
        this.errors.password = '密码长度不能少于6位'
      }
      
      return Object.keys(this.errors).length === 0
    },
    
    async handleLogin() {
      if (!this.validateForm()) {
        return
      }
      
      this.loading = true
      this.errorMessage = ''
      this.errorDetails = ''
      this.errorType = ''

      try {
        await this.login({
          username: this.loginForm.username.trim(),
          password: this.loginForm.password
        })
        
        // 处理记住我功能
        if (this.loginForm.rememberMe) {
          localStorage.setItem('rememberedUsername', this.loginForm.username.trim())
        } else {
          localStorage.removeItem('rememberedUsername')
        }
        
        // 登录成功，重定向
        const redirect = this.$route.query.redirect || '/'
        this.$router.push(redirect)
        
        this.$toast.success('登录成功', '欢迎回来！')
        
      } catch (error) {
        // 处理不同类型的错误
        if (error.response && error.response.data) {
          const errorData = error.response.data
          this.errorMessage = errorData.message || '登录失败，请稍后重试'
          this.errorType = errorData.error_type || 'general'
          this.errorDetails = errorData.details || ''
        } else {
          this.errorMessage = error.message || '网络连接失败，请检查网络后重试'
          this.errorType = 'general'
          this.errorDetails = ''
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
/* ==================== 主容器样式 ==================== */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

/* ==================== 简化的背景装饰 ==================== */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  opacity: 0.1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 120px;
  height: 120px;
  top: 15%;
  left: 15%;
  animation-delay: 0s;
}

.shape-2 {
  width: 80px;
  height: 80px;
  top: 65%;
  right: 20%;
  animation-delay: 3s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 25%;
  left: 25%;
  animation-delay: 6s;
}

.shape-4 {
  width: 40px;
  height: 40px;
  top: 35%;
  right: 35%;
  animation-delay: 1.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-15px);
    opacity: 0.6;
  }
}

/* ==================== 登录卡片 ==================== */
.login-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  width: 100%;
  max-width: 900px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  overflow: hidden;
  animation: slideUp 0.5s ease-out;
  position: relative;
  z-index: 2;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 品牌区域 ==================== */
.brand-section {
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0.5;
}

.brand-logo {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
}

.brand-logo i {
  font-size: 36px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.brand-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  margin: 0;
  position: relative;
  z-index: 1;
}

/* ==================== 登录表单区域 ==================== */
.login-section {
  padding: 60px 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-header {
  margin-bottom: 40px;
  text-align: center;
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 8px;
}

.login-subtitle {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
}

/* ==================== 表单样式 ==================== */
.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 18px;
  z-index: 2;
  transition: color 0.3s ease;
}

.modern-input {
  width: 100%;
  padding: 16px 16px 16px 50px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 16px;
  background: #fafafa;
  transition: all 0.3s ease;
  outline: none;
}

.modern-input:focus {
  border-color: #1e40af;
  background: white;
  box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.1);
}

.modern-input:focus + .floating-label,
.modern-input.has-value + .floating-label {
  transform: translateY(-28px) scale(0.85);
  color: #1e40af;
}

.modern-input:focus ~ .input-icon {
  color: #1e40af;
}

.floating-label {
  position: absolute;
  left: 50px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 16px;
  pointer-events: none;
  transition: all 0.3s ease;
  background: white;
  padding: 0 8px;
  z-index: 1;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  z-index: 2;
}

.password-toggle:hover {
  color: #1e40af;
  background: rgba(30, 64, 175, 0.1);
}

/* ==================== 表单选项 ==================== */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.remember-me {
  display: flex;
  align-items: center;
}

.modern-checkbox {
  display: none;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #6c757d;
  user-select: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  margin-right: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.modern-checkbox:checked + .checkbox-label .checkmark {
  background: #1e40af;
  border-color: #1e40af;
}

.modern-checkbox:checked + .checkbox-label .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.forgot-password {
  color: #1e40af;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* ==================== 错误提示 ==================== */
.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.error-alert {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border: 1px solid #f8bbd9;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.error-content {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.error-icon {
  color: #dc3545;
  font-size: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.error-text {
  flex: 1;
}

.error-title {
  font-weight: 600;
  color: #721c24;
  margin-bottom: 4px;
}

.error-message {
  color: #721c24;
  font-size: 14px;
  margin: 0;
}

.retry-button {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 16px;
}

.retry-button:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* ==================== 登录按钮 ==================== */
.login-button {
  width: 100%;
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 50%, #2563eb 100%);
  border: none;
  border-radius: 12px;
  padding: 16px 24px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  margin-bottom: 32px;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

.login-button.loading {
  pointer-events: none;
  opacity: 0.8;
}

.button-icon {
  margin-right: 8px;
  font-size: 18px;
}

.button-text {
  font-weight: 600;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ==================== 分割线 ==================== */
.divider {
  position: relative;
  text-align: center;
  margin: 32px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e9ecef;
}

.divider-text {
  background: white;
  color: #6c757d;
  padding: 0 16px;
  font-size: 14px;
  position: relative;
}

/* ==================== 注册区域 ==================== */
.register-section {
  text-align: center;
}

.register-text {
  color: #6c757d;
  margin-bottom: 16px;
  font-size: 14px;
}

.register-link {
  display: inline-flex;
  align-items: center;
  color: #1e40af;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  padding: 12px 24px;
  border: 2px solid #1e40af;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.register-link:hover {
  background: #1e40af;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .login-card {
    grid-template-columns: 1fr;
    max-width: 400px;
  }

  .brand-section {
    padding: 40px 30px;
  }

  .brand-title {
    font-size: 2rem;
  }

  .login-section {
    padding: 40px 30px;
  }

  .login-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .brand-section {
    padding: 30px 20px;
  }

  .brand-logo {
    width: 60px;
    height: 60px;
  }

  .brand-logo i {
    font-size: 28px;
  }

  .brand-title {
    font-size: 1.75rem;
  }

  .login-section {
    padding: 30px 20px;
  }

  .modern-input {
    padding: 14px 14px 14px 45px;
    font-size: 14px;
  }

  .input-icon {
    left: 14px;
    font-size: 16px;
  }

  .floating-label {
    left: 45px;
    font-size: 14px;
  }

  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
