# -*- coding: utf-8 -*-
"""
错误处理工具模块
提供统一的错误处理和用户友好的错误信息
"""

from flask import jsonify, current_app


def handle_database_error(error, operation="操作"):
    """
    处理数据库相关错误
    
    Args:
        error: 异常对象
        operation: 操作名称，用于日志记录
    
    Returns:
        tuple: (response, status_code)
    """
    error_str = str(error).lower()
    current_app.logger.error(f"{operation}错误: {str(error)}")
    
    # 检查是否是数据库连接错误
    if any(keyword in error_str for keyword in [
        'can\'t connect to mysql server',
        'timed out',
        'connection refused',
        'operationalerror',
        'database connection',
        'mysql server has gone away',
        'lost connection to mysql server',
        'mysql server has gone away during query'
    ]):
        return jsonify({
            'status': 'error',
            'message': '数据库连接失败，请检查网络连接或联系系统管理员',
            'error_type': 'database_connection',
            'details': '无法连接到数据库服务器，可能的原因：\n1. 网络连接问题\n2. 数据库服务器未启动\n3. 数据库配置错误\n4. 防火墙阻止连接\n5. 数据库服务器负载过高'
        }), 503
    
    # 检查是否是数据库权限错误
    elif any(keyword in error_str for keyword in [
        'access denied',
        'permission denied',
        'authentication failed'
    ]):
        return jsonify({
            'status': 'error',
            'message': '数据库访问权限不足，请联系系统管理员',
            'error_type': 'database_permission',
            'details': '数据库权限问题，可能的原因：\n1. 数据库用户权限不足\n2. 数据库密码错误\n3. 数据库用户被锁定'
        }), 503
    
    # 检查是否是数据库不存在错误
    elif any(keyword in error_str for keyword in [
        'unknown database',
        'database doesn\'t exist',
        'no such database'
    ]):
        return jsonify({
            'status': 'error',
            'message': '数据库不存在，请联系系统管理员初始化数据库',
            'error_type': 'database_missing',
            'details': '数据库不存在，可能的原因：\n1. 数据库尚未创建\n2. 数据库名称配置错误\n3. 数据库被意外删除'
        }), 503
    
    # 检查是否是表不存在错误
    elif any(keyword in error_str for keyword in [
        'table doesn\'t exist',
        'no such table',
        'unknown table'
    ]):
        return jsonify({
            'status': 'error',
            'message': '数据库表结构异常，请联系系统管理员',
            'error_type': 'database_schema',
            'details': '数据库表不存在，可能的原因：\n1. 数据库未正确初始化\n2. 数据库表被意外删除\n3. 数据库版本不匹配'
        }), 503
    
    # 其他数据库错误
    elif any(keyword in error_str for keyword in [
        'mysql',
        'sqlalchemy',
        'database',
        'sql'
    ]):
        return jsonify({
            'status': 'error',
            'message': '数据库操作失败，请稍后重试',
            'error_type': 'database_general',
            'details': f'数据库操作异常：{str(error)}'
        }), 500
    
    # 非数据库错误
    else:
        return jsonify({
            'status': 'error',
            'message': f'{operation}失败，请稍后重试',
            'error_type': 'general'
        }), 500


def handle_validation_error(errors, operation="操作"):
    """
    处理验证错误
    
    Args:
        errors: 验证错误信息
        operation: 操作名称
    
    Returns:
        tuple: (response, status_code)
    """
    return jsonify({
        'status': 'error',
        'message': '输入数据验证失败',
        'error_type': 'validation',
        'errors': errors
    }), 400


def handle_permission_error(message="权限不足", operation="操作"):
    """
    处理权限错误
    
    Args:
        message: 错误信息
        operation: 操作名称
    
    Returns:
        tuple: (response, status_code)
    """
    current_app.logger.warning(f"{operation}权限错误: {message}")
    
    return jsonify({
        'status': 'error',
        'message': message,
        'error_type': 'permission',
        'details': '您没有执行此操作的权限，请联系管理员'
    }), 403


def handle_not_found_error(resource="资源", operation="操作"):
    """
    处理资源不存在错误
    
    Args:
        resource: 资源名称
        operation: 操作名称
    
    Returns:
        tuple: (response, status_code)
    """
    return jsonify({
        'status': 'error',
        'message': f'{resource}不存在',
        'error_type': 'not_found'
    }), 404


def handle_conflict_error(message="资源冲突", operation="操作"):
    """
    处理资源冲突错误
    
    Args:
        message: 错误信息
        operation: 操作名称
    
    Returns:
        tuple: (response, status_code)
    """
    return jsonify({
        'status': 'error',
        'message': message,
        'error_type': 'conflict'
    }), 409


def create_success_response(data=None, message="操作成功"):
    """
    创建成功响应
    
    Args:
        data: 响应数据
        message: 成功信息
    
    Returns:
        dict: 响应数据
    """
    response = {
        'status': 'success',
        'message': message
    }
    
    if data is not None:
        response['data'] = data
    
    return response


def log_error(error, operation="操作", user_id=None):
    """
    记录错误日志
    
    Args:
        error: 异常对象
        operation: 操作名称
        user_id: 用户ID
    """
    log_message = f"{operation}错误: {str(error)}"
    if user_id:
        log_message = f"用户{user_id} {log_message}"
    
    current_app.logger.error(log_message)
