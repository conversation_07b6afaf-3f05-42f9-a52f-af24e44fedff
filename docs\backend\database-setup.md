# 🗄️ 数据库环境配置说明

## 📋 **概述**

本项目支持开发环境和生产环境使用不同的数据库，确保开发和生产数据的隔离。

## 🗄️ **数据库配置**

### **环境分离**
- **开发环境**: `export_excel_dev` 数据库
- **生产环境**: `export_excel_prod` 数据库

### **Redis缓存分离**
- **开发环境**: Redis DB 1
- **生产环境**: Redis DB 0

## 🚀 **快速开始**

### **1. 初始化开发环境数据库**

```bash
# 初始化开发环境数据库
cd backend
python init_db.py --env development

# 重新创建开发环境数据库（删除现有数据）
cd backend
python init_db.py --env development --recreate
```

### **2. 初始化生产环境数据库**

```bash
# 初始化生产环境数据库
cd backend
python init_db.py --env production

# 重新创建生产环境数据库（删除现有数据）
cd backend
python init_db.py --env production --recreate
```

### **3. 启动应用**

```bash
# 开发环境（默认）
cd backend
export FLASK_ENV=development  # Linux/Mac
set FLASK_ENV=development     # Windows
python run.py

# 生产环境
cd backend
export FLASK_ENV=production   # Linux/Mac
set FLASK_ENV=production      # Windows
python run.py
```

## ⚙️ **配置详情**

### **开发环境配置** (`DevelopmentConfig`)
```python
# 数据库
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://junguangchen:1qaz%40WSX@172.16.59.18:3306/export_excel_dev'

# Redis缓存
REDIS_DB = 1  # 使用DB 1
CACHE_REDIS_URL = 'redis://:1qaz@WSX@172.16.59.18:6379/1'

# 缓存时间（较短，便于开发测试）
CACHE_CONFIG = {
    'user_permissions': 300,     # 5分钟
    'components': 600,           # 10分钟
    'templates': 900,            # 15分钟
    'form_config': 600,          # 10分钟
    'user_info': 300,            # 5分钟
}
```

### **生产环境配置** (`ProductionConfig`)
```python
# 数据库
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://junguangchen:1qaz%40WSX@172.16.59.18:3306/export_excel_prod'

# Redis缓存
REDIS_DB = 0  # 使用DB 0
CACHE_REDIS_URL = 'redis://:1qaz@WSX@172.16.59.18:6379/0'

# 缓存时间（较长，提高性能）
CACHE_CONFIG = {
    'user_permissions': 3600,    # 1小时
    'components': 7200,          # 2小时
    'templates': 14400,          # 4小时
    'form_config': 7200,         # 2小时
    'user_info': 3600,           # 1小时
}
```

## 🔧 **数据库管理命令**

### **查看数据库状态**
```bash
# 连接MySQL查看数据库
mysql -h 172.16.59.18 -u junguangchen -p

# 查看所有数据库
SHOW DATABASES;

# 查看开发数据库表
USE export_excel_dev;
SHOW TABLES;

# 查看生产数据库表
USE export_excel_prod;
SHOW TABLES;
```

### **备份数据库**
```bash
# 备份开发数据库
mysqldump -h 172.16.59.18 -u junguangchen -p export_excel_dev > backup_dev.sql

# 备份生产数据库
mysqldump -h 172.16.59.18 -u junguangchen -p export_excel_prod > backup_prod.sql
```

### **恢复数据库**
```bash
# 恢复开发数据库
mysql -h 172.16.59.18 -u junguangchen -p export_excel_dev < backup_dev.sql

# 恢复生产数据库
mysql -h 172.16.59.18 -u junguangchen -p export_excel_prod < backup_prod.sql
```

## 🔍 **环境检测**

### **检查当前环境**
```python
# 在Python代码中检查当前环境
import os
from config import config

env = os.environ.get('FLASK_ENV', 'development')
current_config = config[env]
print(f"当前环境: {env}")
print(f"数据库URI: {current_config.SQLALCHEMY_DATABASE_URI}")
print(f"Redis DB: {current_config.REDIS_DB}")
```

### **验证数据库连接**
```bash
# 使用诊断脚本
cd backend
python diagnose.py
```

## ⚠️ **注意事项**

### **开发环境**
- ✅ 使用 `export_excel_dev` 数据库
- ✅ 使用 Redis DB 1
- ✅ 较短的缓存时间，便于开发调试
- ✅ 详细的调试日志
- ⚠️ 数据可能会被频繁重置

### **生产环境**
- ✅ 使用 `export_excel_prod` 数据库
- ✅ 使用 Redis DB 0
- ✅ 较长的缓存时间，提高性能
- ✅ 简化的日志输出
- ⚠️ 数据需要定期备份

### **数据隔离**
- 🔒 开发和生产数据完全隔离
- 🔒 不同的Redis数据库避免缓存冲突
- 🔒 可以安全地在开发环境进行测试

## 🚨 **故障排除**

### **常见问题**

1. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   systemctl status mysql
   
   # 检查网络连接
   telnet 172.16.59.18 3306
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis服务状态
   systemctl status redis
   
   # 测试Redis连接
   redis-cli -h 172.16.59.18 -p 6379 -a 1qaz@WSX ping
   ```

3. **环境变量未设置**
   ```bash
   # 检查环境变量
   echo $FLASK_ENV
   
   # 设置环境变量
   export FLASK_ENV=development
   ```

4. **数据库不存在**
   ```bash
   # 重新初始化数据库
   cd backend
   python init_db.py --env development
   ```

## 🔧 **数据库脚本功能**

### **统一的初始化脚本**
`backend/init_db.py` 现在包含了所有数据库操作功能：

- ✅ **环境支持**: 支持开发环境和生产环境
- ✅ **初始化**: 基于 app.sql 文件初始化数据库
- ✅ **重新创建**: 删除现有数据库并重新创建
- ✅ **验证**: 自动验证表结构和 AUTO_INCREMENT 配置
- ✅ **安全确认**: 重要操作需要用户确认

### **脚本参数说明**
```bash
# 查看帮助信息
python init_db.py --help

# 参数说明：
# --env: 指定环境 (development/production)
# --recreate: 重新创建数据库（删除现有数据）
```

## 📚 **相关文件**

- `backend/config.py` - 环境配置文件
- `backend/init_db.py` - 统一数据库初始化脚本（包含重建功能）
- `backend/app.sql` - 数据库结构和数据SQL文件
- `backend/diagnose.py` - 系统诊断脚本
