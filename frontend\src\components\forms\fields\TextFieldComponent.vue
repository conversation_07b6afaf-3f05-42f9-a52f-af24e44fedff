<template>
  <div class="text-field-component">
    <input 
      :id="id"
      :type="inputType"
      :value="value"
      :placeholder="field.placeholder"
      :readonly="field.is_readonly"
      :required="field.is_required"
      :class="fieldClasses"
      @input="handleInput"
      @change="handleChange"
      @blur="handleBlur"
    >
  </div>
</template>

<script>
export default {
  name: 'TextFieldComponent',
  props: {
    id: String,
    field: {
      type: Object,
      required: true
    },
    value: {
      type: [String, Number],
      default: ''
    }
  },
  emits: ['update:value', 'field-change'],
  computed: {
    inputType() {
      const typeMap = {
        'text': 'text',
        'email': 'email',
        'url': 'url',
        'password': 'password',
        'color': 'color'
      }
      return typeMap[this.field.field_type] || 'text'
    },
    fieldClasses() {
      let classes = ['form-control']
      
      if (this.field.css_classes) {
        classes.push(this.field.css_classes)
      }
      
      if (this.field.is_readonly) {
        classes.push('readonly-field')
      }
      
      return classes.join(' ')
    }
  },
  methods: {
    handleInput(event) {
      this.$emit('update:value', event.target.value)
    },
    
    handleChange(event) {
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: event.target.value,
        event: 'change'
      })
    },
    
    handleBlur(event) {
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: event.target.value,
        event: 'blur'
      })
    }
  }
}
</script>

<style scoped>
.readonly-field {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.form-control[type="color"] {
  height: 38px;
  padding: 0.375rem 0.75rem;
}
</style>
