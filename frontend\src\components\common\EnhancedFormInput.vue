<template>
  <div class="form-floating-enhanced mb-3">
    <!-- 输入框 -->
    <input
      v-if="type !== 'textarea' && type !== 'select'"
      :type="inputType"
      :id="id"
      :class="inputClasses"
      :placeholder="placeholder"
      :value="modelValue"
      :readonly="readonly"
      :disabled="disabled"
      :required="required"
      @input="updateValue"
      @focus="onFocus"
      @blur="onBlur"
      class="form-control"
    />
    
    <!-- 文本域 -->
    <textarea
      v-else-if="type === 'textarea'"
      :id="id"
      :class="inputClasses"
      :placeholder="placeholder"
      :value="modelValue"
      :readonly="readonly"
      :disabled="disabled"
      :required="required"
      :rows="rows"
      @input="updateValue"
      @focus="onFocus"
      @blur="onBlur"
      class="form-control"
    ></textarea>
    
    <!-- 选择框 -->
    <select
      v-else-if="type === 'select'"
      :id="id"
      :class="inputClasses"
      :value="modelValue"
      :disabled="disabled"
      :required="required"
      @change="updateValue"
      @focus="onFocus"
      @blur="onBlur"
      class="form-select"
    >
      <option value="" disabled>{{ placeholder }}</option>
      <option
        v-for="option in options"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </option>
    </select>
    
    <!-- 浮动标签 -->
    <label :for="id" class="form-label">
      <i v-if="icon" :class="icon" class="me-1"></i>
      {{ label }}
      <span v-if="required" class="text-danger ms-1">*</span>
    </label>
    
    <!-- 验证状态图标 -->
    <div v-if="showValidation" class="validation-icon">
      <i v-if="isValid && modelValue" class="bi bi-check-circle-fill text-success"></i>
      <i v-else-if="!isValid && modelValue" class="bi bi-x-circle-fill text-danger"></i>
    </div>
    
    <!-- 帮助文本 -->
    <div v-if="helpText" class="form-text">
      <i class="bi bi-info-circle me-1"></i>
      {{ helpText }}
    </div>
    
    <!-- 验证反馈 -->
    <div v-if="showValidation && !isValid && errorMessage" class="invalid-feedback d-block">
      <i class="bi bi-exclamation-circle me-1"></i>
      {{ errorMessage }}
    </div>
    
    <div v-if="showValidation && isValid && modelValue" class="valid-feedback d-block">
      <i class="bi bi-check-circle me-1"></i>
      输入正确
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnhancedFormInput',
  props: {
    id: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'text',
      validator: value => ['text', 'email', 'password', 'number', 'tel', 'url', 'date', 'datetime-local', 'textarea', 'select'].includes(value)
    },
    modelValue: {
      type: [String, Number],
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    required: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    helpText: {
      type: String,
      default: ''
    },
    showValidation: {
      type: Boolean,
      default: true
    },
    validationRules: {
      type: Object,
      default: () => ({})
    },
    rows: {
      type: Number,
      default: 3
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue', 'validation-change'],
  data() {
    return {
      isFocused: false,
      isValid: true,
      errorMessage: ''
    }
  },
  computed: {
    inputType() {
      return this.type === 'textarea' || this.type === 'select' ? 'text' : this.type
    },
    inputClasses() {
      return {
        'is-invalid': this.showValidation && !this.isValid && this.modelValue,
        'is-valid': this.showValidation && this.isValid && this.modelValue,
        'has-value': !!this.modelValue
      }
    }
  },
  watch: {
    modelValue: {
      handler(newVal) {
        this.validateField(newVal)
      },
      immediate: true
    }
  },
  methods: {
    updateValue(event) {
      const value = event.target.value
      this.$emit('update:modelValue', value)
      this.validateField(value)
    },
    
    validateField(value) {
      // 必填验证
      if (this.required && !value) {
        this.isValid = false
        this.errorMessage = `${this.label}不能为空`
        this.emitValidation()
        return
      }
      
      // 长度验证
      if (value && this.validationRules.maxLength) {
        if (value.length > this.validationRules.maxLength) {
          this.isValid = false
          this.errorMessage = `${this.label}长度不能超过${this.validationRules.maxLength}字符`
          this.emitValidation()
          return
        }
      }
      
      // 最小长度验证
      if (value && this.validationRules.minLength) {
        if (value.length < this.validationRules.minLength) {
          this.isValid = false
          this.errorMessage = `${this.label}长度不能少于${this.validationRules.minLength}字符`
          this.emitValidation()
          return
        }
      }
      
      // 正则验证
      if (value && this.validationRules.pattern) {
        const regex = new RegExp(this.validationRules.pattern)
        if (!regex.test(value)) {
          this.isValid = false
          this.errorMessage = this.validationRules.message || `${this.label}格式不正确`
          this.emitValidation()
          return
        }
      }
      
      // 邮箱验证
      if (this.type === 'email' && value) {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        if (!emailRegex.test(value)) {
          this.isValid = false
          this.errorMessage = '请输入正确的邮箱格式'
          this.emitValidation()
          return
        }
      }
      
      // 数字验证
      if (this.type === 'number' && value) {
        if (isNaN(value)) {
          this.isValid = false
          this.errorMessage = '请输入有效的数字'
          this.emitValidation()
          return
        }
        
        if (this.validationRules.min !== undefined && Number(value) < this.validationRules.min) {
          this.isValid = false
          this.errorMessage = `${this.label}不能小于${this.validationRules.min}`
          this.emitValidation()
          return
        }
        
        if (this.validationRules.max !== undefined && Number(value) > this.validationRules.max) {
          this.isValid = false
          this.errorMessage = `${this.label}不能大于${this.validationRules.max}`
          this.emitValidation()
          return
        }
      }
      
      this.isValid = true
      this.errorMessage = ''
      this.emitValidation()
    },
    
    emitValidation() {
      this.$emit('validation-change', {
        field: this.id,
        valid: this.isValid,
        message: this.errorMessage,
        value: this.modelValue
      })
    },
    
    onFocus() {
      this.isFocused = true
    },
    
    onBlur() {
      this.isFocused = false
    }
  }
}
</script>

<style scoped>
.form-floating-enhanced {
  position: relative;
}

.validation-icon {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  z-index: 5;
  pointer-events: none;
}

.form-text {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--gray-500);
}

.invalid-feedback,
.valid-feedback {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.valid-feedback {
  color: var(--success-color);
}

.invalid-feedback {
  color: var(--danger-color);
}

/* 特殊状态样式 */
.form-control:disabled,
.form-select:disabled {
  background-color: var(--gray-100);
  opacity: 0.8;
  cursor: not-allowed;
}

.form-control:readonly {
  background-color: var(--gray-50);
  cursor: default;
}

/* 文本域特殊处理 */
textarea.form-control {
  min-height: calc(3.5rem + 2px);
  resize: vertical;
}

/* 选择框特殊处理 */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
}
</style>
