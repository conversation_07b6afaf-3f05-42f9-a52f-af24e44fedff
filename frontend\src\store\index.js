import { createStore } from 'vuex'
import axios from 'axios'

// 配置axios基础URL
const baseURL = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_API_BASE_URL || '/api')  // 生产环境使用/api前缀
  : (process.env.VUE_APP_API_URL || 'http://127.0.0.1:5000')  // 开发环境直连后端

// 创建axios实例
const apiClient = axios.create({
  baseURL: baseURL
})

// 请求拦截器 - 添加token
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理token过期
apiClient.interceptors.response.use(
  response => response,
  error => {
    if (error.response && error.response.status === 401) {
      // Token过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_info')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

/**
 * Vuex状态管理
 * 管理应用的全局状态
 */
export default createStore({
  state: {
    // 认证相关状态
    user: null,
    token: localStorage.getItem('access_token'),
    permissions: [],

    // 原有状态
    excelFiles: [],
    currentFile: null,
    fields: [],
    loading: false,
    error: null,

    // Toast消息状态
    toastMessage: '',
    toastType: 'info',
    showToastFlag: false
  },
  mutations: {
    // 认证相关mutations
    SET_USER(state, user) {
      state.user = user
    },
    SET_TOKEN(state, token) {
      state.token = token
    },
    SET_PERMISSIONS(state, permissions) {
      state.permissions = permissions
    },
    CLEAR_AUTH(state) {
      state.user = null
      state.token = null
      state.permissions = []
    },

    // 原有mutations
    setExcelFiles(state, files) {
      state.excelFiles = files
    },
    setCurrentFile(state, file) {
      state.currentFile = file
    },
    setFields(state, fields) {
      state.fields = fields
    },
    setLoading(state, loading) {
      state.loading = loading
    },
    setError(state, error) {
      state.error = error
    },

    // Toast相关mutations
    SHOW_TOAST(state, { message, type = 'info' }) {
      state.toastMessage = message
      state.toastType = type
      state.showToastFlag = true
    },
    HIDE_TOAST(state) {
      state.showToastFlag = false
      state.toastMessage = ''
    }
  },
  getters: {
    // 认证相关getters
    isAuthenticated: state => !!state.token && !!state.user,
    currentUser: state => state.user,
    userPermissions: state => state.permissions,
    hasPermission: state => permission => state.permissions.includes(permission),
    isAdmin: state => state.user && state.user.is_admin
  },
  actions: {
    // 认证相关actions
    async login({ commit }, credentials) {
      try {
        const response = await apiClient.post('/auth/login', credentials)

        if (response.data.status === 'success') {
          const { access_token, user } = response.data.data

          // 保存到localStorage
          localStorage.setItem('access_token', access_token)
          localStorage.setItem('user_info', JSON.stringify(user))

          // 更新状态
          commit('SET_TOKEN', access_token)
          commit('SET_USER', user)
          commit('SET_PERMISSIONS', user.permissions || [])

          return user
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        if (error.response && error.response.data) {
          throw new Error(error.response.data.message)
        }
        throw error
      }
    },

    async logout({ commit }) {
      try {
        await apiClient.post('/auth/logout')
      } catch (error) {
        // 即使后端登出失败，也要清除本地状态
        console.error('Logout error:', error)
      } finally {
        // 清除本地存储
        localStorage.removeItem('access_token')
        localStorage.removeItem('user_info')

        // 清除状态
        commit('CLEAR_AUTH')
      }
    },

    async initAuth({ commit }) {
      const token = localStorage.getItem('access_token')
      const userInfo = localStorage.getItem('user_info')

      if (token && userInfo) {
        try {
          // 验证token是否有效
          const response = await apiClient.post('/auth/verify-token')

          if (response.data.status === 'success') {
            const user = response.data.data.user

            commit('SET_TOKEN', token)
            commit('SET_USER', user)
            commit('SET_PERMISSIONS', user.permissions || [])

            return user
          } else {
            throw new Error('Token invalid')
          }
        } catch (error) {
          // Token无效，清除本地存储
          localStorage.removeItem('access_token')
          localStorage.removeItem('user_info')
          commit('CLEAR_AUTH')
          throw error
        }
      }
    },

    // Toast消息actions
    showToast({ commit }, { message, type = 'info', duration = 3000 }) {
      commit('SHOW_TOAST', { message, type })

      // 自动隐藏Toast
      setTimeout(() => {
        commit('HIDE_TOAST')
      }, duration)
    },

    hideToast({ commit }) {
      commit('HIDE_TOAST')
    },

    // 获取文件历史记录
    async getFileHistory({ commit }, fileId) {
      commit('setLoading', true)
      try {
        const response = await apiClient.get(`/excel/history/${fileId}`)
        if (response.data.status === 'success') {
          commit('setCurrentFile', response.data.data)
        } else {
          commit('setError', response.data.message)
        }
      } catch (error) {
        commit('setError', error.message)
      } finally {
        commit('setLoading', false)
      }
    }
  },
  modules: {
  }
})
