<template>
  <div class="about-page">
    <!-- 页面标题 -->
    <div class="text-center mb-5">
      <div class="mb-4">
        <i class="bi bi-shield-check text-primary" style="font-size: 4rem;"></i>
      </div>
      <h1 class="display-4 mb-3">梆梆安全-运维信息登记平台</h1>
      <p class="lead text-muted">专业的运维文档生成和管理系统</p>
      <div class="badge bg-primary fs-6 px-3 py-2">
        版本 {{ systemInfo.version }}
      </div>
    </div>

    <!-- 系统信息卡片 -->
    <div class="row g-4 mb-5">
      <!-- 系统概述 -->
      <div class="col-lg-8">
        <div class="card h-100">
          <div class="card-header">
            <h4 class="mb-0">
              <i class="bi bi-info-circle me-2 text-primary"></i>
              系统概述
            </h4>
          </div>
          <div class="card-body">
            <p class="card-text">
              运维信息登记平台是梆梆安全交付中心专门开发的运维文档管理系统，
              旨在帮助运维团队高效管理服务器配置、应用部署和系统监控信息，
              实现运维文档的标准化、自动化生成和集中管理。
            </p>
            
            <h5 class="mt-4 mb-3">🎯 核心功能</h5>
            <div class="row">
              <div class="col-md-6">
                <ul class="list-unstyled">
                  <li class="mb-2">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    表单数据收集与管理
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    Excel文档自动生成
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    组件配置管理
                  </li>
                </ul>
              </div>
              <div class="col-md-6">
                <ul class="list-unstyled">
                  <li class="mb-2">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    用户权限控制
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    性能监控统计
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    API接口管理
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 版本信息 -->
      <div class="col-lg-4">
        <div class="card h-100">
          <div class="card-header">
            <h4 class="mb-0">
              <i class="bi bi-code-square me-2 text-primary"></i>
              版本信息
            </h4>
          </div>
          <div class="card-body">
            <table class="table table-borderless table-sm">
              <tbody>
                <tr>
                  <td><strong>系统版本</strong></td>
                  <td>{{ systemInfo.version }}</td>
                </tr>
                <tr>
                  <td><strong>发布日期</strong></td>
                  <td>{{ systemInfo.releaseDate }}</td>
                </tr>
                <tr>
                  <td><strong>构建版本</strong></td>
                  <td>{{ systemInfo.buildVersion }}</td>
                </tr>
                <tr>
                  <td><strong>环境</strong></td>
                  <td>
                    <span class="badge bg-success">{{ systemInfo.environment }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术栈 -->
    <div class="row g-4 mb-5">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h4 class="mb-0">
              <i class="bi bi-stack me-2 text-primary"></i>
              技术栈
            </h4>
          </div>
          <div class="card-body">
            <div class="row">
              <!-- 前端技术 -->
              <div class="col-md-4">
                <h5 class="text-primary mb-3">
                  <i class="bi bi-display me-2"></i>前端技术
                </h5>
                <ul class="list-unstyled">
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Vue 3.x
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Vue Router 4.x
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Vuex 4.x
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Bootstrap 5
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Axios
                  </li>
                </ul>
              </div>

              <!-- 后端技术 -->
              <div class="col-md-4">
                <h5 class="text-success mb-3">
                  <i class="bi bi-server me-2"></i>后端技术
                </h5>
                <ul class="list-unstyled">
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Flask 2.x
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    SQLAlchemy
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Flask-JWT-Extended
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Gunicorn
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    OpenPyXL
                  </li>
                </ul>
              </div>

              <!-- 基础设施 -->
              <div class="col-md-4">
                <h5 class="text-warning mb-3">
                  <i class="bi bi-hdd-stack me-2"></i>基础设施
                </h5>
                <ul class="list-unstyled">
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    MySQL 8.0
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Redis 6.x
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Nginx
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Jenkins CI/CD
                  </li>
                  <li class="mb-2">
                    <i class="bi bi-arrow-right text-muted me-2"></i>
                    Linux CentOS
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 开发团队 -->
    <div class="row g-4 mb-5">
      <div class="col-md-6">
        <div class="card h-100">
          <div class="card-header">
            <h4 class="mb-0">
              <i class="bi bi-people me-2 text-primary"></i>
              开发团队
            </h4>
          </div>
          <div class="card-body">
            <div class="text-center mb-3">
              <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
            </div>
            <h5 class="text-center mb-3">梆梆安全交付中心</h5>
            <p class="text-center text-muted mb-3">运维团队</p>
            <div class="text-center">
              <span class="badge bg-primary me-2">运维自动化</span>
              <span class="badge bg-success me-2">文档管理</span>
              <span class="badge bg-info">系统集成</span>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="card h-100">
          <div class="card-header">
            <h4 class="mb-0">
              <i class="bi bi-envelope me-2 text-primary"></i>
              联系方式
            </h4>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <i class="bi bi-geo-alt text-muted me-2"></i>
              <strong>地址：</strong>北京市海淀区
            </div>
            <div class="mb-3">
              <i class="bi bi-envelope text-muted me-2"></i>
              <strong>邮箱：</strong><EMAIL>
            </div>
            <div class="mb-3">
              <i class="bi bi-globe text-muted me-2"></i>
              <strong>官网：</strong>
              <a href="https://www.bangcle.com" target="_blank" class="text-decoration-none">
                www.bangcle.com
              </a>
            </div>
            <div class="mb-3">
              <i class="bi bi-telephone text-muted me-2"></i>
              <strong>技术支持：</strong>400-xxx-xxxx
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 更新日志 -->
    <div class="row g-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h4 class="mb-0">
              <i class="bi bi-clock-history me-2 text-primary"></i>
              更新日志
            </h4>
          </div>
          <div class="card-body">
            <div class="timeline">
              <div class="timeline-item mb-4" v-for="log in updateLogs" :key="log.version">
                <div class="d-flex">
                  <div class="flex-shrink-0">
                    <div class="timeline-badge bg-primary">
                      <i class="bi bi-tag"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <h6 class="mb-1">
                      版本 {{ log.version }}
                      <small class="text-muted">{{ log.date }}</small>
                    </h6>
                    <ul class="list-unstyled mb-0">
                      <li v-for="feature in log.features" :key="feature" class="mb-1">
                        <i class="bi bi-plus-circle text-success me-2"></i>
                        {{ feature }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'About',
  data() {
    return {
      systemInfo: {
        version: '1.0.0',
        releaseDate: '2024-12-19',
        buildVersion: 'build-20241219',
        environment: 'Production'
      },
      updateLogs: [
        {
          version: '1.0.0',
          date: '2024-12-19',
          features: [
            '完成系统基础架构搭建',
            '实现用户认证和权限管理',
            '添加表单数据收集功能',
            '集成Excel文档生成',
            '完成组件配置管理',
            '添加性能监控功能'
          ]
        }
      ]
    }
  },
  mounted() {
    // 设置页面标题
    document.title = '关于平台 - 梆梆安全运维信息登记平台'
  }
}
</script>

<style scoped>
.about-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.timeline-badge {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
}

.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.badge {
  font-size: 0.8rem;
}

.list-unstyled li {
  padding: 0.25rem 0;
}
</style>
