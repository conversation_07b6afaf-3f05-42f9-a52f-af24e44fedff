<template>
  <base-form
    v-model="formData"
    form-type="安全测评"
    :component-groups="componentGroups"
    @save-form="$emit('save-form')"
    @load-form="$emit('load-form')"
    @refresh-components="$emit('refresh-components')"
    class="security-testing-form"
  >
    <!-- 安全测评特有的授权信息部分 -->
    <template #custom-sections>
      <div id="authorization-section">
        <authorization-section
          v-model:product-func="formData.产品功能"
          v-model:auth-func="formData.授权功能"
          v-model:auth-start="formData.授权开始日期"
          v-model:auth-end="formData.授权结束日期"
        />
      </div>
    </template>
  </base-form>
</template>

<script>
import BaseForm from '../common/BaseForm.vue'
import AuthorizationSection from './AuthorizationSection.vue'

/**
 * 安全测评表单组件
 * 使用BaseForm提供通用功能，只需定义特有的授权信息部分
 */
export default {
  name: 'SecurityTestingForm',
  components: {
    BaseForm,
    AuthorizationSection
  },
  props: {
    modelValue: { type: Object, required: true },
    componentGroups: { type: Object, required: true }
  },
  emits: ['update:modelValue', 'save-form', 'load-form', 'refresh-components'],
  computed: {
    formData: {
      get() { return this.modelValue },
      set(value) { this.$emit('update:modelValue', value) }
    }
  }

}
</script>

<style scoped>
/* 安全测评表单特有样式 */
.security-testing-form {
  /* 基础样式由BaseForm提供 */
}

/* 授权信息部分特殊样式 */
#authorization-section .card {
  border-left-color: #28a745; /* 绿色边框，表示安全测评 */
}
</style>
