import excelApi from '@/api/excel'
import {
  getSecurityTestingInitialData,
  getSecurityMonitoringInitialData,
  getAppHardeningInitialData,
  getGenericFormInitialData
} from '@/config/formDataConfig'
import { processComponentPorts } from '@/utils/componentUtils'

/**
 * FillSheet 组件的方法集合
 * 包含表单提交、验证、重置等方法
 */

/**
 * 添加组件别名信息到表单数据
 * @param {Object} formData - 表单数据
 * @returns {Object} 包含组件别名的表单数据
 */
export const addComponentAliases = (formData) => {
  try {
    // 获取表单类型
    const formType = formData.文档后缀 || formData.form_type || '安全测评'

    // 从localStorage获取组件别名
    const storageKey = `component_aliases_${formType}`
    const savedAliases = localStorage.getItem(storageKey)

    let componentAliases = {}
    if (savedAliases) {
      try {
        componentAliases = JSON.parse(savedAliases)
        console.log(`获取到 ${formType} 的组件别名:`, componentAliases)
      } catch (e) {
        console.warn('解析组件别名失败:', e)
      }
    }

    // 将组件别名添加到表单数据中
    const formDataWithAliases = {
      ...formData,
      component_aliases: componentAliases,
      form_type: formType
    }

    console.log('添加组件别名后的表单数据:', {
      form_type: formType,
      component_aliases: componentAliases,
      aliases_count: Object.keys(componentAliases).length
    })

    return formDataWithAliases
  } catch (error) {
    console.error('添加组件别名失败:', error)
    return formData
  }
}

/**
 * 提交表单
 * 发送数据到后端API生成Excel文件
 * @param {Object} formData - 表单数据
 * @param {Boolean} forceOverwrite - 是否强制覆盖同名文件
 * @param {Function} resetForm - 重置表单的方法
 * @param {Function} downloadFile - 下载文件的方法
 * @param {Object} state - 组件状态对象，包含 loading 和 error
 * @returns {Promise<void>}
 */
export const submitForm = async (formData, forceOverwrite = false, resetForm, downloadFile, state) => {
  // 调试：检查调用时的参数和调用堆栈
  console.log('fillSheetMethods.submitForm 被调用，参数:', { forceOverwrite })
  console.log('调用堆栈:', new Error().stack)

  if (!validateForm(formData)) {
    return
  }

  state.loading = true
  try {
    // 处理组件端口信息，确保使用默认端口
    const processedFormData = processComponentPorts(formData)

    // 添加组件别名信息
    const formDataWithAliases = addComponentAliases(processedFormData)

    // 如果需要强制覆盖，添加标志
    const formDataWithOverwrite = { ...formDataWithAliases }

    // 调试：检查表单数据中是否已经包含 force_overwrite
    console.log('原始表单数据 keys:', Object.keys(formData))
    console.log('处理后表单数据 keys:', Object.keys(processedFormData))
    console.log('最终表单数据 keys:', Object.keys(formDataWithOverwrite))
    console.log('forceOverwrite 参数值:', forceOverwrite)

    if (formDataWithOverwrite.force_overwrite) {
      console.warn('表单数据中已经包含 force_overwrite 字段:', formDataWithOverwrite.force_overwrite)
    }

    // 检查是否有任何包含 force 或 overwrite 的字段
    const suspiciousKeys = Object.keys(formDataWithOverwrite).filter(key =>
      key.toLowerCase().includes('force') || key.toLowerCase().includes('overwrite')
    )
    if (suspiciousKeys.length > 0) {
      console.warn('发现可疑的字段:', suspiciousKeys)
    }

    if (forceOverwrite) {
      formDataWithOverwrite.force_overwrite = true
      console.log('设置 force_overwrite 为 true')
    } else {
      // 确保在非强制覆盖模式下删除该字段
      delete formDataWithOverwrite.force_overwrite
      console.log('删除 force_overwrite 字段，启用重复检查')
    }

    let response
    try {
      response = await excelApi.fillSheet(formDataWithOverwrite)
      console.log('提交表单响应:', response.data)
      console.log('响应状态码:', response.status)
    } catch (error) {
      // 处理409冲突状态码（重复提交）
      if (error.response && error.response.status === 409) {
        console.log('检测到重复提交，状态码:', error.response.status)
        console.log('重复提交响应数据:', error.response.data)
        response = error.response
      } else {
        // 其他错误，重新抛出
        throw error
      }
    }

    // 处理不同的响应状态
    if (response.data.status === 'success') {
      if (typeof downloadFile === 'function') {
        // 传递状态对象和文件名给downloadFile函数，确保下载完成后重置loading状态
        downloadFile(response.data.data.file_id, state, response.data.data.filename)
      } else {
        // 如果没有提供downloadFile函数，直接重置loading状态
        state.loading = false
      }
      alert('Excel文件生成成功！')
      if (!forceOverwrite && typeof resetForm === 'function') {
        resetForm()  // 只有在非覆盖模式下才重置表单
      }
    } else if (response.data.status === 'duplicate_submission') {
      // 发现重复的表单提交记录，需要用户选择处理方式
      state.loading = false

      // 触发重复处理事件，让组件显示重复处理模态框
      if (typeof state.onDuplicateFound === 'function') {
        state.onDuplicateFound(response.data.data)
      } else {
        // 降级处理：简单的确认对话框
        if (confirm(`发现相同公司和表单类型的历史记录\n公司：${response.data.data.company_name}\n类型：${response.data.data.form_type}\n\n是否强制创建新记录？`)) {
          await submitForm(formData, true, resetForm, downloadFile, state)
        }
      }
    } else if (response.data.status === 'duplicate') {
      // 发现同名文件，询问用户是否覆盖（保留原有逻辑作为备用）
      state.loading = false
      if (confirm(`已存在同名文件: ${response.data.data.filename}\n是否覆盖现有文件？`)) {
        // 用户确认覆盖，重新提交表单并强制覆盖
        await submitForm(formData, true, resetForm, downloadFile, state)
      } else {
        // 用户取消覆盖，可以选择下载现有文件
        if (confirm('是否下载现有文件？') && typeof downloadFile === 'function') {
          downloadFile(response.data.data.existing_file_id)
        }
      }
    } else {
      state.error = response.data.message
      alert(`生成失败: ${state.error}`)
    }
  } catch (error) {
    // 使用兼容性更好的方式获取错误信息
    state.error = (error.response && error.response.data && error.response.data.message) || error.message
    alert(`生成失败: ${state.error}`)
  } finally {
    // 无论是否覆盖模式，都重置loading状态
    state.loading = false
  }
}

/**
 * 验证表单
 * 确保必填字段已填写
 * @param {Object} formData - 表单数据
 * @returns {Boolean} - 表单是否有效
 */
export const validateForm = (formData) => {
  let requiredFields = []

  // 根据模板类型确定必填字段
  if (formData.文档后缀 === '安全监测') {
    requiredFields = [
      '公司名称',
      '前端版本',
      '后端版本',
      '记录日期'
    ]
  } else if (formData.文档后缀 === '应用加固') {
    requiredFields = [
      '公司名称',
      '客户',
      '记录日期'
    ]
  } else if (formData.文档后缀 === '安全测评') {
    // 安全测评表单的必填字段
    requiredFields = ['公司名称', '部署包版本', '管理员页面IP', '用户页面IP', '记录日期']
  } else {
    // 通用表单（包括API平台等新表单类型）的必填字段
    requiredFields = ['公司名称', '版本信息', '管理员页面IP', '用户页面IP', '记录日期']
  }

  const missingFields = requiredFields.filter(field => !formData[field])

  if (missingFields.length > 0) {
    alert(`请填写以下必填字段: ${missingFields.join(', ')}`)
    return false
  }
  return true
}

/**
 * 下载文件
 * 通过文件ID下载生成的Excel文件
 * @param {String} fileId - 文件ID
 * @param {Object} state - 可选的组件状态对象，包含 loading 属性
 * @param {String} expectedFilename - 可选的期望文件名，来自后端响应
 * @returns {Promise<void>}
 */
export const downloadFile = async (fileId, state = null, expectedFilename = null) => {
  try {
    // 直接下载文件
    const response = await excelApi.downloadFile(fileId)

    // 确定最终使用的文件名
    let filename = expectedFilename || `运维文档_${new Date().getTime()}.xlsx`

    // 如果没有期望文件名，尝试从响应头中获取
    if (!expectedFilename) {
      const contentDisposition = response.headers['content-disposition']
      // 使用工具函数解析文件名
      const { parseFilenameFromContentDisposition } = await import('@/utils/fileUtils')
      filename = parseFilenameFromContentDisposition(contentDisposition, filename)
    }

    // 使用工具函数下载文件
    const { downloadBlob } = await import('@/utils/fileUtils')
    downloadBlob(new Blob([response.data]), filename)
  } catch (error) {
    console.error('下载文件失败:', error)
    alert('下载文件失败，请稍后重试')
  } finally {
    // 如果提供了状态对象，重置loading状态
    if (state && typeof state.loading !== 'undefined') {
      state.loading = false
    }
  }
}

/**
 * 格式化日期
 * 将Date对象转换为YYYY-MM-DD格式的字符串
 * @param {Date} date - 日期对象
 * @returns {String} - 格式化后的日期字符串
 */
export const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 获取一年后的日期
 * 返回当前日期加一年的Date对象
 * @returns {Date} - 一年后的日期对象
 */
export const getNextYearDate = () => {
  const date = new Date()
  date.setFullYear(date.getFullYear() + 1)
  return date
}

/**
 * 初始化部署应用数组
 * 从组件分组中提取所有组件名称
 * @param {Object} componentGroups - 组件分组对象
 * @param {String} formType - 表单类型
 * @returns {Array} - 部署应用数组
 */
export const initServiceTypes = (componentGroups, formType) => {
  // 清空数组
  const serviceTypes = []

  // 现在完全使用数据库组件分组
  if (componentGroups) {
    // 根据表单类型选择不同的组件组
    let componentSet = {}

    if (formType === '安全监测') {
      componentSet = componentGroups.security || {}
    } else if (formType === '安全测评') {
      componentSet = componentGroups.testing || {}
    } else if (formType === '应用加固') {
      componentSet = componentGroups.hardening || {}
    } else {
      // 对于新的表单类型，使用表单类型名称作为键
      componentSet = componentGroups[formType] || {}
    }

    // 从各个分组中提取组件名称
    for (const groupKey in componentSet) {
      const group = componentSet[groupKey]
      if (Array.isArray(group)) {
        for (const component of group) {
          serviceTypes.push(component.name)
        }
      }
    }
  }

  console.log(`initServiceTypes: 表单类型 ${formType} 的组件列表:`, serviceTypes)
  return serviceTypes
}

/**
 * 处理模板类型变化
 * 根据选择的模板类型更新表单字段
 * @param {Object} formData - 表单数据
 * @param {Object} state - 组件状态对象

 * @param {Function} initServiceTypesFunc - 初始化部署应用的方法
 */
export const onTemplateTypeChange = async (formData, state, initServiceTypesFunc) => {
  // 如果已经在切换过程中，则不再处理
  if (state.isChangingTemplate) {
    console.log('已经在切换模板中，忽略此次调用')
    return
  }

  // 根据不同的模板类型，更新表单中的字段标签
  const templateType = formData.文档后缀
  console.log('onTemplateTypeChange 处理表单类型:', templateType)

  // 保存当前选择的表单类型到本地存储
  localStorage.setItem('selectedFormType', templateType)

  // 保存当前的基本信息
  const basicInfo = {
    公司名称: formData.公司名称,
    文档后缀: templateType,
    编辑人: formData.编辑人 || formData.记录人, // 兼容两种字段名
    记录日期: formData.记录日期,
    授权开始日期: formData.授权开始日期,
    授权结束日期: formData.授权结束日期,
    备注: formData.备注
  }

  // 标记正在切换模板
  state.isChangingTemplate = true

  // 先切换到占位表单
  switchToPlaceholderForm(formData, basicInfo)

  // 清除之前的定时器（如果存在）
  if (state.placeholderTimeout) {
    clearTimeout(state.placeholderTimeout)
  }

  // 设置定时器，延迟切换到目标表单
  state.placeholderTimeout = setTimeout(() => {
    // 根据模板类型创建全新的表单数据对象
    let newFormData;

    if (templateType === '安全测评') {
      // 创建安全测评表单数据
      newFormData = getSecurityTestingInitialData();
    } else if (templateType === '安全监测') {
      // 创建安全监测表单数据
      newFormData = getSecurityMonitoringInitialData();
    } else if (templateType === '应用加固') {
      // 创建应用加固表单数据
      newFormData = getAppHardeningInitialData();

      // 设置客户字段与公司名称相同
      newFormData.客户 = basicInfo.公司名称;

      // 不添加默认服务器信息，让用户自己添加和选择组件
      // 保持服务器信息为空数组，用户需要手动添加服务器和选择组件
    } else {
      // 对于新的表单类型，使用通用表单数据
      newFormData = getGenericFormInitialData();
      newFormData.文档后缀 = templateType;
    }

    // 恢复基本信息
    newFormData.公司名称 = basicInfo.公司名称;
    newFormData.编辑人 = basicInfo.编辑人 || newFormData.编辑人;
    newFormData.记录日期 = basicInfo.记录日期 || newFormData.记录日期;
    newFormData.备注 = basicInfo.备注 || newFormData.备注;

    // 如果是安全测评表单，还需要恢复授权日期
    if (templateType === '安全测评') {
      newFormData.授权开始日期 = basicInfo.授权开始日期 || newFormData.授权开始日期;
      newFormData.授权结束日期 = basicInfo.授权结束日期 || newFormData.授权结束日期;
    }

    // 用新的表单数据替换原有的表单数据
    Object.keys(formData).forEach(key => {
      delete formData[key];
    });

    Object.keys(newFormData).forEach(key => {
      formData[key] = newFormData[key];
    });

    // 打印当前表单类型，用于调试
    console.log('当前表单类型:', formData.文档后缀);

    // 标记模板切换完成
    // 重新初始化组件列表
    if (typeof initServiceTypesFunc === 'function') {
      initServiceTypesFunc();
    }

    // 标记模板切换完成
    state.isChangingTemplate = false
    console.log('模板切换完成，当前表单类型:', formData.文档后缀)
  }, 150) // 150毫秒后切换到目标表单，减少用户等待时间
}

/**
 * 切换到占位表单
 * 在切换表单类型时，先显示一个占位表单
 * @param {Object} formData - 表单数据
 * @param {Object} basicInfo - 基本信息对象
 */
export const switchToPlaceholderForm = (formData, basicInfo) => {
  // 清空表单内容，创建一个最小化的占位表单
  // 只保留公司名称和文档后缀
  Object.keys(formData).forEach(key => {
    delete formData[key];
  });

  // 设置基本信息
  formData.公司名称 = basicInfo.公司名称;
  formData.文档后缀 = basicInfo.文档后缀;
  formData.记录人 = basicInfo.记录人 || '';
  formData.记录日期 = basicInfo.记录日期 || formatDate(new Date());
  formData.备注 = basicInfo.备注 || '';

  // 重置所有字段标签为默认值
  const fieldLabels = document.querySelectorAll('.col-form-label.fw-bold')

  // 恢复默认标签 - 现在我们只使用部署包版本、升级页面和产品功能
  updateFieldLabel(fieldLabels, '检测工具版本', '部署包版本')
  updateFieldLabel(fieldLabels, '测评工具版本', '部署包版本')
  updateFieldLabel(fieldLabels, '测试工具版本', '部署包版本')

  updateFieldLabel(fieldLabels, '报告生成页面', '升级页面')
  updateFieldLabel(fieldLabels, '测评报告页面', '升级页面')

  updateFieldLabel(fieldLabels, '检测功能', '产品功能')
  updateFieldLabel(fieldLabels, '测评功能', '产品功能')
}

/**
 * 更新表单字段标签
 * @param {NodeList} labels - 字段标签元素列表
 * @param {String} oldText - 原标签文本
 * @param {String} newText - 新标签文本
 */
export const updateFieldLabel = (labels, oldText, newText) => {
  labels.forEach(label => {
    if (label.textContent === oldText) {
      label.textContent = newText
    }
  })
}
