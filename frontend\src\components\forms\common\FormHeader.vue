<template>
  <div>
    <div class="card-header bg-primary text-white py-3">
      <h2 class="text-center mb-0 fw-bold">梆梆安全-运维信息登记平台</h2>
    </div>
    <div class="card-header bg-light py-3">
      <div class="form-title-container mb-2">
        <div class="form-title-row">
          <input
            type="text"
            class="form-control bg-white text-dark border form-title-input"
            v-model="companyName"
            placeholder="客户公司名称"
            required
            @input="updateCompanyName"
          >

          <div class="form-title-separator-container">
            <span class="form-title-separator">-运维文档-</span>
          </div>

          <select
            class="form-select bg-white text-dark border form-title-select"
            v-model="documentType"
            @change="updateDocumentType"
          >
            <option v-for="formType in availableFormTypes" :key="formType" :value="formType">
              {{ formType }}
            </option>
          </select>
        </div>
      </div>

      <!-- 模板选择器 -->
      <div class="template-selector-container mt-3">
        <template-selector
          :form-type="documentType"
          :selected-template="selectedTemplate"
          @template-change="onTemplateChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TemplateSelector from './TemplateSelector.vue'

/**
 * 表单头部组件
 * 包含公司名称输入、文档类型选择和模板选择
 */
export default {
  name: 'FormHeader',
  components: {
    TemplateSelector
  },
  props: {
    // 公司名称
    modelValue: {
      type: String,
      default: '客户公司名称'
    },
    // 文档类型
    docType: {
      type: String,
      default: '安全测评'
    },
    // 可用的表单类型列表
    availableFormTypes: {
      type: Array,
      default: () => ['安全测评', '安全监测', '应用加固']
    },
    // 选中的模板
    selectedTemplate: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'document-type-change', 'template-change'],
  data() {
    return {
      companyName: this.modelValue,
      documentType: this.docType
    }
  },
  watch: {
    // 监听props变化，更新内部数据
    modelValue(newVal) {
      this.companyName = newVal
    },
    docType(newVal) {
      this.documentType = newVal
    }
  },
  methods: {
    /**
     * 更新公司名称
     * 向父组件发送update:modelValue事件
     */
    updateCompanyName() {
      this.$emit('update:modelValue', this.companyName)
    },

    /**
     * 更新文档类型
     * 向父组件发送文档类型变更事件
     */
    updateDocumentType() {
      console.log('表单类型变更为:', this.documentType)
      this.$emit('document-type-change', this.documentType)

      // 延迟一段时间后再次触发事件，确保父组件能够接收到
      setTimeout(() => {
        console.log('再次触发表单类型变更事件:', this.documentType)
        this.$emit('document-type-change', this.documentType)
      }, 100)
    },

    /**
     * 模板变更处理
     * 向父组件发送模板变更事件
     */
    onTemplateChange(template) {
      console.log('模板变更为:', template)
      this.$emit('template-change', template)
    }
  }
}
</script>

<style scoped>
.card-header.bg-primary {
  background-color: #0d6efd !important;
  border-bottom: 1px solid #0d6efd;
  padding: 1.5rem 1rem !important;
}

.card-header.bg-light {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef;
  padding: 1.5rem 1rem !important;
}

.form-title-input {
  font-size: 1.5rem;
  font-weight: bold;
  flex: 2;
  min-width: 0;
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  padding: 0.75rem 1rem;
  background-color: #fff;
}

.form-title-input:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  background-color: #fff;
}

.form-title-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.form-title-row {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 900px;
}

.form-title-separator-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1.5rem;
  min-width: 150px;
}

.form-title-separator {
  font-size: 1.5rem;
  font-weight: bold;
  color: #495057;
  white-space: nowrap;
}

.form-title-select {
  font-size: 1.5rem;
  font-weight: bold;
  flex: 1;
  min-width: 0;
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  padding: 0.75rem 1rem;
  background-color: #fff;
}

.form-title-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  background-color: #fff;
}

/* 模板选择器样式 */
.template-selector-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-title-row {
    flex-direction: column;
    align-items: stretch;
  }

  .form-title-separator-container {
    padding: 1rem 0;
    justify-content: center;
    min-width: auto;
  }

  .form-title-input,
  .form-title-select {
    width: 100%;
  }

  .template-selector-container {
    padding: 0;
  }
}
</style>
