#!/usr/bin/env python3
"""
添加模板管理相关权限到数据库
"""

import os
import sys

# 设置环境变量
os.environ['FLASK_APP'] = 'app.py'
os.environ['FLASK_ENV'] = 'development'

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from app import create_app, db
    from app.models.auth_models import Permission
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在backend目录下运行此脚本")
    sys.exit(1)

def add_template_permissions():
    """添加模板管理相关权限"""
    app = create_app()
    
    with app.app_context():
        # 定义需要添加的权限
        permissions_to_add = [
            {
                'code': 'template.create',
                'name': '创建模板',
                'description': '创建新的表单模板和表单类型',
                'module': 'template'
            },
            {
                'code': 'template.edit',
                'name': '编辑模板',
                'description': '编辑现有的表单模板和表单类型',
                'module': 'template'
            }
        ]
        
        added_count = 0
        
        for perm_data in permissions_to_add:
            # 检查权限是否已存在
            existing_permission = Permission.query.filter_by(code=perm_data['code']).first()
            
            if existing_permission:
                print(f"权限 {perm_data['code']} 已存在，跳过")
                continue
            
            # 创建新权限
            permission = Permission(
                code=perm_data['code'],
                name=perm_data['name'],
                description=perm_data['description'],
                module=perm_data['module'],
                is_active=True
            )
            
            try:
                db.session.add(permission)
                db.session.commit()
                print(f"✅ 成功添加权限: {perm_data['code']} - {perm_data['name']}")
                added_count += 1
            except Exception as e:
                db.session.rollback()
                print(f"❌ 添加权限 {perm_data['code']} 失败: {str(e)}")
        
        print(f"\n权限添加完成！共添加 {added_count} 个权限")
        
        # 显示所有模板相关权限
        print("\n当前所有模板相关权限：")
        template_permissions = Permission.query.filter_by(module='template').all()
        for perm in template_permissions:
            status = "✅" if perm.is_active else "❌"
            print(f"{status} {perm.code} - {perm.name}")

if __name__ == '__main__':
    add_template_permissions()
