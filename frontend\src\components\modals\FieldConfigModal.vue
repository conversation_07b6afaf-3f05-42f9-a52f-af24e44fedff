<template>
  <!-- 字段配置编辑模态框 -->
  <div class="modal fade" id="fieldConfigModal" tabindex="-1" aria-labelledby="fieldConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="fieldConfigModalLabel">
            <i class="bi bi-plus-square me-2"></i>
            {{ isEditing ? '编辑字段配置' : '新建字段配置' }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveField">
            <!-- 公共字段选择 -->
            <div v-if="!isEditing" class="card mb-3">
              <div class="card-header">
                <h6 class="mb-0">
                  <i class="bi bi-magic me-2"></i>快速选择公共字段
                </h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-8">
                    <select class="form-select" v-model="selectedCommonField" @change="applyCommonField">
                      <option value="">选择一个公共字段快速填充...</option>
                      <optgroup label="基本信息字段">
                        <option value="companyName">公司名称 (text, 必填)</option>
                        <option value="recordDate">记录日期 (date, 必填)</option>
                        <option value="customer">客户 (text, 必填)</option>
                      </optgroup>
                      <optgroup label="访问信息字段">
                        <option value="platformUrl">平台地址 (url, 必填)</option>
                        <option value="platformVersion">部署的平台版本 (text, 可选)</option>
                      </optgroup>
                      <optgroup label="服务器信息字段">
                        <option value="serverIp">服务器IP地址 (text, 可选)</option>
                        <option value="serverOs">服务器操作系统 (text, 可选)</option>
                        <option value="serverConfig">服务器配置 (text, 可选)</option>
                        <option value="sshPort">SSH端口 (number, 可选)</option>
                      </optgroup>
                      <optgroup label="维护记录字段">
                        <option value="maintenanceDate">维护日期 (date, 可选)</option>
                        <option value="maintenanceContent">维护内容 (textarea, 可选)</option>
                        <option value="maintenanceBy">维护人员 (text, 可选)</option>
                      </optgroup>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <div class="alert alert-info mb-0 py-2">
                      <small>
                        <i class="bi bi-info-circle me-1"></i>
                        选择公共字段可自动填充标准配置
                      </small>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 基本信息 -->
            <div class="card mb-3">
              <div class="card-header">
                <h6 class="mb-0">基本信息</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="fieldName" class="form-label">字段名称 <span class="text-danger">*</span></label>
                      <input
                        type="text"
                        class="form-control"
                        id="fieldName"
                        v-model="form.field_name"
                        :disabled="isEditing"
                        placeholder="例如: custom_field_1"
                        required
                      >
                      <div class="form-text">英文名称，用于数据存储</div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="fieldLabel" class="form-label">显示标签 <span class="text-danger">*</span></label>
                      <input
                        type="text"
                        class="form-control"
                        id="fieldLabel"
                        v-model="form.field_label"
                        placeholder="例如: 自定义字段"
                        required
                      >
                      <div class="form-text">用户界面显示的标签</div>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="fieldType" class="form-label">字段类型 <span class="text-danger">*</span></label>
                      <select class="form-select" id="fieldType" v-model="form.field_type" required>
                        <option value="">请选择字段类型</option>
                        <option v-for="type in fieldTypes" :key="type.type_name" :value="type.type_name">
                          {{ type.type_label }}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="groupId" class="form-label">所属分组</label>
                      <select class="form-select" id="groupId" v-model="form.group_id">
                        <option value="">请选择分组</option>
                        <option v-for="group in fieldGroups" :key="group.id" :value="group.id">
                          {{ group.group_label }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="mb-3">
                  <label for="fieldDescription" class="form-label">字段描述</label>
                  <textarea
                    class="form-control"
                    id="fieldDescription"
                    v-model="form.field_description"
                    rows="2"
                    placeholder="描述这个字段的用途..."
                  ></textarea>
                </div>
              </div>
            </div>

            <!-- 显示设置 -->
            <div class="card mb-3">
              <div class="card-header">
                <h6 class="mb-0">显示设置</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="placeholder" class="form-label">占位符文本</label>
                      <input
                        type="text"
                        class="form-control"
                        id="placeholder"
                        v-model="form.placeholder"
                        placeholder="请输入..."
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="defaultValue" class="form-label">默认值</label>
                      <input
                        type="text"
                        class="form-control"
                        id="defaultValue"
                        v-model="form.default_value"
                        placeholder="默认值"
                      >
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-4">
                    <div class="mb-3">
                      <label for="displayOrder" class="form-label">显示顺序</label>
                      <input
                        type="number"
                        class="form-control"
                        id="displayOrder"
                        v-model.number="form.display_order"
                        min="0"
                        placeholder="0"
                      >
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="mb-3">
                      <label for="gridColumns" class="form-label">栅格列数</label>
                      <select class="form-select" id="gridColumns" v-model.number="form.grid_columns">
                        <option value="12">全宽 (12列)</option>
                        <option value="6">半宽 (6列)</option>
                        <option value="4">三分之一 (4列)</option>
                        <option value="3">四分之一 (3列)</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="mb-3">
                      <label for="cssClasses" class="form-label">CSS类名</label>
                      <input
                        type="text"
                        class="form-control"
                        id="cssClasses"
                        v-model="form.css_classes"
                        placeholder="custom-class"
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 字段属性 -->
            <div class="card mb-3">
              <div class="card-header">
                <h6 class="mb-0">字段属性</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="isRequired"
                        v-model="form.is_required"
                      >
                      <label class="form-check-label" for="isRequired">
                        必填字段
                      </label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="isReadonly"
                        v-model="form.is_readonly"
                      >
                      <label class="form-check-label" for="isReadonly">
                        只读字段
                      </label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="isAutoFill"
                        v-model="form.is_auto_fill"
                      >
                      <label class="form-check-label" for="isAutoFill">
                        自动填充
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 字段选项（仅对select、radio、checkbox类型显示） -->
            <div v-if="selectedFieldType && selectedFieldType.has_options" class="card mb-3">
              <div class="card-header">
                <h6 class="mb-0">字段选项</h6>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">选项列表</label>
                  <div v-for="(option, index) in fieldOptions" :key="index" class="input-group mb-2">
                    <input
                      type="text"
                      class="form-control"
                      v-model="option.value"
                      placeholder="选项值"
                    >
                    <input
                      type="text"
                      class="form-control"
                      v-model="option.label"
                      placeholder="显示文本"
                    >
                    <button
                      type="button"
                      class="btn btn-outline-danger"
                      @click="removeOption(index)"
                    >
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                  <button type="button" class="btn btn-outline-primary btn-sm" @click="addOption">
                    <i class="bi bi-plus me-1"></i>添加选项
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button type="button" class="btn btn-primary" @click="saveField" :disabled="saving">
            <i class="bi bi-check-circle me-1"></i>
            <span v-if="saving">保存中...</span>
            <span v-else>{{ isEditing ? '更新' : '创建' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { getFieldConfig } from '@/utils/commonFieldManager.js'

export default {
  name: 'FieldConfigModal',
  props: {
    formType: {
      type: String,
      required: true
    },
    fieldTypes: {
      type: Array,
      default: () => []
    },
    fieldGroups: {
      type: Array,
      default: () => []
    }
  },
  emits: ['saved', 'show-toast'],
  data() {
    return {
      isEditing: false,
      saving: false,
      selectedCommonField: '',
      form: {
        field_name: '',
        field_label: '',
        field_type: '',
        field_description: '',
        placeholder: '',
        default_value: '',
        is_required: false,
        is_readonly: false,
        is_auto_fill: false,
        display_order: 0,
        css_classes: '',
        grid_columns: 12,
        group_id: null
      },
      fieldOptions: []
    }
  },
  computed: {
    selectedFieldType() {
      return this.fieldTypes.find(type => type.type_name === this.form.field_type)
    }
  },
  methods: {
    show(field = null) {
      this.isEditing = !!field
      this.selectedCommonField = ''

      if (field) {
        // 编辑模式
        this.form = {
          id: field.id,
          field_name: field.field_name,
          field_label: field.field_label,
          field_type: field.field_type,
          field_description: field.field_description || '',
          placeholder: field.placeholder || '',
          default_value: field.default_value || '',
          is_required: field.is_required,
          is_readonly: field.is_readonly,
          is_auto_fill: field.is_auto_fill,
          display_order: field.display_order || 0,
          css_classes: field.css_classes || '',
          grid_columns: field.grid_columns || 12,
          group_id: field.group_id
        }

        // 加载字段选项
        this.fieldOptions = field.field_options ?
          Object.entries(field.field_options).map(([value, label]) => ({ value, label })) : []
      } else {
        // 新建模式
        this.form = {
          field_name: '',
          field_label: '',
          field_type: '',
          field_description: '',
          placeholder: '',
          default_value: '',
          is_required: false,
          is_readonly: false,
          is_auto_fill: false,
          display_order: 0,
          css_classes: '',
          grid_columns: 12,
          group_id: null
        }
        this.fieldOptions = []
      }

      // 使用强力遮罩清理器
      if (window.backdropKiller) {
        window.backdropKiller.kill()
        setTimeout(() => {
          window.backdropKiller.safeShow('fieldConfigModal', {
            backdrop: false,
            keyboard: true
          })
        }, 100)
      } else {
        // 备用方案
        const modalElement = document.getElementById('fieldConfigModal')
        if (modalElement) {
          setTimeout(() => {
            modalElement.style.display = 'block'
            modalElement.classList.add('show')
            modalElement.setAttribute('aria-modal', 'true')
            modalElement.removeAttribute('aria-hidden')
            document.body.classList.add('modal-open')
          }, 100)
        }
      }
    },

    applyCommonField() {
      if (!this.selectedCommonField) return

      const commonFieldConfig = getFieldConfig(this.selectedCommonField)
      if (!commonFieldConfig) {
        console.warn(`未找到公共字段配置: ${this.selectedCommonField}`)
        return
      }

      // 应用公共字段配置
      this.form.field_name = commonFieldConfig.field_name
      this.form.field_label = commonFieldConfig.field_label
      this.form.field_type = commonFieldConfig.field_type
      this.form.field_description = commonFieldConfig.field_description || ''
      this.form.placeholder = commonFieldConfig.placeholder || ''
      this.form.is_required = commonFieldConfig.is_required || false
      this.form.is_readonly = commonFieldConfig.is_readonly || false
      this.form.is_auto_fill = commonFieldConfig.is_auto_fill || false

      // 处理默认值
      if (commonFieldConfig.default_value) {
        if (typeof commonFieldConfig.default_value === 'function') {
          this.form.default_value = commonFieldConfig.default_value()
        } else {
          this.form.default_value = commonFieldConfig.default_value
        }
      }

      // 根据字段类型自动选择合适的分组
      this.autoSelectGroup(this.selectedCommonField)

      // 显示成功提示
      this.$emit('show-toast',
        `已应用公共字段配置: ${commonFieldConfig.field_label}`,
        '成功',
        'success'
      )

      // 清空选择
      this.selectedCommonField = ''
    },

    autoSelectGroup(fieldName) {
      // 根据字段名自动选择合适的分组
      const fieldGroupMapping = {
        'companyName': 'basic_info',
        'recordDate': 'basic_info',
        'customer': 'customer_info',
        'platformUrl': 'access_info',
        'platformVersion': 'access_info',
        'serverIp': 'server_info',
        'serverOs': 'server_info',
        'serverConfig': 'server_info',
        'sshPort': 'server_info',
        'maintenanceDate': 'maintenance_record',
        'maintenanceContent': 'maintenance_record',
        'maintenanceBy': 'maintenance_record'
      }

      const targetGroupName = fieldGroupMapping[fieldName]
      if (targetGroupName) {
        const targetGroup = this.fieldGroups.find(group => group.group_name === targetGroupName)
        if (targetGroup) {
          this.form.group_id = targetGroup.id
        }
      }
    },

    addOption() {
      this.fieldOptions.push({ value: '', label: '' })
    },

    removeOption(index) {
      this.fieldOptions.splice(index, 1)
    },

    async saveField() {
      if (!this.form.field_name || !this.form.field_label || !this.form.field_type) {
        alert('请填写必填字段')
        return
      }

      this.saving = true
      
      try {
        const data = {
          ...this.form,
          form_type: this.formType
        }

        // 处理字段选项
        if (this.selectedFieldType && this.selectedFieldType.has_options) {
          const options = {}
          this.fieldOptions.forEach(option => {
            if (option.value && option.label) {
              options[option.value] = option.label
            }
          })
          data.field_options = options
        }

        let response
        if (this.isEditing) {
          response = await axios.put(`/excel/form-field-configs/${this.form.id}`, data)
        } else {
          response = await axios.post('/excel/form-field-configs', data)
        }

        if (response.data.status === 'success') {
          // 关闭模态框
          const modal = window.bootstrap.Modal.getInstance(document.getElementById('fieldConfigModal'))
          modal.hide()
          
          // 触发保存事件
          this.$emit('saved', response.data.data)
          
          // 显示成功消息
          this.$emit('show-toast', 
            this.isEditing ? '字段配置更新成功' : '字段配置创建成功', 
            '成功', 
            'success'
          )
        } else {
          throw new Error(response.data.message)
        }
      } catch (error) {
        console.error('保存字段配置失败:', error)
        this.$emit('show-toast', 
          `保存失败: ${error.response && error.response.data && error.response.data.message || error.message}`,
          '错误', 
          'error'
        )
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.card-header h6 {
  color: #495057;
  font-weight: 600;
}

.form-text {
  font-size: 0.875rem;
  color: #6c757d;
}

.text-danger {
  color: #dc3545 !important;
}

.input-group .btn {
  border-left: 0;
}
</style>
