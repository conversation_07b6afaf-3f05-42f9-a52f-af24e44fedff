<template>
  <div class="card mb-4" :class="cardClass">
    <div class="card-header" @click="toggleCollapse($event)" :class="{'cursor-pointer': true, 'collapsed': isCollapsed}">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <slot name="header"></slot>
        </div>
        <i class="bi" :class="isCollapsed ? 'bi-chevron-down' : 'bi-chevron-up'"></i>
      </div>
    </div>
    <div v-show="!isCollapsed" class="card-body collapsible-content">
      <slot></slot>
    </div>
    <div v-if="isCollapsed && showSummary" class="card-footer bg-light py-2 px-3">
      <slot name="summary">
        <div class="text-muted small">
          <i class="bi bi-info-circle me-1"></i>点击展开查看详细内容
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
/**
 * 可折叠卡片组件
 * 用于创建可点击折叠/展开的卡片
 */
export default {
  name: 'CollapsibleCard',
  props: {
    // 初始折叠状态
    initialCollapsed: {
      type: Boolean,
      default: false
    },
    // 卡片样式类
    cardClass: {
      type: String,
      default: ''
    },
    // 是否显示折叠时的摘要信息
    showSummary: {
      type: Boolean,
      default: true
    },
    // 存储折叠状态的键名
    storageKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isCollapsed: this.initialCollapsed
    }
  },
  created() {
    // 如果提供了storageKey，尝试从localStorage恢复折叠状态
    if (this.storageKey) {
      const savedState = localStorage.getItem(`collapse_${this.storageKey}`)
      if (savedState !== null) {
        this.isCollapsed = savedState === 'true'
      }
    }
  },
  methods: {
    /**
     * 切换折叠状态
     * @param {Event} event - 点击事件
     */
    toggleCollapse(event) {
      // 阻止事件冒泡和默认行为，防止触发表单提交
      if (event) {
        event.preventDefault()
        event.stopPropagation()
      }

      this.isCollapsed = !this.isCollapsed

      // 如果提供了storageKey，保存折叠状态到localStorage
      if (this.storageKey) {
        localStorage.setItem(`collapse_${this.storageKey}`, this.isCollapsed)
      }

      // 发出折叠状态变化事件
      this.$emit('collapse-change', this.isCollapsed)

      // 阻止事件继续传播
      return false
    }
  }
}
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
  transition: all 0.25s ease;
}

.card {
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  background-color: #ffffff;
}

.card-header {
  padding: 1rem 1.5rem;
  font-weight: 600;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  color: #495057;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header.collapsed {
  border-bottom: none;
}

.card-body {
  padding: 1.5rem;
  background-color: #ffffff;
}

.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.collapsible-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header i {
  transition: transform 0.3s ease;
  font-size: 1.1rem;
}

.card-header.collapsed i {
  transform: rotate(0deg);
}

.card-header:not(.collapsed) i {
  transform: rotate(180deg);
}
</style>
