import { createRouter, createWebHistory } from 'vue-router'
import store from '../store'
import Home from '../views/Home.vue'
import FillSheet from '../views/FillSheet.vue'
import HistoryData from '../views/HistoryData.vue'
import EditFormSubmission from '../views/EditFormSubmission.vue'
import TemplateManager from '../views/TemplateManager.vue'
import FormTemplateManager from '../views/FormTemplateManager.vue'
import FormFieldConfig from '../views/FormFieldConfig.vue'
import AccessInfoStyleDemo from '../views/AccessInfoStyleDemo.vue'
import Login from '../views/Login.vue'
import Register from '../views/Register.vue'

/**
 * 路由配置
 * 定义应用的路由规则
 */
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { requiresGuest: true }
  },
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: true }
  },
  {
    path: '/fill-sheet',
    name: 'FillSheet',
    component: FillSheet,
    meta: { requiresAuth: true, permission: 'form.create' }
  },
  {
    path: '/history-data',
    name: 'HistoryData',
    component: HistoryData,
    meta: { requiresAuth: true, permission: 'form.view' }
  },
  {
    path: '/edit-form-submission/:id',
    name: 'EditFormSubmission',
    component: EditFormSubmission,
    props: true,
    meta: { requiresAuth: true, permission: 'form.edit' }
  },
  {
    path: '/template-manager',
    name: 'TemplateManager',
    component: TemplateManager,
    meta: { requiresAuth: true, permission: 'template.view' }
  },

  {
    path: '/component-manager',
    name: 'ComponentManager',
    component: () => import('../views/ComponentManager.vue'),
    meta: { requiresAuth: true, permission: 'component.view' }
  },
  {
    path: '/excel-template-manager',
    name: 'ExcelTemplateManager',
    component: () => import('../views/ExcelTemplateManager.vue'),
    meta: { requiresAuth: true, permission: 'template.view' }
  },

  {
    path: '/form-template-manager',
    name: 'FormTemplateManager',
    component: FormTemplateManager,
    meta: { requiresAuth: true, permission: 'template.view' }
  },
  {
    path: '/form-field-config',
    name: 'FormFieldConfig',
    component: FormFieldConfig,
    meta: { requiresAuth: true, permission: 'system.config' }
  },
  {
    path: '/access-info-style-demo',
    name: 'AccessInfoStyleDemo',
    component: AccessInfoStyleDemo,
    meta: { requiresAuth: true }
  },
  {
    path: '/excel-import',
    name: 'ExcelImport',
    component: () => import('../views/ExcelImport.vue')
  },
  {
    path: '/json-import',
    name: 'JsonImport',
    component: () => import('../views/JsonImport.vue')
  },
  // RBAC管理路由
  {
    path: '/user-management',
    name: 'UserManagement',
    component: () => import('../views/UserManagement.vue'),
    meta: { requiresAuth: true, permission: 'user.view' }
  },
  {
    path: '/profile',
    name: 'UserProfile',
    component: () => import('../views/UserProfile.vue'),
    meta: { requiresAuth: true }
  },

  {
    path: '/role-management',
    name: 'RoleManagement',
    component: () => import('../views/RoleManagement.vue'),
    meta: { requiresAuth: true, permission: 'role.view' }
  },
  {
    path: '/permission-management',
    name: 'PermissionManagement',
    component: () => import('../views/PermissionManagement.vue'),
    meta: { requiresAuth: true, permission: 'permission.view' }
  },
  {
    path: '/group-management',
    name: 'GroupManagement',
    component: () => import('../views/GroupManagement.vue'),
    meta: { requiresAuth: true, permission: 'group.view' }
  },

  {
    path: '/api-docs',
    name: 'ApiDocs',
    component: () => import('../views/ApiDocs.vue'),
    meta: { requiresAuth: true, title: 'API文档' }
  },

  {
    path: '/help',
    name: 'Help',
    component: () => import('../views/Help.vue'),
    meta: { requiresAuth: true, title: '帮助文档' }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue'),
    meta: { requiresAuth: true, title: '关于平台' }
  },
  {
    path: '/share/:shareId',
    name: 'ShareView',
    component: () => import('../views/ShareView.vue'),
    meta: {
      requiresAuth: false, // 不需要登录
      isPublic: true // 标记为公开页面
    }
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 清理可能残留的悬浮球DOM元素
  const floatingBalls = document.querySelectorAll('.floating-ball-container')
  if (floatingBalls.length > 0) {
    console.log(`🧹 路由切换时发现 ${floatingBalls.length} 个残留的悬浮球，正在清理...`)
    floatingBalls.forEach(ball => {
      if (ball.parentNode === document.body) {
        document.body.removeChild(ball)
        console.log('🗑️ 已清理残留的悬浮球DOM元素')
      }
    })
  }

  // 初始化认证状态
  if (!store.getters.isAuthenticated) {
    try {
      await store.dispatch('initAuth')
    } catch (error) {
      // 认证失败，继续执行路由守卫逻辑
    }
  }

  const isAuthenticated = store.getters.isAuthenticated
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)
  const requiredPermission = to.meta.permission

  // 需要登录的页面
  if (requiresAuth && !isAuthenticated) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 只允许游客访问的页面（如登录、注册）
  if (requiresGuest && isAuthenticated) {
    next('/')
    return
  }

  // 权限检查
  if (isAuthenticated && requiredPermission) {
    const hasPermission = store.getters.hasPermission(requiredPermission)
    const isAdmin = store.getters.isAdmin

    if (!hasPermission && !isAdmin) {
      // 没有权限，跳转到首页或显示错误页面
      next({
        path: '/',
        query: { error: 'permission_denied' }
      })
      return
    }
  }

  next()
})

export default router
