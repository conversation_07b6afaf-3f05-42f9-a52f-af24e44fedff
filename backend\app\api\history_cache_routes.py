"""
历史记录缓存API路由
提供历史记录的缓存管理功能
"""

from flask import Blueprint, jsonify, request, current_app
from app.auth.decorators import permission_required, get_current_user
from app.utils.cache_utils import HistoryRecordsCacheManager
from app.models.models import HistoryRecord
from app import db
import traceback
from datetime import datetime

# 创建历史记录缓存蓝图
history_cache_bp = Blueprint('history_cache', __name__, url_prefix='/api/history-cache')


@history_cache_bp.route('/search', methods=['GET'])
@permission_required('history.view')
def search_history_with_cache():
    """
    带缓存的历史记录搜索
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        company_name = request.args.get('company_name', '').strip()
        form_type = request.args.get('form_type', '').strip()
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()
        editor = request.args.get('editor', '').strip()
        
        # 构建过滤器
        filters = {
            'company_name': company_name if company_name else None,
            'form_type': form_type if form_type else None,
            'start_date': start_date if start_date else None,
            'end_date': end_date if end_date else None,
            'editor': editor if editor else None
        }
        
        # 尝试从缓存获取
        cached_data = HistoryRecordsCacheManager.get_history_records(
            user_id=current_user.id,
            page=page,
            per_page=per_page,
            **filters
        )
        
        if cached_data:
            current_app.logger.debug(f"历史记录缓存命中: user_id={current_user.id}, page={page}")
            return jsonify({
                'status': 'success',
                'message': '获取历史记录成功（缓存）',
                'data': cached_data,
                'from_cache': True
            })
        
        # 缓存未命中，从数据库查询
        current_app.logger.debug(f"历史记录缓存未命中，查询数据库: user_id={current_user.id}")
        
        # 构建查询
        query = HistoryRecord.query
        
        # 应用过滤器
        if company_name:
            query = query.filter(HistoryRecord.company_name.like(f'%{company_name}%'))
        if form_type:
            query = query.filter(HistoryRecord.form_type == form_type)
        if start_date:
            query = query.filter(HistoryRecord.created_at >= start_date)
        if end_date:
            query = query.filter(HistoryRecord.created_at <= end_date)
        if editor:
            query = query.filter(HistoryRecord.editor.like(f'%{editor}%'))
        
        # 按创建时间倒序排列
        query = query.order_by(HistoryRecord.created_at.desc())
        
        # 分页查询
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # 构建返回数据
        records_data = {
            'records': [record.to_dict() for record in pagination.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            },
            'filters': filters,
            'query_time': datetime.now().isoformat()
        }
        
        # 缓存查询结果
        HistoryRecordsCacheManager.cache_history_records(
            records_data,
            user_id=current_user.id,
            page=page,
            per_page=per_page,
            **filters
        )
        
        current_app.logger.info(f"历史记录查询完成: user_id={current_user.id}, total={pagination.total}")
        
        return jsonify({
            'status': 'success',
            'message': '获取历史记录成功',
            'data': records_data,
            'from_cache': False
        })
        
    except Exception as e:
        current_app.logger.error(f"搜索历史记录失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'搜索历史记录失败: {str(e)}'
        }), 500


@history_cache_bp.route('/clear', methods=['POST'])
@permission_required('history.cache.manage')
def clear_history_cache():
    """
    清除历史记录缓存
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        data = request.get_json() or {}
        target_user_id = data.get('user_id')
        
        # 如果指定了用户ID，清除指定用户的缓存
        if target_user_id:
            success = HistoryRecordsCacheManager.clear_history_cache(target_user_id)
            message = f'用户 {target_user_id} 的历史记录缓存清除成功'
        else:
            # 清除所有历史记录缓存
            success = HistoryRecordsCacheManager.clear_history_cache()
            message = '所有历史记录缓存清除成功'
        
        if success:
            current_app.logger.info(f"清除历史记录缓存: operator={current_user.username}, target_user={target_user_id}")
            return jsonify({
                'status': 'success',
                'message': message
            })
        else:
            return jsonify({
                'status': 'error',
                'message': '清除历史记录缓存失败'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"清除历史记录缓存失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'清除历史记录缓存失败: {str(e)}'
        }), 500


@history_cache_bp.route('/stats', methods=['GET'])
@permission_required('history.cache.view')
def get_history_cache_stats():
    """
    获取历史记录缓存统计信息
    """
    try:
        current_user = get_current_user()
        if not current_user:
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401
        
        # 获取缓存统计信息
        from app.utils.cache_utils import cache
        
        try:
            # 获取Redis信息
            redis_info = cache.cache._write_client.info()
            
            # 获取历史记录相关的缓存键
            history_keys = cache.cache._write_client.keys('history_records:*')
            
            stats = {
                'redis_status': 'connected',
                'total_keys': len(history_keys),
                'memory_usage': redis_info.get('used_memory_human', 'Unknown'),
                'cache_hits': redis_info.get('keyspace_hits', 0),
                'cache_misses': redis_info.get('keyspace_misses', 0),
                'cache_hit_rate': 0
            }
            
            # 计算缓存命中率
            total_requests = stats['cache_hits'] + stats['cache_misses']
            if total_requests > 0:
                stats['cache_hit_rate'] = round((stats['cache_hits'] / total_requests) * 100, 2)
            
            return jsonify({
                'status': 'success',
                'message': '获取缓存统计信息成功',
                'data': stats
            })
            
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'获取缓存统计信息失败: {str(e)}'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"获取历史记录缓存统计失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取历史记录缓存统计失败: {str(e)}'
        }), 500


@history_cache_bp.route('/health', methods=['GET'])
def history_cache_health():
    """
    历史记录缓存系统健康检查（无需权限）
    """
    try:
        # 测试缓存连接
        from app.utils.cache_utils import cache
        cache.cache._write_client.ping()
        
        return jsonify({
            'status': 'success',
            'data': {
                'cache_system': 'Redis',
                'cache_status': 'connected',
                'features': [
                    'history_search_cache',
                    'history_cache_clear',
                    'history_cache_stats'
                ]
            }
        })
    except Exception as e:
        current_app.logger.error(f"历史记录缓存健康检查失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'历史记录缓存系统异常: {str(e)}'
        }), 500
