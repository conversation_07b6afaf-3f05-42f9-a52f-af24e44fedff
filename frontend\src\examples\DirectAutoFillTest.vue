<template>
  <div class="direct-auto-fill-test">
    <div class="container-fluid mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-lightning me-2"></i>
                直接自动填充测试
              </h5>
            </div>
            <div class="card-body">
              
              <!-- 控制面板 -->
              <div class="row mb-4">
                <div class="col-md-4">
                  <label class="form-label fw-bold">表单类型</label>
                  <select v-model="formType" class="form-select">
                    <option value="安全测评">安全测评</option>
                    <option value="安全监测">安全监测</option>
                    <option value="应用加固">应用加固</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">操作</label>
                  <div class="d-flex gap-2">
                    <button class="btn btn-success btn-sm" @click="addServer">
                      添加服务器
                    </button>
                    <button class="btn btn-primary btn-sm" @click="triggerFill">
                      触发填充
                    </button>
                  </div>
                </div>
                <div class="col-md-4">
                  <label class="form-label fw-bold">状态</label>
                  <div>
                    <span class="badge bg-info">服务器: {{ serverList.length }}</span>
                    <span class="badge bg-success ms-1">已填充: {{ filledCount }}</span>
                  </div>
                </div>
              </div>

              <!-- 服务器信息 -->
              <div class="row mb-4">
                <div class="col-12">
                  <h6>服务器信息</h6>
                  <div v-if="serverList.length === 0" class="alert alert-info">
                    暂无服务器，点击"添加服务器"按钮添加
                  </div>
                  <div v-else>
                    <div v-for="(server, index) in serverList" :key="index" class="card mb-2 bg-light">
                      <div class="card-body py-2">
                        <div class="row">
                          <div class="col-md-3">
                            <strong>IP:</strong> {{ server.IP地址 }}
                          </div>
                          <div class="col-md-6">
                            <strong>组件:</strong> 
                            <span v-for="comp in server.部署应用" :key="comp" class="badge bg-primary me-1">
                              {{ comp }}
                            </span>
                          </div>
                          <div class="col-md-3">
                            <strong>端口:</strong>
                            <div class="small">
                              <div v-for="(port, comp) in server.组件端口" :key="comp">
                                {{ comp }}: {{ port }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 访问信息 -->
              <div class="row">
                <div class="col-12">
                  <h6>访问信息 (自动填充测试)</h6>
                  <div class="card">
                    <div class="card-body">
                      <dynamic-access-info-section
                        ref="accessInfoRef"
                        v-model="accessData"
                        :field-config="fieldConfig"
                        :form-type="formType"
                        :server-list="serverList"
                        :key="`access-${formType}-${refreshKey}`"
                      />
                      
                      <!-- 手动触发按钮 -->
                      <div class="mt-3">
                        <button 
                          class="btn btn-warning btn-sm" 
                          @click="forceAutoFill"
                          :disabled="serverList.length === 0"
                        >
                          强制自动填充
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 结果显示 -->
              <div class="row mt-4">
                <div class="col-12">
                  <h6>填充结果</h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-6">
                          <strong>访问数据:</strong>
                          <pre class="small">{{ JSON.stringify(accessData, null, 2) }}</pre>
                        </div>
                        <div class="col-md-6">
                          <strong>字段配置:</strong>
                          <pre class="small">{{ JSON.stringify(fieldConfig, null, 2) }}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicAccessInfoSection from '@/components/forms/common/DynamicAccessInfoSection.vue'
import { getFormFieldConfig } from '@/config/formFieldConfig'

export default {
  name: 'DirectAutoFillTest',
  components: {
    DynamicAccessInfoSection
  },
  data() {
    return {
      formType: '安全测评',
      serverList: [],
      accessData: {},
      refreshKey: 0
    }
  },
  computed: {
    fieldConfig() {
      const config = getFormFieldConfig(this.formType)
      return config.access || {}
    },

    filledCount() {
      return Object.values(this.accessData).filter(value => value && value.trim()).length
    }
  },
  methods: {
    addServer() {
      const server = {
        IP地址: `192.168.1.${100 + this.serverList.length}`,
        部署应用: [],
        组件端口: {}
      }

      // 根据表单类型添加对应组件
      if (this.formType === '安全测评') {
        server.部署应用 = ['front-ssp-admin', 'front-ssp-user', 'luna', 'backend-ssp-user']
        server.组件端口 = {
          'front-ssp-admin': '8080',
          'front-ssp-user': '8081',
          'luna': '9001',
          'backend-ssp-user': '8082'
        }
      } else if (this.formType === '安全监测') {
        server.部署应用 = ['web-service-nginx', 'init', 'kibana']
        server.组件端口 = {
          'web-service-nginx': '443',
          'init': '8181',
          'kibana': '5601'
        }
      } else if (this.formType === '应用加固') {
        server.部署应用 = ['secweb', 'luna']
        server.组件端口 = {
          'secweb': '8000',
          'luna': '9001'
        }
      }

      this.serverList.push(server)
      console.log('添加服务器:', server)
    },

    triggerFill() {
      console.log('手动触发填充')
      this.refreshKey += 1
    },

    forceAutoFill() {
      console.log('强制自动填充')
      if (this.$refs.accessInfoRef && this.$refs.accessInfoRef.forceAutoFill) {
        this.$refs.accessInfoRef.forceAutoFill()
      } else {
        console.log('无法调用forceAutoFill方法')
      }
    },

    clearAll() {
      this.serverList = []
      this.accessData = {}
      this.refreshKey += 1
    }
  },
  watch: {
    formType() {
      this.clearAll()
    },

    serverList: {
      handler(newList) {
        console.log('服务器列表变化:', newList)
      },
      deep: true
    },

    accessData: {
      handler(newData) {
        console.log('访问数据变化:', newData)
      },
      deep: true
    }
  },
  mounted() {
    console.log('DirectAutoFillTest 组件已挂载')
  }
}
</script>

<style scoped>
.direct-auto-fill-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
}

pre {
  max-height: 200px;
  overflow-y: auto;
  font-size: 0.75rem;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.badge {
  font-size: 0.75rem;
}
</style>
