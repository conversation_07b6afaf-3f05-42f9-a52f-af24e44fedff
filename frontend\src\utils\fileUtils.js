/**
 * 文件处理相关工具函数
 */

/**
 * 从HTTP响应头中解析文件名
 * 支持RFC 6266标准的UTF-8编码文件名和传统格式
 * @param {string} contentDisposition - Content-Disposition响应头的值
 * @param {string} defaultFilename - 默认文件名
 * @returns {string} 解析出的文件名
 */
export function parseFilenameFromContentDisposition(contentDisposition, defaultFilename = 'download.xlsx') {
  if (!contentDisposition) {
    return defaultFilename
  }

  // 优先尝试解析 RFC 6266 格式的 filename*=UTF-8''
  const utf8FilenameMatch = contentDisposition.match(/filename\*=UTF-8''([^;]+)/)
  if (utf8FilenameMatch && utf8FilenameMatch[1]) {
    try {
      const decodedFilename = decodeURIComponent(utf8FilenameMatch[1])
      console.log('UTF-8文件名解析成功:', decodedFilename)
      return decodedFilename
    } catch (e) {
      console.warn('UTF-8文件名解析失败:', e)
    }
  }

  // 回退到传统格式 filename="..."
  const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
  if (filenameMatch && filenameMatch[1]) {
    const filename = filenameMatch[1].replace(/['"]/g, '')
    console.log('传统格式文件名解析成功:', filename)
    return filename
  }

  console.log('文件名解析失败，使用默认文件名:', defaultFilename)
  return defaultFilename
}

/**
 * 下载Blob数据为文件
 * @param {Blob} blob - 要下载的数据
 * @param {string} filename - 文件名
 */
export function downloadBlob(blob, filename) {
  // 使用更安全的下载方式，避免HTTPS警告
  try {
    // 尝试使用现代的下载API
    if (window.showSaveFilePicker) {
      window.showSaveFilePicker({
        suggestedName: filename,
        types: [{
          description: 'Excel files',
          accept: { 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'] }
        }]
      }).then(async (fileHandle) => {
        const writable = await fileHandle.createWritable()
        await writable.write(blob)
        await writable.close()
      }).catch(() => {
        // 如果用户取消或API失败，回退到传统方式
        downloadBlobTraditional(blob, filename)
      })
    } else {
      // 回退到传统方式
      downloadBlobTraditional(blob, filename)
    }
  } catch (downloadError) {
    // 如果现代API失败，使用传统方式
    downloadBlobTraditional(blob, filename)
  }
}

/**
 * 使用传统方式下载Blob数据
 * @param {Blob} blob - 要下载的数据
 * @param {string} filename - 文件名
 */
function downloadBlobTraditional(blob, filename) {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.setAttribute('download', filename)
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
