<template>
  <div class="component-manager">
    <div class="container-fluid">
      <!-- 面包屑导航 -->
      <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <router-link to="/form-template-manager" class="text-decoration-none">
              <i class="bi bi-house me-1"></i>表单模板管理
            </router-link>
          </li>
          <li class="breadcrumb-item active" aria-current="page">
            <i class="bi bi-gear-fill me-1"></i>组件管理
          </li>
        </ol>
      </nav>

      <!-- 页面标题和快速操作 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="page-title mb-1">
                    <i class="bi bi-gear-fill me-2"></i>
                    组件管理
                  </h2>
                  <p class="text-muted mb-0" v-if="selectedFormType">
                    当前管理：<strong>{{ selectedFormType }}</strong> 表单类型的组件
                  </p>
                </div>
                <div>
                  <button class="btn btn-outline-secondary me-2" @click="goBack">
                    <i class="bi bi-arrow-left me-1"></i>返回管理页面
                  </button>
                  <div class="btn-group">
                    <button
                      type="button"
                      class="btn btn-primary dropdown-toggle"
                      data-bs-toggle="dropdown"
                      :disabled="!selectedFormType || categories.length === 0"
                    >
                      <i class="bi bi-plus-circle me-1"></i>快速创建
                    </button>
                    <ul class="dropdown-menu">
                      <!-- 当前表单类型的分类列表 -->
                      <li v-if="!selectedFormType" class="dropdown-header">
                        请先选择表单类型
                      </li>
                      <li v-else-if="categories.length === 0" class="dropdown-header">
                        当前表单类型暂无分类
                      </li>
                      <template v-else>
                        <li class="dropdown-header">
                          {{ selectedFormType }} - 快速创建组件
                        </li>
                        <li v-for="category in categories" :key="category.key">
                          <a class="dropdown-item" href="#" @click="createFromCategory(category)">
                            <i :class="category.icon || 'bi-folder'" class="me-2"></i>
                            {{ category.display_name }}
                            <small class="text-muted ms-1">({{ getComponentCount(category.key) }})</small>
                          </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" @click="showCreateCategoryModal">
                          <i class="bi bi-folder-plus me-2"></i>新建分类
                        </a></li>
                      </template>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单类型选择 -->
      <div class="row mb-4">
        <div class="col-md-6">
          <div class="form-floating">
            <select
              class="form-select"
              id="formTypeSelect"
              v-model="selectedFormType"
              @change="loadData"
            >
              <option value="">所有表单类型</option>
              <option v-for="formType in availableFormTypes" :key="formType" :value="formType">
                {{ formType }}
              </option>
            </select>
            <label for="formTypeSelect">表单类型</label>
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex align-items-end justify-content-end">
            <!-- 视图切换按钮 -->
            <div class="btn-group me-3" role="group">
              <input type="radio" class="btn-check" name="viewMode" id="cardView" v-model="viewMode" value="card">
              <label class="btn btn-outline-secondary btn-sm" for="cardView">
                <i class="bi bi-grid-3x3-gap"></i> 卡片视图
              </label>

              <input type="radio" class="btn-check" name="viewMode" id="tableView" v-model="viewMode" value="table">
              <label class="btn btn-outline-secondary btn-sm" for="tableView">
                <i class="bi bi-table"></i> 表格视图
              </label>
            </div>


          </div>
        </div>
      </div>

      <!-- 组件分类和组件列表 -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>
                组件分类与组件
              </h5>
            </div>
            <div class="card-body">
              <!-- 加载状态 -->
              <div v-if="loading" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载组件数据...</p>
              </div>

              <!-- 组件分类列表 -->
              <div v-else>
                <div v-if="categories.length === 0" class="text-center py-4 text-muted">
                  <i class="bi bi-inbox display-4"></i>
                  <p class="mt-2">暂无组件分类数据</p>
                  <button class="btn btn-primary" @click="showCreateCategoryModal">
                    创建第一个分类
                  </button>
                </div>

                <!-- 卡片视图 -->
                <div v-else-if="viewMode === 'card'" class="accordion" id="categoryAccordion">
                  <div
                    v-for="category in categories"
                    :key="category.key"
                    class="accordion-item"
                  >
                    <h2 class="accordion-header">
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        :data-bs-target="`#collapse-${category.key}`"
                        :aria-expanded="expandedCategoryKeys.includes(category.key)"
                        :aria-controls="`collapse-${category.key}`"
                        @click="manualToggleCollapse(category.key)"
                      >
                        <div class="d-flex align-items-center w-100">
                          <i :class="category.icon || 'bi-folder'" class="me-2"></i>
                          <span class="me-2">{{ category.display_name }}</span>
                          <span :class="category.color || 'bg-secondary'" class="badge me-2">
                            {{ category.key }}
                          </span>
                          <span class="badge bg-light text-dark ms-auto me-3">
                            {{ getComponentCount(category.key) }} 个组件
                          </span>
                        </div>
                      </button>
                    </h2>
                    <div
                      :id="`collapse-${category.key}`"
                      class="accordion-collapse collapse"
                      data-bs-parent="#categoryAccordion"
                    >
                      <div class="accordion-body">
                        <!-- 分类操作按钮 -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                          <div>
                            <span class="text-muted">适用表单类型：</span>
                            <span
                              v-for="formType in category.form_types"
                              :key="formType"
                              class="badge bg-info me-1"
                            >
                              {{ formType }}
                            </span>
                          </div>
                          <div class="btn-group btn-group-sm">
                            <!-- 编辑按钮：未分组分类不显示 -->
                            <button
                              v-if="!isUncategorizedCategory(category)"
                              class="btn btn-outline-primary"
                              @click="editCategory(category)"
                              title="编辑分类"
                            >
                              <i class="bi bi-pencil"></i> 编辑
                            </button>
                            <!-- 添加组件按钮：所有分类都显示 -->
                            <button
                              class="btn btn-outline-success"
                              @click="showCreateComponentModal(category.key)"
                              title="添加组件"
                            >
                              <i class="bi bi-plus"></i> 添加组件
                            </button>
                            <!-- 删除按钮：未分组分类不显示 -->
                            <button
                              v-if="!isUncategorizedCategory(category)"
                              class="btn btn-outline-danger"
                              @click="confirmDeleteCategory(category)"
                              title="删除分类"
                            >
                              <i class="bi bi-trash"></i> 删除
                            </button>
                            <!-- 未分组分类的系统保护提示 -->
                            <span
                              v-if="isUncategorizedCategory(category)"
                              class="btn btn-outline-secondary disabled"
                              title="系统保护分类，不可编辑或删除"
                            >
                              <i class="bi bi-shield-check"></i> 系统保护
                            </span>
                          </div>
                        </div>

                        <!-- 组件列表 -->
                        <div class="row">
                          <div
                            v-for="component in getComponentsByCategory(category.key)"
                            :key="component.id"
                            class="col-md-6 col-lg-4 mb-3"
                          >
                            <div class="card component-card h-100">
                              <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                  <h6 class="card-title mb-0">
                                    {{ component.display_name }}
                                    <span
                                      :class="[component.is_active ? 'status-dot-active' : 'status-dot-inactive', 'status-dot']"
                                      @click.stop="toggleActiveStatus(component, !component.is_active)"
                                      title="点击切换启用状态"
                                    ></span>
                                  </h6>
                                  <div class="btn-group" role="group">
                                    <button
                                      class="btn btn-sm btn-outline-primary"
                                      type="button"
                                      @click="editComponent(component)"
                                      title="编辑组件"
                                    >
                                      <i class="bi bi-pencil"></i>
                                    </button>
                                    <button
                                      class="btn btn-sm btn-outline-danger"
                                      type="button"
                                      @click="deleteComponent(component)"
                                      title="删除组件"
                                    >
                                      <i class="bi bi-trash"></i>
                                    </button>
                                  </div>
                                </div>
                                <p class="card-text small text-muted mb-2">
                                  <strong>名称:</strong> {{ component.name }}
                                </p>
                                <p class="card-text small text-muted mb-2">
                                  <strong>端口:</strong> {{ component.default_port }}
                                </p>
                                <p class="card-text small text-muted mb-2" v-if="component.version">
                                  <strong>版本:</strong> {{ component.version }}
                                </p>
                                <p class="card-text small text-muted mb-0">
                                  {{ component.description }}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 无组件提示 -->
                        <div v-if="getComponentsByCategory(category.key).length === 0" class="text-center py-3 text-muted">
                          <i class="bi bi-inbox"></i>
                          <p class="mb-2">该分类下暂无组件</p>
                          <button
                            class="btn btn-sm btn-outline-primary"
                            @click="showCreateComponentModal(category.key)"
                          >
                            添加第一个组件
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 表格视图 -->
                <div v-else-if="viewMode === 'table'">
                  <!-- 表格工具栏 -->
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="d-flex align-items-center">
                      <h6 class="mb-0 me-3">组件列表</h6>
                      <small class="text-muted">共 {{ getSortedComponents().length }} 个组件</small>
                      <span v-if="selectedComponents.length > 0" class="badge bg-primary ms-2">
                        已选择 {{ selectedComponents.length }} 个
                      </span>
                    </div>
                    <div class="d-flex align-items-center">
                      <!-- 批量操作 -->
                      <div v-if="selectedComponents.length > 0" class="btn-group me-2">
                        <button class="btn btn-outline-success btn-sm" @click="batchEnable" title="批量启用">
                          <i class="bi bi-check-circle"></i> 启用
                        </button>
                        <button class="btn btn-outline-warning btn-sm" @click="batchDisable" title="批量禁用">
                          <i class="bi bi-x-circle"></i> 禁用
                        </button>
                        <button class="btn btn-outline-danger btn-sm" @click="batchDelete" title="批量删除">
                          <i class="bi bi-trash"></i> 删除
                        </button>
                      </div>

                      <button
                        class="btn btn-outline-secondary btn-sm me-2"
                        @click="resetColumnWidths()"
                        title="重置列宽为默认值"
                      >
                        <i class="bi bi-arrow-clockwise"></i> 重置列宽
                      </button>
                      <button class="btn btn-outline-info btn-sm me-2" @click="exportComponents" title="导出组件配置">
                        <i class="bi bi-download"></i> 导出
                      </button>
                      <button class="btn btn-outline-primary btn-sm me-2" @click="showCreateCategoryModal()">
                        <i class="bi bi-folder-plus"></i> 新建分类
                      </button>
                      <button class="btn btn-primary btn-sm" @click="showCreateComponentModal()">
                        <i class="bi bi-plus-circle"></i> 新建组件
                      </button>
                    </div>
                  </div>

                  <!-- 简洁的可编辑表格 -->
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>组件名称</th>
                          <th>显示名称</th>
                          <th>分类</th>
                          <th>版本</th>
                          <th>端口</th>
                          <th>协议</th>
                          <th>状态</th>
                          <th>操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="component in getSortedComponents()" :key="component.id">
                          <!-- 组件名称 -->
                          <td>
                            <input
                              v-if="editingComponentId === component.id"
                              type="text"
                              class="form-control form-control-sm"
                              v-model="editingData.name"
                              @blur="saveInlineEdit(component)"
                              @keyup.enter="saveInlineEdit(component)"
                              @keyup.esc="cancelInlineEdit()"
                            >
                            <span v-else @dblclick="startInlineEdit(component)">
                              {{ component.name }}
                            </span>
                          </td>

                          <!-- 显示名称 -->
                          <td>
                            <input
                              v-if="editingComponentId === component.id"
                              type="text"
                              class="form-control form-control-sm"
                              v-model="editingData.display_name"
                              @blur="saveInlineEdit(component)"
                              @keyup.enter="saveInlineEdit(component)"
                              @keyup.esc="cancelInlineEdit()"
                            >
                            <span v-else @dblclick="startInlineEdit(component)">
                              {{ component.display_name || '-' }}
                            </span>
                          </td>

                          <!-- 分类 -->
                          <td>
                            <select
                              v-if="editingComponentId === component.id"
                              class="form-select form-select-sm"
                              v-model="editingData.category_key"
                              @change="saveInlineEdit(component)"
                            >
                              <option value="">选择分类</option>
                              <option
                                v-for="category in categories"
                                :key="category.key"
                                :value="category.key"
                              >
                                {{ category.display_name }}
                              </option>
                            </select>
                            <span v-else @dblclick="startInlineEdit(component)">
                              {{ getCategoryDisplayName(component.category_key) }}
                            </span>
                          </td>

                          <!-- 版本 -->
                          <td>
                            <input
                              v-if="editingComponentId === component.id"
                              type="text"
                              class="form-control form-control-sm"
                              v-model="editingData.version"
                              @blur="saveInlineEdit(component)"
                              @keyup.enter="saveInlineEdit(component)"
                              @keyup.esc="cancelInlineEdit()"
                            >
                            <span v-else @dblclick="startInlineEdit(component)">
                              {{ component.version || '-' }}
                            </span>
                          </td>

                          <!-- 端口 -->
                          <td>
                            <input
                              v-if="editingComponentId === component.id"
                              type="text"
                              class="form-control form-control-sm"
                              v-model="editingData.default_port"
                              @blur="saveInlineEdit(component)"
                              @keyup.enter="saveInlineEdit(component)"
                              @keyup.esc="cancelInlineEdit()"
                            >
                            <span v-else @dblclick="startInlineEdit(component)">
                              {{ component.default_port || '-' }}
                            </span>
                          </td>

                          <!-- 协议 -->
                          <td>
                            <select
                              v-if="editingComponentId === component.id"
                              class="form-select form-select-sm"
                              v-model="editingData.protocol"
                              @change="saveInlineEdit(component)"
                            >
                              <option value="">选择协议</option>
                              <option value="http">HTTP</option>
                              <option value="https">HTTPS</option>
                              <option value="tcp">TCP</option>
                              <option value="udp">UDP</option>
                            </select>
                            <span v-else @dblclick="startInlineEdit(component)">
                              {{ component.protocol ? component.protocol.toUpperCase() : '-' }}
                            </span>
                          </td>

                          <!-- 状态 -->
                          <td>
                            <div class="form-check form-switch">
                              <input
                                class="form-check-input"
                                type="checkbox"
                                :checked="component.is_active"
                                @change="toggleActiveStatus(component, !component.is_active)"
                              >
                              <label class="form-check-label">
                                {{ component.is_active ? '启用' : '禁用' }}
                              </label>
                            </div>
                          </td>

                          <!-- 操作 -->
                          <td>
                            <button
                              v-if="editingComponentId === component.id"
                              class="btn btn-success btn-sm me-1"
                              @click="saveInlineEdit(component)"
                              title="保存"
                            >
                              <i class="bi bi-check"></i>
                            </button>
                            <button
                              v-if="editingComponentId === component.id"
                              class="btn btn-secondary btn-sm me-1"
                              @click="cancelInlineEdit()"
                              title="取消"
                            >
                              <i class="bi bi-x"></i>
                            </button>
                            <button
                              v-if="editingComponentId !== component.id"
                              class="btn btn-outline-primary btn-sm me-1"
                              @click="startInlineEdit(component)"
                              title="编辑"
                            >
                              <i class="bi bi-pencil"></i>
                            </button>
                            <button
                              class="btn btn-outline-danger btn-sm"
                              @click="deleteComponent(component)"
                              title="删除"
                            >
                              <i class="bi bi-trash"></i>
                            </button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- 无组件提示 -->
                  <div v-if="getSortedComponents().length === 0" class="text-center py-5">
                    <div class="empty-state">
                      <i class="bi bi-puzzle text-muted display-4"></i>
                      <h6 class="text-muted mt-3">暂无组件数据</h6>
                      <p class="text-muted">请选择表单类型或创建第一个组件</p>
                      <button class="btn btn-primary" @click="showCreateComponentModal()">
                        <i class="bi bi-plus-circle me-1"></i>
                        创建第一个组件
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 组件编辑模态框 -->
    <div
      class="modal fade"
      id="componentModal"
      tabindex="-1"
      data-bs-backdrop="static"
      data-bs-keyboard="true"
      data-safe-modal="true"
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ editingComponent ? '编辑组件' : '添加组件' }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveComponent">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="componentName" class="form-label">组件名称 *</label>
                    <input 
                      type="text" 
                      class="form-control" 
                      id="componentName"
                      v-model="componentForm.name"
                      required
                      placeholder="如: nginx, mysql"
                    >
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="componentDisplayName" class="form-label">显示名称 *</label>
                    <input 
                      type="text" 
                      class="form-control" 
                      id="componentDisplayName"
                      v-model="componentForm.display_name"
                      required
                      placeholder="如: Nginx Web服务器"
                    >
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="componentCategory" class="form-label">组件分类 *</label>
                    <select class="form-select" id="componentCategory" v-model="componentForm.category_key" required>
                      <option value="">选择分类</option>
                      <option
                        v-for="category in categories"
                        :key="category.key"
                        :value="category.key"
                      >
                        {{ category.display_name }}
                      </option>
                    </select>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="componentVersion" class="form-label">版本</label>
                    <input 
                      type="text" 
                      class="form-control" 
                      id="componentVersion"
                      v-model="componentForm.version"
                      placeholder="如: 1.20.0"
                    >
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="componentPort" class="form-label">默认端口</label>
                    <input 
                      type="text" 
                      class="form-control" 
                      id="componentPort"
                      v-model="componentForm.default_port"
                      placeholder="如: 80, 3306"
                    >
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="componentProtocol" class="form-label">协议</label>
                    <select class="form-select" id="componentProtocol" v-model="componentForm.protocol">
                      <option value="">选择协议</option>
                      <option value="http">HTTP</option>
                      <option value="https">HTTPS</option>
                      <option value="tcp">TCP</option>
                      <option value="udp">UDP</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label for="componentDescription" class="form-label">描述</label>
                <textarea 
                  class="form-control" 
                  id="componentDescription"
                  v-model="componentForm.description"
                  rows="3"
                  placeholder="描述组件的用途和特点"
                ></textarea>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" @click="saveComponent">
              {{ editingComponent ? '更新' : '添加' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类管理模态框 -->
    <div class="modal fade" id="categoryModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ editingCategory ? '编辑分类' : '新建分类' }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveCategory">
              <div class="mb-3">
                <label for="categoryKey" class="form-label">分类标识 *</label>
                <input
                  type="text"
                  class="form-control"
                  id="categoryKey"
                  v-model="categoryForm.key"
                  :disabled="editingCategory"
                  placeholder="如: web-server, database"
                  required
                >
                <div class="form-text">
                  分类的唯一标识，只能包含字母、数字、连字符，创建后不可修改
                </div>
              </div>
              <div class="mb-3">
                <label for="categoryDisplayName" class="form-label">显示名称 *</label>
                <input
                  type="text"
                  class="form-control"
                  id="categoryDisplayName"
                  v-model="categoryForm.display_name"
                  placeholder="如: Web服务器, 数据库"
                  required
                >
              </div>
              <div class="mb-3">
                <label for="categoryDescription" class="form-label">描述</label>
                <textarea
                  class="form-control"
                  id="categoryDescription"
                  v-model="categoryForm.description"
                  rows="3"
                  placeholder="描述该分类的用途和特点"
                ></textarea>
              </div>
              <div class="mb-3">
                <label for="categoryIcon" class="form-label">图标选择</label>
                <div class="row">
                  <div class="col-md-8">
                    <input
                      type="text"
                      class="form-control"
                      id="categoryIcon"
                      v-model="categoryForm.icon"
                      placeholder="如: bi-server, bi-database"
                    >
                    <div class="form-text">
                      Bootstrap Icons 图标类名，或从下方选择
                    </div>
                  </div>
                  <div class="col-md-4 text-center">
                    <div class="icon-preview p-3 border rounded">
                      <i :class="categoryForm.icon || 'bi-folder'" style="font-size: 2rem;"></i>
                      <div class="small text-muted mt-1">预览</div>
                    </div>
                  </div>
                </div>

                <!-- 常用图标选择 -->
                <div class="mt-3">
                  <label class="form-label small">常用图标：</label>
                  <div class="icon-selector">
                    <button
                      v-for="icon in commonIcons"
                      :key="icon.class"
                      type="button"
                      class="btn btn-outline-secondary btn-sm me-2 mb-2"
                      :class="{ 'active': categoryForm.icon === icon.class }"
                      @click="categoryForm.icon = icon.class"
                      :title="icon.name"
                    >
                      <i :class="icon.class"></i>
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" @click="saveCategory">
              {{ editingCategory ? '更新' : '创建' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Toast 通知组件 -->
    <ToastNotification ref="toast" />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Modal } from 'bootstrap'
import axios from 'axios'
import ToastNotification from '@/components/common/ToastNotification.vue'

export default {
  name: 'ComponentManager',
  components: {
    ToastNotification
  },
  setup() {
    const router = useRouter()
    const route = useRoute()

    // Toast 引用
    const toast = ref(null)

    // 分类管理相关数据
    const editingCategory = ref(null)
    const categoryForm = reactive({
      key: '',
      display_name: '',
      description: '',
      icon: 'bi-folder',
      form_type: ''
    })

    // 常用图标列表
    const commonIcons = ref([
      { class: 'bi-folder', name: '文件夹' },
      { class: 'bi-server', name: '服务器' },
      { class: 'bi-database', name: '数据库' },
      { class: 'bi-globe', name: '网络/Web' },
      { class: 'bi-shield-check', name: '安全' },
      { class: 'bi-layers', name: '中间件/层级' },
      { class: 'bi-hdd', name: '存储' },
      { class: 'bi-router', name: '路由器/网络' },
      { class: 'bi-eye', name: '监控' },
      { class: 'bi-gear', name: '设置/工具' },
      { class: 'bi-cloud', name: '云服务' },
      { class: 'bi-cpu', name: 'CPU/处理器' },
      { class: 'bi-memory', name: '内存' },
      { class: 'bi-diagram-3', name: '架构/图表' },
      { class: 'bi-box', name: '容器/组件' },
      { class: 'bi-plugin', name: '插件' },
      { class: 'bi-terminal', name: '终端/命令行' },
      { class: 'bi-code-square', name: '代码' },
      { class: 'bi-bug', name: '调试/测试' },
      { class: 'bi-lightning', name: '性能/快速' }
    ])

    // 响应式数据
    const selectedFormType = ref('')
    const availableFormTypes = ref(['安全测评', '安全监测', '应用加固'])
    const categories = ref([])
    const componentsByCategory = ref({})
    const loading = ref(false)
    const editingComponent = ref(null)
    const viewMode = ref('card')
    const selectedComponents = ref([])
    const expandedCategoryKeys = ref([])

    // 内联编辑相关
    const editingComponentId = ref(null)
    const editingData = reactive({
      name: '',
      display_name: '',
      category_key: '',
      version: '',
      default_port: '',
      protocol: ''
    })

    // 组件表单数据
    const componentForm = reactive({
      name: '',
      display_name: '',
      category_key: '',
      version: '',
      default_port: '',
      description: '',
      protocol: '',
      form_type: ''
    })

    // 计算属性
    const getAllComponents = () => {
      const allComponents = []
      console.log('🔍 获取所有组件，当前分类数据:', componentsByCategory.value)

      Object.entries(componentsByCategory.value).forEach(([categoryKey, categoryData]) => {
        if (categoryData && categoryData.components) {
          console.log(`  📁 分类 ${categoryKey}: ${categoryData.components.length} 个组件`)
          allComponents.push(...categoryData.components)
        } else {
          console.log(`  📁 分类 ${categoryKey}: 无组件数据`)
        }
      })

      console.log(`📊 总共获取到 ${allComponents.length} 个组件`)
      return allComponents
    }

    const getSortedComponents = () => {
      const sorted = getAllComponents().sort((a, b) => a.name.localeCompare(b.name))
      console.log('📋 排序后的组件列表:', sorted.map(c => `${c.name} (${c.display_name})`))
      return sorted
    }

    // 表格选择相关
    const isAllSelected = computed(() => {
      const allComponents = getSortedComponents()
      return allComponents.length > 0 && selectedComponents.value.length === allComponents.length
    })

    const toggleSelectAll = () => {
      const allComponents = getSortedComponents()
      if (isAllSelected.value) {
        selectedComponents.value = []
      } else {
        selectedComponents.value = allComponents.map(c => c.id)
      }
    }

    // 组件图标和样式方法
    const getComponentIcon = (categoryKey) => {
      const iconMap = {
        'web': 'bi bi-globe',
        'database': 'bi bi-database',
        'middleware': 'bi bi-layers',
        'security': 'bi bi-shield-check',
        'monitor': 'bi bi-eye',
        'network': 'bi bi-router',
        'storage': 'bi bi-hdd',
        'other': 'bi bi-gear'
      }
      return iconMap[categoryKey?.toLowerCase()] || 'bi bi-puzzle'
    }

    const getCategoryBadgeClass = (categoryKey) => {
      const classMap = {
        'web': 'badge bg-primary',
        'database': 'badge bg-success',
        'middleware': 'badge bg-warning text-dark',
        'security': 'badge bg-danger',
        'monitor': 'badge bg-info',
        'network': 'badge bg-secondary',
        'storage': 'badge bg-dark',
        'other': 'badge bg-light text-dark'
      }
      return classMap[categoryKey?.toLowerCase()] || 'badge bg-light text-dark'
    }

    const getCategoryDisplayName = (categoryKey) => {
      // 首先从数据库的categories中查找
      const category = categories.value.find(cat => cat.key === categoryKey)
      if (category) {
        return category.display_name
      }

      // 如果没找到，使用默认映射
      const nameMap = {
        'web': 'Web服务器',
        'database': '数据库',
        'middleware': '中间件',
        'security': '安全组件',
        'monitor': '监控组件',
        'network': '网络组件',
        'storage': '存储组件',
        'other': '其他'
      }
      return nameMap[categoryKey?.toLowerCase()] || (categoryKey || '未分类')
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleString('zh-CN')
    }

    // 内联编辑方法
    const startInlineEdit = (component) => {
      editingComponentId.value = component.id
      Object.assign(editingData, {
        name: component.name || '',
        display_name: component.display_name || '',
        category_key: component.category_key || '',
        version: component.version || '',
        default_port: component.default_port || '',
        protocol: component.protocol || ''
      })
    }

    const cancelInlineEdit = () => {
      editingComponentId.value = null
      Object.assign(editingData, {
        name: '',
        display_name: '',
        category_key: '',
        version: '',
        default_port: '',
        protocol: ''
      })
    }

    const saveInlineEdit = async (component) => {
      try {
        const response = await axios.put(`/excel/components/${component.id}`, {
          ...component,
          name: editingData.name,
          display_name: editingData.display_name,
          category_key: editingData.category_key,
          version: editingData.version,
          default_port: editingData.default_port,
          protocol: editingData.protocol,
          form_type: selectedFormType.value
        })

        if (response.data.status === 'success') {
          await loadData()
          cancelInlineEdit()
          showToast('组件信息更新成功', '成功', 'success')
        } else {
          showToast('保存失败: ' + response.data.message, '错误', 'error')
        }
      } catch (error) {
        console.error('保存组件失败:', error)
        showToast('保存失败: ' + (error.response?.data?.message || error.message), '错误', 'error')
      }
    }

    // 方法
    const loadFormTypes = async () => {
      try {
        const response = await axios.get('/excel/form-types')
        if (response.data.status === 'success') {
          availableFormTypes.value = response.data.data.map(formType => formType.name)
        }
      } catch (error) {
        console.error('加载表单类型失败:', error)
        availableFormTypes.value = ['安全测评', '安全监测', '应用加固']
      }
    }

    const loadData = async () => {
      loading.value = true
      console.log('🔄 开始加载数据，当前表单类型:', selectedFormType.value)

      try {
        // 加载分类
        const categoriesUrl = `/excel/component-categories${selectedFormType.value ? `?form_type=${encodeURIComponent(selectedFormType.value)}` : ''}`
        console.log('📂 加载分类 URL:', categoriesUrl)

        const categoriesResponse = await axios.get(categoriesUrl)
        if (categoriesResponse.data.status === 'success') {
          categories.value = categoriesResponse.data.data || []
          console.log('✅ 分类加载成功，数量:', categories.value.length)
        }

        // 加载组件数据 - 修改逻辑，总是尝试加载组件
        const componentsUrl = selectedFormType.value
          ? `/excel/components/by-category?form_type=${encodeURIComponent(selectedFormType.value)}`
          : '/excel/components/by-category'

        console.log('🧩 加载组件 URL:', componentsUrl)

        const componentsResponse = await axios.get(componentsUrl)
        if (componentsResponse.data.status === 'success') {
          componentsByCategory.value = componentsResponse.data.data || {}
          console.log('✅ 组件加载成功，分类数量:', Object.keys(componentsByCategory.value).length)

          // 打印每个分类的组件数量和详细信息
          Object.entries(componentsByCategory.value).forEach(([categoryKey, categoryData]) => {
            const componentCount = categoryData.components ? categoryData.components.length : 0
            console.log(`  📁 ${categoryKey}: ${componentCount} 个组件`)

            if (categoryData.components && categoryData.components.length > 0) {
              categoryData.components.forEach(component => {
                console.log(`    🧩 ${component.name} (${component.display_name}) - ID: ${component.id}`)
              })
            }
          })

          // 🔧 检查是否有遗漏的组件 - 尝试加载所有组件进行对比
          console.log('🔍 检查是否有遗漏的组件...')
          try {
            const allComponentsUrl = selectedFormType.value
              ? `/excel/components?form_type=${encodeURIComponent(selectedFormType.value)}`
              : '/excel/components'

            const allComponentsResponse = await axios.get(allComponentsUrl)
            if (allComponentsResponse.data.status === 'success') {
              const allComponents = allComponentsResponse.data.data || []
              console.log(`📊 API返回的所有组件数量: ${allComponents.length}`)

              // 统计分类组件的总数
              const categoryComponentCount = Object.values(componentsByCategory.value)
                .reduce((total, categoryData) => total + (categoryData.components ? categoryData.components.length : 0), 0)

              console.log(`📊 分类组件总数: ${categoryComponentCount}`)

              if (allComponents.length > categoryComponentCount) {
                console.warn(`⚠️ 发现遗漏组件! 总数: ${allComponents.length}, 分类显示: ${categoryComponentCount}`)

                // 找出遗漏的组件
                const categoryComponentIds = new Set()
                Object.values(componentsByCategory.value).forEach(categoryData => {
                  if (categoryData.components) {
                    categoryData.components.forEach(comp => categoryComponentIds.add(comp.id))
                  }
                })

                const missingComponents = allComponents.filter(comp => !categoryComponentIds.has(comp.id))
                console.log('🚨 遗漏的组件:', missingComponents.map(c => `${c.name} (ID: ${c.id}, 分类: ${c.category_key})`))

                // 🔧 自动修复：将遗漏的组件添加到对应分类中
                console.log('🔧 自动修复遗漏组件...')
                missingComponents.forEach(component => {
                  const categoryKey = component.category_key || 'uncategorized'

                  if (!componentsByCategory.value[categoryKey]) {
                    // 创建新分类
                    componentsByCategory.value[categoryKey] = {
                      category_info: {
                        key: categoryKey,
                        display_name: categoryKey === 'uncategorized' ? '未分类' :
                                     categoryKey === 'uncategorized-testing' ? '测试组件' : categoryKey,
                        description: '自动创建的分类',
                        icon: 'bi-folder'
                      },
                      components: []
                    }
                    console.log(`📁 创建新分类: ${categoryKey}`)
                  }

                  // 添加组件到分类
                  componentsByCategory.value[categoryKey].components.push(component)
                  console.log(`✅ 将组件 ${component.name} 添加到分类 ${categoryKey}`)
                })

                console.log('🎉 遗漏组件修复完成!')

                // 🔧 同步更新分类列表，确保新分类能在UI中显示
                const existingCategoryKeys = new Set(categories.value.map(cat => cat.key))
                Object.entries(componentsByCategory.value).forEach(([categoryKey, categoryData]) => {
                  if (!existingCategoryKeys.has(categoryKey) && categoryData.category_info) {
                    categories.value.push({
                      key: categoryKey,
                      display_name: categoryData.category_info.display_name,
                      description: categoryData.category_info.description,
                      icon: categoryData.category_info.icon,
                      form_type: selectedFormType.value
                    })
                    console.log(`📋 添加分类到列表: ${categoryKey}`)
                  }
                })
              }
            }
          } catch (error) {
            console.warn('⚠️ 检查遗漏组件时出错:', error)
          }
        } else {
          console.warn('⚠️ 组件加载失败:', componentsResponse.data.message)
          componentsByCategory.value = {}
        }
      } catch (error) {
        console.error('❌ 加载数据失败:', error)
        categories.value = []
        componentsByCategory.value = {}
      } finally {
        loading.value = false
        console.log('🏁 数据加载完成')
      }
    }

    const handleUrlParams = () => {
      const formType = route.query.formType
      if (formType) {
        selectedFormType.value = formType
        loadData()
      }
    }

    // 组件分类相关方法
    const getComponentCount = (categoryKey) => {
      const categoryData = componentsByCategory.value[categoryKey]
      return categoryData ? categoryData.components.length : 0
    }

    const getComponentsByCategory = (categoryKey) => {
      const categoryData = componentsByCategory.value[categoryKey]
      return categoryData ? categoryData.components : []
    }

    const isUncategorizedCategory = (category) => {
      return category && category.key && category.key.startsWith('uncategorized')
    }

    const manualToggleCollapse = (categoryKey) => {
      const index = expandedCategoryKeys.value.indexOf(categoryKey)
      if (index > -1) {
        expandedCategoryKeys.value.splice(index, 1)
      } else {
        expandedCategoryKeys.value.push(categoryKey)
      }
    }

    // 快速创建方法
    const createFromCategory = (category) => {
      if (!selectedFormType.value) {
        alert('请先选择表单类型')
        return
      }

      // 直接调用创建组件模态框，并预设分类
      showCreateComponentModal(category.key)
    }

    const showTemplateManager = () => {
      alert('模板管理功能开发中...')
    }

    // 分类管理方法
    const showCreateCategoryModal = () => {
      if (!selectedFormType.value) {
        showToast('请先选择表单类型', '提示', 'warning')
        return
      }

      editingCategory.value = null
      Object.assign(categoryForm, {
        key: '',
        display_name: '',
        description: '',
        icon: 'bi-folder',
        form_type: selectedFormType.value
      })

      const modal = new Modal(document.getElementById('categoryModal'))
      modal.show()
    }

    const editCategory = (category) => {
      if (!selectedFormType.value) {
        showToast('请先选择表单类型', '提示', 'warning')
        return
      }

      editingCategory.value = category
      Object.assign(categoryForm, {
        key: category.key,
        display_name: category.display_name,
        description: category.description || '',
        icon: category.icon || 'bi-folder',
        form_type: selectedFormType.value
      })

      const modal = new Modal(document.getElementById('categoryModal'))
      modal.show()
    }

    const saveCategory = async () => {
      try {
        const url = editingCategory.value
          ? `/excel/component-categories/${editingCategory.value.id}`
          : '/excel/component-categories'

        const method = editingCategory.value ? 'put' : 'post'

        // 🔧 修复：后端API期望 form_types 数组，而不是 form_type 字符串
        const requestData = {
          ...categoryForm,
          form_types: categoryForm.form_type ? [categoryForm.form_type] : [],
          form_type: categoryForm.form_type  // 同时保留单数字段以兼容
        }

        console.log('📋 准备保存分类数据:', requestData)
        console.log('🔗 请求URL:', url)
        console.log('📤 请求方法:', method)

        const response = await axios[method](url, requestData)

        if (response.data.status === 'success') {
          const modal = Modal.getInstance(document.getElementById('categoryModal'))
          modal.hide()

          await loadData()
          showToast(editingCategory.value ? '分类更新成功' : '分类创建成功', '成功', 'success')
        } else {
          showToast('操作失败: ' + response.data.message, '错误', 'error')
        }
      } catch (error) {
        console.error('保存分类失败:', error)
        showToast('操作失败: ' + (error.response?.data?.message || error.message), '错误', 'error')
      }
    }

    const confirmDeleteCategory = async (category) => {
      if (!confirm(`确定要删除分类"${category.display_name}"吗？\n\n注意：该分类下的组件将被移动到"未分类"中。`)) {
        return
      }

      try {
        const response = await axios.delete(`/excel/component-categories/${category.id}`)

        if (response.data.status === 'success') {
          await loadData()
          showToast(response.data.message, '成功', 'success')
        } else {
          showToast('删除失败: ' + response.data.message, '错误', 'error')
        }
      } catch (error) {
        console.error('删除分类失败:', error)
        showToast('删除失败: ' + (error.response?.data?.message || error.message), '错误', 'error')
      }
    }

    const showCreateComponentModal = (categoryKey = '') => {
      if (!selectedFormType.value) {
        alert('请先选择表单类型')
        return
      }

      editingComponent.value = null
      Object.assign(componentForm, {
        name: '',
        display_name: '',
        category_key: categoryKey,
        version: '',
        default_port: '',
        description: '',
        protocol: '',
        form_type: selectedFormType.value
      })

      // 使用安全的模态框显示方法
      if (window.backdropKiller && window.backdropKiller.safeShow) {
        window.backdropKiller.safeShow('componentModal', { backdrop: true })
      } else {
        // 后备方案：使用原生Bootstrap
        const modalElement = document.getElementById('componentModal')
        modalElement.setAttribute('data-safe-modal', 'true')
        const modal = new Modal(modalElement, {
          backdrop: 'static',
          keyboard: true
        })
        modal.show()
      }
    }



    // 批量操作方法
    const batchEnable = () => {
      alert('批量启用功能开发中...')
    }

    const batchDisable = () => {
      alert('批量禁用功能开发中...')
    }

    const batchDelete = () => {
      alert('批量删除功能开发中...')
    }

    const exportComponents = () => {
      alert('导出组件功能开发中...')
    }

    const resetColumnWidths = () => {
      alert('重置列宽功能开发中...')
    }

    const toggleActiveStatus = async (component, newStatus) => {
      try {
        const response = await axios.put(`/excel/components/${component.id}`, {
          ...component,
          is_active: newStatus
        })

        if (response.data.status === 'success') {
          await loadData()
          showToast(`组件已${newStatus ? '启用' : '禁用'}`, '成功', 'success')
        } else {
          showToast('操作失败: ' + response.data.message, '错误', 'error')
        }
      } catch (error) {
        console.error('切换组件状态失败:', error)
        showToast('操作失败: ' + (error.response?.data?.message || error.message), '错误', 'error')
      }
    }

    const goBack = () => {
      router.push('/form-template-manager')
    }

    const showAddComponentModal = () => {
      showCreateComponentModal()
    }

    const editComponent = (component) => {
      editingComponent.value = component
      Object.assign(componentForm, {
        name: component.name || '',
        display_name: component.display_name || '',
        category_key: component.category_key || '',
        version: component.version || '',
        default_port: component.default_port || '',
        description: component.description || '',
        protocol: component.protocol || '',
        form_type: selectedFormType.value
      })

      // 使用安全的模态框显示方法
      if (window.backdropKiller && window.backdropKiller.safeShow) {
        window.backdropKiller.safeShow('componentModal', { backdrop: true })
      } else {
        // 后备方案：使用原生Bootstrap
        const modalElement = document.getElementById('componentModal')
        modalElement.setAttribute('data-safe-modal', 'true')
        const modal = new Modal(modalElement, {
          backdrop: 'static',
          keyboard: true
        })
        modal.show()
      }
    }

    const saveComponent = async () => {
      try {
        const url = editingComponent.value 
          ? `/excel/components/${editingComponent.value.id}`
          : '/excel/components'
        
        const method = editingComponent.value ? 'put' : 'post'
        
        const response = await axios[method](url, componentForm)

        if (response.data.status === 'success') {
          console.log('✅ 组件保存成功:', response.data.data)
          console.log('📋 保存的组件信息:', {
            name: componentForm.name,
            display_name: componentForm.display_name,
            category_key: componentForm.category_key,
            form_type: componentForm.form_type
          })
          console.log('🆔 新组件ID:', response.data.data?.id)
          console.log('📂 新组件分类:', response.data.data?.category_key)

          // 安全关闭模态框
          const modalElement = document.getElementById('componentModal')
          if (modalElement) {
            const modal = Modal.getInstance(modalElement)
            if (modal) {
              modal.hide()
            } else {
              // 手动关闭
              modalElement.style.display = 'none'
              modalElement.classList.remove('show')
              document.body.classList.remove('modal-open')
              // 清理可能的遮罩
              const backdrops = document.querySelectorAll('.modal-backdrop, .custom-modal-backdrop')
              backdrops.forEach(backdrop => backdrop.remove())
            }
          }

          console.log('🔄 开始重新加载数据...')
          await loadData()
          showToast(editingComponent.value ? '组件更新成功' : '组件添加成功', '成功', 'success')
        } else {
          console.error('❌ 组件保存失败:', response.data.message)
          showToast('操作失败: ' + response.data.message, '错误', 'error')
        }
      } catch (error) {
        console.error('保存组件失败:', error)
        showToast('操作失败: ' + (error.response?.data?.message || error.message), '错误', 'error')
      }
    }

    const deleteComponent = async (component) => {
      if (!confirm(`确定要删除组件"${component.display_name || component.name}"吗？此操作不可恢复。`)) {
        return
      }

      try {
        const response = await axios.delete(`/excel/components/${component.id}`)

        if (response.data.status === 'success') {
          await loadData()
          showToast('组件删除成功', '成功', 'success')
        } else {
          showToast('删除失败: ' + response.data.message, '错误', 'error')
        }
      } catch (error) {
        console.error('删除组件失败:', error)
        showToast('删除失败: ' + (error.response?.data?.message || error.message), '错误', 'error')
      }
    }

    // Toast 提示方法
    const showToast = (message, title = '提示', type = 'info') => {
      if (toast.value) {
        toast.value.showToast(message, title, type)
      } else {
        // 后备方案：使用 alert
        console.log(`Toast [${type}] ${title}: ${message}`)
        if (type === 'error') {
          alert(`错误: ${message}`)
        } else if (type === 'success') {
          alert(`成功: ${message}`)
        } else {
          alert(`${title}: ${message}`)
        }
      }
    }

    // 生命周期
    onMounted(async () => {
      await loadFormTypes()
      handleUrlParams()
    })

    return {
      selectedFormType,
      availableFormTypes,
      categories,
      componentsByCategory,
      loading,
      editingComponent,
      componentForm,
      viewMode,
      selectedComponents,
      expandedCategoryKeys,
      editingComponentId,
      editingData,
      toast,
      editingCategory,
      categoryForm,
      commonIcons,
      getAllComponents,
      getSortedComponents,
      isAllSelected,
      toggleSelectAll,
      getComponentCount,
      getComponentsByCategory,
      isUncategorizedCategory,
      manualToggleCollapse,
      createFromCategory,
      showTemplateManager,
      showCreateCategoryModal,
      editCategory,
      saveCategory,
      confirmDeleteCategory,
      showCreateComponentModal,
      batchEnable,
      batchDisable,
      batchDelete,
      exportComponents,
      resetColumnWidths,
      toggleActiveStatus,
      loadData,
      goBack,
      showAddComponentModal,
      editComponent,
      saveComponent,
      deleteComponent,
      getComponentIcon,
      getCategoryBadgeClass,
      getCategoryDisplayName,
      formatDate,
      startInlineEdit,
      cancelInlineEdit,
      saveInlineEdit,
      showToast
    }
  }
}
</script>

<style scoped>
/* 简洁的表格样式 */
.table td {
  vertical-align: middle;
  padding: 0.5rem;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  padding: 0.75rem 0.5rem;
}

/* 内联编辑样式 */
.table td span {
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.15s;
}

.table td span:hover {
  background-color: #f8f9fa;
}

/* 表单控件样式 */
.form-control-sm, .form-select-sm {
  font-size: 0.875rem;
}

/* 空状态样式 */
.empty-state {
  padding: 2rem;
  text-align: center;
}

/* 图标选择器样式 */
.icon-selector {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 0.5rem;
  background-color: #f8f9fa;
}

.icon-selector .btn {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 2px;
}

.icon-selector .btn.active {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: white;
}

.icon-preview {
  background-color: #f8f9fa;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }

  .table td, .table th {
    padding: 0.25rem;
  }

  .icon-selector .btn {
    width: 35px;
    height: 35px;
    margin: 1px;
  }
}
</style>
