<template>
  <div class="mt-4">
    <div class="row mb-3">
      <div class="col-12">
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
          <button
            type="button"
            class="btn btn-outline-secondary me-md-2"
            @click="$emit('reset')"
          >
            <i class="bi bi-arrow-counterclockwise me-1"></i>重置表单
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            :disabled="loading"
          >
            <span v-if="loading" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
            <i v-else class="bi bi-file-earmark-excel me-1"></i>生成Excel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 表单底部组件
 * 包含重置和提交按钮
 */
export default {
  name: 'FormFooter',
  props: {
    // 是否正在加载
    loading: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
/* 按钮样式 */
button.btn {
  transition: all 0.2s ease;
}

button.btn:hover {
  transform: translateY(-1px);
}
</style>
