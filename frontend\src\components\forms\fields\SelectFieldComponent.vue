<template>
  <div class="select-field-component">
    <select 
      :id="id"
      :value="value"
      :required="field.is_required"
      :disabled="field.is_readonly"
      :class="fieldClasses"
      @change="handleChange"
    >
      <option value="">{{ field.placeholder || '请选择...' }}</option>
      <option 
        v-for="(label, optionValue) in options" 
        :key="optionValue" 
        :value="optionValue"
      >
        {{ label }}
      </option>
    </select>
  </div>
</template>

<script>
export default {
  name: 'SelectFieldComponent',
  props: {
    id: String,
    field: {
      type: Object,
      required: true
    },
    value: {
      type: [String, Number],
      default: ''
    }
  },
  emits: ['update:value', 'field-change'],
  computed: {
    options() {
      return this.field.field_options || {}
    },
    fieldClasses() {
      let classes = ['form-select']
      
      if (this.field.css_classes) {
        classes.push(this.field.css_classes)
      }
      
      if (this.field.is_readonly) {
        classes.push('readonly-field')
      }
      
      return classes.join(' ')
    }
  },
  methods: {
    handleChange(event) {
      const newValue = event.target.value
      this.$emit('update:value', newValue)
      this.$emit('field-change', {
        fieldName: this.field.field_name,
        fieldType: this.field.field_type,
        value: newValue,
        event: 'change'
      })
    }
  }
}
</script>

<style scoped>
.readonly-field {
  background-color: #f8f9fa;
  cursor: not-allowed;
}
</style>
