# ============================================================================
# Export Excel Backend - Stable Dependencies
# ============================================================================
# 生成时间: 2025-01-15
# Python版本: >=3.11
# 说明: 使用经过验证的稳定版本，确保安装成功
# ============================================================================

# Web框架核心
Flask==2.3.3
Werkzeug==2.3.7
Jinja2==3.1.2
MarkupSafe==2.1.3
itsdangerous==2.1.2
click==8.1.7
blinker==1.6.3

# 认证与安全
Flask-JWT-Extended==4.5.3
Flask-Bcrypt==1.0.1
PyJWT==2.8.0
bcrypt==4.0.1
cryptography==41.0.7

# 数据库相关
Flask-SQLAlchemy==3.0.5
SQLAlchemy==1.4.46
Flask-Migrate==4.0.5
alembic==1.12.1
PyMySQL==1.1.0

# 数据处理与Excel
pandas==2.1.4
numpy==1.26.4
openpyxl==3.1.2
python-dateutil==2.8.2
pytz==2023.3
six==1.16.0

# 缓存与性能
Flask-Caching==2.1.0
redis==5.0.1
psutil==5.9.6

# HTTP与网络
Flask-Cors==4.0.0
requests==2.31.0
urllib3==2.0.7
certifi==2023.11.17

# 图像处理
Pillow==10.1.0

# 表单处理
Flask-WTF==1.1.1
WTForms==3.1.1

# 工具与实用程序
python-dotenv==1.0.1
watchdog==3.0.0

# 生产环境部署
gunicorn==21.2.0

# Windows环境支持
colorama==0.4.6

# 模板引擎（Alembic依赖）
Mako==1.3.0

# 类型支持
typing_extensions==4.8.0
